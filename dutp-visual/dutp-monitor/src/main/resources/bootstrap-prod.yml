spring:
  config:
    activate:
      on-profile: prod
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        namespace: prod
        username: dutp
        password: dutp@!2025
      config:
        # 命名空间
        server-addr: 127.0.0.1:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
        namespace: prod
        username: dutp
        password: dutp@!2025

version: '3'
services: 
  elasticsearch:
    image: registry.cn-beijing.aliyuncs.com/belllab/elasticsearch:7.17.6
    restart: always
    container_name: elasticsearch
    volumes:
      - ./config/ik:/usr/share/elasticsearch/plugins/ik
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      - cluster.name=elasticsearch
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
    networks: 
      - stack
    ulimits:
      nofile:
          soft: 65535
          hard: 65535
  kibana:
    image: registry.cn-beijing.aliyuncs.com/belllab/kibana:7.17.6
    restart: always
    container_name: kibana
    ports:
      - 5601:5601
    # volumes:
            #    - "./config/kibana.yml:/usr/share/kibana/config/kibana.yml"
    networks: 
      - stack
    depends_on: ['elasticsearch']
 # rocket mq name server
  rmqnamesrv:
    image: registry.cn-beijing.aliyuncs.com/belllab/rocketmq:4.9.4
    restart: always
    container_name: rocket-server
    networks:
      - rocketmq
    ports:
      - 9876:9876
    command: sh mqnamesrv
  # rocket mq broker
  rmqbroker:
    image: registry.cn-beijing.aliyuncs.com/belllab/rocketmq:4.9.4
    restart: always
    container_name: rocket-broker
    volumes:
      # 映射本地目录权限一定要设置为 777 权限，否则启动不成功
      # - ../volumes/data/rocket/broker/logs:/home/<USER>/logs
      # - ../volumes/data/rocket/broker/store:/home/<USER>/store
      - ./config/broker.conf:/opt/rocketmq-4.9.4/conf/broker.conf
    environment:
      - NAMESRV_ADDR=rmqnamesrv:9876
      # - JAVA_OPTS:=-Duser.home=/opt
      - JAVA_OPT_EXT=-server -Xms64m -Xmx64m -Xmn64m
    depends_on:
      - rmqnamesrv
    networks:
      - rocketmq
    ports:
      - 10909:10909
      - 10911:10911
    command: sh mqbroker -c /opt/rocketmq-4.9.4/conf/broker.conf
networks: 
  rocketmq:
    driver: bridge
  stack:
    driver: bridge


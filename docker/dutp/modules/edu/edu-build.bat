@echo off
setlocal enabledelayedexpansion
set WEB_IMAGE_NAME=image-dutp-edu
for /f "tokens=*" %%j in ('docker images ls -a -q -f "reference=%WEB_IMAGE_NAME%"') do (
    docker rmi -f %%j
)
docker build -t %WEB_IMAGE_NAME%:%1 .
docker login --username=bell_developer@bell registry.cn-beijing.aliyuncs.com --password=bell@!2023
for /f "tokens=*" %%i in ('docker images --filter "reference=%WEB_IMAGE_NAME%" --format "{{.ID}}"') do (
    SET WEB_IMAGE_ID=%%i
)

echo %WEB_IMAGE_ID%
docker tag %WEB_IMAGE_ID% registry.cn-beijing.aliyuncs.com/dutp/dutp-edu-api:%1
docker push registry.cn-beijing.aliyuncs.com/dutp/dutp-edu-api:%1
#!/bin/bash
new_version="$1"
str_version="LASTVERSION"
if [ "$1" == "" ]; then
	echo "input web-api version"
else
	sed -i "s/$str_version/$new_version/g" ./docker-compose.yml
	docker stop `docker ps -a |grep dutp-basic-api | awk '{print $1}'`
	docker rm `docker ps -a |grep dutp-basic-api | awk '{print $1}'`
	docker rmi `docker images |grep dutp-basic-api | awk '{print $3}'`
	docker login --username=bell_developer@bell registry.cn-beijing.aliyuncs.com --password=bell@!2023
	docker pull registry.cn-beijing.aliyuncs.com/dutp/dutp-basic-api:"$1"
	docker compose up -d
	sed -i "s/$new_version/$str_version/g" ./docker-compose.yml
fi 

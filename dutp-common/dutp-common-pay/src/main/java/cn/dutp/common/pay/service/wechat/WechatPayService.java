package cn.dutp.common.pay.service.wechat;

import cn.dutp.common.core.utils.SpringUtils;
import cn.dutp.common.pay.beans.WechatPayKey;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.service.partnerpayments.app.AppServiceExtension;
import com.wechat.pay.java.service.partnerpayments.app.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * 微信支付服务
 * <AUTHOR>
 * 此为微信支付相关接口，需在微信商户平台配置相关参数
 * 使用了APP接口，参数和JSAPI接口效果一致,预下单后参数返回给前端使用
 * H5版本和Native版本下单请分别调用WechatPayh5Service和WechatPayNativeService
 * 支付回调暂未实现
 */
@Service
public class WechatPayService {

    private static final WechatPayKey WECHAT_PAY_KEY = SpringUtils.getBean(WechatPayKey.class);

    private final static Logger log = LoggerFactory.getLogger(WechatPayService.class);

    public static AppServiceExtension serviceExtension;


    @PostConstruct
    public void init() {
        // 初始化商户配置
        Config config = new RSAAutoCertificateConfig.Builder()
                        .merchantId(WECHAT_PAY_KEY.getMerchantId())
                        // 使用 com.wechat.pay.java.core.util 中的函数从本地文件中加载商户私钥，商户私钥会用来生成请求的签名
                        .privateKeyFromPath(WECHAT_PAY_KEY.getPrivateKeyPath())
                        .merchantSerialNumber(WECHAT_PAY_KEY.getMerchantSerialNumber())
                        .apiV3Key(WECHAT_PAY_KEY.getApiV3Key())
                        .build();
        // 初始化服务
        serviceExtension = new AppServiceExtension.Builder().config(config).build();
    }

    /**
     * JSAPI支付下单，并返回JSAPI调起支付数据
     * 返回的参数用于提供前端拉起支付
     *
     * @param description 商品描述，用于支付界面显示
     * @param outTradeNo 商户订单号，确保唯一性，由商户自定义
     * @param totalFee 订单总金额，单位为分
     * @return 返回支付所需的数据对象，包含前端拉起支付所需的参数
     */
    public PrepayWithRequestPaymentResponse prepayWithRequestPayment(String description,String outTradeNo,int totalFee) {
        // 创建预支付请求对象
        PrepayRequest request = new PrepayRequest();
        // 设置小程序ID
        request.setSpAppid(WECHAT_PAY_KEY.getAppId());
        // 设置商户号
        request.setSpMchid(WECHAT_PAY_KEY.getMerchantId());
        // 设置商品描述
        request.setDescription(description);
        // 设置商户订单号
        request.setOutTradeNo(outTradeNo);

        // 创建金额对象
        Amount amount = new Amount();
        // 设置订单总金额
        amount.setTotal(totalFee);
        // 将金额对象添加到请求对象中
        request.setAmount(amount);
        
        log.info("微信支付请求参数：{}", request);
        // 调用接口
        return serviceExtension.prepayWithRequestPayment(request, WECHAT_PAY_KEY.getAppId());
    }

    /** 微信支付订单号查询订单 */
    public Transaction queryOrderById(String transactionId) {

        QueryOrderByIdRequest request = new QueryOrderByIdRequest();
        request.setTransactionId(transactionId);
        request.setSpMchid(WECHAT_PAY_KEY.getMerchantId());

        log.info("微信支付订单号查询参数：{}", request);
        // 调用接口
        return serviceExtension.queryOrderById(request);
    }


    /** 商户订单号查询订单 */
    public static Transaction queryOrderByOutTradeNo(String outTradeNo) {

        QueryOrderByOutTradeNoRequest request = new QueryOrderByOutTradeNoRequest();
        request.setOutTradeNo(outTradeNo);
        request.setSpMchid(WECHAT_PAY_KEY.getMerchantId());

        log.info("微信支付商户订单号查询参数：{}", request);
        // 调用接口
        return serviceExtension.queryOrderByOutTradeNo(request);
    }

    /** 商户订单号关闭订单 */
    public void closeOrder (String outTradeNo) {
        CloseOrderRequest request = new CloseOrderRequest();
        request.setOutTradeNo(outTradeNo);

        log.info("微信支付商户订单号关闭参数：{}", request);
        serviceExtension.closeOrder(request);
    }

}

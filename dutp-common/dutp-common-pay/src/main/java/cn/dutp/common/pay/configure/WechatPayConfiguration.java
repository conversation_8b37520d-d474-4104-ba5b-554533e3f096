package cn.dutp.common.pay.configure;



import cn.dutp.common.pay.beans.WechatPayKey;
import cn.dutp.common.pay.configure.properties.WechatPayProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 读取yml中的支付配置
 * @author: wangjiaxin
 */

@Configuration
@EnableConfigurationProperties(WechatPayProperties.class)
public class WechatPayConfiguration {

    @Bean
    public WechatPayKey wechatPayKey(WechatPayProperties wechatPayProperties) {
        WechatPayKey wechatPayKey = new WechatPayKey();

        //将配置里读到的属性赋给 wechatPayKey
        wechatPayKey.setMerchantId(wechatPayProperties.getMerchantId());
        wechatPayKey.setPrivateKeyPath(wechatPayProperties.getPrivateKeyPath());
        wechatPayKey.setMerchantSerialNumber(wechatPayProperties.getMerchantSerialNumber());
        wechatPayKey.setApiV3Key(wechatPayProperties.getApiV3Key());
        wechatPayKey.setAppId(wechatPayProperties.getAppId());
        wechatPayKey.setNotifyUrl(wechatPayProperties.getNotifyUrl());
        wechatPayKey.setRefundNotifyUrl(wechatPayProperties.getRefundNotifyUrl());

        return wechatPayKey;
    }

}


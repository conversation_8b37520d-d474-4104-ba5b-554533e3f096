package cn.dutp.common.qrcode.service;

import cn.dutp.common.core.exception.ServiceException;
import com.google.zxing.*;
import com.google.zxing.client.j2se.BufferedImageLuminanceSource;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.common.HybridBinarizer;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFPicture;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 二维码解析服务
 * <AUTHOR>
 */
@Service
public class QrcodeReaderService {

    private final static Logger log = LoggerFactory.getLogger(QrcodeReaderService.class);

    /**
     * 生成二维码图片
     *
     * @param content 要编码为二维码的内容
     * @param outputFilePath 二维码图片的输出路径
     * @param width 二维码图片的宽度
     * @param height 二维码图片的高度
     */
    public static void generateQrCode(String content, Path outputFilePath, int width, int height)  throws ServiceException {
        // 创建BitMatrix对象，它包含了二维码的矩阵信息
        BitMatrix bitMatrix = getBitMatrix(content, width, height);


        // 使用MatrixToImageWriter类将BitMatrix写入到指定路径，使用PNG格式
        try {
            MatrixToImageWriter.writeToPath(bitMatrix, "png", outputFilePath);
        } catch (IOException e) {
            log.error("生成二维码失败：{}",e.getMessage());
            throw new ServiceException("生成二维码失败："+e.getMessage());
        }
    }

    /**
     * 将给定的内容转换为指定宽度和高度的BitMatrix，用于生成QR码。
     *
     * @param content 要编码的内容字符串。
     * @param width   生成的QR码图像的宽度。
     * @param height  生成的QR码图像的高度。
     * @return BitMatrix对象，包含编码后的内容。
     */
    private static BitMatrix getBitMatrix(String content, int width, int height) throws ServiceException {
        // 初始化编码提示配置，使用HashMap存储配置项
        Map<EncodeHintType, Object> hints = new HashMap<>(4);

        // 设置字符集为UTF-8，确保内容编码正确
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");

        // 设置错误纠正级别为L，提高QR码的容错率
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.L);

        // 使用MultiFormatWriter编码内容为QR码格式的BitMatrix
        try {
            return new MultiFormatWriter().encode(content, BarcodeFormat.QR_CODE, width, height, hints);
        } catch (WriterException e) {
            log.error("生成二维码BitMatrix失败：{}",e.getMessage());
            throw new ServiceException("生成二维码失败："+e.getMessage());
        }
    }

    /**
     * 生成二维码图像
     *
     * @param content 二维码所包含的内容
     * @param width 二维码图像的宽度
     * @param height 二维码图像的高度
     * @return 返回一个包含二维码图像的OutputStream
     */
    public static OutputStream generateQrCode(String content, int width, int height)  throws ServiceException {
        // 创建一个BitMatrix对象，它将用于存储二维码的数据
        BitMatrix bitMatrix = getBitMatrix(content, width, height);

        // 将BitMatrix转换为BufferedImage对象，以便于图像处理
        BufferedImage bufferedImage = toBufferedImage(bitMatrix);

        // 创建一个ByteArrayOutputStream对象，用于存储二维码图像的二进制数据
        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        // 将BufferedImage写入ByteArrayOutputStream中，使用PNG格式编码
        try {
            ImageIO.write(bufferedImage, "png", baos);
        } catch (IOException e) {
            log.error("生成二维码,写入文件失败：{}",e.getMessage());
            throw new ServiceException("生成二维码写入文件失败："+e.getMessage());
        }

        // 返回包含二维码图像数据的ByteArrayOutputStream对象
        return baos;
    }


    /**
     * 将BitMatrix对象转换为BufferedImage对象
     * 此方法主要用于将二维码生成过程中的BitMatrix转换为可以显示或保存的图像
     *
     * @param matrix BitMatrix对象，包含了二维码的矩阵信息
     * @return BufferedImage对象，可以用于显示或保存二维码图像
     */
    private static BufferedImage toBufferedImage(BitMatrix matrix) {
        // 获取二维码矩阵的宽度和高度
        int width = matrix.getWidth();
        int height = matrix.getHeight();
        // 创建一个BufferedImage对象，用于渲染二维码图像
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        // 遍历二维码矩阵的每一个点，设置图像的像素值
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                // 根据矩阵点的值设置图像对应像素的颜色，true代表黑色，false代表白色
                image.setRGB(x, y, matrix.get(x, y)? 0xFF000000 : 0xFFFFFFFF);
            }
        }
        // 返回渲染完成的二维码图像
        return image;
    }

/**
 * 解析网络图片中的二维码信息。
 *
 * @param imageUrl 网络图片的URL地址
 * @return 二维码中包含的信息
 * @throws Exception 如果下载图片或解析二维码过程中发生错误
 */
public static String decodeQrCode(String imageUrl) throws Exception {
    // 下载网络图片到本地临时文件
    URL url = new URL(imageUrl);
    File tempFile;
    try (InputStream in = url.openStream()) {
        tempFile = File.createTempFile("tempImage", ".png");
        tempFile.deleteOnExit();
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                fos.write(buffer, 0, bytesRead);
            }
        }
    }

    // 使用 ZXing 解析二维码
    BufferedImage image = ImageIO.read(tempFile);
    LuminanceSource source = new BufferedImageLuminanceSource(image);
    BinaryBitmap bitmap = new BinaryBitmap(new HybridBinarizer(source));
    Result result = new MultiFormatReader().decode(bitmap);
    log.info("解析结果：" + result.getText());
    return result.getText();
}


    /**
     * 从给定的URL下载文档文件，并解码其中的二维码。
     * 支持doc和docx文件格式。
     *
     * @param url 文档文件的URL地址
     * @return 二维码解码后的字符串列表
     */
    public static List<String> decodeQrCodeFromUrl(String url) {
        // 先查一下路径里带不带doc或者docx
        String extension = url.substring(url.lastIndexOf(".") + 1);
        String suffix;
        // 此处留下后缀名，如果都识别不了，返回异常
        if (extension.contains("doc") || extension.contains("docx")) {
            // 明确是哪种
            suffix = extension.contains("docx") ? ".docx" : ".doc";
        } else {
            log.error("路径中寻找文件后缀名是：{}，找不到doc或者docx，处理失败", extension);
            throw new ServiceException("文件后缀名不正确,只能处理doc或者docx文件");
        }
        // 下载网络图片到本地临时文件
        File tempFile;
        try (InputStream in = new URL(url).openStream()) {
            tempFile = File.createTempFile("tempImage", suffix);
            tempFile.deleteOnExit();
            // 将输入流中的数据写入临时文件
            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                // 循环读取输入流中的数据，并写入临时文件
                while ((bytesRead = in.read(buffer)) != -1) {
                    fos.write(buffer, 0, bytesRead);
                }
            }
        } catch (IOException e) {
            log.error("从URL下载文件失败：{}", e.getMessage());
            throw new ServiceException("从URL下载文件失败：" + e.getMessage());
        }
        // 调用方法解码临时文件中的二维码
        return decodeQrCodeFromWordDocument(tempFile);
    }

    /**
     *     新增方法，输入一个 word 文档，识别其中的图片是否为二维码，如果是则将内容列表返回
     *     遇到异常时，如果有结果，会将部分结果返回，如果还没有结果，那么将会报错
     */

    public static List<String> decodeQrCodeFromWordDocument(File wordFile)  {
        List<String> qrContents = new ArrayList<>();
        // 使用 try-with-resources 确保文档流正确关闭
        try (XWPFDocument document = new XWPFDocument(Files.newInputStream(wordFile.toPath()))) {
            // 遍历文档中的所有段落
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                // 遍历段落中的所有文本运行
                for (XWPFRun run : paragraph.getRuns()) {
                    // 遍历文本运行中的所有嵌入图片
                    for (XWPFPicture picture : run.getEmbeddedPictures()) {
                        // 获取图片数据
                        byte[] pictureData = picture.getPictureData().getData();
                        ByteArrayInputStream bis = new ByteArrayInputStream(pictureData);
                        BufferedImage image = ImageIO.read(bis);
                        // 判断图片是否为二维码
                        if (isQrCodeImage(image)) {
                            LuminanceSource source = new BufferedImageLuminanceSource(image);
                            BinaryBitmap bitmap = new BinaryBitmap(new HybridBinarizer(source));
                            Result result = new MultiFormatReader().decode(bitmap);
                            qrContents.add(result.getText());
                        }
                    }
                }
            }
        } catch (NotFoundException e) {
            // 如果已经获取了一些结果，打log返回结果，否则直接抛异常
            if (!qrContents.isEmpty()) {
                log.error("在文档中识别到部分二维码内容，已返回部分结果", e);
                return qrContents;
            }
            throw new ServiceException("文档二维码失败失败："+e.getMessage());

        } catch (IOException e) {
            if (!qrContents.isEmpty()) {
                log.error("文件读取失败,在文档中识别到部分二维码内容，已返回部分结果", e);
                return qrContents;
            }
            throw new ServiceException("文件读取失败："+e.getMessage());

        }
        return qrContents;
    }

    private static boolean isQrCodeImage(BufferedImage image) {
        try {
            LuminanceSource source = new BufferedImageLuminanceSource(image);
            BinaryBitmap bitmap = new BinaryBitmap(new HybridBinarizer(source));
            try {
                new MultiFormatReader().decode(bitmap);
                return true;
            } catch (NotFoundException e) {
                return false;
            }
        } catch (Exception e) {
            return false;
        }
    }

}

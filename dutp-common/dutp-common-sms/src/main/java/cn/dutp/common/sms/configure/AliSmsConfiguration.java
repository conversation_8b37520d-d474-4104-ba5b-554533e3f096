package cn.dutp.common.sms.configure;

import cn.dutp.common.sms.configure.properties.AliSmsProperties;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.teaopenapi.models.Config;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties(AliSmsProperties.class)
public class AliSmsConfiguration {
    @Bean
    public Client createClient(AliSmsProperties aliSmsProperties) throws Exception {
        Config config = new Config()
                .setRegionId(aliSmsProperties.getRegionId())
                .setAccessKeyId(aliSmsProperties.getAccessKeyId())
                .setAccessKeySecret(aliSmsProperties.getAccessKeySecret());
        // 配置 Endpoint
        config.endpoint = "dysmsapi.aliyuncs.com";
        return new Client(config);
    }

}

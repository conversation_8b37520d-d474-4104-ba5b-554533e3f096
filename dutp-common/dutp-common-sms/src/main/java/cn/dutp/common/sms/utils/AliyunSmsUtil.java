package cn.dutp.common.sms.utils;

import cn.dutp.common.core.constant.Constants;
import cn.dutp.common.core.utils.SpringUtils;
import cn.dutp.common.core.utils.StringUtils;
import cn.dutp.common.redis.service.RedisService;
import cn.dutp.common.sms.configure.properties.AliSmsProperties;
import com.alibaba.fastjson2.JSONObject;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;


@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class AliyunSmsUtil {
    private static RedisService redisService = SpringUtils.getBean(RedisService.class);;

    private static final AliSmsProperties aliSmsProperties = SpringUtils.getBean(AliSmsProperties.class);

    private static final Client client = SpringUtils.getBean(Client.class);

    /**
     *
     * @param phone
     * @param code
     * @param templateCode
     * @param timeOut 分钟
     * @return
     */
    public static String sendSMSCode(String phone, String codeParam, String code, String templateCode, long timeOut) {
        String key = Constants.PREFIX_PHONE_CODE + phone + "_" + templateCode;
        try {
            log.error("发送短信：" + phone + "，code:" + code + "，templateCode: " + templateCode);
            // 构造请求对象，请填入请求参数值
            SendSmsRequest sendSmsRequest = new SendSmsRequest()
                    .setPhoneNumbers(phone)
                    .setSignName(aliSmsProperties.getSignature())
                    .setTemplateCode(templateCode)
                    .setTemplateParam("{\"" + codeParam + "\":\"" + code + "\"}");
            // 获取响应对象
            SendSmsResponse sendSmsResponse = client.sendSms(sendSmsRequest);
            log.error("发送短信回调：" + phone + "，code:" + code + "，templateCode: " + templateCode + ", response:" + JSONObject.toJSONString(sendSmsResponse));
            // 响应包含服务端响应的 body 和 headers
            Integer resultCode = sendSmsResponse.getStatusCode();
            if (resultCode.intValue() == 200) {
                redisService.setCacheObject(key, code, timeOut, TimeUnit.MINUTES);
            } else {
                redisService.setCacheObject(key, code, timeOut, TimeUnit.MINUTES);
                log.error("发送短信报错：" + phone + "，code:" + code + "，templateCode: " + templateCode + ", resultCode:" + resultCode + ", response:" + JSONObject.toJSONString(sendSmsResponse));
                return StringUtils.EMPTY;
            }
            return code;
        } catch (Exception e) {
            redisService.setCacheObject(key, code, timeOut, TimeUnit.MINUTES);
            log.error("发送短信报错：" + phone + "，code:" + code + "，templateCode: " + templateCode + ", error:" + e.getMessage());
            log.error(JSONObject.toJSONString(e.getStackTrace()));
            e.printStackTrace();
            return StringUtils.EMPTY;
        }
    }

    public static String sendTemplateSMS(String phone, String code,String templateParam, String templateCode, long timeOut) {
        try {
            String key = Constants.PREFIX_PHONE_CODE + phone + "_" + templateCode;
            // 构造请求对象，请填入请求参数值
            SendSmsRequest sendSmsRequest = new SendSmsRequest()
                    .setPhoneNumbers(phone)
                    .setSignName(aliSmsProperties.getSignature())
                    .setTemplateCode(templateCode)
                    .setTemplateParam(templateParam);
            // 获取响应对象
            SendSmsResponse sendSmsResponse = client.sendSms(sendSmsRequest);
            // 响应包含服务端响应的 body 和 headers
            Integer resultCode = sendSmsResponse.getStatusCode();
            if (resultCode.intValue() == 200) {
                redisService.setCacheObject(Constants.PREFIX_PHONE_CODE + phone + "_" + templateCode, code, timeOut, TimeUnit.MINUTES);
            } else {
                return StringUtils.EMPTY;
            }
            return code;
        } catch (Exception e) {
            e.printStackTrace();
            return StringUtils.EMPTY;
        }
    }
    /**
     *
     * @param phone
     * @param code
     * @param templateCode
     * @return 404验证码无效
     *          500 验证码错误
     *          200 验证成功
     */
    public static Integer checkCode(String phone, String code, String templateCode) {
        String key = Constants.PREFIX_PHONE_CODE + phone + "_" + templateCode;
        String redisCode = redisService.getCacheObject(key);
        if (StringUtils.isEmpty(redisCode)) {
            return 404;
        } else if (!redisCode.equals(code)) {
            return 500;
        } else {
            return 200;
        }
    }
}

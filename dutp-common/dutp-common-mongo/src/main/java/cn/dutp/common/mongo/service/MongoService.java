package cn.dutp.common.mongo.service;

import com.mongodb.client.result.UpdateResult;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.stereotype.Component;


import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.List;


/**
 * <AUTHOR>
 */
@Component
public class MongoService {

    @Autowired
    private MongoTemplate mongoTemplate;

    public MongoService() {
    }

    public MongoService(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    /**
     * 插入单个文档
     *
     * @param collectionName 集合名称
     * @param document       要插入的文档
     */
    public void insertOne(String collectionName, Document document) {
        mongoTemplate.insert(document, collectionName);
    }

    /**
     * 插入多个文档
     *
     * @param collectionName 集合名称
     * @param documents      要插入的文档列表
     */
    public void insertMany(String collectionName, List<Document> documents) {
        mongoTemplate.insert(documents, collectionName);
    }

    /**
     * 根据查询条件查询单个文档
     *
     * @param collectionName 集合名称
     * @param query          查询条件
     * @return 查询到的文档
     */
    public Document findOne(String collectionName, Query query) {
        return mongoTemplate.findOne(query, Document.class, collectionName);
    }

    /**
     * 根据查询条件查询单个文档
     * @param collectionName 集合名称
     * @param query 查询条件
     * @param clazz 返回类型
     * @return
     * @param <T>
     */
    public <T> T findOne(String collectionName, Query query, Class<T> clazz) {
        return mongoTemplate.findOne(query, clazz, collectionName);
    }

    /**
     * 查询所有文档
     *
     * @param collectionName 集合名称
     * @return 查询到的文档列表
     */
    public List<Document> findAll(String collectionName) {
        return mongoTemplate.findAll(Document.class, collectionName);
    }

    /**
     * 根据多个 ID 查询多个文档
     *
     * @param collectionName 集合名称
     * @param query 查询条件
     * @return 查询到的文档列表
     */
    public List<Document> findByIds(String collectionName,  Query query) {
        return mongoTemplate.find(query, Document.class, collectionName);
    }


    /**
     * 更新单个文档
     *
     * @param collectionName 集合名称
     * @param query          查询条件
     * @param update         更新条件
     * @return 更新结果
     */
    public UpdateResult updateOne(String collectionName, Query query, Update update) {
        return mongoTemplate.updateFirst(query, update, Document.class, collectionName);
    }

    /**
     * 更新单个文档 没有则插入，并返回更新后的文档
     * @param collectionName 集合名称
     * @param query 查询条件
     * @param update 更新条件
     * @param clazz 更新类型
     * @return
     */
    public <T> T updateOne(String collectionName, Query query, Update update, Class<T> clazz) {
        // 返回更新后的文档
        FindAndModifyOptions options = new FindAndModifyOptions();
        options.returnNew(true);
        options.upsert(true);
        return mongoTemplate.findAndModify(query, update, options, clazz, collectionName);
    }

    /**
     * 更新多个文档
     *
     * @param collectionName 集合名称
     * @param query          查询条件
     * @param update         更新条件
     * @return 更新结果
     */
    public UpdateResult updateMany(String collectionName, Query query, Update update) {
        return mongoTemplate.updateMulti(query, update, Document.class, collectionName);
    }

    /**
     * 删除单个文档
     *
     * @param collectionName 集合名称
     * @param query          查询条件
     */
    public void deleteOne(String collectionName, Query query) {
        mongoTemplate.remove(query, Document.class, collectionName);
    }

    /**
     * 删除多个文档
     *
     * @param collectionName 集合名称
     * @param query          查询条件
     */
    public void deleteMany(String collectionName, Query query) {
        mongoTemplate.remove(query, Document.class, collectionName);
    }

}

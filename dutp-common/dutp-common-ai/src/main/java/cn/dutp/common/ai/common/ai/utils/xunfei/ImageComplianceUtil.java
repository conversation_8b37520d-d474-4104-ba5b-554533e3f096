package cn.dutp.common.ai.common.ai.utils.xunfei;

import java.util.Base64;

public class ImageComplianceUtil {
    // 拼接业务参数（notify_url回调地址可以不填写，此处使用官方默认值）
    public static String getJsonString(String imageUrlOrPath) {
        try {
            String json = "{\n" +
//                    "  \"content\": \"" + Base64.getEncoder().encodeToString(CommonUtil.imageToByteArray(imageUrlOrPath)) + "\"\n" +
                    "  \"content\": \"" + imageUrlOrPath + "\"\n" +
                    "}";
            return json;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}

package cn.dutp.common.ai.common.ai.utils.baidu;

import com.alibaba.fastjson2.JSON;
import okhttp3.*;

import java.io.IOException;

public class BaiduCommonUtil {

    static final OkHttpClient HTTP_CLIENT = new OkHttpClient().newBuilder().build();

    /**
     * 从用户的AK，SK生成鉴权签名（Access Token）
     *
     * @return 鉴权签名（Access Token）
     * @throws IOException IO异常
     */
    public static String getAccessToken(String clientId, String clientSecret,String baiduTokenUrl) throws IOException {
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        RequestBody body = RequestBody.create(mediaType, "grant_type=client_credentials&client_id=" + clientId
                + "&client_secret=" + clientSecret);
        Request request = new Request.Builder()
                .url(baiduTokenUrl)
                .method("POST", body)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        return JSON.parseObject(response.body().string()).getString("access_token");
    }
}

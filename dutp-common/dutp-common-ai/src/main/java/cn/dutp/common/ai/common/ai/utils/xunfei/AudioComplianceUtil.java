package cn.dutp.common.ai.common.ai.utils.xunfei;
/**
 * 音频合规鉴权工具类
 */
public class AudioComplianceUtil {
    // 拼接业务参数（notify_url回调地址可以不填写，此处使用官方默认值）
    public static String getJsonString(String audioUrl,String mp3Url) {
        String json = "{\n" +
                "  \"audio_list\": [\n" +
                "    {\n" +
                "      \"audio_type\": \"" + mp3Url.substring(audioUrl.length() - 3) + "\",\n" +
                "      \"file_url\": \"" + mp3Url + "\",\n" +
                "      \"name\": \"" + "xxx" + mp3Url.substring(audioUrl.length() - 8) + "\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"notify_url\": \"http://wdfgdzx.top:8000/user/audio_video_callback\"\n" +
                "}";
        return json;
    }
}

package cn.dutp.common.ai.common.ai.domain;

import cn.dutp.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

@Data
public class ChatAiRequest {

    /**
     * 指令
     */
    private String prompt;

    /**
     * 功能1续写2缩写3扩写4润色5生成试题6生成总结7生成学习目标8生成脑图9生成大纲10匹配案例11文生语音12文生图片13百度图片增强14百度图像修复15百度视频生成16文字纠错17文字纠错(上传黑名单 (违禁词替换) 和白名单 (免审词设置))18视频合规19文本合规20文本合规(上传黑白名单)21图片合规22音频合规23翻译
     * 24重写（文本改写）25编辑器代码实训26阅读精灵27阅读精灵中可能提出的问题99关键字提取
     */
    @Excel(name = "功能1续写2缩写3扩写4润色5生成试题6生成总结7生成学习目标8生成脑图9生成大纲10匹配案例11文生语音12文生图片13百度图片增强14百度图像修复15百度视频生成16文字纠错17文字纠错(上传黑名单 (违禁词替换) 和白名单 (免审词设置))18视频合规19文本合规20文本合规(上传黑白名单)21图片合规22音频合规23翻译" +
            "24重写（文本改写）25编辑器代码实训26阅读精灵27阅读精灵中可能提出的问题99关键字提取")
    private Integer ability;

    /**
     * 讯飞大模型类型，lite指向Lite版本，generalv3指向Pro版本,pro-128k指向Pro-128K版本,generalv3.5指向Max版本,max-32k指向Max-32K版本,4.0Ultra指向4.0 Ultra版本;
     * 固定值generalv3.5
     */
    private String xfType = "generalv3.5";

    /**
     * 问题
     */
    private String question;

    /**
     * 回答
     */
    private String answer;

    /**
     * 语种
     */
    private Long languageId;

    /**
     * 发音人
     */
    private Long voiceId;

    /**
     * 原始语种
     */
    private String fromLanguage;

    /**
     * 翻译语种
     */
    private String toLanguage;

    /**
     * 白名单，上传文本纠错黑白名单和文本合规黑白名单时需要
     */
    private String whiteList;

    /**
     * 黑名单，上传文本纠错黑白名单和文本合规黑白名单时需要
     */
    private String blackList;

    /**
     * 是否验证黑白名单，文字纠错和文本合规时需要
     */
    private Boolean checkBlackWhite;

    /**
     * 图片修复时，框选修复范围
     */
    private double top;

    /**
     * 图片修复时，框选修复范围
     */
    private double left;

    /**
     * 图片修复时，框选修复范围
     */
    private double width;

    /**
     * 图片修复时，框选修复范围
     */
    private double height;

    /**
     * 章节ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    /**
     * 音色
     */
    private String vcn;

    /**
     * 音速 0-100 默认50
     */
    private Integer speed;

    private String tte;

    /**
     * 开发语言
     */
    private String developmentLanguage;

    /**
     * 发起请求用户类型0前台用户1后台用户
     */
    private String userType;

    /**
     * 待识别的内容
     */
    private String languageStr;
}

package cn.dutp.common.ai.common.ai.utils.xunfei;
/**
 * 文本合规工具类
 */
public class TextComplianceUtil {

    // 拼接业务参数（如果需要使用黑白名单资源，放开lib_ids与categories参数）
    public static String getJsonString(String content,boolean checkBlackWhite,String whiteStr,String blackStr) {
        if (checkBlackWhite) {
            String json = "{\n" +
                    "  \"is_match_all\": 1,\n" +
                    "  \"content\": \"" + content + "\"\n" +
                    "  \"lib_ids\": [\n" +
                    "    \"" + blackStr + "\",\n" +/*根据自己创建获取词库ID,黑名单*/
                    "    \"" + whiteStr + "\"\n" +/*根据自己创建获取词库ID,白名单*/
                    "  ],\n" +
                    "  \"categories\": [\n" +
                    "    \"pornDetection\",\n" +
                    "    \"violentTerrorism\",\n" +
                    "    \"political\",\n" +
                    "    \"lowQualityIrrigation\",\n" +
                    "    \"contraband\",\n" +
                    "    \"advertisement\",\n" +
                    "    \"uncivilizedLanguage\"\n" +
                    "  ]\n" +
                    "}";
                    return json;
        } else {
            String json = "{\n" +
                    "  \"is_match_all\": 1,\n" +
                    "  \"content\": \"" + content + "\"\n" +
                    "}";
            return json;
        }

    }
}

package cn.dutp.common.websocket.interceptor;

import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

import java.net.InetSocketAddress;
import java.util.Map;

/**
 * WebSocket握手拦截器
 * 用于在握手阶段处理客户端连接信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30
 */
public class WebSocketChannelInterceptor implements HandshakeInterceptor {
    /**
     * 握手前处理
     * 1. 获取客户端远程地址
     * 2. 将地址信息存入WebSocket会话属性
     * 3. 记录连接日志（含颜色标识方便日志追踪）
     *
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @param wsHandler WebSocket处理器
     * @param attributes 会话属性集合
     * @return true表示继续握手流程，false中断连接
     */
    @Override
    public boolean beforeHandshake(ServerHttpRequest request,
                                   ServerHttpResponse response,
                                   WebSocketHandler wsHandler,
                                   Map<String, Object> attributes) {
        InetSocketAddress remoteAddress = request.getRemoteAddress();
        // 存储客户端IP地址供后续处理使用
        attributes.put("remoteAddress", remoteAddress.getHostString());
        // 🟡 标记日志用于监控连接建立事件
        System.out.println("🟡 保存 remoteAddress: " + remoteAddress.getHostString());
        return true;
    }

    /**
     * 握手后处理（当前未实现）
     * 可用于：
     * - 握手完成后的清理工作
     * - 初始化WebSocket会话资源
     * - 记录握手完成日志
     */
    @Override
    public void afterHandshake(ServerHttpRequest request,
                               ServerHttpResponse response,
                               WebSocketHandler wsHandler,
                               Exception exception) {
        // 当前暂无特殊处理需求
    }
}


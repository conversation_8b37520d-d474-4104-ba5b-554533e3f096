package cn.dutp.common.websocket.listener;

import org.springframework.context.ApplicationListener;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.messaging.SessionConnectEvent;

/**
 * WebSocket连接监听器
 * 处理客户端连接事件，记录连接信息
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Component
public class WebSocketConnectListener implements ApplicationListener<SessionConnectEvent> {

    /**
     * 处理会话连接事件
     *
     * @param event 会话连接事件对象
     *              包含连接相关的消息头和会话信息
     */
    @Override
    public void onApplicationEvent(SessionConnectEvent event) {
        // 包装消息头
        StompHeaderAccessor accessor = StompHeaderAccessor.wrap(event.getMessage());

        // 读取在握手拦截器中保存的远程IP地址
        String addr = accessor.getSessionAttributes() != null
                ? (String) accessor.getSessionAttributes().get("remoteAddress")
                : null;

        String sessionId = accessor.getSessionId();
        System.out.println("🟢 新客户端连接: " + sessionId);
        System.out.println("🌐 客户端 IP: " + addr);
    }
}

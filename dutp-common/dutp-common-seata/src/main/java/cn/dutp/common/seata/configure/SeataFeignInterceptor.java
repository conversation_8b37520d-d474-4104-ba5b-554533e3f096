package cn.dutp.common.seata.configure;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import io.seata.core.context.RootContext;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.stereotype.Component;
/**
 * <p>seata拦截器,获取全局事务xid</p>
 * <AUTHOR>
 * @date 20241219
 */
@Slf4j
@Component
@ConditionalOnClass({RequestInterceptor.class, GlobalTransactional.class})
public class SeataFeignInterceptor implements RequestInterceptor
{
    @Override
    public void apply(RequestTemplate request) {
        String xid = RootContext.getXID();
        if (xid != null && !xid.trim().isEmpty()) {
            log.debug("seata - feign add seata-xid http request header [{}]", xid);
            request.header(RootContext.KEY_XID, xid);
        }
    }
}

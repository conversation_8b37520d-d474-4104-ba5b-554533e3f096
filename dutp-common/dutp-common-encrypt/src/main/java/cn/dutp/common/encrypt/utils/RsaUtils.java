package cn.dutp.common.encrypt.utils;


import cn.dutp.common.core.exception.UtilException;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import javax.crypto.Cipher;

import java.security.*;
import java.util.Base64;
/**
 * 加密工具类，包含RSA对称算法的解密和解密
 * 需要先生成密钥对，然后进行加解密
 * <AUTHOR>
 */

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class RsaUtils {

    private static final String ALGORITHM_RSA = "RSA";

    /**
     * 生成RSA密钥对
     * 此方法使用Java的KeyPairGenerator类来生成一个RSA密钥对，密钥长度为2048位
     * RSA是一种非对称加密算法，广泛应用于安全数据传输和数字签名
     * 生成的密钥对包含一个公钥和一个私钥，它们可以用于加密和解密数据，以及验证数字签名
     *
     * @return KeyPair 返回生成的密钥对
     */
    public static KeyPair generateKeyPair() throws UtilException {

        KeyPairGenerator keyPairGenerator = null;
        try {
            // 实例化密钥生成器，指定生成密钥对的算法为RSA
            keyPairGenerator = KeyPairGenerator.getInstance("RSA");
        } catch (NoSuchAlgorithmException e) {
            throw new UtilException("加密组件KeyPairGenerator初始化失败："+e.getMessage(),e);
        }
        // 初始化密钥生成器，指定密钥的长度为2048位
        keyPairGenerator.initialize(2048);
        // 生成密钥对并返回
        return keyPairGenerator.generateKeyPair();
    }
    /**
     * 使用公钥加密明文
     * @param plainText 待加密的明文
     * @param publicKey 用于加密的公钥
     * @return 经过Base64编码的加密字符串
     */
    public static String encrypt(String plainText, PublicKey publicKey) throws UtilException {
        Cipher cipher = null;
        try {
            // 创建RSA加密实例
            cipher = Cipher.getInstance(ALGORITHM_RSA);
            // 初始化加密模式，使用提供的公钥
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        } catch (Exception e) {
            throw new UtilException("加密组件Cipher初始化失败："+e.getMessage(),e);
        }
        byte[] encryptedBytes = null;
        try {
            // 使用加密器将明文字节数组加密为密文字节数组
            encryptedBytes = cipher.doFinal(plainText.getBytes());
        } catch (Exception e) {
            throw new UtilException("加密组件Cipher加密字符串失败："+e.getMessage(),e);
        }
        // 将密文字节数组使用Base64编码为字符串并返回
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    /**
     * 使用RSA算法和私钥对经过Base64编码的加密文本进行解密
     *
     * @param encryptedText 经过Base64编码的加密文本
     * @param privateKey 用于解密的私钥
     * @return 解密后的原始文本
     */
    public static String decrypt(String encryptedText, PrivateKey privateKey) throws UtilException {
        Cipher cipher = null;
        try {
            // 创建Cipher实例，指定使用RSA加密算法
            cipher = Cipher.getInstance(ALGORITHM_RSA);
            // 初始化Cipher为解密模式，并使用提供的私钥
            cipher.init(Cipher.DECRYPT_MODE, privateKey);
        } catch (Exception e) {
            throw new UtilException("加密组件Cipher初始化失败："+e.getMessage(),e);
        }
        // 使用Base64解码器将加密文本解码为字节数组
        byte[] decodedBytes = Base64.getDecoder().decode(encryptedText);
        byte[] decryptedBytes = null;
        try {
            // 使用Cipher的doFinal方法对解码后的字节数组进行解密
            decryptedBytes = cipher.doFinal(decodedBytes);
        } catch (Exception e) {
            throw new UtilException("加密组件Cipher解密字符串失败："+e.getMessage(),e);
        }
        // 将解密后的字节数组转换为字符串并返回
        return new String(decryptedBytes);
    }
}

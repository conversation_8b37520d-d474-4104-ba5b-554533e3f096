package cn.dutp.common.encrypt.utils;


import cn.dutp.common.core.exception.UtilException;
import cn.dutp.common.core.utils.SpringUtils;
import cn.dutp.common.encrypt.utils.beans.EncryptKey;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 加密工具类，包含AES对称算法的加解密和MD5,SHA-256加密算法
 * <AUTHOR>
 */

@Component
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class AesUtils {


    private static final EncryptKey ENCRYPTION_KEY = SpringUtils.getBean(EncryptKey.class);

    /**
     * 加一个缓存，用于存储密钥，避免重复生成，但是重启会消失，只是为了减少重复生成浪费
     */
    private static final Map<String, SecretKey> KEY_CACHE = new HashMap<>();

    private static final String ALGORITHM_AES = "AES";

    private static final String DEFAULT_KEY = "dutp20241016dutp";


    private static String getKey() {
        return ENCRYPTION_KEY.getAesKey() != null ? ENCRYPTION_KEY.getAesKey() : DEFAULT_KEY;
    }
    /**
     * 对明文进行加密
     *
     * @param plainText 明文字符串，需要被加密
     * @return 加密后的字符串
     * 此方法使用默认密钥对明文进行加密，为用户提供一个更简洁的接口
     * 通过重载encrypt方法，允许用户在没有指定密钥的情况下对文本进行加密
     */
    public static String encrypt(String plainText) throws UtilException {
        return encrypt(plainText, getKey());
    }

    /**
     * 使用默认密钥解密给定的加密文本
     * 此方法提供了一个简化的接口，用于解密使用默认密钥加密的文本
     * 如果需要使用其他密钥进行解密，可以调用重载方法并传入自定义密钥
     *
     * @param encryptedText 已加密的文本，需要被解密
     * @return 解密后的原始文本
     */
    public static String decrypt(String encryptedText) throws UtilException {
        // 调用重载方法，使用默认密钥进行解密
        return decrypt(encryptedText, getKey());
    }


    /**
     * 使用AES算法加密给定的明文
     *
     * @param plainText 明文字符串，将被加密
     * @param encryptionKey 加密密钥，用于生成加密所需的密钥对象
     * @return 加密后的字符串，使用Base64编码以便于传输和存储
     */
    public static String encrypt(String plainText, String encryptionKey) throws UtilException {
        // 根据加密密钥生成一个SecretKeySpec对象，指定加密算法为AES
        SecretKeySpec secretKey = new SecretKeySpec(encryptionKey.getBytes(), ALGORITHM_AES);

        // 获取Cipher对象并配置为AES加密模式
        Cipher cipher = null;
        try {
            cipher = Cipher.getInstance(ALGORITHM_AES);

        // 初始化Cipher为加密模式，并传入密钥
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        } catch (Exception e) {
            throw new UtilException("加密组件CIPHER初始化失败："+e.getMessage(),e);
        }

        try {
        // 将明文字符串转换为字节数组，并使用Cipher进行加密，得到加密后的字节数组
        byte[] encryptedBytes = cipher.doFinal(plainText.getBytes());

        // 将加密后的字节数组使用Base64编码转换为字符串，并返回
        return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            throw new UtilException("字符串加密异常："+e.getMessage(),e);
        }
    }

    /**
     * 使用AES算法解密给定的加密文本
     *
     * @param encryptedText 已加密的文本，需要被解密
     * @param encryptionKey 解密密钥，用于生成解密所需的密钥对象
     * @return 解密后的文本字符串
     */
    public static String decrypt(String encryptedText, String encryptionKey) throws UtilException {
        // 根据加密密钥生成一个密钥对象，使用AES算法
        SecretKeySpec secretKey = new SecretKeySpec(encryptionKey.getBytes(), ALGORITHM_AES);

        Cipher cipher = null;
        try {
        // 创建一个AES算法的密码对象
        cipher = Cipher.getInstance(ALGORITHM_AES);

        // 初始化密码对象为解密模式，并设置密钥
        cipher.init(Cipher.DECRYPT_MODE, secretKey);
        } catch (Exception e) {
            throw new UtilException("加密组件CIPHER初始化失败："+e.getMessage(),e);
        }

        // 使用Base64解码器将加密文本解码为字节数组
        byte[] decodedBytes = Base64.getDecoder().decode(encryptedText);

        try {
            // 使用密码对象解密编码后的字节数组
            byte[] decryptedBytes = cipher.doFinal(decodedBytes);

            // 将解密后的字节数组转换为字符串并返回
            return new String(decryptedBytes);
        }catch (Exception e){
            throw new UtilException("字符串解密异常："+e.getMessage(),e);
        }
    }


    /**
     * 生成 AES 密钥并缓存起来，如果已存在则直接返回缓存中的密钥。
     * 本方法首先检查是否已缓存了指定 keyId 的密钥，如果已缓存则直接返回该密钥，避免重复生成。
     * 如果未缓存，则生成一个新的 AES 密钥，将其缓存，并返回这个新生成的密钥。
     * 使用密钥缓存可以提高密钥的复用性，减少密钥生成的次数，同时减少存储空间的需求。
     *
     * @param keyId 用于标识不同密钥的唯一字符串
     * @return secretKey 密钥
     */
    public static SecretKey generateAndCacheKey(String keyId) throws UtilException {
        // 检查是否已缓存了指定 keyId 的密钥
        if (KEY_CACHE.containsKey(keyId)) {
            // 如果已缓存，则直接返回该密钥
            return KEY_CACHE.get(keyId);
        }
        // 创建 AES 密钥生成器
        KeyGenerator keyGenerator = null;
        try {
            keyGenerator = KeyGenerator.getInstance(ALGORITHM_AES);
        } catch (NoSuchAlgorithmException e) {
            throw new UtilException(e);
        }
        // 初始化密钥生成器，指定密钥长度为 128 位
        keyGenerator.init(128);
        // 生成一个新的 AES 密钥
        SecretKey secretKey = keyGenerator.generateKey();
        // 将新生成的密钥缓存起来
        KEY_CACHE.put(keyId, secretKey);
        // 返回新生成的密钥
        return secretKey;
    }
}

package cn.dutp.common.core.utils;

import cn.dutp.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.cos.COSName;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.codehaus.jettison.json.JSONArray;
import org.codehaus.jettison.json.JSONException;
import org.codehaus.jettison.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.Files;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;


/**
 * docx转json工具类
 */
@Slf4j
@Component
public class PdfToJsonConverter {

    private final int DEFAULT_DPI = 96;

    @Autowired
    private AliyunOssStsUtil aliyunOssStsUtil;

    /**
     * 解析word文件生成json
     *
     * @param docInputStream
     * @return
     * @throws Exception
     */
    public List<JSONObject> analyzePdf(MultipartFile pdfFile) throws Exception {
        String fileName = pdfFile.getOriginalFilename();
        if (!fileName.endsWith(".pdf")) {
            throw new ServiceException("文件格式不正确，请上传.pdf文件");
        }
        return analyzePdf(pdfFile.getInputStream());
    }

    /**
     * 解析word文件生成json
     *
     * @param docInputStream
     * @return
     * @throws Exception
     */
    public List<JSONObject> analyzePdf(InputStream pdfInputStream) throws Exception {
        List<JSONObject> pdfList = new ArrayList<>();
        PDDocument document = PDDocument.load(pdfInputStream);

        // 解析 PDF 文字（包含样式）
        List<JSONObject> styledTextList = extractStyledTextFromPdf(document);
        // 解析 PDF 图片
        List<String> base64Images = extractImagesFromPdf(document);
        // 转换为 Tiptap JSON
        JSONObject tiptapJson = convertToTiptapJson(styledTextList, base64Images);
        pdfList.add(tiptapJson);
        try {

        }catch (Exception e){
            log.error("pdf解析失败", e);
        }
        return pdfList;
    }

    /** 解析 PDF 文字内容（支持样式，合并同一段落） */
    public static List<JSONObject> extractStyledTextFromPdf(PDDocument document) throws IOException {
        CustomPDFTextStripper stripper = new CustomPDFTextStripper();
        stripper.setSortByPosition(true);  // 避免重复
        stripper.getText(document);

        return stripper.getParagraphs();
    }

    /** 解析 PDF 图片 */
    public static List<String> extractImagesFromPdf(PDDocument document) throws IOException {
        List<String> base64Images = new ArrayList<>();

        for (PDPage page : document.getPages()) {
            Iterable<COSName> xObjectNames = page.getResources().getXObjectNames();

            List<PDImageXObject> images = StreamSupport.stream(xObjectNames.spliterator(), false)
                    .map(name -> {
                        try {
                            return (page.getResources().isImageXObject(name)) ?
                                    (PDImageXObject) page.getResources().getXObject(name) : null;
                        } catch (IOException e) {
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            for (PDImageXObject image : images) {
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                BufferedImage bufferedImage = image.getImage();
                ImageIO.write(bufferedImage, "png", outputStream);
                String base64Image = Base64.getEncoder().encodeToString(outputStream.toByteArray());
                base64Images.add(base64Image);
            }
        }

        return base64Images;
    }

    /** 转换为 Tiptap JSON 格式 */
    public static JSONObject convertToTiptapJson(List<JSONObject> paragraphs, List<String> images) {
        JSONObject doc = new JSONObject();
        try {
            doc.put("type", "doc");
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }

        JSONArray contentArray = new JSONArray();

        for (JSONObject paragraph : paragraphs) {
            JSONObject paragraphNode = new JSONObject();
            try {
                paragraphNode.put("type", "paragraph");
                paragraphNode.put("content", new JSONArray().put(paragraph));
                contentArray.put(paragraphNode);

                // 处理图片
                for (String base64Image : images) {
                    JSONObject imageNode = new JSONObject();
                    imageNode.put("type", "image");
                    imageNode.put("attrs", new JSONObject().put("src", "data:image/png;base64," + base64Image));
                    contentArray.put(imageNode);
                }

                doc.put("content", contentArray);
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }

        }


        return doc;
    }

}
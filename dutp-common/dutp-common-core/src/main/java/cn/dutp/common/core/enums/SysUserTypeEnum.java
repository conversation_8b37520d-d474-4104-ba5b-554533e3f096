package cn.dutp.common.core.enums;

/**
 * @autho
 */
public enum SysUserTypeEnum {
    //"00"系统用户 "01""教务用户"
    SYS_USER_TYPE("00","系统用户"),
    EDU_USER_TYPE("01","教务用户");

    private final String code;
    private final String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    SysUserTypeEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

}

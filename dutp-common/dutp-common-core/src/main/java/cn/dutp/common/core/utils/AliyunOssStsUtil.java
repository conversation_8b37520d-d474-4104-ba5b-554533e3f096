package cn.dutp.common.core.utils;

import cn.dutp.common.core.config.AliyunOssConfig;
import cn.dutp.common.core.domain.UploadFileDto;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.auth.sts.AssumeRoleRequest;
import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.http.ProtocolType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URL;
import java.util.Date;
import java.util.Objects;

/**
 * 阿里云oss工具类
 */
@Slf4j
@Component
@EnableConfigurationProperties(AliyunOssConfig.class)
public class AliyunOssStsUtil {

    @Autowired
    private AliyunOssConfig aliyunOssConfig;


    /**
     * 根据子账号访问权限获取阿里云oss sts临时权限（临时访问凭证），该凭证可保存到缓存中
     *
     * @param roleSessionName 临时Token的会话名称，RoleSessionName 是临时Token的会话名称，自己指定用于标识你的用户，主要用于审计，或者用于区分Token颁发给谁
     * @return 令牌
     * @throws Exception
     */
    public AssumeRoleResponse getAssumeRole(String roleSessionName) throws Exception {
        // 创建一个 Aliyun Acs Client, 用于发起 OpenAPI 请求
        // 只有 RAM用户（子账号）才能调用 AssumeRole 接口
        // 阿里云主账号的AccessKeys不能用于发起AssumeRole请求
        // 请首先在RAM控制台创建一个RAM用户，并为这个用户创建AccessKeys
        IClientProfile profile = DefaultProfile.getProfile(aliyunOssConfig.getRegionId(), aliyunOssConfig.getAccessKeyId(), aliyunOssConfig.getAccessKeySecret());
        DefaultAcsClient client = new DefaultAcsClient(profile);
        // 创建一个 AssumeRoleRequest 并设置请求参数
        final AssumeRoleRequest request = new AssumeRoleRequest();
        request.setMethod(MethodType.POST);
        // 此处必须为 HTTPS
        request.setProtocol(ProtocolType.HTTPS);

        // RoleSessionName 是临时Token的会话名称，自己指定用于标识你的用户，主要用于审计，或者用于区分Token颁发给谁
        // 但是注意RoleSessionName的长度和规则，不要有空格，只能有'-' '_' 字母和数字等字符
        // 具体规则请参考API文档中的格式要求
        // 临时Token的会话名称，自己指定用于标识你的用户，主要用于区分Token颁发给谁
        // acs:ram::$accountID:role/$roleName
        request.setRoleSessionName(roleSessionName);
        // RoleArn 需要在 RAM 控制台上获取
        request.setRoleArn(aliyunOssConfig.getRoleArn());
        // 授权策略
        // request.setPolicy(readJson(aliyunOssPolicyFile));
        // 设置token时间（最小15分钟，最大1小时） The Min/Max value of DurationSeconds is 15min/1hr
        // request.setDurationSeconds(60 * 60L);
        // 发起请求，并得到response
        final AssumeRoleResponse assumeRoleResponse = client.getAcsResponse(request);
        log.info("assumeRoleResponse ======\n{}", JSONObject.toJSONString(assumeRoleResponse));
        return assumeRoleResponse;
    }

    /**
     * 获取ossClient客户端
     *
     * @param roleSessionName 临时Token的会话名称，RoleSessionName 是临时Token的会话名称，自己指定用于标识你的用户，主要用于审计，或者用于区分Token颁发给谁
     * @return
     */
    public OSSClient getOssClient(String roleSessionName) {
        try {
            AssumeRoleResponse assumeRoleResponse = this.getAssumeRole(roleSessionName);
            // 用户拿到STS临时凭证后，通过其中的安全令牌（SecurityToken）和临时访问密钥（AccessKeyId和AccessKeySecret）生成OSSClient。
            // 创建OSSClient实例。
            return new OSSClient(aliyunOssConfig.getOssEndPoint(), new DefaultCredentialProvider(assumeRoleResponse.getCredentials().getAccessKeyId(), assumeRoleResponse.getCredentials().getAccessKeySecret(), assumeRoleResponse.getCredentials().getSecurityToken()), null);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 返回oss 签名URL
     *
     * @param ossClient
     * @param bucketName
     * @param ossFilePath
     * @return
     */
    public URL getOssSignUrl(OSSClient ossClient, String bucketName, String ossFilePath) {
        // 默认1小时
        int urlExpirationMinute = 60;
        if (aliyunOssConfig.getUrlExpirationMinute() != null && aliyunOssConfig.getUrlExpirationMinute() > 0) {
            urlExpirationMinute = aliyunOssConfig.getUrlExpirationMinute();
        }
        // 设置URL过期时间为1小时。
        Date expiration = new Date(new Date().getTime() + (urlExpirationMinute * 60 * 1000));
        // 生成以GET方法访问的签名URL，访客可以直接通过浏览器访问相关内容。
        return ossClient.generatePresignedUrl(bucketName, ossFilePath, expiration);
    }

    /**
     * 上传文件
     *
     * @param roleSessionName 临时会话名称，用于标识用户token
     * @param ossFilePath
     * @param bytes
     * @return
     */
    public String uploadObjectToOss(String roleSessionName, String ossFilePath, byte[] bytes) {
        OSSClient ossClient = null;
        try {
            ossClient = this.getOssClient(roleSessionName);
            // 判断bucketName是否存储，如果不存在则创建
            boolean isExist = ossClient.doesBucketExist(aliyunOssConfig.getBucket());
            if (!isExist) {
                ossClient.createBucket(aliyunOssConfig.getBucket());
            }
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(bytes);
            ossClient.putObject(aliyunOssConfig.getBucket(), ossFilePath, byteArrayInputStream, null);
            String url = this.getOssSignUrl(ossClient, aliyunOssConfig.getBucket(), ossFilePath).toString();
            // "http://你的BucketName.你的Endpoint/自定义路径/" + fileName;
            String fileDomainUrl = "https://".concat(aliyunOssConfig.getBucket()).concat(".").concat(aliyunOssConfig.getOssEndPoint().replace("-internal", ""));
            String fileUrl = fileDomainUrl.concat("/").concat(ossFilePath);
            return fileUrl;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            this.closeOssClient(ossClient);
        }
        return "";
    }

    /**
     * 上传
     *
     * @param roleSessionName
     * @param ossFilePath
     * @param inputStream
     * @return
     */
    public String uploadObjectToOss(String roleSessionName, String ossFilePath, InputStream inputStream) {
        OSSClient ossClient = null;
        try {
            ossClient = this.getOssClient(roleSessionName);
            // 判断bucketName是否存储，如果不存在则创建
            boolean isExist = ossClient.doesBucketExist(aliyunOssConfig.getBucket());
            if (!isExist) {
                ossClient.createBucket(aliyunOssConfig.getBucket());
            }
            ossClient.putObject(aliyunOssConfig.getBucket(), ossFilePath, inputStream, null);
            String url = this.getOssSignUrl(ossClient, aliyunOssConfig.getBucket(), ossFilePath).toString();
            // "http://你的BucketName.你的Endpoint/自定义路径/" + fileName;
            // String fileDomainUrl = "https://".concat(aliyunOssConfig.getBucket()).concat(".").concat(aliyunOssConfig.getOssEndPoint());
            String fileDomainUrl = "https://".concat(aliyunOssConfig.getBucket()).concat(".").concat(aliyunOssConfig.getOssEndPoint().replace("-internal", ""));
            String fileUrl = fileDomainUrl.concat("/").concat(ossFilePath);
            return fileUrl;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            this.closeOssClient(ossClient);
        }
        return "";
    }

    /**
     * 关闭客户端
     *
     * @param ossClient
     */
    public void closeOssClient(OSSClient ossClient) {
        if (ossClient != null) {
            ossClient.shutdown();
        }
    }

    /**
     * 上传文件
     *
     * @param file
     * @return
     * @throws Exception
     */
    public UploadFileDto uploadFile(MultipartFile file) throws Exception {
        UploadFileDto uploadFileDto = new UploadFileDto();
        String fileName = System.currentTimeMillis() + getFileSuffix(file);
        String path = uploadObjectToOss(fileName, fileName, file.getBytes());
        uploadFileDto.setFileName(file.getName());
        uploadFileDto.setFileUrl(path);
        uploadFileDto.setFileSize(file.getSize());
        return uploadFileDto;
    }

    /**
     * 上传文件
     *
     * @return
     * @throws Exception
     */
    public UploadFileDto uploadFile(byte[] bytes, String suffix) {
        if (ObjectUtil.isEmpty(suffix)) {
            suffix = ".png";
        }
        UploadFileDto uploadFileDto = new UploadFileDto();
        String fileName = System.currentTimeMillis() + suffix;
        String path = uploadObjectToOss(fileName, fileName, bytes);
        uploadFileDto.setFileName(fileName);
        uploadFileDto.setFileUrl(path);
        uploadFileDto.setFileSize(0l);
        return uploadFileDto;
    }

    /**
     * 上传文件
     *
     * @return
     * @throws Exception
     */
    public UploadFileDto uploadFile(InputStream inputStream, String suffix) {
        if (ObjectUtil.isEmpty(suffix)) {
            suffix = ".png";
        }
        UploadFileDto uploadFileDto = new UploadFileDto();
        String fileName = System.currentTimeMillis() + suffix;
        String path = uploadObjectToOss(fileName, fileName, inputStream);
        uploadFileDto.setFileName(fileName);
        uploadFileDto.setFileUrl(path);
        uploadFileDto.setFileSize(0l);
        return uploadFileDto;
    }

    /**
     * 上传文件
     *
     * @param file
     * @return
     */
    public UploadFileDto uploadFile(File file) {
        UploadFileDto uploadFileDto = new UploadFileDto();
        String fileName = System.currentTimeMillis() + getFileSuffix(file);
        String path = uploadObjectToOss(fileName, fileName, getBytes(file));
        uploadFileDto.setFileName(file.getName());
        uploadFileDto.setFileUrl(path);
        uploadFileDto.setFileSize(file.length());
        return uploadFileDto;
    }

    /**
     * 上传文件
     *
     * @param fileName
     * @param fileSize
     * @param bytes
     * @return
     */
    public UploadFileDto uploadFile(String fileName, Long fileSize, byte[] bytes) {
        UploadFileDto uploadFileDto = new UploadFileDto();
        String ossFileName = System.currentTimeMillis() + getFileSuffix(fileName);
        String path = uploadObjectToOss(ossFileName, fileName, bytes);
        uploadFileDto.setFileName(fileName);
        uploadFileDto.setFileUrl(path);
        uploadFileDto.setFileSize(fileSize);
        return uploadFileDto;
    }

    /**
     * 获取字节数组
     *
     * @param file
     * @return
     */
    public byte[] getBytes(File file) {
        try (InputStream inputStream = new FileInputStream(file);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            return outputStream.toByteArray();
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取文件后缀
     *
     * @param file
     * @return
     */
    private String getFileSuffix(MultipartFile file) {
        String suffix = ".png";
        if (Objects.requireNonNull(file.getOriginalFilename()).contains(".")) {
            suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
        }
        return suffix;
    }

    /**
     * 获取文件名 无后缀
     *
     * @param fileName
     * @return
     */
    private String getFileNameNotSuffix(String fileName) {
        String newFileName = fileName;
        if (Objects.requireNonNull(fileName).contains(".")) {
            newFileName = fileName.substring(0, fileName.lastIndexOf("."));
        }
        return newFileName;
    }

    /**
     * 获取文件后缀
     *
     * @param fileName
     * @return
     */
    private String getFileSuffix(String fileName) {
        String suffix = ".png";
        if (Objects.requireNonNull(fileName).contains(".")) {
            suffix = fileName.substring(fileName.lastIndexOf("."));
        }
        return suffix;
    }

    /**
     * 获取文件后缀
     *
     * @param file
     * @return
     */
    private String getFileSuffix(File file) {
        String suffix = ".png";
        if (Objects.requireNonNull(file.getName()).contains(".")) {
            suffix = file.getName().substring(file.getName().lastIndexOf("."));
        }
        return suffix;
    }
}

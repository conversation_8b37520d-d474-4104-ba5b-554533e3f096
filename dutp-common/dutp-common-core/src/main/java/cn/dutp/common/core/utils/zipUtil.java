package cn.dutp.common.core.utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * ZIP 压缩工具类
 */
public class zipUtil {

    /**
     * 压缩文件或目录到指定的 ZIP 文件
     *
     * @param sourcePath  要压缩的文件或目录的路径
     * @param zipFilePath 生成的 ZIP 文件的路径
     */
    public static File generateZip(String sourcePath, String zipFilePath) {
        try {
            // 确保 ZIP 文件所在的目录存在
            File zipFile = new File(zipFilePath);
            File zipParentDir = zipFile.getParentFile();
            if (zipParentDir != null && !zipParentDir.exists()) {
                if (!zipParentDir.mkdirs()) {
                    throw new IOException("无法创建 ZIP 文件所在的目录: " + zipParentDir.getAbsolutePath());
                }
            }

            try (FileOutputStream fos = new FileOutputStream(zipFile);
                 ZipOutputStream zipOut = new ZipOutputStream(fos)) {
                File fileToZip = new File(sourcePath);
                if (fileToZip != null && !fileToZip.exists()) {
                    if (!fileToZip.mkdirs()) {
                        throw new IOException("无法创建 ZIP 文件所在的目录: " + fileToZip.getAbsolutePath());
                    }
                }
                if (fileToZip.isDirectory()) {
                    // 如果是目录，遍历其下的子文件和子目录
                    File[] children = fileToZip.listFiles();
                    if (children != null) {
                        for (File childFile : children) {
                            compressFile(childFile, childFile.getName(), zipOut);
                        }
                    }
                } else {
                    // 如果是单个文件，直接压缩
                    compressFile(fileToZip, fileToZip.getName(), zipOut);
                }
            }
            return zipFile;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 递归压缩文件或目录
     *
     * @param fileToZip 要压缩的文件或目录
     * @param fileName  文件名
     * @param zipOut    ZIP 输出流
     * @throws IOException 如果发生 I/O 错误
     */
    private static void compressFile(File fileToZip, String fileName, ZipOutputStream zipOut) throws IOException {
        if (fileToZip.isHidden()) {
            return;
        }
        if (fileToZip.isDirectory()) {
            if (fileName.endsWith(File.separator)) {
                zipOut.putNextEntry(new ZipEntry(fileName));
            } else {
                zipOut.putNextEntry(new ZipEntry(fileName + File.separator));
            }
            zipOut.closeEntry();
            File[] children = fileToZip.listFiles();
            if (children != null) {
                for (File childFile : children) {
                    compressFile(childFile, fileName + File.separator + childFile.getName(), zipOut);
                }
            }
            return;
        }
        try (FileInputStream fis = new FileInputStream(fileToZip)) {
            ZipEntry zipEntry = new ZipEntry(fileName);
            zipOut.putNextEntry(zipEntry);
            byte[] bytes = new byte[1024];
            int length;
            while ((length = fis.read(bytes)) >= 0) {
                zipOut.write(bytes, 0, length);
            }
        }
    }
}

package cn.dutp.common.security.handler;

import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.system.api.model.LoginUser;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.log.Log;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.ReflectionException;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 自定义sql字段填充器，自动填充创建修改相关字段
 *
 * <AUTHOR>
 * @date 2020/3/30 15:21
 */
@Component
public class CustomMetaObjectHandler implements MetaObjectHandler {

    private static final Log log = Log.get();

    private static final String CREATE_USER = "createBy";

    private static final String CREATE_TIME = "createTime";

    private static final String UPDATE_USER = "updateBy";

    private static final String UPDATE_TIME = "updateTime";

    @Override
    public void insertFill(MetaObject metaObject) {
        try {
            //为空则设置createUser（BaseEntity)
            Object createUser = metaObject.getValue(CREATE_USER);
            if(ObjectUtil.isNull(createUser)) {
                setFieldValByName(CREATE_USER, this.getUserUniqueId(), metaObject);
            }

            //为空则设置createTime（BaseEntity)
            Object createTime = metaObject.getValue(CREATE_TIME);
            if(ObjectUtil.isNull(createTime)) {
                setFieldValByName(CREATE_TIME,  DateUtil.date(), metaObject);
            }
        } catch (ReflectionException e) {
            log.warn(">>> CustomMetaObjectHandler处理过程中无相关字段，不做处理");
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        try {
            //设置updateUser（BaseEntity)
            setFieldValByName(UPDATE_USER, this.getUserUniqueId(), metaObject);
            //设置updateTime（BaseEntity)
            setFieldValByName(UPDATE_TIME, new Date(), metaObject);
        } catch (ReflectionException e) {
            log.warn(">>> CustomMetaObjectHandler处理过程中无相关字段，不做处理");
        }
    }

    /**
     * 获取用户唯一id
     */
    private String getUserUniqueId() {
        try {
            LoginUser sysLoginUser = SecurityUtils.getLoginUser();
            if(ObjectUtil.isNotNull(sysLoginUser)) {
                return sysLoginUser.getUser().getUserName();
            } else {
                return "innerOperator";
            }
        } catch (Exception e) {
            return "innerOperator";
        }
    }
}

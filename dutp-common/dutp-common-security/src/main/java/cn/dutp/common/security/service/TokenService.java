package cn.dutp.common.security.service;

import cn.dutp.common.core.constant.CacheConstants;
import cn.dutp.common.core.constant.SecurityConstants;
import cn.dutp.common.core.utils.JwtUtils;
import cn.dutp.common.core.utils.ServletUtils;
import cn.dutp.common.core.utils.StringUtils;
import cn.dutp.common.core.utils.bean.BeanUtils;
import cn.dutp.common.core.utils.ip.IpUtils;
import cn.dutp.common.core.utils.uuid.IdUtils;
import cn.dutp.common.redis.service.RedisService;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.system.api.domain.SysUser;
import cn.dutp.system.api.model.LoginDutpUser;
import cn.dutp.system.api.model.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * token验证处理
 *
 * <AUTHOR>
 */
@Component
public class TokenService
{
    private static final Logger log = LoggerFactory.getLogger(TokenService.class);

    @Autowired
    private RedisService redisService;

    protected static final long MILLIS_SECOND = 1000;

    protected static final long MILLIS_MINUTE = 60 * MILLIS_SECOND;

    private final static long expireTime = CacheConstants.EXPIRATION;

    private final static String ACCESS_TOKEN = CacheConstants.LOGIN_TOKEN_KEY;

    private final static Long MILLIS_MINUTE_TEN = CacheConstants.REFRESH_TIME * MILLIS_MINUTE;

    /**
     * 创建令牌
     */
    public Map<String, Object> createToken(LoginUser loginUser, String entrance)
    {
        Integer userType = "edu".equals(entrance) ? 2 : "stu".equals(entrance) ? 3 : 1;
        String token = IdUtils.fastUUID();
        Long userId = loginUser.getSysUser().getUserId();
        Long schoolId = loginUser.getSysUser().getSchoolId();
        String userName = loginUser.getSysUser().getUserName();
        loginUser.setToken(token);
        loginUser.setUserid(userId);
        loginUser.setUsername(userName);
        loginUser.setUserType(userType);
        loginUser.setIpaddr(IpUtils.getIpAddr());
        loginUser.setSchoolId(schoolId);
        refreshToken(loginUser);

        // Jwt存储信息
        Map<String, Object> claimsMap = new HashMap<String, Object>();
        claimsMap.put(SecurityConstants.USER_KEY, token);
        claimsMap.put(SecurityConstants.DETAILS_USER_ID, userId);
        claimsMap.put(SecurityConstants.DETAILS_USERNAME, userName);

        // 接口返回信息
        Map<String, Object> rspMap = new HashMap<String, Object>();
        rspMap.put("access_token", JwtUtils.createToken(claimsMap));
        rspMap.put("expires_in", expireTime);
        return rspMap;
    }
    /**
     * 创建教师，学生，无身份者令牌
     */
    public Map<String, Object> createToken(LoginDutpUser loginDutpUser)
    {
        String token = IdUtils.fastUUID();
        Long userId = loginDutpUser.getDutpUser().getUserId();
        Long schoolId = loginDutpUser.getDutpUser().getSchoolId();
        String userType = loginDutpUser.getDutpUser().getUserType();
        String userName = loginDutpUser.getDutpUser().getUserName();
        LoginUser loginUser = new LoginUser();
        SysUser loginSysUser = new SysUser();
        BeanUtils.copyProperties(loginDutpUser.getDutpUser(), loginSysUser);
        loginUser.setSysUser(loginSysUser);
        loginUser.setToken(token);
        loginUser.setUserid(userId);
        loginUser.setUserType(3);
        loginUser.setHomeUserType(Integer.parseInt(userType));
        loginUser.setSchoolId(schoolId);
        loginUser.setUsername(userName);
        loginUser.setRealName(loginDutpUser.getDutpUser().getRealName());
        loginUser.setIpaddr(IpUtils.getIpAddr());
        loginUser.setLoginTime(loginDutpUser.getLoginTime());
        loginUser.setExpireTime(loginDutpUser.getExpireTime());
        loginUser.setPermissions(loginDutpUser.getPermissions());
        loginUser.setRoles(loginDutpUser.getRoles());
        refreshToken(loginUser);
        // Jwt存储信息
        Map<String, Object> claimsMap = new HashMap<String, Object>();
        claimsMap.put(SecurityConstants.USER_KEY, token);
        claimsMap.put(SecurityConstants.DETAILS_USER_ID, userId);
        claimsMap.put(SecurityConstants.DETAILS_USERNAME, userName);
        // 接口返回信息
        Map<String, Object> rspMap = new HashMap<String, Object>();
        rspMap.put("access_token", JwtUtils.createToken(claimsMap));
        rspMap.put("expires_in", expireTime);
        rspMap.put("loginUser", loginUser);
        return rspMap;
    }
    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUser()
    {
        return getLoginUser(ServletUtils.getRequest());
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUser(HttpServletRequest request)
    {
        // 获取请求携带的令牌
        String token = SecurityUtils.getToken(request);
        return getLoginUser(token);
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUser(String token)
    {
        LoginUser user = null;
        try
        {
            if (StringUtils.isNotEmpty(token))
            {
                String userkey = JwtUtils.getUserKey(token);
                user = redisService.getCacheObject(getTokenKey(userkey));
                return user;
            }
        }
        catch (Exception e)
        {
            log.error("获取用户信息异常'{}'", e.getMessage());
        }
        return user;
    }

    /**
     * 设置用户身份信息
     */
    public void setLoginUser(LoginUser loginUser)
    {
        if (StringUtils.isNotNull(loginUser) && StringUtils.isNotEmpty(loginUser.getToken()))
        {
            refreshToken(loginUser);
        }
    }

    /**
     * 删除用户缓存信息
     */
    public void delLoginUser(String token)
    {
        if (StringUtils.isNotEmpty(token))
        {
            String userkey = JwtUtils.getUserKey(token);
            redisService.deleteObject(getTokenKey(userkey));
        }
    }

    /**
     * 验证令牌有效期，相差不足120分钟，自动刷新缓存
     *
     * @param loginUser
     */
    public void verifyToken(LoginUser loginUser)
    {
        long expireTime = loginUser.getExpireTime();
        long currentTime = System.currentTimeMillis();
        if (expireTime - currentTime <= MILLIS_MINUTE_TEN)
        {
            refreshToken(loginUser);
        }
    }

    /**
     * 刷新令牌有效期
     *
     * @param loginUser 登录信息
     */
    public void refreshToken(LoginUser loginUser)
    {
        loginUser.setLoginTime(System.currentTimeMillis());
        loginUser.setExpireTime(loginUser.getLoginTime() + expireTime * MILLIS_MINUTE);
        // 根据uuid将loginUser缓存
        String userKey = getTokenKey(loginUser.getToken());
        redisService.setCacheObject(userKey, loginUser, expireTime, TimeUnit.MINUTES);
    }
    /**
     * 刷新教师，学生，无身份者令牌有效期
     *
     * @param loginDutpUser 登录信息
     */
    public void refreshToken(LoginDutpUser loginDutpUser)
    {
        loginDutpUser.setLoginTime(System.currentTimeMillis());
        loginDutpUser.setExpireTime(loginDutpUser.getLoginTime() + expireTime * MILLIS_MINUTE);
        // 根据uuid将loginUser缓存
        String userKey = getTokenKey(loginDutpUser.getToken());
        redisService.setCacheObject(userKey, loginDutpUser, expireTime, TimeUnit.MINUTES);
    }

    private String getTokenKey(String token)
    {
        return ACCESS_TOKEN + token;
    }
}
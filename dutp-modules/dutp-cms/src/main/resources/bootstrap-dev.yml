spring:
  config:
    activate:
      on-profile: dev
  cloud:
    nacos:
      discovery:
        server-addr: 192.168.0.222:8848
        namespace: 4063714a-1a13-4560-922a-f6bd1e0382ec
        group: DEFAULT_GROUP

      config:
        # 命名空间
        server-addr: 192.168.0.222:8848
        namespace: 4063714a-1a13-4560-922a-f6bd1e0382ec
        group: DEFAULT_GROUP

        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
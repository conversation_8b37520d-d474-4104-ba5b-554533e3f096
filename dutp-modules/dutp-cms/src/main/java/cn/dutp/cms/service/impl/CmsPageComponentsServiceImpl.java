package cn.dutp.cms.service.impl;

import java.util.List;

import cn.dutp.cms.domain.CmsComponents;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.cms.mapper.CmsPageComponentsMapper;
import cn.dutp.cms.domain.CmsPageComponents;
import cn.dutp.cms.service.ICmsPageComponentsService;

/**
 * DUTP-CMS-0001cms系统组件Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Service
public class CmsPageComponentsServiceImpl extends ServiceImpl<CmsPageComponentsMapper, CmsPageComponents> implements ICmsPageComponentsService
{
    @Autowired
    private CmsPageComponentsMapper cmsPageComponentsMapper;

    /**
     * 查询DUTP-CMS-0001cms系统组件
     *
     * @param pageId DUTP-CMS-0001cms系统组件主键
     * @return DUTP-CMS-0001cms系统组件
     */
    @Override
    public CmsPageComponents selectCmsPageComponentsByPageId(Long pageId)
    {
        return this.getById(pageId);
    }

    /**
     * 查询DUTP-CMS-0001cms系统组件列表
     *
     * @param cmsPageComponents DUTP-CMS-0001cms系统组件
     * @return DUTP-CMS-0001cms系统组件
     */
    @Override
    public List<CmsComponents> selectCmsPageComponentsList(CmsPageComponents cmsPageComponents)
    {
        return this.baseMapper.selectManageCmsPageComponentsList(cmsPageComponents.getPageId());
    }

    /**
     * 新增DUTP-CMS-0001cms系统组件
     *
     * @param cmsPageComponents DUTP-CMS-0001cms系统组件
     * @return 结果
     */
    @Override
    public boolean insertCmsPageComponents(CmsPageComponents cmsPageComponents)
    {
        return this.save(cmsPageComponents);
    }

    /**
     * 修改DUTP-CMS-0001cms系统组件
     *
     * @param cmsPageComponents DUTP-CMS-0001cms系统组件
     * @return 结果
     */
    @Override
    public boolean updateCmsPageComponents(CmsPageComponents cmsPageComponents)
    {
        return this.updateById(cmsPageComponents);
    }

    /**
     * 批量删除DUTP-CMS-0001cms系统组件
     *
     * @param pageIds 需要删除的DUTP-CMS-0001cms系统组件主键
     * @return 结果
     */
    @Override
    public boolean deleteCmsPageComponentsByPageIds(List<Long> pageIds)
    {
        return this.removeByIds(pageIds);
    }

}

package cn.dutp.cms.mapper;

import java.util.List;

import cn.dutp.cms.domain.CmsComponents;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import cn.dutp.cms.domain.CmsPageComponents;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
/**
 * DUTP-CMS-0001cms系统组件Mapper接口
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Repository
public interface CmsPageComponentsMapper extends BaseMapper<CmsPageComponents>
{
    @Select("SELECT " +
            "cc.component_id, " +
            "cc.component_name, " +
            "cc.component_group, " +
            "cc.component_image, " +
            "cc.file_path, " +
            "cc.form_path, " +
            "cc.component_key, " +
            "cpc.component_data, " +
            "cpc.page_component_id " +
            "FROM " +
            "cms_components cc " +
            "INNER JOIN cms_page_components cpc ON cc.component_id = cpc.component_id " +
            "WHERE cc.del_flag = 0 " +
            "and cpc.page_id = #{pageId} ")
    List<CmsComponents> selectManageCmsPageComponentsList(@Param("pageId") Long pageId);
}

package cn.dutp.cms.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * DUTP-CMS-0001cms系统组件对象 cms_page_components
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
@TableName("cms_page_components")
public class CmsPageComponents extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableId(type = IdType.ASSIGN_ID)
    private Long pageComponentId;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long pageId;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long componentId;
    private String componentData;
    private Integer sort;

}

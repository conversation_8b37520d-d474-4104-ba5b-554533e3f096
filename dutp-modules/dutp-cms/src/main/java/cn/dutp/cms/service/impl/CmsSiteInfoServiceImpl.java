package cn.dutp.cms.service.impl;

import java.util.List;

import cn.dutp.cms.domain.vo.SiteInfoVo;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;
import cn.dutp.cms.mapper.CmsSiteInfoMapper;
import cn.dutp.cms.domain.CmsSiteInfo;
import cn.dutp.cms.service.ICmsSiteInfoService;

/**
 * 站点信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
@Service
public class CmsSiteInfoServiceImpl extends ServiceImpl<CmsSiteInfoMapper, CmsSiteInfo> implements ICmsSiteInfoService
{
    @Autowired
    private CmsSiteInfoMapper cmsSiteInfoMapper;

    /**
     * 查询站点信息
     *
     * @param siteId 站点信息主键
     * @return 站点信息
     */
    @Override
    public CmsSiteInfo selectCmsSiteInfoBySiteId(Long siteId)
    {
        return this.getById(siteId);
    }

    /**
     * 查询站点信息列表
     *
     * @param cmsSiteInfo 站点信息
     * @return 站点信息
     */
    @Override
    public List<CmsSiteInfo> selectCmsSiteInfoList(CmsSiteInfo cmsSiteInfo)
    {
        LambdaQueryWrapper<CmsSiteInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增站点信息
     *
     * @param cmsSiteInfo 站点信息
     * @return 结果
     */
    @Override
    public boolean insertCmsSiteInfo(CmsSiteInfo cmsSiteInfo)
    {
        return this.save(cmsSiteInfo);
    }

    /**
     * 修改站点信息
     *
     * @param cmsSiteInfo 站点信息
     * @return 结果
     */
    @Override
    public boolean updateCmsSiteInfo(CmsSiteInfo cmsSiteInfo)
    {
        return this.updateById(cmsSiteInfo);
    }

    /**
     * 批量删除站点信息
     *
     * @param siteIds 需要删除的站点信息主键
     * @return 结果
     */
    @Override
    public boolean deleteCmsSiteInfoBySiteIds(List<Long> siteIds)
    {
        return this.removeByIds(siteIds);
    }

    @Override
    public SiteInfoVo selectHomeSiteInfoBySiteId(Long siteId) {
        CmsSiteInfo siteInfo = this.getById(siteId);
        SiteInfoVo siteInfoVo = new SiteInfoVo();
        BeanUtil.copyProperties(siteInfo,siteInfoVo);
        return siteInfoVo;
    }

}

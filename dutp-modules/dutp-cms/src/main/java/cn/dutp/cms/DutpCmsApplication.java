package cn.dutp.cms;

import cn.dutp.common.security.annotation.EnableCustomConfig;
import cn.dutp.common.security.annotation.EnableDutpFeignClients;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

@EnableCustomConfig
@EnableDutpFeignClients
@SpringBootApplication
@EnableDiscoveryClient
public class DutpCmsApplication {
    public static void main(String[] args) {
        SpringApplication.run(DutpCmsApplication.class, args);
        System.out.println("CMS模块启动成功");
    }
}

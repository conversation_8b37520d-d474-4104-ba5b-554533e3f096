package cn.dutp.cms.service;

import java.util.List;
import cn.dutp.cms.domain.CmsPage;
import cn.dutp.common.core.web.domain.AjaxResult;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 页面管理Service接口
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
public interface ICmsPageService extends IService<CmsPage>
{
    /**
     * 查询页面管理
     *
     * @param pageId 页面管理主键
     * @return 页面管理
     */
    public CmsPage selectCmsPageByPageId(Long pageId);

    /**
     * 查询页面管理列表
     *
     * @param cmsPage 页面管理
     * @return 页面管理集合
     */
    public List<CmsPage> selectCmsPageList(CmsPage cmsPage);

    /**
     * 新增页面管理
     *
     * @param cmsPage 页面管理
     * @return 结果
     */
    public boolean insertCmsPage(CmsPage cmsPage);

    /**
     * 修改页面管理
     *
     * @param cmsPage 页面管理
     * @return 结果
     */
    public boolean updateCmsPage(CmsPage cmsPage);

    /**
     * 批量删除页面管理
     *
     * @param pageIds 需要删除的页面管理主键集合
     * @return 结果
     */
    public boolean deleteCmsPageByPageIds(List<Long> pageIds);

    AjaxResult saveDecoration(CmsPage cmsPage);
}

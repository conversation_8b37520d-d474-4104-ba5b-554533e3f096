package cn.dutp.cms.service.impl;

import java.util.ArrayList;
import java.util.List;

import cn.dutp.cms.domain.vo.CmsMenuVo;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.cms.mapper.CmsMenuMapper;
import cn.dutp.cms.domain.CmsMenu;
import cn.dutp.cms.service.ICmsMenuService;

/**
 * 系统菜单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Service
public class CmsMenuServiceImpl extends ServiceImpl<CmsMenuMapper, CmsMenu> implements ICmsMenuService
{
    @Autowired
    private CmsMenuMapper cmsMenuMapper;

    /**
     * 查询系统菜单
     *
     * @param menuId 系统菜单主键
     * @return 系统菜单
     */
    @Override
    public CmsMenu selectCmsMenuByMenuId(Long menuId)
    {
        return this.getById(menuId);
    }

    /**
     * 查询系统菜单列表
     *
     * @param cmsMenu 系统菜单
     * @return 系统菜单
     */
    @Override
    public List<CmsMenu> selectCmsMenuList(CmsMenu cmsMenu)
    {
        LambdaQueryWrapper<CmsMenu> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(ObjectUtil.isNotEmpty(cmsMenu.getMenuName())) {
            lambdaQueryWrapper.like(CmsMenu::getMenuName
                    , cmsMenu.getMenuName());
        }
        lambdaQueryWrapper.orderByAsc(CmsMenu::getSort);
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增系统菜单
     *
     * @param cmsMenu 系统菜单
     * @return 结果
     */
    @Override
    public boolean insertCmsMenu(CmsMenu cmsMenu)
    {
        return this.save(cmsMenu);
    }

    /**
     * 修改系统菜单
     *
     * @param cmsMenu 系统菜单
     * @return 结果
     */
    @Override
    public boolean updateCmsMenu(CmsMenu cmsMenu)
    {
        return this.updateById(cmsMenu);
    }

    /**
     * 批量删除系统菜单
     *
     * @param menuIds 需要删除的系统菜单主键
     * @return 结果
     */
    @Override
    public boolean deleteCmsMenuByMenuIds(List<Long> menuIds)
    {
        return this.removeByIds(menuIds);
    }

    @Override
    public List<CmsMenuVo> getHomeMenuList() {
        LambdaQueryWrapper<CmsMenu> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CmsMenu::getParentId,0).orderByAsc(CmsMenu::getSort);
        List<CmsMenu> dbList = this.list(lambdaQueryWrapper);
        List<CmsMenuVo> cmsMenuVos = new ArrayList<>();
        dbList.forEach(item->{
            CmsMenuVo cmsMenuVo = new CmsMenuVo();
            BeanUtil.copyProperties(item, cmsMenuVo);
            cmsMenuVos.add(cmsMenuVo);
        });
        return cmsMenuVos;
    }

}

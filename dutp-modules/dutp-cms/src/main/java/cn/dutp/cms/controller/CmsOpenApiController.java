package cn.dutp.cms.controller;

import cn.dutp.message.api.RemoteUserMessageService;
import cn.dutp.message.api.domain.DutpUserMessage;
import cn.dutp.cms.domain.CmsComponents;
import cn.dutp.cms.domain.CmsPageComponents;
import cn.dutp.cms.domain.vo.CmsMenuVo;
import cn.dutp.cms.service.ICmsMenuService;
import cn.dutp.cms.service.ICmsPageComponentsService;
import cn.dutp.cms.service.ICmsSiteInfoService;
import cn.dutp.common.core.domain.R;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 站点信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
@RestController
@RequestMapping("/openApi")
public class CmsOpenApiController extends BaseController
{
    @Autowired
    private ICmsSiteInfoService cmsSiteInfoService;
    @Autowired
    private ICmsPageComponentsService cmsPageComponentsService;
    @Autowired
    private ICmsMenuService cmsMenuService;
    @Autowired
    private RemoteUserMessageService remoteUserMessageService;
    /**
     * 获取站点信息详细信息
     */
    @GetMapping(value = "/getSiteInfoById/{siteId}")
    public AjaxResult getInfo(@PathVariable("siteId") Long siteId)
    {
        return success(cmsSiteInfoService.selectHomeSiteInfoBySiteId(siteId));
    }

    @GetMapping(value="/getPageCmsComponents/{pageId}")
    public AjaxResult getPageCmsComponents(@PathVariable("pageId") Long pageId){
        CmsPageComponents cmsPageComponents = new CmsPageComponents();
        cmsPageComponents.setPageId(pageId);
        List<CmsComponents> list = cmsPageComponentsService.selectCmsPageComponentsList(cmsPageComponents);
        return AjaxResult.success(list);
    }

    @GetMapping("/menus")
    public AjaxResult menus(){
        List<CmsMenuVo> list = cmsMenuService.getHomeMenuList();
        return AjaxResult.success(list);
    }
    @GetMapping("/testMessage")
    public AjaxResult testMessage() {
        DutpUserMessage dutpUserMessage = new DutpUserMessage();
        dutpUserMessage.setContent("hhhah");
        dutpUserMessage.setTitle("ksksksk");
        R<Boolean> result =  remoteUserMessageService.addMessage(dutpUserMessage);
        return AjaxResult.success();
    }
}

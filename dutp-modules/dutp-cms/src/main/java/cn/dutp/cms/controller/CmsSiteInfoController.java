package cn.dutp.cms.controller;

import java.util.List;
import java.util.Arrays;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.common.core.annotation.Anonymous;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.cms.domain.CmsSiteInfo;
import cn.dutp.cms.service.ICmsSiteInfoService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 站点信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
@RestController
@RequestMapping("/siteInfo")
public class CmsSiteInfoController extends BaseController
{
    @Autowired
    private ICmsSiteInfoService cmsSiteInfoService;

/**
 * 查询站点信息列表
 */
    @GetMapping("/list")
    public TableDataInfo list(CmsSiteInfo cmsSiteInfo)
    {
        startPage();
        List<CmsSiteInfo> list = cmsSiteInfoService.selectCmsSiteInfoList(cmsSiteInfo);
        return getDataTable(list);
    }

    /**
     * 导出站点信息列表
     */
    @Log(title = "导出站点信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CmsSiteInfo cmsSiteInfo)
    {
        List<CmsSiteInfo> list = cmsSiteInfoService.selectCmsSiteInfoList(cmsSiteInfo);
        ExcelUtil<CmsSiteInfo> util = new ExcelUtil<CmsSiteInfo>(CmsSiteInfo.class);
        util.exportExcel(response, list, "站点信息数据");
    }

    /**
     * 获取站点信息详细信息
     */
    @GetMapping(value = "/{siteId}")
    public AjaxResult getInfo(@PathVariable("siteId") Long siteId)
    {
        return success(cmsSiteInfoService.selectCmsSiteInfoBySiteId(siteId));
    }

    /**
     * 新增站点信息
     */
    @Log(title = "新增站点信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CmsSiteInfo cmsSiteInfo)
    {
        return toAjax(cmsSiteInfoService.insertCmsSiteInfo(cmsSiteInfo));
    }

    /**
     * 修改站点信息
     */
    @Log(title = "编辑站点信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CmsSiteInfo cmsSiteInfo)
    {
        return toAjax(cmsSiteInfoService.updateCmsSiteInfo(cmsSiteInfo));
    }

    /**
     * 删除站点信息
     */
    @Log(title = "删除站点信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{siteIds}")
    public AjaxResult remove(@PathVariable Long[] siteIds)
    {
        return toAjax(cmsSiteInfoService.deleteCmsSiteInfoBySiteIds(Arrays.asList(siteIds)));
    }
}

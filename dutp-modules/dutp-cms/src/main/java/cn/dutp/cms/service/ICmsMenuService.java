package cn.dutp.cms.service;

import java.util.List;
import cn.dutp.cms.domain.CmsMenu;
import cn.dutp.cms.domain.vo.CmsMenuVo;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 系统菜单Service接口
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
public interface ICmsMenuService extends IService<CmsMenu>
{
    /**
     * 查询系统菜单
     *
     * @param menuId 系统菜单主键
     * @return 系统菜单
     */
    public CmsMenu selectCmsMenuByMenuId(Long menuId);

    /**
     * 查询系统菜单列表
     *
     * @param cmsMenu 系统菜单
     * @return 系统菜单集合
     */
    public List<CmsMenu> selectCmsMenuList(CmsMenu cmsMenu);

    /**
     * 新增系统菜单
     *
     * @param cmsMenu 系统菜单
     * @return 结果
     */
    public boolean insertCmsMenu(CmsMenu cmsMenu);

    /**
     * 修改系统菜单
     *
     * @param cmsMenu 系统菜单
     * @return 结果
     */
    public boolean updateCmsMenu(CmsMenu cmsMenu);

    /**
     * 批量删除系统菜单
     *
     * @param menuIds 需要删除的系统菜单主键集合
     * @return 结果
     */
    public boolean deleteCmsMenuByMenuIds(List<Long> menuIds);

    List<CmsMenuVo> getHomeMenuList();
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.cms.mapper.CmsPageComponentsMapper">
    
    <resultMap type="CmsPageComponents" id="CmsPageComponentsResult">
        <result property="pageId"    column="page_id"    />
        <result property="componentId"    column="component_id"    />
        <result property="componentData"    column="component_data"    />
        <result property="sort"    column="sort"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCmsPageComponentsVo">
        select page_id, component_id, component_data, sort, create_by, create_time, update_by, update_time from cms_page_components
    </sql>

    <select id="selectCmsPageComponentsList" parameterType="CmsPageComponents" resultMap="CmsPageComponentsResult">
        <include refid="selectCmsPageComponentsVo"/>
        <where>  
            <if test="componentData != null  and componentData != ''"> and component_data = #{componentData}</if>
            <if test="sort != null "> and sort = #{sort}</if>
        </where>
    </select>
    
    <select id="selectCmsPageComponentsByPageId" parameterType="Long" resultMap="CmsPageComponentsResult">
        <include refid="selectCmsPageComponentsVo"/>
        where page_id = #{pageId}
    </select>

    <insert id="insertCmsPageComponents" parameterType="CmsPageComponents" useGeneratedKeys="true" keyProperty="pageId">
        insert into cms_page_components
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="componentId != null">component_id,</if>
            <if test="componentData != null">component_data,</if>
            <if test="sort != null">sort,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="componentId != null">#{componentId},</if>
            <if test="componentData != null">#{componentData},</if>
            <if test="sort != null">#{sort},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCmsPageComponents" parameterType="CmsPageComponents">
        update cms_page_components
        <trim prefix="SET" suffixOverrides=",">
            <if test="componentId != null">component_id = #{componentId},</if>
            <if test="componentData != null">component_data = #{componentData},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where page_id = #{pageId}
    </update>

    <delete id="deleteCmsPageComponentsByPageId" parameterType="Long">
        delete from cms_page_components where page_id = #{pageId}
    </delete>

    <delete id="deleteCmsPageComponentsByPageIds" parameterType="String">
        delete from cms_page_components where page_id in 
        <foreach item="pageId" collection="array" open="(" separator="," close=")">
            #{pageId}
        </foreach>
    </delete>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.cms.mapper.CmsPageMapper">
    
    <resultMap type="CmsPage" id="CmsPageResult">
        <result property="pageId"    column="page_id"    />
        <result property="pageName"    column="page_name"    />
        <result property="pageType"    column="page_type"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCmsPageVo">
        select page_id, page_name, page_type, del_flag, create_by, create_time, update_by, update_time from cms_page
    </sql>

    <select id="selectCmsPageList" parameterType="CmsPage" resultMap="CmsPageResult">
        <include refid="selectCmsPageVo"/>
        <where>  
            <if test="pageName != null  and pageName != ''"> and page_name like concat('%', #{pageName}, '%')</if>
            <if test="pageType != null "> and page_type = #{pageType}</if>
        </where>
    </select>
    
    <select id="selectCmsPageByPageId" parameterType="Long" resultMap="CmsPageResult">
        <include refid="selectCmsPageVo"/>
        where page_id = #{pageId}
    </select>

    <insert id="insertCmsPage" parameterType="CmsPage" useGeneratedKeys="true" keyProperty="pageId">
        insert into cms_page
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pageName != null">page_name,</if>
            <if test="pageType != null">page_type,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pageName != null">#{pageName},</if>
            <if test="pageType != null">#{pageType},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCmsPage" parameterType="CmsPage">
        update cms_page
        <trim prefix="SET" suffixOverrides=",">
            <if test="pageName != null">page_name = #{pageName},</if>
            <if test="pageType != null">page_type = #{pageType},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where page_id = #{pageId}
    </update>

    <delete id="deleteCmsPageByPageId" parameterType="Long">
        delete from cms_page where page_id = #{pageId}
    </delete>

    <delete id="deleteCmsPageByPageIds" parameterType="String">
        delete from cms_page where page_id in 
        <foreach item="pageId" collection="array" open="(" separator="," close=")">
            #{pageId}
        </foreach>
    </delete>
</mapper>
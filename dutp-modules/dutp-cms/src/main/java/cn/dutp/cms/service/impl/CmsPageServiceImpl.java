package cn.dutp.cms.service.impl;

import java.util.List;

import cn.dutp.cms.domain.CmsComponents;
import cn.dutp.cms.domain.CmsPageComponents;
import cn.dutp.cms.mapper.CmsPageComponentsMapper;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.cms.mapper.CmsPageMapper;
import cn.dutp.cms.domain.CmsPage;
import cn.dutp.cms.service.ICmsPageService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 页面管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Service
public class CmsPageServiceImpl extends ServiceImpl<CmsPageMapper, CmsPage> implements ICmsPageService
{
    @Autowired
    private CmsPageMapper cmsPageMapper;
    @Autowired
    private CmsPageComponentsMapper cmsPageComponentsMapper;

    /**
     * 查询页面管理
     *
     * @param pageId 页面管理主键
     * @return 页面管理
     */
    @Override
    public CmsPage selectCmsPageByPageId(Long pageId)
    {
        return this.getById(pageId);
    }

    /**
     * 查询页面管理列表
     *
     * @param cmsPage 页面管理
     * @return 页面管理
     */
    @Override
    public List<CmsPage> selectCmsPageList(CmsPage cmsPage)
    {
        LambdaQueryWrapper<CmsPage> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(cmsPage.getPageName())) {
                lambdaQueryWrapper.like(CmsPage::getPageName
                ,cmsPage.getPageName());
            }
                if(ObjectUtil.isNotEmpty(cmsPage.getPageType())) {
                lambdaQueryWrapper.eq(CmsPage::getPageType
                ,cmsPage.getPageType());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增页面管理
     *
     * @param cmsPage 页面管理
     * @return 结果
     */
    @Override
    public boolean insertCmsPage(CmsPage cmsPage)
    {
        return this.save(cmsPage);
    }

    /**
     * 修改页面管理
     *
     * @param cmsPage 页面管理
     * @return 结果
     */
    @Override
    public boolean updateCmsPage(CmsPage cmsPage)
    {
        return this.updateById(cmsPage);
    }

    /**
     * 批量删除页面管理
     *
     * @param pageIds 需要删除的页面管理主键
     * @return 结果
     */
    @Override
    public boolean deleteCmsPageByPageIds(List<Long> pageIds)
    {
        return this.removeByIds(pageIds);
    }

    @Override
    @Transactional
    public AjaxResult saveDecoration(CmsPage cmsPage) {
        LambdaUpdateWrapper<CmsPageComponents> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(CmsPageComponents::getPageId,cmsPage.getPageId());
        cmsPageComponentsMapper.delete(lambdaUpdateWrapper);
        List<CmsComponents> cmsComponents = cmsPage.getComponentList();
        for (int i=0;i<cmsComponents.size();i++) {
            CmsComponents cmsComponent = cmsComponents.get(i);
            CmsPageComponents cmsPageComponents = new CmsPageComponents();
            cmsPageComponents.setPageId(cmsPage.getPageId());
            cmsPageComponents.setComponentData(cmsComponent.getComponentData());
            cmsPageComponents.setComponentId(cmsComponent.getComponentId());
            cmsPageComponents.setSort(i + 1);
            cmsPageComponentsMapper.insert(cmsPageComponents);
        }
        return AjaxResult.success();
    }

}

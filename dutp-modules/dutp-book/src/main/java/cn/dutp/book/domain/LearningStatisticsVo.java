package cn.dutp.book.domain;

import cn.dutp.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

/**
 * 学习统计
 *
 * <AUTHOR>
 * @date 2024-11-30
 */
@Data
@Slf4j
public class LearningStatisticsVo extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 学习的教材本数
     */
    private Integer bookCount;

    /**
     * 学习时长
     */
    private BigDecimal learningDuration;

    /**
     * 笔记条数
     */
    private Integer notesCount;

    /**
     * 习题数量
     */
    private Integer exercisesCount;
}

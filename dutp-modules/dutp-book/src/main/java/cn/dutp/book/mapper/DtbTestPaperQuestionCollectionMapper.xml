<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbTestPaperQuestionCollectionMapper">
    
    <resultMap type="DtbTestPaperQuestionCollection" id="DtbTestPaperQuestionCollectionResult">
        <result property="collectionId"    column="collection_id"    />
        <result property="paperId"    column="paper_id"    />
        <result property="sort"    column="sort"    />
        <result property="questionType"    column="question_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap id="DtbTestPaperQuestionCollectionDtbTestPaperQuestionResult" type="DtbTestPaperQuestionCollection" extends="DtbTestPaperQuestionCollectionResult">
        <collection property="dtbTestPaperQuestionList" ofType="DtbTestPaperQuestion" column="collection_id" select="selectDtbTestPaperQuestionList" />
    </resultMap>

    <resultMap type="DtbTestPaperQuestion" id="DtbTestPaperQuestionResult">
        <result property="paperQuestionId"    column="paper_question_id"    />
        <result property="paperId"    column="paper_id"    />
        <result property="collectionId"    column="collection_id"    />
        <result property="questionId"    column="question_id"    />
        <result property="sort"    column="sort"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDtbTestPaperQuestionCollectionVo">
        select collection_id, paper_id, sort, question_type, create_by, create_time, update_by, update_time from dtb_test_paper_question_collection
    </sql>

    <select id="selectDtbTestPaperQuestionCollectionList" parameterType="DtbTestPaperQuestionCollection" resultMap="DtbTestPaperQuestionCollectionResult">
        <include refid="selectDtbTestPaperQuestionCollectionVo"/>
        <where>  
            <if test="paperId != null "> and paper_id = #{paperId}</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="questionType != null "> and question_type = #{questionType}</if>
        </where>
    </select>
    
    <select id="selectDtbTestPaperQuestionCollectionByCollectionId" parameterType="Long" resultMap="DtbTestPaperQuestionCollectionDtbTestPaperQuestionResult">
        select collection_id, paper_id, sort, question_type, create_by, create_time, update_by, update_time
        from dtb_test_paper_question_collection
        where collection_id = #{collectionId}
    </select>

    <select id="selectDtbTestPaperQuestionList" resultMap="DtbTestPaperQuestionResult">
        select paper_question_id, paper_id, collection_id, question_id, sort, create_by, create_time, update_by, update_time
        from dtb_test_paper_question
        where collection_id = #{collection_id}
    </select>

    <insert id="insertDtbTestPaperQuestionCollection" parameterType="DtbTestPaperQuestionCollection" useGeneratedKeys="true" keyProperty="collectionId">
        insert into dtb_test_paper_question_collection
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="paperId != null">paper_id,</if>
            <if test="sort != null">sort,</if>
            <if test="questionType != null">question_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="paperId != null">#{paperId},</if>
            <if test="sort != null">#{sort},</if>
            <if test="questionType != null">#{questionType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDtbTestPaperQuestionCollection" parameterType="DtbTestPaperQuestionCollection">
        update dtb_test_paper_question_collection
        <trim prefix="SET" suffixOverrides=",">
            <if test="paperId != null">paper_id = #{paperId},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="questionType != null">question_type = #{questionType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where collection_id = #{collectionId}
    </update>

    <delete id="deleteDtbTestPaperQuestionCollectionByCollectionId" parameterType="Long">
        delete from dtb_test_paper_question_collection where collection_id = #{collectionId}
    </delete>

    <delete id="deleteDtbTestPaperQuestionCollectionByCollectionIds" parameterType="String">
        delete from dtb_test_paper_question_collection where collection_id in 
        <foreach item="collectionId" collection="array" open="(" separator="," close=")">
            #{collectionId}
        </foreach>
    </delete>
    
    <delete id="deleteDtbTestPaperQuestionByCollectionIds" parameterType="String">
        delete from dtb_test_paper_question where collection_id in 
        <foreach item="collectionId" collection="array" open="(" separator="," close=")">
            #{collectionId}
        </foreach>
    </delete>

    <delete id="deleteDtbTestPaperQuestionByCollectionId" parameterType="Long">
        delete from dtb_test_paper_question where collection_id = #{collectionId}
    </delete>

    <insert id="batchDtbTestPaperQuestion">
        insert into dtb_test_paper_question( paper_question_id, paper_id, collection_id, question_id, sort, create_by, create_time, update_by, update_time) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.paperQuestionId}, #{item.paperId}, #{item.collectionId}, #{item.questionId}, #{item.sort}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>
</mapper>
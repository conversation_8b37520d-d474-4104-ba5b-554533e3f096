package cn.dutp.book.service.impl;

import java.util.List;

import cn.dutp.book.domain.vo.DtbUserBookMarkVO;
import cn.dutp.common.core.constant.HttpStatus;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.book.mapper.DtbUserBookMarkMapper;
import cn.dutp.book.domain.DtbUserBookMark;
import cn.dutp.book.service.IDtbUserBookMarkService;

/**
 * DUTP-DTB_021书签Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class DtbUserBookMarkServiceImpl extends ServiceImpl<DtbUserBookMarkMapper, DtbUserBookMark> implements IDtbUserBookMarkService
{
    @Autowired
    private DtbUserBookMarkMapper dtbUserBookMarkMapper;

    /**
     * 查询DUTP-DTB_021书签
     *
     * @param markId DUTP-DTB_021书签主键
     * @return DUTP-DTB_021书签
     */
    @Override
    public DtbUserBookMark selectDtbUserBookMarkByMarkId(Long markId)
    {
        return this.getById(markId);
    }

    /**
     * 查询DUTP-DTB_021书签列表
     *
     * @param dtbUserBookMark DUTP-DTB_021书签
     * @return DUTP-DTB_021书签
     */
    @Override
    public List<DtbUserBookMark> selectDtbUserBookMarkList(DtbUserBookMark dtbUserBookMark)
    {
        LambdaQueryWrapper<DtbUserBookMark> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbUserBookMark.getPageNumber())) {
                lambdaQueryWrapper.eq(DtbUserBookMark::getPageNumber
                ,dtbUserBookMark.getPageNumber());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookMark.getUserId())) {
                lambdaQueryWrapper.eq(DtbUserBookMark::getUserId
                ,dtbUserBookMark.getUserId());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookMark.getBookId())) {
                lambdaQueryWrapper.eq(DtbUserBookMark::getBookId
                ,dtbUserBookMark.getBookId());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookMark.getChapterId())) {
                lambdaQueryWrapper.eq(DtbUserBookMark::getChapterId
                ,dtbUserBookMark.getChapterId());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增DUTP-DTB_021书签
     *
     * @param dtbUserBookMark DUTP-DTB_021书签
     * @return 结果
     */
    @Override
    public AjaxResult insertDtbUserBookMark(DtbUserBookMark dtbUserBookMark)
    {
        this.save(dtbUserBookMark);
        return AjaxResult.success();
    }

    /**
     * 修改DUTP-DTB_021书签
     *
     * @param dtbUserBookMark DUTP-DTB_021书签
     * @return 结果
     */
    @Override
    public boolean updateDtbUserBookMark(DtbUserBookMark dtbUserBookMark)
    {
        return this.updateById(dtbUserBookMark);
    }

    /**
     * 批量删除DUTP-DTB_021书签
     *
     * @param markIds 需要删除的DUTP-DTB_021书签主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbUserBookMarkByMarkIds(List<Long> markIds)
    {
        return this.removeByIds(markIds);
    }

    @Override
    public void deleteDtbUserBookMarkByPage(Integer pageNumber, Long chapterId) {
        LambdaQueryWrapper<DtbUserBookMark> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DtbUserBookMark::getPageNumber,pageNumber).eq(DtbUserBookMark::getChapterId,chapterId);
        dtbUserBookMarkMapper.delete(lambdaQueryWrapper);
    }

    @Override
    public AjaxResult selectReaderUserBookMarkList(DtbUserBookMark dtbUserBookMark) {
        List<DtbUserBookMarkVO> vos = dtbUserBookMarkMapper.selectReaderUserBookMarkList(dtbUserBookMark);
        return AjaxResult.success(vos);
    }

    @Override
    public AjaxResult deleteBookMark(Long markId) {
        DtbUserBookMark dtbUserBookMark = this.getById(markId);
        if (dtbUserBookMark.getUserId().longValue()!=SecurityUtils.getUserId().longValue()) {
            return AjaxResult.error(HttpStatus.FORBIDDEN,"没有删除的权限");
        }
        this.baseMapper.deleteById(markId);
        return AjaxResult.success();
    }
}

package cn.dutp.book.service;


import java.util.List;

import cn.dutp.book.domain.DtbBookChapterAuditLog;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 章节目录审核记录Service接口
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface IDtbBookChapterAuditLogService extends IService<DtbBookChapterAuditLog> {
    /**
     * 查询章节目录审核记录
     *
     * @param logId 章节目录审核记录主键
     * @return 章节目录审核记录
     */
    DtbBookChapterAuditLog selectDtbBookChapterAuditLogByLogId(Long logId);

    /**
     * 查询章节目录审核记录列表
     *
     * @param dtbBookChapterAuditLog 章节目录审核记录
     * @return 章节目录审核记录集合
     */
    List<DtbBookChapterAuditLog> selectDtbBookChapterAuditLogList(DtbBookChapterAuditLog dtbBookChapterAuditLog);

    /**
     * 撤销章节申请
     *
     * @param dtbBookChapterAuditLog 章节目录审核记录
     * @return 结果
     */
    boolean insertDtbBookChapterAuditLog(DtbBookChapterAuditLog dtbBookChapterAuditLog);

    /**
     * 修改章节目录审核记录
     *
     * @param dtbBookChapterAuditLog 章节目录审核记录
     * @return 结果
     */
    boolean updateDtbBookChapterAuditLog(DtbBookChapterAuditLog dtbBookChapterAuditLog);

    /**
     * 批量删除章节目录审核记录
     *
     * @param logIds 需要删除的章节目录审核记录主键集合
     * @return 结果
     */
    boolean deleteDtbBookChapterAuditLogByLogIds(List<Long> logIds);

    DtbBookChapterAuditLog getRejectedReasonInfo(Long chapterId);

    List<DtbBookChapterAuditLog> chapterAuditHistoryList(Long chapterId);
}

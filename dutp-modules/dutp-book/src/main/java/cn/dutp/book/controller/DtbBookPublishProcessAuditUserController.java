package cn.dutp.book.controller;

import cn.dutp.book.domain.DtbBookPublishProcessAuditUser;
import cn.dutp.book.service.IDtbBookPublishProcessAuditUserService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 发布流程审核人Controller
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@RestController
@RequestMapping("/processAuditUser")
public class DtbBookPublishProcessAuditUserController extends BaseController
{
    @Autowired
    private IDtbBookPublishProcessAuditUserService dtbBookPublishProcessAuditUserService;

    /**
     * 查询发布流程审核人列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbBookPublishProcessAuditUser dtbBookPublishProcessAuditUser)
    {
        startPage();
        List<DtbBookPublishProcessAuditUser> list = dtbBookPublishProcessAuditUserService.selectDtbBookPublishProcessAuditUserList(dtbBookPublishProcessAuditUser);
        return getDataTable(list);
    }

    /**
     * 导出发布流程审核人列表
     */
    @RequiresPermissions("system:user:export")
    @Log(title = "发布流程审核人", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbBookPublishProcessAuditUser dtbBookPublishProcessAuditUser)
    {
        List<DtbBookPublishProcessAuditUser> list = dtbBookPublishProcessAuditUserService.selectDtbBookPublishProcessAuditUserList(dtbBookPublishProcessAuditUser);
        ExcelUtil<DtbBookPublishProcessAuditUser> util = new ExcelUtil<DtbBookPublishProcessAuditUser>(DtbBookPublishProcessAuditUser.class);
        util.exportExcel(response, list, "发布流程审核人数据");
    }

    /**
     * 获取发布流程审核人详细信息
     */
    @RequiresPermissions("system:user:query")
    @GetMapping(value = "/{auditUserId}")
    public AjaxResult getInfo(@PathVariable("auditUserId") Long auditUserId)
    {
        return success(dtbBookPublishProcessAuditUserService.selectDtbBookPublishProcessAuditUserByAuditUserId(auditUserId));
    }

    /**
     * 新增发布流程审核人
     */
    @RequiresPermissions("system:user:add")
    @Log(title = "新增发布流程审核人", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookPublishProcessAuditUser dtbBookPublishProcessAuditUser)
    {
        return toAjax(dtbBookPublishProcessAuditUserService.insertDtbBookPublishProcessAuditUser(dtbBookPublishProcessAuditUser));
    }

    /**
     * 修改发布流程审核人
     */
    @RequiresPermissions("system:user:edit")
    @Log(title = "修改发布流程审核人", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookPublishProcessAuditUser dtbBookPublishProcessAuditUser)
    {
        return toAjax(dtbBookPublishProcessAuditUserService.updateDtbBookPublishProcessAuditUser(dtbBookPublishProcessAuditUser));
    }

    /**
     * 删除发布流程审核人
     */
    @RequiresPermissions("system:user:remove")
    @Log(title = "删除发布流程审核人", businessType = BusinessType.DELETE)
    @DeleteMapping("/{auditUserIds}")
    public AjaxResult remove(@PathVariable Long[] auditUserIds)
    {
        return toAjax(dtbBookPublishProcessAuditUserService.deleteDtbBookPublishProcessAuditUserByAuditUserIds(Arrays.asList(auditUserIds)));
    }
}

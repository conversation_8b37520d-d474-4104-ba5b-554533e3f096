package cn.dutp.book.controller;

import cn.dutp.book.domain.DutpAiTranslationLanguage;
import cn.dutp.book.service.IDutpAiTranslationLanguageService;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 翻译语种Controller
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
@RestController
@RequestMapping("/aiTranslation/language")
public class DutpAiTranslationLanguageController extends BaseController
{
    @Autowired
    private IDutpAiTranslationLanguageService dutpAiTranslationLanguageService;

    /**
     * 查询翻译语种列表
     */
    @GetMapping("/list")
    public AjaxResult list(DutpAiTranslationLanguage dutpAiTranslationLanguage)
    {
        List<DutpAiTranslationLanguage> list = dutpAiTranslationLanguageService.selectDutpAiTranslationLanguageList(dutpAiTranslationLanguage);
        return success(list);
    }

}


package cn.dutp.book.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.book.mapper.DtbUserBookStudyDataMapper;
import cn.dutp.book.domain.DtbUserBookStudyData;
import cn.dutp.book.service.IDtbUserBookStudyDataService;

/**
 * DUTP-DTB_024学生学习数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class DtbUserBookStudyDataServiceImpl extends ServiceImpl<DtbUserBookStudyDataMapper, DtbUserBookStudyData> implements IDtbUserBookStudyDataService
{
    @Autowired
    private DtbUserBookStudyDataMapper dtbUserBookStudyDataMapper;

    /**
     * 查询DUTP-DTB_024学生学习数据
     *
     * @param dataId DUTP-DTB_024学生学习数据主键
     * @return DUTP-DTB_024学生学习数据
     */
    @Override
    public DtbUserBookStudyData selectDtbUserBookStudyDataByDataId(Long dataId)
    {
        return this.getById(dataId);
    }

    /**
     * 查询DUTP-DTB_024学生学习数据列表
     *
     * @param dtbUserBookStudyData DUTP-DTB_024学生学习数据
     * @return DUTP-DTB_024学生学习数据
     */
    @Override
    public List<DtbUserBookStudyData> selectDtbUserBookStudyDataList(DtbUserBookStudyData dtbUserBookStudyData)
    {
        LambdaQueryWrapper<DtbUserBookStudyData> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbUserBookStudyData.getUserId())) {
                lambdaQueryWrapper.eq(DtbUserBookStudyData::getUserId
                ,dtbUserBookStudyData.getUserId());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookStudyData.getBookId())) {
                lambdaQueryWrapper.eq(DtbUserBookStudyData::getBookId
                ,dtbUserBookStudyData.getBookId());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookStudyData.getChapterId())) {
                lambdaQueryWrapper.eq(DtbUserBookStudyData::getChapterId
                ,dtbUserBookStudyData.getChapterId());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookStudyData.getStudyTime())) {
                lambdaQueryWrapper.eq(DtbUserBookStudyData::getStudyTime
                ,dtbUserBookStudyData.getStudyTime());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookStudyData.getVideoTime())) {
                lambdaQueryWrapper.eq(DtbUserBookStudyData::getVideoTime
                ,dtbUserBookStudyData.getVideoTime());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookStudyData.getNoteQuantity())) {
                lambdaQueryWrapper.eq(DtbUserBookStudyData::getNoteQuantity
                ,dtbUserBookStudyData.getNoteQuantity());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookStudyData.getLightQuantity())) {
                lambdaQueryWrapper.eq(DtbUserBookStudyData::getLightQuantity
                ,dtbUserBookStudyData.getLightQuantity());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookStudyData.getDiscussQuantity())) {
                lambdaQueryWrapper.eq(DtbUserBookStudyData::getDiscussQuantity
                ,dtbUserBookStudyData.getDiscussQuantity());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookStudyData.getUserQuantity())) {
                lambdaQueryWrapper.eq(DtbUserBookStudyData::getUserQuantity
                ,dtbUserBookStudyData.getUserQuantity());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookStudyData.getQuestionQuantity())) {
                lambdaQueryWrapper.eq(DtbUserBookStudyData::getQuestionQuantity
                ,dtbUserBookStudyData.getQuestionQuantity());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookStudyData.getQuestionRate())) {
                lambdaQueryWrapper.eq(DtbUserBookStudyData::getQuestionRate
                ,dtbUserBookStudyData.getQuestionRate());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增DUTP-DTB_024学生学习数据
     *
     * @param dtbUserBookStudyData DUTP-DTB_024学生学习数据
     * @return 结果
     */
    @Override
    public boolean insertDtbUserBookStudyData(DtbUserBookStudyData dtbUserBookStudyData)
    {
        return this.save(dtbUserBookStudyData);
    }

    /**
     * 修改DUTP-DTB_024学生学习数据
     *
     * @param dtbUserBookStudyData DUTP-DTB_024学生学习数据
     * @return 结果
     */
    @Override
    public boolean updateDtbUserBookStudyData(DtbUserBookStudyData dtbUserBookStudyData)
    {
        return this.updateById(dtbUserBookStudyData);
    }

    /**
     * 批量删除DUTP-DTB_024学生学习数据
     *
     * @param dataIds 需要删除的DUTP-DTB_024学生学习数据主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbUserBookStudyDataByDataIds(List<Long> dataIds)
    {
        return this.removeByIds(dataIds);
    }

}

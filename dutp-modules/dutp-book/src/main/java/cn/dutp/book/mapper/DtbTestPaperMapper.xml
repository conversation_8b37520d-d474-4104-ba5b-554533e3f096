<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbTestPaperMapper">
    
    <resultMap type="DtbTestPaper" id="DtbTestPaperResult">
        <result property="paperId"    column="paper_id"    />
        <result property="paperTitle"    column="paper_title"    />
        <result property="questionQuantity"    column="question_quantity"    />
        <result property="totalScore"    column="total_score"    />
        <result property="paperType"    column="paper_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="sysUserId"    column="sys_user_id"    />
    </resultMap>

    <resultMap id="DtbTestPaperDtbTestPaperQuestionCollectionResult" type="DtbTestPaper" extends="DtbTestPaperResult">
        <collection property="dtbTestPaperQuestionCollectionList" ofType="DtbTestPaperQuestionCollection" column="paper_id" select="selectDtbTestPaperQuestionCollectionList" />
    </resultMap>

    <resultMap type="DtbTestPaperQuestionCollection" id="DtbTestPaperQuestionCollectionResult">
        <result property="collectionId"    column="collection_id"    />
        <result property="paperId"    column="paper_id"    />
        <result property="sort"    column="sort"    />
        <result property="questionType"    column="question_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDtbTestPaperVo">
        select paper_id, paper_title, question_quantity, total_score, paper_type, create_by, create_time, update_by, update_time, del_flag from dtb_test_paper
    </sql>

    <select id="selectDtbTestPaperList" parameterType="DtbTestPaper" resultMap="DtbTestPaperResult">
        <include refid="selectDtbTestPaperVo"/>
        <where>  
            <if test="paperTitle != null  and paperTitle != ''"> and paper_title = #{paperTitle}</if>
            <if test="questionQuantity != null "> and question_quantity = #{questionQuantity}</if>
            <if test="totalScore != null "> and total_score = #{totalScore}</if>
            <if test="paperType != null "> and paper_type = #{paperType}</if>
        </where>
    </select>
    
    <select id="selectDtbTestPaperByPaperId" parameterType="Long" resultMap="DtbTestPaperDtbTestPaperQuestionCollectionResult">
        select paper_id, paper_title, question_quantity, total_score, paper_type, create_by, create_time, update_by, update_time, del_flag,sys_user_id
        from dtb_test_paper
        where paper_id = #{paperId}
    </select>

    <select id="selectDtbTestPaperQuestionCollectionList" resultMap="DtbTestPaperQuestionCollectionResult">
        select collection_id, paper_id, sort, question_type, create_by, create_time, update_by, update_time
        from dtb_test_paper_question_collection
        where paper_id = #{paper_id}
    </select>

    <insert id="insertDtbTestPaper" parameterType="DtbTestPaper" useGeneratedKeys="true" keyProperty="paperId">
        insert into dtb_test_paper
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="paperTitle != null">paper_title,</if>
            <if test="questionQuantity != null">question_quantity,</if>
            <if test="totalScore != null">total_score,</if>
            <if test="paperType != null">paper_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="paperTitle != null">#{paperTitle},</if>
            <if test="questionQuantity != null">#{questionQuantity},</if>
            <if test="totalScore != null">#{totalScore},</if>
            <if test="paperType != null">#{paperType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateDtbTestPaper" parameterType="DtbTestPaper">
        update dtb_test_paper
        <trim prefix="SET" suffixOverrides=",">
            <if test="paperTitle != null">paper_title = #{paperTitle},</if>
            <if test="questionQuantity != null">question_quantity = #{questionQuantity},</if>
            <if test="totalScore != null">total_score = #{totalScore},</if>
            <if test="paperType != null">paper_type = #{paperType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where paper_id = #{paperId}
    </update>

    <delete id="deleteDtbTestPaperByPaperId" parameterType="Long">
        delete from dtb_test_paper where paper_id = #{paperId}
    </delete>

    <delete id="deleteDtbTestPaperByPaperIds" parameterType="String">
        delete from dtb_test_paper where paper_id in 
        <foreach item="paperId" collection="array" open="(" separator="," close=")">
            #{paperId}
        </foreach>
    </delete>
    
    <delete id="deleteDtbTestPaperQuestionCollectionByPaperIds" parameterType="String">
        delete from dtb_test_paper_question_collection where paper_id in 
        <foreach item="paperId" collection="array" open="(" separator="," close=")">
            #{paperId}
        </foreach>
    </delete>

    <delete id="deleteDtbTestPaperQuestionCollectionByPaperId" parameterType="Long">
        delete from dtb_test_paper_question_collection where paper_id = #{paperId}
    </delete>

    <insert id="batchDtbTestPaperQuestionCollection">
        insert into dtb_test_paper_question_collection( collection_id, paper_id, sort, question_type, create_by, create_time, update_by, update_time) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.collectionId}, #{item.paperId}, #{item.sort}, #{item.questionType}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>

    <select id="selectQuestionsByCollectionId" resultType="DtbTestPaperQuestion">
    SELECT *
    FROM dtb_test_paper_question
    WHERE collection_id = #{collectionId}
    ORDER BY sort ASC
    </select>




<update id="moveToRecycleBin">
    update dtb_test_paper set del_flag = '1', update_by = #{username}, update_time = sysdate()
    where paper_id in
    <foreach collection="paperIds" item="paperId" open="(" separator="," close=")">
        #{paperId}
    </foreach>
</update>

<update id="restoreFromRecycleBin">
    update dtb_test_paper set del_flag = '0', update_by = #{username}, update_time = sysdate()
    where paper_id in
    <foreach collection="paperIds" item="paperId" open="(" separator="," close=")">
        #{paperId}
    </foreach>
    and del_flag = '1'
</update>


    <select id="selectRecycleBinList" parameterType="DtbTestPaper" resultMap="DtbTestPaperResult">
    select * from dtb_test_paper
    <where>
        del_flag = '1'
        <if test="paperTitle != null and paperTitle != ''">
            AND paper_title like concat('%', #{paperTitle}, '%')
        </if>
        <if test="questionQuantity != null">
            AND question_quantity = #{questionQuantity}
        </if>
        <if test="totalScore != null">
            AND total_score = #{totalScore}
        </if>
        <if test="paperType != null">
            AND paper_type = #{paperType}
        </if>
        <if test="sysUserId != null">
            AND sys_user_id = #{sysUserId}
        </if>
    </where>
    order by update_time desc
</select>

<update id="deleteBatchByIds">
    UPDATE dtb_test_paper 
    SET del_flag = 2 
    WHERE paper_id IN 
    <foreach collection="paperIds" item="paperId" open="(" separator="," close=")">
        #{paperId}
    </foreach>
    AND sys_user_id = #{userId}
</update>
</mapper>
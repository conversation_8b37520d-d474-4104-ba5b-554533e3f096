package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.List;

/**
 * 心里测试量维度对象 mooc_psychology_health_scale_facet
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Data
@TableName("mooc_psychology_health_scale_facet")
public class MoocPsychologyHealthScaleFacet extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long facetId;

    /**
     * 量表id
     */
    @Excel(name = "量表id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long scaleId;

    /**
     * 维度名称
     */
    @Excel(name = "维度名称")
    private String facetName;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Long sort;

    /**
     * 排序
     */
    @TableField(exist = false)
    private Long facetSort;

    /**
     * 量表题干
     */
    @TableField(exist = false)
    private List<MoocPsychologyHealthScaleQuestion> moocPsychologyHealthScaleQuestion;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("facetId", getFacetId())
                .append("scaleId", getScaleId())
                .append("facetName", getFacetName())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

package cn.dutp.book.service;

import cn.dutp.domain.DtbBookPurchaseCode;
import cn.dutp.domain.DtbBookPurchaseCodeExcel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
/**
 * 发行管理Service接口
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
public interface IDtbBookPurchaseCodeService extends IService<DtbBookPurchaseCode>
{

    /**
     * 查询发行管理列表
     *
     * @param dtbBookPurchaseCode 发行管理
     * @return 发行管理集合
     */
    List<DtbBookPurchaseCode> selectDtbBookPurchaseCodeList(DtbBookPurchaseCode dtbBookPurchaseCode);

    /**
     * 批量添加发行码
     * @param dtbBookPurchaseCodeList
     * @return
     */
    boolean batchPurchaseCode (List<DtbBookPurchaseCode> dtbBookPurchaseCodeList);

    /**
     * 导出购书吗验证
     * @param dtbBookPurchaseCodeList
     * @return
     */
    List<DtbBookPurchaseCodeExcel> exprortCodeValid (List<DtbBookPurchaseCode> dtbBookPurchaseCodeList);

    /**
     * 查看购书码页面的购书码列表
     *
     * @param dtbBookPurchaseCode 发行管理
     * @return 发行管理集合
     */
    List<DtbBookPurchaseCode> selectCodelList(DtbBookPurchaseCode dtbBookPurchaseCode);

    boolean codeUnbind(DtbBookPurchaseCode dtbBookPurchaseCode);


    boolean codeBacthUnbind(List<DtbBookPurchaseCode> dtbBookPurchaseCodeList);

    boolean updateTimeLimit(List<DtbBookPurchaseCode> dtbBookPurchaseCodeList);

    boolean deleteBacthPurchaseCode(List<DtbBookPurchaseCode> dtbBookPurchaseCodeList);
}

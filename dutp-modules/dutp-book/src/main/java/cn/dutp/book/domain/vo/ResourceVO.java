package cn.dutp.book.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

import java.util.Date;


@Data
public class ResourceVO {
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long resourceId;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long  folderId;
    private String name;
    private String folderName;
    private String type;  // "folder" 或 "file"
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;
    private String fileName;
    private String fileUrl;
    private Long fileSize;
    private String fileType;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;
    private String createBy;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long BookId;
    private String defaultType;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;
} 
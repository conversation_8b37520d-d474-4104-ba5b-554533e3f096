package cn.dutp.book.domain.vo;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * DUTP-DTB_018足迹对象 dtb_user_book_foot_print
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
@Data
public class DtbUserBookFootPrintVo extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** $column.columnComment */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long footPrintId;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /** 查看次数 */
    @Excel(name = "查看次数")
    private Long seeQuantity;

    /** 书名 */
    @Excel(name = "书名")
    private String bookName;

    /** 封面 */
    @Excel(name = "封面")
    private String cover;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("footPrintId", getFootPrintId())
                .append("bookId", getBookId())
                .append("userId", getUserId())
                .append("seeQuantity", getSeeQuantity())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("bookName", getBookName())
                .append("cover", getCover())
                .toString();
    }
}

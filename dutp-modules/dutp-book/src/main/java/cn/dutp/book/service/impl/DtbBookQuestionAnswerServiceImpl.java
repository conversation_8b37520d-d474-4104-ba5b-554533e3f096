package cn.dutp.book.service.impl;

import java.util.List;

import cn.dutp.common.security.utils.SecurityUtils;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.book.mapper.DtbBookQuestionAnswerMapper;
import cn.dutp.book.domain.DtbBookQuestionAnswer;
import cn.dutp.book.service.IDtbBookQuestionAnswerService;

/**
 * 问题答案Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
@Service
public class DtbBookQuestionAnswerServiceImpl extends ServiceImpl<DtbBookQuestionAnswerMapper, DtbBookQuestionAnswer> implements IDtbBookQuestionAnswerService
{
    @Autowired
    private DtbBookQuestionAnswerMapper dtbBookQuestionAnswerMapper;

    /**
     * 查询问题答案
     *
     * @param answerId 问题答案主键
     * @return 问题答案
     */
    @Override
    public DtbBookQuestionAnswer selectDtbBookQuestionAnswerByAnswerId(Long answerId)
    {
        return this.getById(answerId);
    }

    /**
     * 查询问题答案列表
     *
     * @param dtbBookQuestionAnswer 问题答案
     * @return 问题答案
     */
    @Override
    public List<DtbBookQuestionAnswer> selectDtbBookQuestionAnswerList(DtbBookQuestionAnswer dtbBookQuestionAnswer)
    {
        LambdaQueryWrapper<DtbBookQuestionAnswer> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbBookQuestionAnswer.getAnswerId())) {
                lambdaQueryWrapper.eq(DtbBookQuestionAnswer::getAnswerId
                ,dtbBookQuestionAnswer.getAnswerId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookQuestionAnswer.getChapterId())) {
                lambdaQueryWrapper.eq(DtbBookQuestionAnswer::getChapterId
                ,dtbBookQuestionAnswer.getChapterId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookQuestionAnswer.getAnswerContent())) {
                lambdaQueryWrapper.eq(DtbBookQuestionAnswer::getAnswerContent
                ,dtbBookQuestionAnswer.getAnswerContent());
            }
                if(ObjectUtil.isNotEmpty(dtbBookQuestionAnswer.getScore())) {
                lambdaQueryWrapper.eq(DtbBookQuestionAnswer::getScore
                ,dtbBookQuestionAnswer.getScore());
            }
                if(ObjectUtil.isNotEmpty(dtbBookQuestionAnswer.getBookId())) {
                lambdaQueryWrapper.eq(DtbBookQuestionAnswer::getBookId
                ,dtbBookQuestionAnswer.getBookId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookQuestionAnswer.getUserId())) {
                lambdaQueryWrapper.eq(DtbBookQuestionAnswer::getUserId
                ,dtbBookQuestionAnswer.getUserId());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增问题答案
     *
     * @param dtbBookQuestionAnswer 问题答案
     * @return 结果
     */
    @Override
    public boolean insertDtbBookQuestionAnswer(DtbBookQuestionAnswer dtbBookQuestionAnswer)
    {
        // 获取登录用户id 根据用户id查询出当前登录人的待办事项(待审批数据)
        Long userId = SecurityUtils.getUserId();
        dtbBookQuestionAnswer.setUserId(userId);
        return this.save(dtbBookQuestionAnswer);
    }

    /**
     * 修改问题答案
     *
     * @param dtbBookQuestionAnswer 问题答案
     * @return 结果
     */
    @Override
    public boolean updateDtbBookQuestionAnswer(DtbBookQuestionAnswer dtbBookQuestionAnswer)
    {
        return this.updateById(dtbBookQuestionAnswer);
    }

    /**
     * 批量删除问题答案
     *
     * @param answerIds 需要删除的问题答案主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookQuestionAnswerByAnswerIds(List<Long> answerIds)
    {
        return this.removeByIds(answerIds);
    }

}

package cn.dutp.book.domain.vo;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class DtbUserBookMarkVO
{
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long markId;
    @Excel(name = "页码")
    private Integer pageNumber;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;
    @Excel(name = "书签内容")
    private String pageContent;
    private String chapterName;
}

package cn.dutp.book.controller;

import cn.dutp.book.domain.DtbBookPublishProcess;
import cn.dutp.book.service.IDtbBookPublishProcessService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】Controller
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@RestController
@RequestMapping("/process")
public class DtbBookPublishProcessController extends BaseController {
    @Autowired
    private IDtbBookPublishProcessService dtbBookPublishProcessService;

    /**
     * 查询DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbBookPublishProcess dtbBookPublishProcess) {
        startPage();
        List<DtbBookPublishProcess> list = dtbBookPublishProcessService.selectDtbBookPublishProcessList(dtbBookPublishProcess);
        return getDataTable(list);
    }


    /**
     * 获取DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】详细信息
     */
    @GetMapping(value = "/{processId}")
    public AjaxResult getInfo(@PathVariable("processId") Long processId) {
        return success(dtbBookPublishProcessService.selectDtbBookPublishProcessByProcessId(processId));
    }

    /**
     * 新增DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】
     */
    @Log(title = "新增DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookPublishProcess dtbBookPublishProcess) {
        return toAjax(dtbBookPublishProcessService.insertDtbBookPublishProcess(dtbBookPublishProcess));
    }

    /**
     * 节点审批 -- 补录保存
     */
    @Log(title = "节点审批 -- 补录保存", businessType = BusinessType.INSERT)
    @PostMapping("/addProcessAddition")
    public AjaxResult addProcessAddition(@RequestBody DtbBookPublishProcess dtbBookPublishProcess) {
        return toAjax(dtbBookPublishProcessService.addProcessAddition(dtbBookPublishProcess));
    }

    /**
     * 节点审批 -- 补录编辑
     */
    @Log(title = "节点审批 -- 补录编辑", businessType = BusinessType.INSERT)
    @PostMapping("/editProcessAddition")
    public AjaxResult editProcessAddition(@RequestBody DtbBookPublishProcess dtbBookPublishProcess) {
        return toAjax(dtbBookPublishProcessService.editProcessAddition(dtbBookPublishProcess));
    }

    /**
     * 补录详情
     */
    @GetMapping(value = "/getProcessAddition")
    public AjaxResult getProcessAddition(DtbBookPublishProcess dtbBookPublishProcess) {
        return success(dtbBookPublishProcessService.getProcessAddition(dtbBookPublishProcess));
    }

    /**
     * 修改DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】
     */
    @Log(title = "修改DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookPublishProcess dtbBookPublishProcess) {
        return toAjax(dtbBookPublishProcessService.updateDtbBookPublishProcess(dtbBookPublishProcess));
    }

    /**
     * 删除DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】
     */
    @Log(title = "删除DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{processIds}")
    public AjaxResult remove(@PathVariable Long[] processIds) {
        return toAjax(dtbBookPublishProcessService.deleteDtbBookPublishProcessByProcessIds(Arrays.asList(processIds)));
    }

    /**
     * 获取审批详情
     */
    @GetMapping("/processDataList")
    public AjaxResult processDataList(DtbBookPublishProcess dtbBookPublishProcess) {
        return success(dtbBookPublishProcessService.processDataList(dtbBookPublishProcess));
    }

    /**
     * 获取上次流程的节点信息
     */
    @GetMapping("/getPrevProcessInfo")
    public AjaxResult getPrevProcessInfo(DtbBookPublishProcess dtbBookPublishProcess) {
        return success(dtbBookPublishProcessService.getPrevProcessInfo(dtbBookPublishProcess));
    }

    /**
     * 获取DUTP-DTB_007数字教材发布流程【我的消息跳转至审批详情】详细信息
     */
    @GetMapping(value = "/getProcessInfoLink/{processId}")
    public AjaxResult getProcessInfoLink(@PathVariable("processId") Long processId) {
        return success(dtbBookPublishProcessService.getProcessInfoLink(processId));
    }

    /**
     * 章节点击编辑时备份
     */
    @Log(title = "章节点击编辑时备份", businessType = BusinessType.INSERT)
    @PostMapping("/updateChapterBackup")
    public AjaxResult updateChapterBackup(@RequestBody DtbBookPublishProcess dtbBookPublishProcess){
        return dtbBookPublishProcessService.updateChapterBackup(dtbBookPublishProcess);
    }
}

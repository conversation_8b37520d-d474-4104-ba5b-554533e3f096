package cn.dutp.book.service.impl;

import cn.dutp.book.DutpBookApplication;
import cn.dutp.book.domain.DtbBookChapter;
import cn.dutp.book.domain.DtbBookChapterContent;
import cn.dutp.book.domain.DutpTask;
import cn.dutp.book.mapper.DtbBookChapterMapper;
import cn.dutp.book.mapper.DutpTaskMapper;
import cn.dutp.book.service.IDtbBookService;
import cn.dutp.book.service.IDutpTaskService;
import cn.dutp.book.websocket.modal.BookChapterContentEditorMessage;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.mongo.service.MongoService;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBook;
import cn.dutp.message.api.RemoteUserMessageService;
import cn.dutp.message.api.domain.DutpUserMessage;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.springframework.beans.factory.annotation.Value;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketHttpHeaders;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.net.URI;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static cn.dutp.book.service.impl.DtbBookChapterContentServiceImpl.BOOK_CHAPTER_CONTENT;

/**
 * 任务管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@Service
@Slf4j
public class DutpTaskServiceImpl extends ServiceImpl<DutpTaskMapper, DutpTask> implements IDutpTaskService {
    @Autowired
    private DutpTaskMapper dutpTaskMapper;

    @Autowired
    private DtbBookChapterMapper chapterMapper;

    @Autowired
    private MongoService mongoService;

    @Autowired
    private IDtbBookService dtbBookService;

    @Autowired
    private RemoteUserMessageService remoteUserMessageService;

    @Value("${wsbase.url}")
    private String websocketUrl;

    /**
     * 查询任务管理
     *
     * @param taskId 任务管理主键
     * @return 任务管理
     */
    @Override
    public DutpTask selectDutpTaskByTaskId(Long taskId) {
        return this.getById(taskId);
    }

    /**
     * 查询任务管理列表
     *
     * @param dutpTask 任务管理
     * @return 任务管理
     */
    @Override
    public List<DutpTask> selectDutpTaskList(DutpTask dutpTask) {
        Long userId = SecurityUtils.getUserId();
        List<DutpTask> list = dutpTaskMapper.selectTaskList(userId,dutpTask);
        return list;
    }

    /**
     * 新增任务管理
     *
     * @param dutpTask 任务管理
     * @return 结果
     */
    @Override
    public boolean insertDutpTask(DutpTask dutpTask) {
        return this.save(dutpTask);
    }

    /**
     * 修改任务管理
     *
     * @param dutpTask 任务管理
     * @return 结果
     */
    @Override
    public boolean updateDutpTask(DutpTask dutpTask) {
        return this.updateById(dutpTask);
    }

    /**
     * 批量删除任务管理
     *
     * @param taskIds 需要删除的任务管理主键
     * @return 结果
     */
    @Override
    public boolean deleteDutpTaskByTaskIds(List<Long> taskIds) {
        return this.removeByIds(taskIds);
    }

    @Override
    @Transactional
    public AjaxResult editCaptionStyle(DutpTask dutpTask) {

        dutpTask = this.baseMapper.selectById(dutpTask.getTaskId());
        if (ObjectUtil.isEmpty(dutpTask)){
            return AjaxResult.error("任务不存在");
        }
        // 教材id
        Long bookId = dutpTask.getDataId();
        if (bookId == null) {
            return AjaxResult.error("教材id为空");
        }
        DtbBook book = dtbBookService.getById(bookId);
        int tableNumberType = book.getTableNumberType();
        int imageNumberType = book.getImageNumberType();

        DtbBookChapter dtbBookChapter = new DtbBookChapter();
        dtbBookChapter.setBookId(bookId);
        List<DtbBookChapter> chapterList = chapterMapper.dtbBookChapterMapper(dtbBookChapter);
        if (ObjectUtil.isEmpty(chapterList)) {
            return AjaxResult.error("未查询到章节");
        }
        List<Long> chapterIds = chapterList.stream().map(DtbBookChapter::getChapterId).collect(Collectors.toList());
        // 连接websocket 踢出在线编辑人 执行更新题库
        webscoketSend(3,chapterIds);
        try {
            // 开始更新题注
            log.info("题注更新任务开始执行");


            // TODO 添加10秒的等待时间，用于测试WebSocket连接状态 测试后删除
            try {
                Thread.sleep(10 * 1000); // 等待10秒
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt(); // 重新设置中断状态
                log.error("线程休眠被中断: {}", e.getMessage());
            }

            dutpTask.setStartTime(new Date());
            int i = 1;
            int[] imageNum = {0};
            int[] tableNum = {0};
            for (DtbBookChapter chapter : chapterList) {
                if (imageNumberType == 2){
                    imageNum[0] = 0;
                }
                if (tableNumberType == 2){
                    tableNum[0] = 0;
                }
                // 查询章节内容
                Query query = new Query(Criteria.where("chapterId").is(chapter.getChapterId()));
                DtbBookChapterContent chapterContent = mongoService.findOne(BOOK_CHAPTER_CONTENT, query, DtbBookChapterContent.class);
                String contentJson = chapterContent.getContent();
                if (ObjectUtil.isEmpty(contentJson)) {
                    continue;
                }

                // 解析 JSON
                ObjectMapper objectContent = new ObjectMapper();
                JsonNode rootNode = objectContent.readTree(contentJson);

                // 处理 JSON 数据
                updateImageLayouts(rootNode, tableNumberType, imageNumberType, i, imageNum, tableNum);
                // 输出更新后的 JSON
                String updatedJson = objectContent.writerWithDefaultPrettyPrinter().writeValueAsString(rootNode);
                System.out.println(updatedJson);
                // 保存内容
                Update update = new Update();
                update.set("content", JSONUtil.toJsonStr(updatedJson));
                mongoService.updateOne(BOOK_CHAPTER_CONTENT, query, update, DtbBookChapterContent.class);
                i++;
            }

            // 更新完成
            dutpTask.setTaskState(2);
            dutpTask.setEndTime(new Date());
            this.updateById(dutpTask);

            // 连接websocket 清空system 编辑状态
            webscoketSend(4,chapterIds);

            // 发送消息
            DutpUserMessage dutpUserMessage = new DutpUserMessage();
            dutpUserMessage.setContent("您好，题注更新任务已完成。【教材名称："+book.getBookName()+"；教材编号："+book.getBookNo()+"】。");
            dutpUserMessage.setTitle("任务提醒");
            dutpUserMessage.setFromUserId(2l);
            dutpUserMessage.setToUserId(dutpTask.getUserId());
            dutpUserMessage.setMessageType(1);
            dutpUserMessage.setFromUserType(1);
            dutpUserMessage.setToUserType(1);
            remoteUserMessageService.addMessage(dutpUserMessage);

        } catch (Exception e) {
            e.printStackTrace();
            log.info("题注更新任务执行失败：{}", e);
        }
        log.info("题注更新任务执行结束");

        return AjaxResult.success();
    }

    private static void updateImageLayouts(JsonNode node, int tableNumberType, int imageNumberType, int chapterNum, int[] imageNum, int[] tableNum) {
        if (node.isObject()) {
            ObjectNode objectNode = (ObjectNode) node;

            // 查找 type 为 imageLayout 的节点
            if (objectNode.has("type") && ("imageLayout".equals(objectNode.get("type").asText()) || "image".equals(objectNode.get("type").asText()))) {
                JsonNode attrsNode = objectNode.get("attrs");
                if (attrsNode != null && attrsNode.isObject()) {
                    ObjectNode attrsObject = (ObjectNode) attrsNode;
                    if (attrsNode.path("isShowNo").asInt() == 1){
                        if (imageNumberType == 1) {
                            attrsObject.put("isShowNo", 1);
                            imageNum[0]++;
                            attrsObject.put("number", "图" + imageNum[0]);
                        } else if (imageNumberType == 2) {
                            attrsObject.put("isShowNo", 1);
                            imageNum[0]++;
                            attrsObject.put("number", "图" + chapterNum + "-" + imageNum[0]);
                        } else {
                            attrsObject.put("isShowNo", 0);
                        }
                    }
                }

            }

            // 查找 type 为 table和tablePlus 的节点
            if (objectNode.has("type") && "tablePlus".equals(objectNode.get("type").asText())) {
                JsonNode attrsNode = objectNode.get("attrs");
                if (attrsNode != null && attrsNode.isObject()) {
                    ObjectNode attrsObject = (ObjectNode) attrsNode;
                    if (attrsNode.path("isShowNo").asInt() == 1){
                        if (tableNumberType == 1) {
                            attrsObject.put("isShowNo", 1);
                            tableNum[0]++;
                            attrsObject.put("number", "表" + tableNum[0]);
                        } else if (tableNumberType == 2) {
                            attrsObject.put("isShowNo", 1);
                            tableNum[0]++;
                            attrsObject.put("number", "表" + chapterNum + "-" + tableNum[0]);
                        } else {
                            attrsObject.put("isShowNo", 0);
                        }
                    }

                }
            }
            // 查找 type 为 imageGallery 的节点
            if (objectNode.has("type") && "imageGallery".equals(objectNode.get("type").asText())) {
                JsonNode attrsNode = objectNode.get("attrs");
                if (attrsNode != null && attrsNode.isObject()) {
                    ObjectNode attrsObject = (ObjectNode) attrsNode;
                    if (attrsNode.path("isShowNo").asInt() == 1){
                        if (imageNumberType == 1) {
                            attrsObject.put("isShowNo", 1);
                            imageNum[0]++;
                            attrsObject.put("number", "图" + imageNum[0]);
                        } else if (imageNumberType == 2) {
                            attrsObject.put("isShowNo", 1);
                            imageNum[0]++;
                            attrsObject.put("number", "图" + chapterNum + "-" + imageNum[0]);
                        } else {
                            attrsObject.put("isShowNo", 0);
                        }
                    }
                }
            }
            // 递归遍历子节点
            if (node.has("content")) {
                for (JsonNode child : node.get("content")) {
                    updateImageLayouts(child, tableNumberType, imageNumberType, chapterNum, imageNum, tableNum);
                }
            }
        }
    }

    public void webscoketSend(Integer optCode,List<Long> chapterIds){
        // 创建 WebSocket 连接 踢出所有正在编辑的人
        String token = "system";
        StandardWebSocketClient client = new StandardWebSocketClient();
        ObjectMapper objectMapper = new ObjectMapper();
        // 创建 WebSocket 头部，添加 Token 到 `Sec-WebSocket-Protocol`
        WebSocketHttpHeaders headers = new WebSocketHttpHeaders();
        headers.setSecWebSocketProtocol(Collections.singletonList("system"));

        try {
            client.doHandshake(new TextWebSocketHandler() {
                @Override
                public void afterConnectionEstablished(org.springframework.web.socket.WebSocketSession session) {
                    System.out.println("Connected to server!");

                    // 发送对象消息
                    BookChapterContentEditorMessage message = new BookChapterContentEditorMessage();
                    message.setOptCode(optCode);
                    message.setChapterIds(chapterIds);

                    try {
                        String jsonMessage = objectMapper.writeValueAsString(message);
                        session.sendMessage(new TextMessage(jsonMessage));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                @Override
                protected void handleTextMessage(org.springframework.web.socket.WebSocketSession session, TextMessage message) {
                    System.out.println("Received from server: " + message.getPayload());
                }
            }, headers, URI.create(websocketUrl+"/chapterContentEditor")).get();
        } catch (Exception e) {
            e.printStackTrace();
            log.info("webSocket连接异常：{}", e);
        }
    }
}

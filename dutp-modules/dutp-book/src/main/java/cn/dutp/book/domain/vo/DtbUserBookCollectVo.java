package cn.dutp.book.domain.vo;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * DUTP-DTB_015收藏教材对象 dtb_user_book_collect
 *
 * <AUTHOR>
 * @date 2024-11-21
 */
@Data
public class DtbUserBookCollectVo extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** $column.columnComment */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /** $column.columnComment */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;


    /** 书名 */
    @Excel(name = "书名")
    private String bookName;

    /** 封面 */
    @Excel(name = "封面")
    private String cover;

    /** 封面 */
    private String isbn;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("userId", getUserId())
                .append("bookId", getBookId())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("bookName", getBookName())
                .append("cover", getCover())
                .append("isbn", getIsbn())
                .toString();
    }
}

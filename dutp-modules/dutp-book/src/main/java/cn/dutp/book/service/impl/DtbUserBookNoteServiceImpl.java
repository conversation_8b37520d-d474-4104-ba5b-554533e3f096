package cn.dutp.book.service.impl;

import cn.dutp.book.domain.DtbUserBookNote;
import cn.dutp.book.domain.DtbUserBookNoteAttachment;
import cn.dutp.book.domain.form.ReaderCommonForm;
import cn.dutp.book.domain.vo.DtbUserBookNoteAttachmentVO;
import cn.dutp.book.domain.vo.DtbUserBookNoteVo;
import cn.dutp.book.mapper.DtbBookBookMapper;
import cn.dutp.book.mapper.DtbUserBookNoteAttachmentMapper;
import cn.dutp.book.mapper.DtbUserBookNoteMapper;
import cn.dutp.book.service.IDtbUserBookNoteService;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBook;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.UncheckedIOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static cn.dutp.common.core.utils.JsonToDocxConverter.insertImage;

/**
 * DUTP-DTB_022笔记/标注Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@Service
public class DtbUserBookNoteServiceImpl extends ServiceImpl<DtbUserBookNoteMapper, DtbUserBookNote> implements IDtbUserBookNoteService {
    @Autowired
    private DtbUserBookNoteMapper dtbUserBookNoteMapper;
    @Autowired
    private DtbUserBookNoteAttachmentMapper attachmentMapper;
    @Autowired
    private DtbBookBookMapper bookMapper;

    /**
     * 查询DUTP-DTB_022笔记/标注
     *
     * @param noteId DUTP-DTB_022笔记/标注主键
     * @return DUTP-DTB_022笔记/标注
     */
    @Override
    public DtbUserBookNote selectDtbUserBookNoteByNoteId(Long noteId) {
        return this.getById(noteId);
    }

    /**
     * 查询DUTP-DTB_022笔记/标注列表
     *
     * @param dtbUserBookNote DUTP-DTB_022笔记/标注
     * @return DUTP-DTB_022笔记/标注
     */
    @Override
    public List<DtbUserBookNote> selectDtbUserBookNoteList(DtbUserBookNote dtbUserBookNote) {
        LambdaQueryWrapper<DtbUserBookNote> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotEmpty(dtbUserBookNote.getUserId())) {
            lambdaQueryWrapper.eq(DtbUserBookNote::getUserId
                    , dtbUserBookNote.getUserId());
        }
        if (ObjectUtil.isNotEmpty(dtbUserBookNote.getBookId())) {
            lambdaQueryWrapper.eq(DtbUserBookNote::getBookId
                    , dtbUserBookNote.getBookId());
        }
        if (ObjectUtil.isNotEmpty(dtbUserBookNote.getChapterId())) {
            lambdaQueryWrapper.eq(DtbUserBookNote::getChapterId
                    , dtbUserBookNote.getChapterId());
        }
        if (ObjectUtil.isNotEmpty(dtbUserBookNote.getPageNumber())) {
            lambdaQueryWrapper.eq(DtbUserBookNote::getPageNumber
                    , dtbUserBookNote.getPageNumber());
        }
        if (ObjectUtil.isNotEmpty(dtbUserBookNote.getNodeContent())) {
            lambdaQueryWrapper.eq(DtbUserBookNote::getNodeContent
                    , dtbUserBookNote.getNodeContent());
        }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增DUTP-DTB_022笔记/标注
     *
     * @param dtbUserBookNote DUTP-DTB_022笔记/标注
     * @return 结果
     */
    @Override
    @Transactional
    public AjaxResult insertDtbUserBookNote(DtbUserBookNote dtbUserBookNote) {
        this.save(dtbUserBookNote);
        List<DtbUserBookNoteAttachment> attachments = dtbUserBookNote.getAttachments();
        if (ObjectUtil.isNotEmpty(attachments)) {
            attachments.forEach(item -> {
                item.setNoteId(dtbUserBookNote.getNoteId());
                attachmentMapper.insert(item);
            });
        }
        return AjaxResult.success();
    }

    /**
     * 修改DUTP-DTB_022笔记/标注
     *
     * @param dtbUserBookNote DUTP-DTB_022笔记/标注
     * @return 结果
     */
    @Override
    @Transactional
    public AjaxResult updateDtbUserBookNote(DtbUserBookNote dtbUserBookNote) {
        LambdaQueryWrapper<DtbUserBookNoteAttachment> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DtbUserBookNoteAttachment::getNoteId, dtbUserBookNote.getNoteId());
        attachmentMapper.delete(lambdaQueryWrapper);
        List<DtbUserBookNoteAttachment> attachments = dtbUserBookNote.getAttachments();
        if (ObjectUtil.isNotEmpty(attachments)) {
            attachments.forEach(item -> {
                item.setNoteId(dtbUserBookNote.getNoteId());
                attachmentMapper.insert(item);
            });
        }
        this.updateById(dtbUserBookNote);
        return AjaxResult.success();
    }

    /**
     * 批量删除DUTP-DTB_022笔记/标注
     *
     * @param noteIds 需要删除的DUTP-DTB_022笔记/标注主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbUserBookNoteByNoteIds(List<Long> noteIds) {
        return this.removeByIds(noteIds);
    }

    @Override
    public AjaxResult selectReaderUserBookNoteList(ReaderCommonForm readerForm) {
        if (readerForm.getNoteType().intValue() != 1) {
            return AjaxResult.error("功能建设中");
        } else {
            List<DtbUserBookNoteVo> noteVos = dtbUserBookNoteMapper.selectReaderUserBookNoteList(readerForm);
            noteVos.forEach(item -> {
                List<DtbUserBookNoteAttachmentVO> attachments = attachmentMapper.selectNodeAttachmentList(item.getNoteId());
                item.setAttachments(attachments);
            });
            return AjaxResult.success(noteVos);
        }
    }

    @Override
    public void exportBookNote(HttpServletResponse response, DtbUserBookNote dtbUserBookNote) {
        if (dtbUserBookNote.getBookId() == null) {
            throw new ServiceException("bookId不能为空");
        }
        DtbBook book = bookMapper.selectOne(new LambdaQueryWrapper<DtbBook>()
                .select(DtbBook::getBookName)
                .eq(DtbBook::getBookId, dtbUserBookNote.getBookId()));
        List<DtbUserBookNote> dtbUserBookNoteList = dtbUserBookNoteMapper.exportBookNote(dtbUserBookNote);

        XWPFDocument doc = new XWPFDocument();
        // 创建临时工作目录
        Path tempDir = null;
        try {
            tempDir = Files.createTempDirectory("zip-gen-");
        } catch (IOException e) {
            log.info("创建目录失败：", e);
        }
        Path audioDir = tempDir.resolve("笔记音频");
        try {
            Files.createDirectories(audioDir);
        } catch (IOException e) {
            log.info("创建目录失败：", e);
        }
        Map<Long, List<DtbUserBookNote>> notesMap = dtbUserBookNoteList.stream().collect(Collectors.groupingBy(DtbUserBookNote::getChapterId));


        notesMap.forEach((k, v) -> {
            int idx = 0;
            for (DtbUserBookNote userBookNote : v) {
                idx++;
                if (idx == 1) {
                    XWPFParagraph paragraph = doc.createParagraph();
                    XWPFRun run = paragraph.createRun();
                    run.setText(userBookNote.getChapterName());
                    run.setBold(true);
                }
                XWPFParagraph paragraph = doc.createParagraph();
                XWPFRun run = paragraph.createRun();
                run.setText(idx + ". " + userBookNote.getBookContent());
                XWPFParagraph paragraph1 = doc.createParagraph();
                XWPFRun run1 = paragraph1.createRun();
                run1.setText("          笔记内容：" + userBookNote.getNodeContent());

                List<DtbUserBookNoteAttachmentVO> attachments = attachmentMapper.selectNodeAttachmentList(userBookNote.getNoteId());

                if (ObjectUtil.isEmpty(attachments)) {
                    XWPFParagraph paragraph8 = doc.createParagraph();
                    XWPFRun run2 = paragraph8.createRun();
                    run2.setText("          时间：" + DateUtil.format(userBookNote.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                    continue;
                }
                // 笔记图片
                List<DtbUserBookNoteAttachmentVO> imgList = attachments.stream().filter(o -> o.getAttachmentType() == 1).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(imgList)) {
                    XWPFParagraph paragraph2 = doc.createParagraph();
                    XWPFRun run2 = paragraph2.createRun();
                    run2.setText("          笔记图片：");
                    for (DtbUserBookNoteAttachmentVO attachmentVO : imgList) {
                        XWPFParagraph paragraph3 = doc.createParagraph();
                        insertImage(paragraph3, attachmentVO.getAttachmentUrl(), attachmentVO.getAttachmentName(), 20, 20);
                    }
                }
                XWPFParagraph paragraph8 = doc.createParagraph();
                XWPFRun run2 = paragraph8.createRun();
                run2.setText("          时间：" + DateUtil.format(userBookNote.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                // 笔记音频
                List<DtbUserBookNoteAttachmentVO> auditList = attachments.stream().filter(o -> o.getAttachmentType() == 2).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(auditList)) {
                    for (DtbUserBookNoteAttachmentVO attachmentVO : auditList) {
                        try {
                            downloadFile(attachmentVO.getAttachmentUrl(), audioDir);
                        } catch (IOException e) {
                            log.info("下载音频失败：", e);
                        }
                    }

                }
            }
        });

        String username = SecurityUtils.getLoginUser().getUsername();
        if (username.length() > 4) {
            username = username.substring(username.length() - 4);
        }

        response.setContentType("application/octet-stream;charset=utf-8");
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(username + "_" + book.getBookName() + ".zip", StandardCharsets.UTF_8.name()));
        } catch (UnsupportedEncodingException e) {
            log.info("编码失败：", e);
        }

        // 写入ZIP文件
        try (ZipOutputStream zos = new ZipOutputStream(response.getOutputStream())) {
            // 添加音频文件夹
            addDirectoryToZip(zos, audioDir, "笔记音频/");
            // 添加Word文档
            zos.putNextEntry(new ZipEntry("笔记.docx"));
            doc.write(zos);
        } catch (IOException e) {
            log.info("导出笔记失败，", e);
            throw new ServiceException("导出失败");
        } finally {
            try {
                doc.close();
            } catch (IOException e) {
                log.info("导出笔记失败，", e);
            }
        }

    }

    /**
     * 添加文件到压缩包
     *
     * @param zos
     * @param dir
     * @param basePath
     * @throws IOException
     */
    private void addDirectoryToZip(ZipOutputStream zos, Path dir, String basePath) {
        try (Stream<Path> pathStream = Files.walk(dir)) {
            pathStream.forEach(path -> {
                if (!Files.isDirectory(path)) {
                    String entryName = basePath + dir.relativize(path);
                    try {
                        zos.putNextEntry(new ZipEntry(entryName));
                        Files.copy(path, zos);
                        zos.closeEntry();
                    } catch (IOException e) {
                        throw new UncheckedIOException(e);
                    }
                }
                log.info("添加文件到压缩包：{}", path);
            });
        } catch (Exception e) {
            log.error("添加文件到压缩包：", e);
        }
    }

    /**
     * 通过url下载文件
     *
     * @param url
     * @param targetDir
     * @throws IOException
     */
    private void downloadFile(String url, Path targetDir) throws IOException {
        String fileName = url.substring(url.lastIndexOf('/') + 1);
        Path outputPath = targetDir.resolve(fileName);

        try (InputStream in = HttpRequest.get(url).header("Referer", "https://ebook.dutp.cn").execute().bodyStream()) {
            Files.copy(in, outputPath, StandardCopyOption.REPLACE_EXISTING);
        } catch (Exception e) {
            log.error("下载文件失败：", e);
        }
    }
}

package cn.dutp.book.domain.vo;

import cn.dutp.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 数字教材作者编辑团队权限
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@Data
public class DtbBookPermissionsVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 教材ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 1书稿联系人 2主编 3副主编 4参编 5策划编辑 6责任编辑
     */
    private List<Integer> permissions;

    /**
     * 是否是编辑团队
     */
    private Boolean isEditor;

    /**
     * 是否是作者团队
     */
    private Boolean isAuthor;
}

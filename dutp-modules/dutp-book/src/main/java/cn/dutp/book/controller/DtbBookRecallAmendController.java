package cn.dutp.book.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.book.domain.DtbBookRecallAmend;
import cn.dutp.book.service.IDtbBookRecallAmendService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * DUTP-DTB_011书籍召回修正记录Controller
 *
 * <AUTHOR>
 * @date 2024-12-10
 */
@RestController
@RequestMapping("/amend")
public class DtbBookRecallAmendController extends BaseController {
    @Autowired
    private IDtbBookRecallAmendService dtbBookRecallAmendService;

    /**
     * 查询DUTP-DTB_011书籍召回记录列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbBookRecallAmend dtbBookRecallAmend) {
        startPage();
        List<DtbBookRecallAmend> list = dtbBookRecallAmendService.selectDtbBookRecallAmendList(dtbBookRecallAmend);
        return getDataTable(list);
    }

    /**
     * 查询DUTP-DTB_011书籍修正记录列表
     */
    @GetMapping("/selectDtbBookAmendList")
    public TableDataInfo selectDtbBookAmendList(DtbBookRecallAmend dtbBookRecallAmend) {
        startPage();
        List<DtbBookRecallAmend> list = dtbBookRecallAmendService.selectDtbBookAmendList(dtbBookRecallAmend);
        return getDataTable(list);
    }

    /**
     * 导出DUTP-DTB_011书籍召回修正记录列表
     */
    @RequiresPermissions("system:amend:export")
    @Log(title = "DUTP-DTB_011书籍召回修正记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbBookRecallAmend dtbBookRecallAmend) {
        List<DtbBookRecallAmend> list = dtbBookRecallAmendService.selectDtbBookRecallAmendList(dtbBookRecallAmend);
        ExcelUtil<DtbBookRecallAmend> util = new ExcelUtil<DtbBookRecallAmend>(DtbBookRecallAmend.class);
        util.exportExcel(response, list, "DUTP-DTB_011书籍召回修正记录数据");
    }

    /**
     * 获取DUTP-DTB_011书籍召回修正记录详细信息
     */
    @RequiresPermissions("system:amend:query")
    @GetMapping(value = "/{recallId}")
    public AjaxResult getInfo(@PathVariable("recallId") Long recallId) {
        return success(dtbBookRecallAmendService.selectDtbBookRecallAmendByRecallId(recallId));
    }

    /**
     * 新增DUTP-DTB_011书籍召回修正记录
     */
    @RequiresPermissions("system:amend:add")
    @Log(title = "DUTP-DTB_011书籍召回修正记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookRecallAmend dtbBookRecallAmend) {
        return toAjax(dtbBookRecallAmendService.insertDtbBookRecallAmend(dtbBookRecallAmend));
    }

    /**
     * 修改DUTP-DTB_011书籍召回修正记录
     */
    @RequiresPermissions("system:amend:edit")
    @Log(title = "DUTP-DTB_011书籍召回修正记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookRecallAmend dtbBookRecallAmend) {
        return toAjax(dtbBookRecallAmendService.updateDtbBookRecallAmend(dtbBookRecallAmend));
    }

    /**
     * 删除DUTP-DTB_011书籍召回修正记录
     */
    @RequiresPermissions("system:amend:remove")
    @Log(title = "DUTP-DTB_011书籍召回修正记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{recallIds}")
    public AjaxResult remove(@PathVariable Long[] recallIds) {
        return toAjax(dtbBookRecallAmendService.deleteDtbBookRecallAmendByRecallIds(Arrays.asList(recallIds)));
    }
}

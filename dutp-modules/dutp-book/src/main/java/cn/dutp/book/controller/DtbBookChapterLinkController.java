package cn.dutp.book.controller;

import cn.dutp.book.domain.DtbBookChapterLink;
import cn.dutp.book.service.IDtbBookChapterLinkService;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 外部链接扫描Controller
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@RestController
@RequestMapping("/bookChapterOutLink")
public class DtbBookChapterLinkController extends BaseController {
    @Autowired
    private IDtbBookChapterLinkService dtbBookChapterLinkService;

    /**
     * 查询外部链接扫描列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbBookChapterLink dtbBookChapterLink) {
        startPage();
        List<DtbBookChapterLink> list = dtbBookChapterLinkService.selectDtbBookChapterLinkList(dtbBookChapterLink);
        return getDataTable(list);
    }

    /**
     * 获取外部链接扫描详细信息
     */
    @RequiresPermissions("book:bookChapterOutLink:query")
    @GetMapping(value = "/{linkId}")
    public AjaxResult getInfo(@PathVariable("linkId") Long linkId) {
        return success(dtbBookChapterLinkService.selectDtbBookChapterLinkByLinkId(linkId));
    }

    /**
     * 修改外部链接扫描
     */
    @RequiresPermissions("book:bookChapterOutLink:edit")
    @Log(title = "修改外部链接扫描", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookChapterLink dtbBookChapterLink) {
        return toAjax(dtbBookChapterLinkService.updateDtbBookChapterLink(dtbBookChapterLink));
    }

}

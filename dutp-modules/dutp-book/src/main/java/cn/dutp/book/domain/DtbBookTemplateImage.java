package cn.dutp.book.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 教材模板图片对象 dtb_book_template_image
 *
 * <AUTHOR>
 * @date 2025-04-25
 */
@Data
@TableName("dtb_book_template_image")
public class DtbBookTemplateImage implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 图片id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long imageId;

    /**
     * url
     */
    private String url;

    /**
     * 模板Id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long templateId;

    /**
     * 用户Id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 1模板 2用户
     */
    private Integer type;

    /**
     * 1背景图片 2行内图片
     */
    private Integer belongTo;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 章节Id
     */
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    /**
     * 素材来源  1模板内 2模板库
     */
    @TableField(exist = false)
    private Integer fromTo;

    /**
     * 主题色
     */
    @TableField(exist = false)
    private Integer themeColor;
}

package cn.dutp.book.service.impl;

import cn.dutp.book.mapper.DtbBookCodeExportItemMapper;
import cn.dutp.book.service.DtbBookCodeExportItemService;
import cn.dutp.domain.DtbBookCodeExportItem;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【dtb_book_code_export_item(导出购书码明细)】的数据库操作Service实现
 * @createDate 2025-02-21 10:35:37
 */
@Service
public class DtbBookCodeExportItemServiceImpl extends ServiceImpl<DtbBookCodeExportItemMapper, DtbBookCodeExportItem>
        implements DtbBookCodeExportItemService {

    @Override
    public boolean addExportItem(DtbBookCodeExportItem exportItem) {
        return this.save(exportItem);
    }
}





package cn.dutp.book.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.book.domain.DtbUserBookNote;
import cn.dutp.book.service.IDtbUserBookNoteService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * DUTP-DTB_022笔记/标注Controller
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@RestController
@RequestMapping("/book/note")
public class DtbUserBookNoteController extends BaseController
{
    @Autowired
    private IDtbUserBookNoteService dtbUserBookNoteService;

    /**
     * 查询DUTP-DTB_022笔记/标注列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbUserBookNote dtbUserBookNote)
    {
        startPage();
        List<DtbUserBookNote> list = dtbUserBookNoteService.selectDtbUserBookNoteList(dtbUserBookNote);
        return getDataTable(list);
    }

    /**
     * 导出DUTP-DTB_022笔记/标注列表
     */
    @RequiresPermissions("book:note:export")
    @Log(title = "导出DUTP-DTB_022笔记/标注", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbUserBookNote dtbUserBookNote)
    {
        List<DtbUserBookNote> list = dtbUserBookNoteService.selectDtbUserBookNoteList(dtbUserBookNote);
        ExcelUtil<DtbUserBookNote> util = new ExcelUtil<DtbUserBookNote>(DtbUserBookNote.class);
        util.exportExcel(response, list, "DUTP-DTB_022笔记/标注数据");
    }

    /**
     * 获取DUTP-DTB_022笔记/标注详细信息
     */
    @RequiresPermissions("book:note:query")
    @GetMapping(value = "/{noteId}")
    public AjaxResult getInfo(@PathVariable("noteId") Long noteId)
    {
        return success(dtbUserBookNoteService.selectDtbUserBookNoteByNoteId(noteId));
    }

    /**
     * 新增DUTP-DTB_022笔记/标注
     */
    @RequiresPermissions("book:note:add")
    @Log(title = "新增DUTP-DTB_022笔记/标注", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbUserBookNote dtbUserBookNote)
    {
        return dtbUserBookNoteService.insertDtbUserBookNote(dtbUserBookNote);
    }

    /**
     * 修改DUTP-DTB_022笔记/标注
     */
    @RequiresPermissions("book:note:edit")
    @Log(title = "修改DUTP-DTB_022笔记/标注", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbUserBookNote dtbUserBookNote)
    {
        return dtbUserBookNoteService.updateDtbUserBookNote(dtbUserBookNote);
    }

    /**
     * 删除DUTP-DTB_022笔记/标注
     */
    @RequiresPermissions("book:note:remove")
    @Log(title = "删除DUTP-DTB_022笔记/标注", businessType = BusinessType.DELETE)
    @DeleteMapping("/{noteIds}")
    public AjaxResult remove(@PathVariable Long[] noteIds)
    {
        return toAjax(dtbUserBookNoteService.deleteDtbUserBookNoteByNoteIds(Arrays.asList(noteIds)));
    }
}

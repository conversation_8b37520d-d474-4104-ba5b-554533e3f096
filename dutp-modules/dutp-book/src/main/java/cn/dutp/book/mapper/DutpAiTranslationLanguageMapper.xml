<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DutpAiTranslationLanguageMapper">
    
    <resultMap type="cn.dutp.book.domain.DutpAiTranslationLanguage" id="DutpAiTranslationLanguageResult">
        <result property="languageId"    column="language_id"    />
        <result property="parameter"    column="parameter"    />
        <result property="language"    column="language"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="promptId"    column="prompt_id"    />
    </resultMap>

    <sql id="selectDutpAiTranslationLanguageVo">
        select language_id, parameter, language, del_flag, create_by, create_time, update_by, update_time, prompt_id from dutp_ai_translation_language
    </sql>

    <select id="selectDutpAiTranslationLanguageList" parameterType="cn.dutp.book.domain.DutpAiTranslationLanguage" resultMap="DutpAiTranslationLanguageResult">
        <include refid="selectDutpAiTranslationLanguageVo"/>
        <where>  
            <if test="parameter != null  and parameter != ''"> and parameter = #{parameter}</if>
            <if test="language != null  and language != ''"> and language = #{language}</if>
            <if test="promptId != null "> and prompt_id = #{promptId}</if>
        </where>
    </select>
    
    <select id="selectDutpAiTranslationLanguageByLanguageId" parameterType="Long" resultMap="DutpAiTranslationLanguageResult">
        <include refid="selectDutpAiTranslationLanguageVo"/>
        where language_id = #{languageId}
    </select>

    <insert id="insertDutpAiTranslationLanguage" parameterType="cn.dutp.book.domain.DutpAiTranslationLanguage" useGeneratedKeys="true" keyProperty="languageId">
        insert into dutp_ai_translation_language
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parameter != null">parameter,</if>
            <if test="language != null">language,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="promptId != null">prompt_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parameter != null">#{parameter},</if>
            <if test="language != null">#{language},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="promptId != null">#{promptId},</if>
         </trim>
    </insert>

    <update id="updateDutpAiTranslationLanguage" parameterType="cn.dutp.book.domain.DutpAiTranslationLanguage">
        update dutp_ai_translation_language
        <trim prefix="SET" suffixOverrides=",">
            <if test="parameter != null">parameter = #{parameter},</if>
            <if test="language != null">language = #{language},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="promptId != null">prompt_id = #{promptId},</if>
        </trim>
        where language_id = #{languageId}
    </update>

    <delete id="deleteDutpAiTranslationLanguageByLanguageId" parameterType="Long">
        delete from dutp_ai_translation_language where language_id = #{languageId}
    </delete>

    <delete id="deleteDutpAiTranslationLanguageByLanguageIds" parameterType="String">
        delete from dutp_ai_translation_language where language_id in 
        <foreach item="languageId" collection="array" open="(" separator="," close=")">
            #{languageId}
        </foreach>
    </delete>
</mapper>
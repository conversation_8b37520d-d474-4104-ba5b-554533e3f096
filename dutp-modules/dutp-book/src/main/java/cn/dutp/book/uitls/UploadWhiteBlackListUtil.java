package cn.dutp.book.uitls;

import com.alibaba.nacos.shaded.com.google.gson.JsonObject;
import okhttp3.*;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * 文字纠错 黑白名单上传
 */
public class UploadWhiteBlackListUtil {

    public static final MediaType JSON = MediaType.parse("application/json;charset=utf-8");

    public static final String REG_BODY = "{\n" +
            "\t\"common\":{\n" +
            "\t    \"app_id\":\"%s\",\n" +
            "\t    \"uid\":\"%s\"\n" +
            "\t},\n" +
            "\t\"business\":{\n" +
            "\t   \"res_id\":\"%s\"\n" +
            "\t},\n" +
            "\t\"data\":\"%s\"\n" +
            "}";
    public static void reg(String appId,String uid,String resId,String regUrl,
            String whiteList,String blackList) throws Exception {
        String body = String.format(REG_BODY,
                appId, uid, resId, changeCode(whiteList,blackList));
        OkHttpClient client = new OkHttpClient.Builder().build();
        RequestBody requestBody = RequestBody.create(JSON, body);
        Request request = new Request.Builder().url(regUrl).//
                //addHeader("Content-Type", "application/json").//
                        post(requestBody).//
                        build();
        Response resp = client.newCall(request).execute();
        System.out.println(resp.code() + ":" + resp.body().string());
        // 结果示例{"code":0,"message":"success","sid":"ctm000cd05a@hu1932e54a66703a7902"}
    }

    //将需要上传的数组转码
    private static String changeCode(String whiteList,String blackList){
        JsonObject lists = new JsonObject();
        lists.addProperty("white_list",whiteList);
        lists.addProperty("black_list",blackList);

        //对lists进行base加密
        return Base64.getEncoder().encodeToString(lists.toString().getBytes(StandardCharsets.UTF_8));

    }
}

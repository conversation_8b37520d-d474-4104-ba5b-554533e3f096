package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.List;

/**
 * 心理测试量对象 mooc_psychology_health_scale
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Data
@TableName("mooc_psychology_health_scale")
public class MoocPsychologyHealthScale extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long scaleId;

    /**
     * 量表名称
     */
    @Excel(name = "量表名称")
    private String scaleName;

    /**
     * 量表题干
     */
    @Excel(name = "量表题干")
    private String scanQuestion;

    /**
     * 测评方法
     */
    @Excel(name = "测评方法")
    private String evaluationMethod;

    /**
     * 评价参考
     */
    @Excel(name = "评价参考")
    private String evaluateReference;

    /**
     * 量表类型：1顺序作答2跳转作答
     */
    @Excel(name = "量表类型：1顺序作答2跳转作答")
    private Integer scaleType;

    /**
     * 题目顺序：1单维度量表2多维度量表
     */
    @Excel(name = "题目顺序：1单维度量表2多维度量表")
    private Integer questionSort;

    /** 用户ID */
    @Excel(name = "用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 显示类型：1显示维度名称顺序显示2隐藏维度名称乱序显示(多维度用)
     */
    @Excel(name = "显示类型：1显示维度名称顺序显示2隐藏维度名称乱序显示(多维度用)")
    private Integer showSortType;

    /**
     * 状态：1启用2禁用
     */
    @Excel(name = "状态：1启用2禁用")
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 量表维度
     */
    @TableField(exist = false)
    private List<MoocPsychologyHealthScaleFacet> moocPsychologyHealthScaleFacet;

    /**
     * 量表题干
     */
    @TableField(exist = false)
    private List<MoocPsychologyHealthScaleQuestion> moocPsychologyHealthScaleQuestion;

    /**
     * 选项
     */
    @TableField(exist = false)
    private List<MoocPsychologyHealthScaleQuestionOption> psychologyHealthScaleQuestionOption;

    /**
     * 引用教材名称
     */
    @TableField(exist = false)
    private String bookName;

    /**
     * 用户名称
     */
    @TableField(exist = false)
    private String userName;

    /**
     * 用户昵称
     */
    @TableField(exist = false)
    private String nickName;

    /**
     * 填报量表个数
     */
    @TableField(exist = false)
    private Integer testCount;

    /**
     * 试题数量
     */
    @TableField(exist = false)
    private Integer questionCount;

    /**
     * 分数
     */
    @TableField(exist = false)
    private Integer score;

    /**
     * 姓名
     */
    @TableField(exist = false)
    private String realName;

    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    /**
     * 是否可删除 0不可删除 1可删除
     */
    @TableField(exist = false)
    private Integer isDelFlag;

    /**
     * domId
     */
    @TableField(exist = false)
    private String domId;

    private Long showSumScore;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("scaleId", getScaleId())
                .append("scaleName", getScaleName())
                .append("scanQuestion", getScanQuestion())
                .append("evaluationMethod", getEvaluationMethod())
                .append("evaluateReference", getEvaluateReference())
                .append("scaleType", getScaleType())
                .append("questionSort", getQuestionSort())
                .append("showSortType", getShowSortType())
                .append("status", getStatus())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 翻译语种对象 dutp_ai_translation_language
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@TableName("dutp_ai_translation_language")
public class DutpAiTranslationLanguage extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long languageId;

    /**
     * 参数
     */
    @Excel(name = "参数")
    private String parameter;

    /**
     * 语种
     */
    @Excel(name = "语种")
    private String language;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 指令id
     */
    @Excel(name = "指令id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long promptId;

    /**
     * ai模型类型 1讯飞 2百度
     */
    @TableField(exist = false)
    private Integer modelType;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("languageId", getLanguageId())
                .append("parameter", getParameter())
                .append("language", getLanguage())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("promptId", getPromptId())
                .toString();
    }
}

package cn.dutp.book.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.List;

/**
 * 教材章节
 *
 * <AUTHOR>
 * @date 2024-11-30
 */
@Data
public class DtbBookChapterCatalogVO {
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long catalogId;
    private String title;
    private String domId;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;
    private List<DtbBookChapterCatalogVO> children;
    private Integer pageNumber;
}

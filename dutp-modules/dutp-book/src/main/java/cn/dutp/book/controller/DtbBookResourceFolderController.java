package cn.dutp.book.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.book.domain.vo.ResourceVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.book.domain.DtbBookResourceFolder;
import cn.dutp.book.service.IDtbBookResourceFolderService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 教材资源文件夹Controller
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
@RestController
@RequestMapping("/bookResourcefolder")
public class DtbBookResourceFolderController extends BaseController
{
    @Autowired
    private IDtbBookResourceFolderService dtbBookResourceFolderService;

    /**
     * 查询教材资源文件夹列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbBookResourceFolder dtbBookResourceFolder)
    {
        startPage();
        List<DtbBookResourceFolder> list = dtbBookResourceFolderService.selectDtbBookResourceFolderList(dtbBookResourceFolder);
        return getDataTable(list);
    }


    @GetMapping("/listBookResourcefolderResource")
    public TableDataInfo listBookResourcefolderResource(ResourceVO dtbBookResourceFolder)
    {
        startPage();
        List<ResourceVO> list = dtbBookResourceFolderService.listBookResourcefolderResource(dtbBookResourceFolder);
        return getDataTable(list);
    }



    @GetMapping("/listAll")
    public TableDataInfo listAll(DtbBookResourceFolder dtbBookResourceFolder)
    {
        List<DtbBookResourceFolder> list = dtbBookResourceFolderService.selectDtbBookResourceFolderList(dtbBookResourceFolder);
        return getDataTable(list);
    }

    /**
     * 导出教材资源文件夹列表
     */
    @RequiresPermissions("book:bookResourcefolder:export")
    @Log(title = "教材资源文件夹", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbBookResourceFolder dtbBookResourceFolder)
    {
        List<DtbBookResourceFolder> list = dtbBookResourceFolderService.selectDtbBookResourceFolderList(dtbBookResourceFolder);
        ExcelUtil<DtbBookResourceFolder> util = new ExcelUtil<DtbBookResourceFolder>(DtbBookResourceFolder.class);
        util.exportExcel(response, list, "教材资源文件夹数据");
    }

    /**
     * 获取教材资源文件夹详细信息
     */
    @RequiresPermissions("book:bookResourcefolder:query")
    @GetMapping(value = "/{folderId}")
    public AjaxResult getInfo(@PathVariable("folderId") Long folderId)
    {
        return success(dtbBookResourceFolderService.selectDtbBookResourceFolderByFolderId(folderId));
    }

    /**
     * 新增教材资源文件夹
     */
    @RequiresPermissions("book:bookResourcefolder:add")
    @Log(title = "新增教材资源文件夹", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookResourceFolder dtbBookResourceFolder)
    {
        return toAjax(dtbBookResourceFolderService.insertDtbBookResourceFolder(dtbBookResourceFolder));
    }

    /**
     * 修改教材资源文件夹
     */
    @RequiresPermissions("book:bookResourcefolder:edit")
    @Log(title = "修改教材资源文件夹", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookResourceFolder dtbBookResourceFolder)
    {
        return toAjax(dtbBookResourceFolderService.updateDtbBookResourceFolder(dtbBookResourceFolder));
    }

    /**
     * 删除教材资源文件夹
     */
    @RequiresPermissions("book:bookResourcefolder:remove")
    @Log(title = "删除教材资源文件夹", businessType = BusinessType.DELETE)
    @DeleteMapping("/{folderIds}")
    public AjaxResult remove(@PathVariable Long[] folderIds)
    {
        return toAjax(dtbBookResourceFolderService.deleteDtbBookResourceFolderByFolderIds(Arrays.asList(folderIds)));
    }
}

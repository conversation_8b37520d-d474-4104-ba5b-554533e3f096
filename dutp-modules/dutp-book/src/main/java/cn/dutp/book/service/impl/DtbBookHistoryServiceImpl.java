package cn.dutp.book.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.book.mapper.DtbBookHistoryMapper;
import cn.dutp.book.domain.DtbBookHistory;
import cn.dutp.book.service.IDtbBookHistoryService;

/**
 * 数字教材保存历史Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-30
 */
@Service
public class DtbBookHistoryServiceImpl extends ServiceImpl<DtbBookHistoryMapper, DtbBookHistory> implements IDtbBookHistoryService
{
    @Autowired
    private DtbBookHistoryMapper dtbBookHistoryMapper;

    /**
     * 查询数字教材保存历史
     *
     * @param historyId 数字教材保存历史主键
     * @return 数字教材保存历史
     */
    @Override
    public DtbBookHistory selectDtbBookHistoryByHistoryId(Long historyId)
    {
        return this.getById(historyId);
    }

    /**
     * 查询数字教材保存历史列表
     *
     * @param dtbBookHistory 数字教材保存历史
     * @return 数字教材保存历史
     */
    @Override
    public List<DtbBookHistory> selectDtbBookHistoryList(DtbBookHistory dtbBookHistory)
    {
        LambdaQueryWrapper<DtbBookHistory> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbBookHistory.getBookId())) {
                lambdaQueryWrapper.eq(DtbBookHistory::getBookId
                ,dtbBookHistory.getBookId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookHistory.getChapterId())) {
                lambdaQueryWrapper.eq(DtbBookHistory::getChapterId
                ,dtbBookHistory.getChapterId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookHistory.getChapterObjectId())) {
                lambdaQueryWrapper.eq(DtbBookHistory::getChapterObjectId
                ,dtbBookHistory.getChapterObjectId());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增数字教材保存历史
     *
     * @param dtbBookHistory 数字教材保存历史
     * @return 结果
     */
    @Override
    public boolean insertDtbBookHistory(DtbBookHistory dtbBookHistory)
    {
        return this.save(dtbBookHistory);
    }

    /**
     * 修改数字教材保存历史
     *
     * @param dtbBookHistory 数字教材保存历史
     * @return 结果
     */
    @Override
    public boolean updateDtbBookHistory(DtbBookHistory dtbBookHistory)
    {
        return this.updateById(dtbBookHistory);
    }

    /**
     * 批量删除数字教材保存历史
     *
     * @param historyIds 需要删除的数字教材保存历史主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookHistoryByHistoryIds(List<Long> historyIds)
    {
        return this.removeByIds(historyIds);
    }

}

package cn.dutp.book.mapper;

import cn.dutp.book.domain.DtbBookChapterLink;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 外部链接扫描Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Repository
public interface DtbBookChapterLinkMapper extends BaseMapper<DtbBookChapterLink> {

    List<DtbBookChapterLink> selectDtbBookChapterLinkList(DtbBookChapterLink dtbBookChapterLink);
}

package cn.dutp.book.service;

import java.util.List;
import cn.dutp.book.domain.DtbBookType;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 中图分类Service接口
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
public interface IDtbBookTypeService extends IService<DtbBookType>
{
    /**
     * 查询中图分类
     *
     * @param typeId 中图分类主键
     * @return 中图分类
     */
    public DtbBookType selectDtbBookTypeByTypeId(Long typeId);

    /**
     * 查询中图分类列表
     *
     * @param dtbBookType 中图分类
     * @return 中图分类集合
     */
    public List<DtbBookType> selectDtbBookTypeList(DtbBookType dtbBookType);

    /**
     * 新增中图分类
     *
     * @param dtbBookType 中图分类
     * @return 结果
     */
    public boolean insertDtbBookType(DtbBookType dtbBookType);

    /**
     * 修改中图分类
     *
     * @param dtbBookType 中图分类
     * @return 结果
     */
    public boolean updateDtbBookType(DtbBookType dtbBookType);

    /**
     * 批量删除中图分类
     *
     * @param typeId 需要删除的中图分类主键集合
     * @return 结果
     */
    public boolean deleteDtbBookTypeByTypeIds(Long typeId);

}

package cn.dutp.book.service;

import java.util.List;

import cn.dutp.book.domain.DtbBookFile;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 教材资源Service接口
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface IDtbBookFileService extends IService<DtbBookFile> {
    /**
     * 查询教材资源
     *
     * @param bookFileId 教材资源主键
     * @return 教材资源
     */
    public DtbBookFile selectDtbBookFileByBookFileId(Long bookFileId);

    /**
     * 查询教材资源列表
     *
     * @param dtbBookFile 教材资源
     * @return 教材资源集合
     */
    public List<DtbBookFile> selectDtbBookFileList(DtbBookFile dtbBookFile);

    /**
     * 新增教材资源
     *
     * @param dtbBookFile 教材资源
     * @return 结果
     */
    public boolean insertDtbBookFile(DtbBookFile dtbBookFile);

    /**
     * 修改教材资源
     *
     * @param dtbBookFile 教材资源
     * @return 结果
     */
    public boolean updateDtbBookFile(DtbBookFile dtbBookFile);

    /**
     * 批量删除教材资源
     *
     * @param bookFileIds 需要删除的教材资源主键集合
     * @return 结果
     */
    public boolean deleteDtbBookFileByBookFileIds(List<Long> bookFileIds);

    int editBatch(List<DtbBookFile> fileList);
}

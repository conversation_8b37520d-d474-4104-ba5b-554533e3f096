package cn.dutp.book.service;

import java.util.List;
import java.util.Map;

import cn.dutp.book.domain.MoocPsychologyHealthScale;
import cn.dutp.book.domain.MoocPsychologyHealthUserResult;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 心理测试量表Service接口
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
public interface IMoocPsychologyHealthScaleService extends IService<MoocPsychologyHealthScale>
{

    void addPsychologyHealth(MoocPsychologyHealthScale scale);

    List<MoocPsychologyHealthScale> selectMoocPsychologyHealthScaleList(MoocPsychologyHealthScale scale);

    boolean deletedByScaleIds(List<Long> list);

    /**
     * 修改心理健康表
     * @param scale 对象
     * @return 结果
     */
    boolean editPsychologyHealth(MoocPsychologyHealthScale scale);

    /**
     * 复制心里健康
     * @param scale 对象
     * @return 结果
     */
    boolean copyPsychologyHealth(MoocPsychologyHealthScale scale);

    /**
     * 获取心理健康详情
     * @param scaleId id
     * @return 结果
     */
    MoocPsychologyHealthScale getPsychologyById(Long scaleId);

    List<MoocPsychologyHealthUserResult> getTestResultList(MoocPsychologyHealthScale scale);

    Map<String,Object> getTestResultDetail(Long resultId);

    List<MoocPsychologyHealthScale> listForEditor(MoocPsychologyHealthScale scale);

    Map<String,Object> getPsychologyHealthById(Long scaleId);

    Boolean savePsychologyHealthAnswer(MoocPsychologyHealthScale scale);
}

package cn.dutp.book.service;

import cn.dutp.book.domain.DtbBookChapter;
import cn.dutp.book.domain.vo.DtbBookChapterTreeVO;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.domain.DtbBook;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 数字教材章节目录Service接口
 *
 * <AUTHOR>
 * @date 2024-11-30
 */
public interface IDtbBookChapterService extends IService<DtbBookChapter> {
    /**
     * 查询数字教材章节目录
     *
     * @param chapterId 数字教材章节目录主键
     * @return 数字教材章节目录
     */
    DtbBookChapter selectDtbBookChapterByChapterId(Long chapterId);

    /**
     * 查询数字教材章节目录列表
     *
     * @param dtbBookChapter 数字教材章节目录
     * @return 数字教材章节目录集合
     */
    List<DtbBookChapterTreeVO> selectDtbBookChapterList(DtbBookChapter dtbBookChapter);
    List<DtbBookChapterTreeVO> selectProcessChapterList(DtbBookChapter dtbBookChapter);

    /**
     * 新增数字教材章节目录
     *
     * @param dtbBookChapter 数字教材章节目录
     * @return 结果
     */
    boolean insertDtbBookChapter(DtbBookChapter dtbBookChapter);

    /**
     * 修改数字教材章节目录
     *
     * @param dtbBookChapter 数字教材章节目录
     * @return 结果
     */
    boolean updateDtbBookChapter(DtbBookChapter dtbBookChapter);

    /**
     * 批量删除数字教材章节目录
     *
     * @param chapterIds 需要删除的数字教材章节目录主键集合
     * @return 结果
     */
    boolean deleteDtbBookChapterByChapterIds(List<Long> chapterIds);

    /**
     * 获取章节目录列表
     *
     * @param dtbBookChapter
     * @return
     */
    List<DtbBookChapterTreeVO> queryBookChapterList(DtbBookChapter dtbBookChapter);

    List<DtbBookChapter> listForRecycle(DtbBookChapter dtbBookChapter);

    int recycleChapter(DtbBookChapter dtbBookChapter);

    List<DtbBookChapter> listForSort(DtbBookChapter dtbBookChapter);

    int updateChapterSort(List<DtbBookChapter> dtbBookChapterList);

    int edit(DtbBookChapter dtbBookChapter);

    List<DtbBookChapter> listForSelect(DtbBookChapter dtbBookChapter);

    /**
     * 获取数字教材章节目录列表
     *
     * @param dtbBookChapter
     * @return
     */
    List<DtbBookChapter> queryBookChapterListByBookDetail(DtbBookChapter dtbBookChapter);

    boolean updateCharpterFree(List<DtbBookChapter> dtbBookChapterList);

    AjaxResult getBookChapters(Long bookId);

    /**
     * 简版阅读器
     * @param bookId
     * @return
     */
    AjaxResult getBookChaptersSimple(Long bookId,int fromType);
    AjaxResult getChaptersSimpleBook(Long shareId);

    DtbBookChapter chapterInfo(Long chapterId);

    List<DtbBookChapter> queryChapterList(Long bookId);

    List<DtbBookChapter> queryChapterListLastVersion(Long bookId);

    int updateChapterTemplate(DtbBookChapter chapter);

    int importChapter(MultipartFile file, Long chapterId);
    int importChapterPdf(MultipartFile file, Long chapterId);

    /**
     * 首页章节查询
     *
     * @param dtbBook
     * @return
     */
    TableDataInfo homepageChapterSearch(DtbBook dtbBook);

    void export(DtbBookChapter dtbBookChapter);

    List<DtbBookChapterTreeVO> getChapterCatalog(Long chapterId);
}

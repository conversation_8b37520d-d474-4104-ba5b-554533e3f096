<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbUserFlashCardMapper">
    
    <resultMap type="DtbUserFlashCard" id="DtbUserFlashCardResult">
        <result property="cardId"    column="card_id"    />
        <result property="userId"    column="user_id"    />
        <result property="bookId"    column="book_id"    />
        <result property="chapterId"    column="chapter_id"    />
        <result property="pageNumber"    column="page_number"    />
        <result property="sourceText"    column="source_text"    />
        <result property="cardType"    column="card_type"    />
        <result property="question"    column="question"    />
        <result property="options"    column="options"    />
        <result property="answer"    column="answer"    />
        <result property="memoryStatus"    column="memory_status"    />
        <result property="reviewCount"    column="review_count"    />
        <result property="lastReviewTime"    column="last_review_time"    />
        <result property="nextReviewTime"    column="next_review_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectDtbUserFlashCardVo">
        select card_id, user_id, book_id, chapter_id, page_number, source_text, card_type, question, options, answer, memory_status, review_count, last_review_time, next_review_time, create_by, create_time, update_by, update_time, del_flag from dtb_user_flash_card
    </sql>

    <select id="selectDtbUserFlashCardList" parameterType="DtbUserFlashCard" resultMap="DtbUserFlashCardResult">
        <include refid="selectDtbUserFlashCardVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="bookId != null "> and book_id = #{bookId}</if>
            <if test="chapterId != null "> and chapter_id = #{chapterId}</if>
            <if test="pageNumber != null "> and page_number = #{pageNumber}</if>
            <if test="sourceText != null  and sourceText != ''"> and source_text = #{sourceText}</if>
            <if test="cardType != null "> and card_type = #{cardType}</if>
            <if test="question != null  and question != ''"> and question = #{question}</if>
            <if test="options != null  and options != ''"> and options = #{options}</if>
            <if test="answer != null  and answer != ''"> and answer = #{answer}</if>
            <if test="memoryStatus != null "> and memory_status = #{memoryStatus}</if>
            <if test="reviewCount != null "> and review_count = #{reviewCount}</if>
            <if test="lastReviewTime != null "> and last_review_time = #{lastReviewTime}</if>
            <if test="nextReviewTime != null "> and next_review_time = #{nextReviewTime}</if>
        </where>
    </select>
    
    <select id="selectDtbUserFlashCardByCardId" parameterType="Long" resultMap="DtbUserFlashCardResult">
        <include refid="selectDtbUserFlashCardVo"/>
        where card_id = #{cardId}
    </select>

    <insert id="insertDtbUserFlashCard" parameterType="DtbUserFlashCard" useGeneratedKeys="true" keyProperty="cardId">
        insert into dtb_user_flash_card
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="bookId != null">book_id,</if>
            <if test="chapterId != null">chapter_id,</if>
            <if test="pageNumber != null">page_number,</if>
            <if test="sourceText != null and sourceText != ''">source_text,</if>
            <if test="cardType != null">card_type,</if>
            <if test="question != null">question,</if>
            <if test="options != null">options,</if>
            <if test="answer != null">answer,</if>
            <if test="memoryStatus != null">memory_status,</if>
            <if test="reviewCount != null">review_count,</if>
            <if test="lastReviewTime != null">last_review_time,</if>
            <if test="nextReviewTime != null">next_review_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="bookId != null">#{bookId},</if>
            <if test="chapterId != null">#{chapterId},</if>
            <if test="pageNumber != null">#{pageNumber},</if>
            <if test="sourceText != null and sourceText != ''">#{sourceText},</if>
            <if test="cardType != null">#{cardType},</if>
            <if test="question != null">#{question},</if>
            <if test="options != null">#{options},</if>
            <if test="answer != null">#{answer},</if>
            <if test="memoryStatus != null">#{memoryStatus},</if>
            <if test="reviewCount != null">#{reviewCount},</if>
            <if test="lastReviewTime != null">#{lastReviewTime},</if>
            <if test="nextReviewTime != null">#{nextReviewTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateDtbUserFlashCard" parameterType="DtbUserFlashCard">
        update dtb_user_flash_card
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="chapterId != null">chapter_id = #{chapterId},</if>
            <if test="pageNumber != null">page_number = #{pageNumber},</if>
            <if test="sourceText != null and sourceText != ''">source_text = #{sourceText},</if>
            <if test="cardType != null">card_type = #{cardType},</if>
            <if test="question != null">question = #{question},</if>
            <if test="options != null">options = #{options},</if>
            <if test="answer != null">answer = #{answer},</if>
            <if test="memoryStatus != null">memory_status = #{memoryStatus},</if>
            <if test="reviewCount != null">review_count = #{reviewCount},</if>
            <if test="lastReviewTime != null">last_review_time = #{lastReviewTime},</if>
            <if test="nextReviewTime != null">next_review_time = #{nextReviewTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where card_id = #{cardId}
    </update>

    <delete id="deleteDtbUserFlashCardByCardId" parameterType="Long">
        delete from dtb_user_flash_card where card_id = #{cardId}
    </delete>

    <delete id="deleteDtbUserFlashCardByCardIds" parameterType="String">
        delete from dtb_user_flash_card where card_id in 
        <foreach item="cardId" collection="array" open="(" separator="," close=")">
            #{cardId}
        </foreach>
    </delete>
</mapper>
package cn.dutp.book.service.impl;


import cn.dutp.book.domain.DtbBookPublishStep;
import cn.dutp.book.domain.DtbBookVersion;
import cn.dutp.book.mapper.DtbBookPublishStepMapper;
import cn.dutp.book.service.IDtbBookPublishStepService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * DUTP-DTB-027数字教材初版步骤Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-27
 */
@Service
public class DtbBookPublishStepServiceImpl extends ServiceImpl<DtbBookPublishStepMapper, DtbBookPublishStep> implements IDtbBookPublishStepService {

    @Autowired
    private DtbBookPublishStepMapper dtbBookPublishStepMapper;


    /**
     * 查询DUTP-DTB-027数字教材初版步骤列表
     *
     * @param dtbBookPublishStep DUTP-DTB-027数字教材初版步骤
     * @return DUTP-DTB-027数字教材初版步骤
     */
    @Override
    public List<DtbBookPublishStep> selectDtbBookPublishStepList(DtbBookPublishStep dtbBookPublishStep) {
        LambdaQueryWrapper<DtbBookPublishStep> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(ObjectUtil.isNotEmpty(dtbBookPublishStep.getSchoolFlag())) {
            lambdaQueryWrapper.eq(DtbBookPublishStep::getSchoolFlag
                    ,dtbBookPublishStep.getSchoolFlag());
        }
        return this.list(lambdaQueryWrapper);
    }

    @Override
    public DtbBookPublishStep getStepById(Long stepId) {
        return this.baseMapper.selectById(stepId);
    }
}

package cn.dutp.book.mapper;


import org.springframework.stereotype.Repository;
import cn.dutp.book.domain.DtbBookChapterResource;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * 教材资源与章节对应关系Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Repository
public interface DtbBookChapterResourceMapper extends BaseMapper<DtbBookChapterResource>
{

    List<DtbBookChapterResource> queryBookChapterResource(Long oldBookId, Integer sort);
}

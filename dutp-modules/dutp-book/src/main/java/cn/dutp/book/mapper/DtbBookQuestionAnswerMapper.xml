<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbBookQuestionAnswerMapper">

    <resultMap type="DtbBookQuestionAnswer" id="DtbBookQuestionAnswerResult">
        <result property="answerId" column="answer_id"/>
        <result property="questionId" column="question_id"/>
        <result property="chapterId" column="chapter_id"/>
        <result property="answerContent" column="answer_content"/>
        <result property="score" column="score"/>
        <result property="bookId" column="book_id"/>
        <result property="userId" column="user_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectDtbBookQuestionAnswerVo">
        select answer_id,
               question_id,
               chapter_id,
               answer_content,
               score,
               book_id,
               user_id,
               create_by,
               create_time,
               update_by,
               update_time
        from dtb_book_question_answer
    </sql>
    <select id="queryTestQuestionListByChapterId" resultType="cn.dutp.book.domain.DtbBookQuestionAnswer">
        SELECT
            a.score,
            q.question_type
        FROM
            dtb_book_question_answer a
                INNER JOIN dtb_book_question q
        WHERE
            q.del_flag = '0'
            AND a.chapter_id = #{chapterId}
    </select>

</mapper>
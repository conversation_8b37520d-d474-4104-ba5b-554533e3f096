<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DutpAiUserConfigMapper">
    
    <resultMap type="DutpAiUserConfig" id="DutpAiUserConfigResult">
        <result property="aiConfigId"    column="ai_config_id"    />
        <result property="userId"    column="user_id"    />
        <result property="aiExperimentCount"    column="ai_experiment_count"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>
</mapper>
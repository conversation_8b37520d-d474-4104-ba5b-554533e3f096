package cn.dutp.book.domain.vo;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class DtbBookShareCommentVo
{
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long commentId;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;
    private Integer pageNumber;
    @Excel(name = "笔记内容")
    private String commentContent;
    private String chapterName;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;
    private Integer shareFlag;
    private String fromWordId;
    private String endWordId;
    private String bookContent;
    private List<DtbBookShareCommentAttachmentVO> attachments;
}

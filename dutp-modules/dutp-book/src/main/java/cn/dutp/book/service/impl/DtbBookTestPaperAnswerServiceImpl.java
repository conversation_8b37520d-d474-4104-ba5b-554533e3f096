package cn.dutp.book.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.book.mapper.DtbBookTestPaperAnswerMapper;
import cn.dutp.book.domain.DtbBookTestPaperAnswer;
import cn.dutp.book.service.IDtbBookTestPaperAnswerService;

/**
 * 用户答卷Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-20
 */
@Service
public class DtbBookTestPaperAnswerServiceImpl extends ServiceImpl<DtbBookTestPaperAnswerMapper, DtbBookTestPaperAnswer> implements IDtbBookTestPaperAnswerService
{
    @Autowired
    private DtbBookTestPaperAnswerMapper dtbBookTestPaperAnswerMapper;

    /**
     * 查询用户答卷
     *
     * @param paperAnswerId 用户答卷主键
     * @return 用户答卷
     */
    @Override
    public DtbBookTestPaperAnswer selectDtbBookTestPaperAnswerByPaperAnswerId(Long paperAnswerId)
    {
        return this.getById(paperAnswerId);
    }

    /**
     * 查询用户答卷列表
     *
     * @param dtbBookTestPaperAnswer 用户答卷
     * @return 用户答卷
     */
    @Override
    public List<DtbBookTestPaperAnswer> selectDtbBookTestPaperAnswerList(DtbBookTestPaperAnswer dtbBookTestPaperAnswer)
    {
        LambdaQueryWrapper<DtbBookTestPaperAnswer> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbBookTestPaperAnswer.getUserId())) {
                lambdaQueryWrapper.eq(DtbBookTestPaperAnswer::getUserId
                ,dtbBookTestPaperAnswer.getUserId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookTestPaperAnswer.getBookPaperId())) {
                lambdaQueryWrapper.eq(DtbBookTestPaperAnswer::getBookPaperId
                ,dtbBookTestPaperAnswer.getBookPaperId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookTestPaperAnswer.getPaperId())) {
                lambdaQueryWrapper.eq(DtbBookTestPaperAnswer::getPaperId
                ,dtbBookTestPaperAnswer.getPaperId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookTestPaperAnswer.getTotalScore())) {
                lambdaQueryWrapper.eq(DtbBookTestPaperAnswer::getTotalScore
                ,dtbBookTestPaperAnswer.getTotalScore());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增用户答卷
     *
     * @param dtbBookTestPaperAnswer 用户答卷
     * @return 结果
     */
    @Override
    public boolean insertDtbBookTestPaperAnswer(DtbBookTestPaperAnswer dtbBookTestPaperAnswer)
    {
        return this.save(dtbBookTestPaperAnswer);
    }

    /**
     * 修改用户答卷
     *
     * @param dtbBookTestPaperAnswer 用户答卷
     * @return 结果
     */
    @Override
    public boolean updateDtbBookTestPaperAnswer(DtbBookTestPaperAnswer dtbBookTestPaperAnswer)
    {
        return this.updateById(dtbBookTestPaperAnswer);
    }

    /**
     * 批量删除用户答卷
     *
     * @param paperAnswerIds 需要删除的用户答卷主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookTestPaperAnswerByPaperAnswerIds(List<Long> paperAnswerIds)
    {
        return this.removeByIds(paperAnswerIds);
    }

}

package cn.dutp.book.service.impl;

import cn.dutp.book.mapper.DtbBookCodeExportInfoMapper;
import cn.dutp.book.service.DtbBookCodeExportInfoService;
import cn.dutp.domain.DtbBookCodeExportInfo;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 购书码导出记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-21
 */
@Service
public class DtbBookCodeExportInfoServiceImpl extends ServiceImpl<DtbBookCodeExportInfoMapper, DtbBookCodeExportInfo> implements DtbBookCodeExportInfoService {
    @Autowired
    private DtbBookCodeExportInfoMapper dtbBookCodeExportInfoMapper;


    @Override
    public boolean addExportInfo(DtbBookCodeExportInfo exportInfo) {
        int insert = dtbBookCodeExportInfoMapper.insert(exportInfo);
        return true;
    }
}






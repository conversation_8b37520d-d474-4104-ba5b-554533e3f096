package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 数字教材简介对象 dtb_book_attribute
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@Data
@TableName("dtb_book_attribute")
public class DtbBookAttribute extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long attributeId;

    /**
     * 教材ID
     */
    @Excel(name = "教材ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 版权信息
     */
    @Excel(name = "版权信息")
    private String copyright;

    /**
     * 版权声明
     */
    @Excel(name = "版权声明")
    private String declaration;

    /**
     * 编辑推荐
     */
    @Excel(name = "编辑推荐")
    private String recommend;

    /**
     * 教材简介
     */
    @Excel(name = "教材简介")
    private String introduce;

    /**
     * 其他属性{'label':'作者','content':'新华社'}json格式
     */
    @Excel(name = "其他属性{'label':'作者','content':'新华社'}json格式")
    private String otherAttributes;

}

package cn.dutp.book.service;

import java.util.List;
import cn.dutp.book.domain.DtbBookTestPaperAnswerDetail;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 试卷答题明细Service接口
 *
 * <AUTHOR>
 * @date 2025-02-20
 */
public interface IDtbBookTestPaperAnswerDetailService extends IService<DtbBookTestPaperAnswerDetail>
{
    /**
     * 查询试卷答题明细
     *
     * @param answerDetailId 试卷答题明细主键
     * @return 试卷答题明细
     */
    public DtbBookTestPaperAnswerDetail selectDtbBookTestPaperAnswerDetailByAnswerDetailId(Long answerDetailId);

    /**
     * 查询试卷答题明细列表
     *
     * @param dtbBookTestPaperAnswerDetail 试卷答题明细
     * @return 试卷答题明细集合
     */
    public List<DtbBookTestPaperAnswerDetail> selectDtbBookTestPaperAnswerDetailList(DtbBookTestPaperAnswerDetail dtbBookTestPaperAnswerDetail);

    /**
     * 新增试卷答题明细
     *
     * @param dtbBookTestPaperAnswerDetail 试卷答题明细
     * @return 结果
     */
    public boolean insertDtbBookTestPaperAnswerDetail(DtbBookTestPaperAnswerDetail dtbBookTestPaperAnswerDetail);

    /**
     * 修改试卷答题明细
     *
     * @param dtbBookTestPaperAnswerDetail 试卷答题明细
     * @return 结果
     */
    public boolean updateDtbBookTestPaperAnswerDetail(DtbBookTestPaperAnswerDetail dtbBookTestPaperAnswerDetail);

    /**
     * 批量删除试卷答题明细
     *
     * @param answerDetailIds 需要删除的试卷答题明细主键集合
     * @return 结果
     */
    public boolean deleteDtbBookTestPaperAnswerDetailByAnswerDetailIds(List<Long> answerDetailIds);

}

package cn.dutp.book.service;

import cn.dutp.book.domain.DutpTask;
import cn.dutp.common.core.web.domain.AjaxResult;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 任务管理Service接口
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
public interface IDutpTaskService extends IService<DutpTask> {
    /**
     * 查询任务管理
     *
     * @param taskId 任务管理主键
     * @return 任务管理
     */
    public DutpTask selectDutpTaskByTaskId(Long taskId);

    /**
     * 查询任务管理列表
     *
     * @param dutpTask 任务管理
     * @return 任务管理集合
     */
    public List<DutpTask> selectDutpTaskList(DutpTask dutpTask);

    /**
     * 新增任务管理
     *
     * @param dutpTask 任务管理
     * @return 结果
     */
    public boolean insertDutpTask(DutpTask dutpTask);

    /**
     * 修改任务管理
     *
     * @param dutpTask 任务管理
     * @return 结果
     */
    public boolean updateDutpTask(DutpTask dutpTask);

    public AjaxResult editCaptionStyle(DutpTask dutpTask);

    /**
     * 批量删除任务管理
     *
     * @param taskIds 需要删除的任务管理主键集合
     * @return 结果
     */
    public boolean deleteDutpTaskByTaskIds(List<Long> taskIds);

}

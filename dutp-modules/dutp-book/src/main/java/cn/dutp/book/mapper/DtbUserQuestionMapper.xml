<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbUserQuestionMapper">
    
    <resultMap type="DtbUserQuestion" id="DtbUserQuestionResult">
        <result property="questionId"    column="question_id"    />
        <result property="questionType"    column="question_type"    />
        <result property="questionContent"    column="question_content"    />
        <result property="rightAnswer"    column="right_answer"    />
        <result property="analysis"    column="analysis"    />
        <result property="userId"    column="user_id"    />
        <result property="disorder"    column="disorder"    />
        <result property="sort"    column="sort"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="folderId"    column="folder_id"    />
        <result property="codeContent" column="code_content"/>
        <result property="folderName"    column="folder_name"    />
        <result property="questionRemark" column="question_remark"/>
    </resultMap>

    <resultMap type="DtbUserQuestion" id="DtbUserQuestionWithOptionsResult" extends="DtbUserQuestionResult">
        <collection property="options" 
                    column="question_id" 
                    select="selectQuestionOptions"
                    fetchType="eager"/>
    </resultMap>

    <resultMap id="QuestionOptionResult" type="DtbUserQuestionOption">
        <result property="optionId" column="option_id"/>
        <result property="optionContent" column="option_content"/>
        <result property="questionId" column="question_id"/>
        <result property="rightFlag" column="right_flag"/>
        <result property="optionPosition" column="option_position"/>
        <result property="sort" column="sort"/>
    </resultMap>

    <sql id="selectDtbUserQuestionVo">
        select question_id, question_type, question_content, right_answer, analysis, user_id, 
               disorder, sort, create_by, create_time, update_by, update_time, del_flag, 
               folder_id, code_content, create_source 
        from dtb_user_question
    </sql>

    <select id="selectDtbUserQuestionList" parameterType="DtbUserQuestion" resultMap="DtbUserQuestionResult">
        <include refid="selectDtbUserQuestionVo"/>
        <where>  
            <if test="questionType != null "> and question_type = #{questionType}</if>
            <if test="questionContent != null  and questionContent != ''"> and question_content like concat('%', #{questionContent}, '%')</if>
            <if test="rightAnswer != null  and rightAnswer != ''"> and right_answer = #{rightAnswer}</if>
            <if test="analysis != null  and analysis != ''"> and analysis = #{analysis}</if>
            <if test="chapterId != null "> and chapter_id = #{chapterId}</if>
            <if test="bookId != null "> and book_id = #{bookId}</if>
            <if test="disorder != null "> and disorder = #{disorder}</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="folderId != null "> and folder_id = #{folderId}</if>
        </where>
    </select>

    <select id="selectDtbUserQuestionByQuestionId" parameterType="Long" resultMap="DtbUserQuestionResult">
        <include refid="selectDtbUserQuestionVo"/>
        where question_id = #{questionId}
    </select>


    <insert id="insertDtbUserQuestion" parameterType="DtbUserQuestion" useGeneratedKeys="true" keyProperty="questionId">
        insert into dtb_user_question
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="questionType != null">question_type,</if>
            <if test="questionContent != null">question_content,</if>
            <if test="rightAnswer != null">right_answer,</if>
            <if test="analysis != null">analysis,</if>
            <if test="chapterId != null">chapter_id,</if>
            <if test="bookId != null">book_id,</if>
            <if test="disorder != null">disorder,</if>
            <if test="sort != null">sort,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="folderId != null">folder_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="questionType != null">#{questionType},</if>
            <if test="questionContent != null">#{questionContent},</if>
            <if test="rightAnswer != null">#{rightAnswer},</if>
            <if test="analysis != null">#{analysis},</if>
            <if test="chapterId != null">#{chapterId},</if>
            <if test="bookId != null">#{bookId},</if>
            <if test="disorder != null">#{disorder},</if>
            <if test="sort != null">#{sort},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="folderId != null">#{folderId},</if>
         </trim>
    </insert>

    <update id="updateDtbUserQuestion" parameterType="DtbUserQuestion">
        update dtb_user_question
        <trim prefix="SET" suffixOverrides=",">
            <if test="questionType != null">question_type = #{questionType},</if>
            <if test="questionContent != null">question_content = #{questionContent},</if>
            <if test="rightAnswer != null">right_answer = #{rightAnswer},</if>
            <if test="analysis != null">analysis = #{analysis},</if>
            <if test="chapterId != null">chapter_id = #{chapterId},</if>
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="disorder != null">disorder = #{disorder},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="folderId != null">folder_id = #{folderId},</if>
        </trim>
        where question_id = #{questionId}
    </update>

    <delete id="deleteDtbUserQuestionByQuestionId" parameterType="Long">
        delete from dtb_user_question where question_id = #{questionId}
    </delete>

    <delete id="deleteDtbUserQuestionByQuestionIds" parameterType="String">
        delete from dtb_user_question where question_id in
        <foreach item="questionId" collection="questionIds" open="(" separator="," close=")">
            #{questionId}
        </foreach>
    </delete>

    <select id="selectQuestionOptions" resultMap="QuestionOptionResult">
        SELECT 
            option_id, option_content, question_id, right_flag, 
            option_position, sort
        FROM dtb_user_question_option
        WHERE question_id = #{question_id}
        AND (del_flag = '0' OR del_flag IS NULL)
        ORDER BY sort
    </select>

    <select id="selectDtbUserQuestionListWithOptions" parameterType="DtbUserQuestion" resultMap="DtbUserQuestionWithOptionsResult">
        SELECT 
            q.question_id, q.question_type, q.question_content, q.right_answer, 
            q.analysis, q.user_id, q.disorder, q.sort,
            q.create_by, q.create_time, q.update_by, q.update_time, 
            q.del_flag, q.folder_id, q.code_content,q.question_remark

        FROM dtb_user_question q
        <where>
            <if test="questionType != null">AND q.question_type = #{questionType}</if>
            <if test="questionContent != null and questionContent != ''">AND q.question_content like concat('%', #{questionContent}, '%')</if>
            <if test="userId != null">AND q.user_id = #{userId}</if>
            <if test="disorder != null">AND q.disorder = #{disorder}</if>
            <if test="folderId != null">AND q.folder_id = #{folderId}</if>
            AND q.del_flag = '0'
        </where>
        ORDER BY q.sort desc, q.create_time desc
    </select>

    <select id="countDtbUserQuestion" parameterType="cn.dutp.book.domain.DtbUserQuestion" resultType="java.lang.Integer">
    SELECT COUNT(*) FROM dtb_user_question
    WHERE del_flag = '0'
    <if test="questionType != null">
        AND question_type = #{questionType}
    </if>
    <if test="questionContent != null and questionContent != ''">
        AND question_content like concat('%', #{questionContent}, '%')
    </if>
    <if test="userId != null">
        AND user_id = #{userId}
    </if>
    <if test="disorder != null">
        AND disorder = #{disorder}
    </if>
    <if test="folderId != null">
        AND folder_id = #{folderId}
    </if>
</select>


<!-- 移入回收站 -->
<update id="moveToRecycleBin">
    update dtb_user_question set del_flag = '1' , update_time = sysdate(),update_by=#{username}
    where question_id in
    <foreach collection="questionIds" item="questionId" open="(" separator="," close=")">
        #{questionId}
    </foreach>
</update>

<!-- 从回收站恢复 -->
<update id="restoreByIds">
    update dtb_user_question set del_flag = '0', update_time = sysdate(),update_by=#{username}
    where question_id in
    <foreach collection="questionIds" item="questionId" open="(" separator="," close=")">
        #{questionId}
    </foreach>
</update>

<!-- 查询回收站列表 -->
<select id="recycleBinList" resultMap="DtbUserQuestionResult">
    select q.*, folder.folder_name from dtb_user_question q
               left join dtb_user_question_folder folder on q.folder_id = folder.folder_id
    where q.del_flag = '1'
    <if test="folderId != null">
        and q.folder_id = #{folderId}
    </if>
    <if test="userId != null">
        and q.user_id = #{userId}
    </if>
    <if test="questionType != null and questionType != ''">
        and q.question_type = #{questionType}
    </if>
    order by q.create_time desc
</select>


    <select id="selectDtbUserQuestionBatch"  resultMap="DtbUserQuestionResult">
        <include refid="selectDtbUserQuestionVo"/>
        where question_id in (
        <foreach collection="ids" item="item">
            #{item}
        </foreach>
        )
    </select>


    <select id="checkPaperReference" parameterType="Long[]" resultType="int">
        SELECT COUNT(1)
        FROM dtb_test_paper_question
        LEFT JOIN dtb_test_paper ON dtb_test_paper_question.paper_id = dtb_test_paper.paper_id
        WHERE dtb_test_paper_question.question_id in
        <foreach collection="questionIds" item="questionId" open="(" separator="," close=")">
            #{questionId}
        </foreach>
        AND dtb_test_paper.del_flag = '0'
    </select>

</mapper>
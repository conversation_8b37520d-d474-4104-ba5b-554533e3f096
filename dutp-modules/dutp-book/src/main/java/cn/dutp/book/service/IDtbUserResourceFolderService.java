package cn.dutp.book.service;

import java.util.List;

import cn.dutp.book.domain.DtbUserResource;
import cn.dutp.book.domain.DtbUserResourceFolder;
import cn.dutp.book.domain.vo.ResourceVO;

import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 个人资源文件夹Service接口
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
public interface IDtbUserResourceFolderService extends IService<DtbUserResourceFolder>
{
    /**
     * 查询个人资源文件夹
     *
     * @param userFolderId 个人资源文件夹主键
     * @return 个人资源文件夹
     */
    public DtbUserResourceFolder selectDtbUserResourceFolderByUserFolderId(Long userFolderId);



     /**
     * 查询合并的资源列表
     * @param parentFolderId 父文件夹ID
     * @param userId 用户ID
     * @return 资源列表
     */
    List<ResourceVO> selectCombinedResources(DtbUserResource dtbUserResourceFolder);

    /**
     * 查询个人资源文件夹列表
     *
     * @param dtbUserResourceFolder 个人资源文件夹
     * @return 个人资源文件夹集合
     */
    public List<DtbUserResourceFolder> selectDtbUserResourceFolderList(DtbUserResourceFolder dtbUserResourceFolder);

    /**
     * 新增个人资源文件夹
     *
     * @param dtbUserResourceFolder 个人资源文件夹
     * @return 结果
     */
    public boolean insertDtbUserResourceFolder(DtbUserResourceFolder dtbUserResourceFolder);

    /**
     * 修改个人资源文件夹
     *
     * @param dtbUserResourceFolder 个人资源文件夹
     * @return 结果
     */
    public boolean updateDtbUserResourceFolder(DtbUserResourceFolder dtbUserResourceFolder);

    /**
     * 批量删除个人资源文件夹
     *
     * @param userFolderIds 需要删除的个人资源文件夹主键集合
     * @return 结果
     */
    public boolean deleteDtbUserResourceFolderByUserFolderIds(List<Long> userFolderIds);

}

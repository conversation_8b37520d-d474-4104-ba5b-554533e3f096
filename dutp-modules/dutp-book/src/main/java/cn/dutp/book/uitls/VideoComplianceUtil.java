package cn.dutp.book.uitls;

/**
 * 视频合规鉴权工具类
 */
public class VideoComplianceUtil {

    // 拼接业务参数（notify_url回调地址可以不填写，此处使用官方默认值）
    public static String getJsonString(String audioUrl) {
        String json = "{\n" +
                "  \"video_list\": [\n" +
                "    {\n" +
                "      \"video_type\": \"" + audioUrl.substring(audioUrl.length() - 3) + "\",\n" +
                "      \"file_url\": \"" + audioUrl + "\",\n" +
                "      \"name\": \"" + "xxx" + audioUrl.substring(audioUrl.length() - 8) + "\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"notify_url\": \"http://wdfgdzx.top:9999/user/audio_video_callback?mark=myselfGive\"\n" +
                "}";
        return json;
    }
}

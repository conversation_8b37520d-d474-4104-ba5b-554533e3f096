<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbBookBookMapper">
    
    <resultMap type="cn.dutp.domain.DtbBook" id="DtbBookResult">
        <result property="bookId"    column="book_id"    />
        <result property="bookName"    column="book_name"    />
        <result property="cover"    column="cover"    />
        <result property="isbn"    column="isbn"    />
        <result property="bookNo"    column="book_no"    />
        <result property="publishDate"    column="publish_date"    />
        <result property="publishOrganization"    column="publish_organization"    />
        <result property="publishStatus"    column="publish_status"    />
        <result property="schoolId"    column="school_id"    />
        <result property="bookType"    column="book_type"    />
        <result property="soldQuantity"    column="sold_quantity"    />
        <result property="readQuantity"    column="read_quantity"    />
        <result property="priceCounter"    column="price_counter"    />
        <result property="priceSale"    column="price_sale"    />
        <result property="bookOrganize"    column="book_organize"    />
        <result property="topicNo"    column="topic_no"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="shelfTime"    column="shelf_time"    />
        <result property="unshelfTime"    column="unshelf_time"    />
        <result property="shelfState"    column="shelf_state"    />

        <result property="topSubjectId"    column="top_subject_id"    />
        <result property="secondSubjectId"    column="second_subject_id"    />
        <result property="thirdSubjectId"    column="third_subject_id"    />
        <result property="forthSubjectId"    column="forth_subject_id"    />
    </resultMap>

    <sql id="selectDtbBookVo">
        select book_id, book_name, cover, ibsn, book_no, publish_date, publish_organization, publish_status, school_id, book_type,  writer_id, sold_quantity, read_quantity, price_counter, price_sale, book_organize, topic_no, del_flag, create_by, create_time, update_by, update_time, shelf_time, unshelf_time, shelf_state, sort from dtb_book
    </sql>

    <select id="selectDtbBookList" parameterType="cn.dutp.domain.DtbBook" resultMap="DtbBookResult">
        <include refid="selectDtbBookVo"/>
        <where>  
            <if test="bookName != null  and bookName != ''"> and book_name like concat('%', #{bookName}, '%')</if>
            <if test="cover != null  and cover != ''"> and cover = #{cover}</if>
            <if test="ibsn != null  and ibsn != ''"> and ibsn = #{ibsn}</if>
            <if test="bookNo != null  and bookNo != ''"> and book_no = #{bookNo}</if>
            <if test="publishDate != null "> and publish_date = #{publishDate}</if>
            <if test="publishOrganization != null  and publishOrganization != ''"> and publish_organization = #{publishOrganization}</if>
            <if test="publishStatus != null "> and publish_status = #{publishStatus}</if>
            <if test="schoolId != null "> and school_id = #{schoolId}</if>
            <if test="bookType != null "> and book_type = #{bookType}</if>
            <if test="writerId != null "> and writer_id = #{writerId}</if>
            <if test="soldQuantity != null "> and sold_quantity = #{soldQuantity}</if>
            <if test="readQuantity != null "> and read_quantity = #{readQuantity}</if>
            <if test="priceCounter != null "> and price_counter = #{priceCounter}</if>
            <if test="priceSale != null "> and price_sale = #{priceSale}</if>
            <if test="bookOrganize != null "> and book_organize = #{bookOrganize}</if>
            <if test="topicNo != null  and topicNo != ''"> and topic_no = #{topicNo}</if>
            <if test="shelfTime != null "> and shelf_time = #{shelfTime}</if>
            <if test="unshelfTime != null "> and unshelf_time = #{unshelfTime}</if>
            <if test="shelfState != null "> and shelf_state = #{shelfState}</if>
            <if test="sort != null "> and sort = #{sort}</if>
        </where>
    </select>
    
    <select id="selectDtbBookByBookId" parameterType="Long" resultMap="DtbBookResult">
        <include refid="selectDtbBookVo"/>
        where book_id = #{bookId}
    </select>
    <select id="selectListByDtbBook" resultType="cn.dutp.domain.DtbBook">
        SELECT
            b.book_id,
            b.book_name,
            b.book_no,
            b.cover,
            b.isbn,
            b.issn,
            b.publish_status,
            b.shelf_state,
            b.master_flag,
            b.price_counter,
            b.price_sale,
            b.topic_no,
            b.publish_date,
            b.edition,
            b.create_time,
            b.current_step_id,
            b.current_version_id,
            b.last_version_id,
            bv.version_no,
            bv2.version_no as amend_version_no,
            bv2.create_time as amend_create_time,
            ph.house_name,
            s.school_name,
            <!-- ba.area_name, -->
            bps.step_name,
            bps.step_id,
            b3.book_name as master_book_name,
            GROUP_CONCAT( b2.book_name ) AS deputy_book_name
        FROM
            dtb_book b
                INNER JOIN dutp_publishing_house ph ON ph.house_id = b.house_id
                INNER JOIN dtb_book_version bv ON bv.version_id = b.current_version_id
                INNER JOIN dtb_book_version bv2 ON bv2.version_id = b.last_version_id
                INNER JOIN dtb_book_publish_step bps on bps.step_id = b.current_step_id
                left JOIN (
                SELECT
                b1.book_id,
                GROUP_CONCAT( s.school_name ) AS school_name,
                group_concat( concat(';', s.school_id ,';'))  AS school_Id_list
                FROM
                dtb_book b1
                INNER JOIN dtb_book_school bs ON bs.book_id = b1.book_id
                INNER JOIN dutp_school s ON bs.school_id = s.school_id
                WHERE
                b1.del_flag = '0'
                GROUP BY
                b1.book_id
                ) s ON s.book_id = b.book_id
                LEFT JOIN dtb_book b2 ON b2.master_book_id = b.book_id and b2.del_flag = '0'
                LEFT JOIN dtb_book b3 ON b3.book_id = b.master_book_id and b3.del_flag = '0'
        WHERE
            b.del_flag = '0'
          and b.book_organize = #{bookOrganize}
        <if test="schoolId != null"> and s.school_id_list like concat('%;', #{schoolId}, ';%') </if>
        <if test="bookName != null  and bookName != ''"> and b.book_name like concat('%', #{bookName}, '%')</if>
        <if test="isbn != null  and isbn != ''"> and b.isbn like concat('%', #{isbn}, '%') </if>
        <if test="issn != null  and issn != ''"> and b.issn like concat('%', #{issn}, '%') </if>
        <if test="bookNo != null  and bookNo != ''"> and b.book_no = #{bookNo}</if>
        <if test="houseId != null "> and b.house_id = #{houseId}</if>
        <if test="masterFlag != null "> and b.master_flag = #{masterFlag}</if>
        <if test="shelfState != null "> and b.shelf_state = #{shelfState}</if>
        <if test="publishStatus != null "> and  b.publish_status = #{publishStatus}</if>
        <if test="stepId != null "> and  bps.step_id = #{stepId}</if>

        GROUP BY
            b.book_id
        order by b.create_time desc
    </select>

    <select id="checkRepeatByIssn" resultType="java.lang.Integer">
        select count(*) from dtb_book
                        where issn = #{issn} and del_flag = '0' and master_flag != 3
                        <if test="bookId != null">and book_id != #{bookId} </if>
    </select>

    <select id="checkRepeatByIsbn" resultType="java.lang.Integer">
        select count(*) from dtb_book
                        where isbn = #{isbn} and del_flag = '0' and master_flag != 3
                        <if test="bookId != null">and book_id != #{bookId} </if>
    </select>
    <select id="queryBookByBookId" resultType="cn.dutp.domain.DtbBook">
        SELECT
        b.book_id,
        b.book_name,
        b.book_no,
        b.cover,
        b.isbn,
        b.issn,
        b.publish_status,
        b.shelf_state,
        b.master_flag,
        b.price_counter,
        b.price_sale,
        b.topic_no,
        b.publish_date,
        b.edition,
        b.current_step_id,
        b.current_step_id as step_id,
        b.author_label,
        b.author_value,
        b.top_subject_id,
        b.second_subject_id,
        b.third_subject_id,
        b.forth_subject_id,
        b.book_organize,
        b.book_type,
        b.record_no,
        b.language_id,
        b.master_book_id,
        b.create_time,
        b.dept_id,
        bv.version_id,
        bv.version_no,
        lastver.version_id as lastVersionId,
        lastver.version_no as lastVersionNo,
        ph.house_name,
        ph.house_id,
        l.language_name as languageName,
        b.read_quantity
        FROM
        dtb_book b
        INNER JOIN dutp_publishing_house ph ON ph.house_id = b.house_id
        INNER JOIN dtb_book_version bv ON bv.version_id = b.current_version_id
        INNER JOIN dtb_book_version lastver ON lastver.version_id = b.last_version_id
        left join dutp_language l on l.language_id = b.language_id
        WHERE
        b.del_flag = '0'
        and b.book_id = #{bookId}
    </select>
    <select id="queryBookListByBookId" resultType="cn.dutp.domain.DtbBook">
        SELECT
        b.book_id,
        b.book_name,
        b.book_no,
        b.cover,
        b.shelf_state
        FROM
        dtb_book b
        WHERE
        b.del_flag = '0'
        and b.master_book_id = #{bookId}
    </select>

    <insert id="insertDtbBook" parameterType="cn.dutp.domain.DtbBook" useGeneratedKeys="true" keyProperty="bookId">
        insert into dtb_book
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bookName != null">book_name,</if>
            <if test="cover != null">cover,</if>
            <if test="ibsn != null">ibsn,</if>
            <if test="bookNo != null">book_no,</if>
            <if test="publishDate != null">publish_date,</if>
            <if test="publishOrganization != null">publish_organization,</if>
            <if test="publishStatus != null">publish_status,</if>
            <if test="schoolId != null">school_id,</if>
            <if test="bookType != null">book_type,</if>
            <if test="writerId != null">writer_id,</if>
            <if test="soldQuantity != null">sold_quantity,</if>
            <if test="readQuantity != null">read_quantity,</if>
            <if test="priceCounter != null">price_counter,</if>
            <if test="priceSale != null">price_sale,</if>
            <if test="bookOrganize != null">book_organize,</if>
            <if test="topicNo != null">topic_no,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="shelfTime != null">shelf_time,</if>
            <if test="unshelfTime != null">unshelf_time,</if>
            <if test="shelfState != null">shelf_state,</if>
            <if test="sort != null">sort,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bookName != null">#{bookName},</if>
            <if test="cover != null">#{cover},</if>
            <if test="ibsn != null">#{ibsn},</if>
            <if test="bookNo != null">#{bookNo},</if>
            <if test="publishDate != null">#{publishDate},</if>
            <if test="publishOrganization != null">#{publishOrganization},</if>
            <if test="publishStatus != null">#{publishStatus},</if>
            <if test="schoolId != null">#{schoolId},</if>
            <if test="bookType != null">#{bookType},</if>
            <if test="extBookId != null">#{extBookId},</if>
            <if test="writerId != null">#{writerId},</if>
            <if test="soldQuantity != null">#{soldQuantity},</if>
            <if test="readQuantity != null">#{readQuantity},</if>
            <if test="priceCounter != null">#{priceCounter},</if>
            <if test="priceSale != null">#{priceSale},</if>
            <if test="bookOrganize != null">#{bookOrganize},</if>
            <if test="topicNo != null">#{topicNo},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="shelfTime != null">#{shelfTime},</if>
            <if test="unshelfTime != null">#{unshelfTime},</if>
            <if test="shelfState != null">#{shelfState},</if>
            <if test="sort != null">#{sort},</if>
         </trim>
    </insert>

    <update id="updateDtbBook" parameterType="cn.dutp.domain.DtbBook">
        update dtb_book
        <trim prefix="SET" suffixOverrides=",">
            <if test="bookName != null">book_name = #{bookName},</if>
            <if test="cover != null">cover = #{cover},</if>
            <if test="ibsn != null">ibsn = #{ibsn},</if>
            <if test="bookNo != null">book_no = #{bookNo},</if>
            <if test="publishDate != null">publish_date = #{publishDate},</if>
            <if test="publishOrganization != null">publish_organization = #{publishOrganization},</if>
            <if test="publishStatus != null">publish_status = #{publishStatus},</if>
            <if test="schoolId != null">school_id = #{schoolId},</if>
            <if test="bookType != null">book_type = #{bookType},</if>
            <if test="writerId != null">writer_id = #{writerId},</if>
            <if test="soldQuantity != null">sold_quantity = #{soldQuantity},</if>
            <if test="readQuantity != null">read_quantity = #{readQuantity},</if>
            <if test="priceCounter != null">price_counter = #{priceCounter},</if>
            <if test="priceSale != null">price_sale = #{priceSale},</if>
            <if test="bookOrganize != null">book_organize = #{bookOrganize},</if>
            <if test="topicNo != null">topic_no = #{topicNo},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="shelfTime != null">shelf_time = #{shelfTime},</if>
            <if test="unshelfTime != null">unshelf_time = #{unshelfTime},</if>
            <if test="shelfState != null">shelf_state = #{shelfState},</if>
            <if test="sort != null">sort = #{sort},</if>
        </trim>
        where book_id = #{bookId}
    </update>
    <update id="updateByBookId" parameterType="cn.dutp.domain.DtbBook">
        update dtb_book set book_name = #{bookName},
                            author_label = #{authorLabel},
                            author_value = #{authorValue},
                            cover = #{cover},
                            top_subject_id = #{topSubjectId},
                            second_subject_id = #{secondSubjectId},
                            third_subject_id = #{thirdSubjectId},
                            forth_subject_id = #{forthSubjectId},
                            isbn = #{isbn},
                            issn = #{issn},
                            publish_date = #{publishDate},
                            shelf_state = #{shelfState},
                            house_id = #{houseId},
                            book_type = #{bookType},
                            master_flag = #{masterFlag},
                            master_book_id = #{masterBookId},
                            price_counter = #{priceCounter},
                            price_sale = #{priceSale},
                            book_organize = #{bookOrganize},
                            topic_no = #{topicNo},
                            language_id = #{languageId},
                            dept_id = #{deptId},
                            edition = #{edition}
                        where book_id = #{bookId}

    </update>

    <update id="updateDeputyBook">
        update dtb_book set master_book_id = #{book.bookId}, book_type = #{book.bookType}, isbn = #{book.isbn}, issn = #{book.issn}, top_subject_id = #{book.topSubjectId}, second_subject_id = #{book.secondSubjectId}, third_subject_id = #{book.thirdSubjectId}, forth_subject_id = #{book.forthSubjectId} where
               book_id in
        <foreach item="bookId" collection="deputyBookIdList" open="(" separator="," close=")">
            #{bookId}
        </foreach>
    </update>

    <delete id="deleteDtbBookByBookId" parameterType="Long">
        delete from dtb_book where book_id = #{bookId}
    </delete>

    <delete id="deleteDtbBookByBookIds" parameterType="String">
        delete from dtb_book where book_id in 
        <foreach item="bookId" collection="array" open="(" separator="," close=")">
            #{bookId}
        </foreach>
    </delete>

    <select id="selectDtbBookListWithGroup" resultType="cn.dutp.domain.DtbBook">
        select db.book_id, db.book_name, db.author_label, db.author_value, db.cover, db.isbn, db.issn, 
               db.book_no, db.publish_date, db.current_version_id, db.last_version_id, db.shelf_time, 
               db.unshelf_time, db.publish_organization, db.publish_status, db.shelf_state, db.school_id,
               db.house_id, db.book_type, db.master_flag, db.master_book_id, db.sold_quantity, 
               db.read_quantity, db.price_counter, db.price_sale, db.book_organize, db.topic_no,
               db.language_id, db.current_step_id, db.edition, db.table_number_type, db.image_number_type,
               db.top_subject_id, db.second_subject_id, db.third_subject_id, db.forth_subject_id,
               db.del_flag, db.create_by, db.create_time, db.update_by, db.update_time
        from dtb_book db
        INNER join dtb_book_group dbg on dbg.book_id = db.book_id and dbg.del_flag = '0'
        <where>
            <if test="userId != null">
            dbg.user_id = #{userId}
            </if>
            and db.del_flag = '0'
            <if test="dtbBook.bookName != null  and dtbBook.bookName != ''"> and (db.book_name like concat('%', #{dtbBook.bookName}, '%')  or db.book_no = #{dtbBook.bookName} )</if>
            <if test="dtbBook.isbn != null  and dtbBook.isbn != ''"> and (db.isbn = #{dtbBook.isbn} or  db.issn = #{dtbBook.isbn})</if>
            <if test="dtbBook.topSubjectId != null  and dtbBook.topSubjectId != ''"> and db.top_subject_id = #{dtbBook.topSubjectId}</if>
            <if test="dtbBook.secondSubjectId != null  and dtbBook.secondSubjectId != ''"> and db.second_subject_id = #{dtbBook.secondSubjectId}</if>
            <if test="dtbBook.thirdSubjectId != null  and dtbBook.thirdSubjectId != ''"> and db.third_subject_id = #{dtbBook.thirdSubjectId}</if>
            <if test="dtbBook.forthSubjectId != null  and dtbBook.forthSubjectId != ''"> and db.forth_subject_id = #{dtbBook.forthSubjectId}</if>
        </where>
        GROUP BY db.book_id
        <if test="dtbBook.orderType != null and dtbBook.orderType != ''">
            <choose>
                <when test="dtbBook.orderType == 'createTime_asc'">
                    ORDER BY db.create_time ASC
                </when>
                <when test="dtbBook.orderType == 'createTime_desc'">
                    ORDER BY db.create_time DESC
                </when>
                <when test="dtbBook.orderType == 'bookName_asc'">
                    ORDER BY db.book_name ASC
                </when>
                <when test="dtbBook.orderType == 'bookName_desc'">
                    ORDER BY db.book_name DESC
                </when>
                <otherwise>
                    ORDER BY db.create_time DESC
                </otherwise>
            </choose>
        </if>
        <if test="dtbBook.orderType == null or dtbBook.orderType == ''">
            ORDER BY db.create_time DESC
        </if>
    </select>
    <select id="listOfAuthorAndEditor" resultType="cn.dutp.domain.DtbBook">
        SELECT
        b.book_id,
        b.book_name,
        b.book_no,
        b.cover,
        b.isbn,
        b.issn,
        b.publish_status,
        b.shelf_state,
        b.master_flag,
        b.price_counter,
        b.price_sale,
        b.topic_no,
        b.publish_date,
        b.edition,
        b.create_time,
        b.current_step_id,
        b.current_version_id,
        b.last_version_id,
        bv.version_no,
        bv2.version_no as amend_version_no,
        bv2.create_time as amend_create_time,
        ph.house_name,
        s.school_name,
        <!-- ba.area_name, -->
        bps.step_name,
        bps.step_id,
        b3.book_name as master_book_name,
        GROUP_CONCAT( b2.book_name ) AS deputy_book_name
        FROM
        dtb_book b
        INNER JOIN dutp_publishing_house ph ON ph.house_id = b.house_id
        INNER JOIN dtb_book_version bv ON bv.version_id = b.current_version_id
        INNER JOIN dtb_book_version bv2 ON bv2.version_id = b.last_version_id
        INNER JOIN dtb_book_publish_step bps on bps.step_id = b.current_step_id
        INNER JOIN (
        SELECT DISTINCT b4.book_id
        FROM
        dtb_book b4
        INNER JOIN dtb_book_group g on g.book_id = b4.book_id and g.user_id = #{userId} and g.del_flag = '0'
        ) bb on bb.book_id = b.book_id
        left
        JOIN (
        SELECT
        b1.book_id,
        GROUP_CONCAT( s.school_name ) AS school_name,
        group_concat( concat(';', s.school_id ,';'))  AS school_Id_list
        FROM
        dtb_book b1
        INNER JOIN dtb_book_school bs ON bs.book_id = b1.book_id
        INNER JOIN dutp_school s ON bs.school_id = s.school_id
        WHERE
        b1.del_flag = '0'
        GROUP BY
        b1.book_id
        ) s ON s.book_id = b.book_id
        LEFT JOIN dtb_book b2 ON b2.master_book_id = b.book_id and b2.del_flag = '0'
        LEFT JOIN dtb_book b3 ON b3.book_id = b.master_book_id and b3.del_flag = '0'
        WHERE
        b.del_flag = '0'
        and b.book_organize = #{bookOrganize}
        <if test="schoolId != null"> and s.school_id_list like concat('%;', #{schoolId}, ';%') </if>
        <if test="bookName != null  and bookName != ''"> and b.book_name like concat('%', #{bookName}, '%')</if>
        <if test="isbn != null  and isbn != ''"> and b.isbn like concat('%', #{isbn}, '%') </if>
        <if test="issn != null  and issn != ''"> and b.issn like concat('%', #{issn}, '%') </if>
        <if test="bookNo != null  and bookNo != ''"> and b.book_no = #{bookNo}</if>
        <if test="houseId != null "> and b.house_id = #{houseId}</if>
        <if test="masterFlag != null "> and b.master_flag = #{masterFlag}</if>
        <if test="shelfState != null "> and b.shelf_state = #{shelfState}</if>
        <if test="publishStatus != null "> and  b.publish_status = #{publishStatus}</if>
        <if test="stepId != null "> and  bps.step_id = #{stepId}</if>
        GROUP BY
        b.book_id
        order by b.create_time desc
    </select>
    <select id="queryBookByChapterId" resultType="cn.dutp.domain.DtbBook">
        SELECT
            b.book_id,
            b.current_version_id AS version_id
        FROM
            dtb_book b
                INNER JOIN dtb_book_chapter c ON c.book_id = b.book_id
                AND c.version_id = b.current_version_id
        WHERE
            c.del_flag = '0'
          AND c.chapter_id = #{chapterId}
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbUserBookFootPrintMapper">

    <resultMap type="DtbUserBookFootPrintVo" id="DtbUserBookFootPrintResult">
        <result property="footPrintId"    column="foot_print_id"    />
        <result property="bookId"    column="book_id"    />
        <result property="userId"    column="user_id"    />
        <result property="seeQuantity"    column="see_quantity"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="bookName"    column="book_name"    />
        <result property="cover"    column="cover"    />
    </resultMap>

    <sql id="selectDtbUserBookFootPrintVo">
        SELECT
            dubfp.foot_print_id,
            dubfp.book_id,
            dubfp.user_id,
            dubfp.see_quantity,
            dubfp.create_by,
            dubfp.create_time,
            dubfp.update_by,
            dubfp.update_time,
            db.book_name,
            db.cover
        FROM
            dtb_user_book_foot_print dubfp LEFT JOIN dtb_book db
            ON dubfp.book_id = db.book_id
    </sql>

    <select id="selectDtbUserBookFootPrintList" parameterType="DtbUserBookFootPrint" resultMap="DtbUserBookFootPrintResult">
        <include refid="selectDtbUserBookFootPrintVo"/>
        <where>
            <if test="bookId != null "> and dubfp.book_id = #{bookId}</if>
            <if test="userId != null "> and dubfp.user_id = #{userId}</if>
            <if test="seeQuantity != null "> and dubfp.see_quantity = #{seeQuantity}</if>
        </where>
        order by dubfp.update_time desc
    </select>

    <select id="selectDtbUserBookFootPrintByFootPrintId" parameterType="Long" resultMap="DtbUserBookFootPrintResult">
        <include refid="selectDtbUserBookFootPrintVo"/>
        where foot_print_id = #{footPrintId}
    </select>

    <insert id="insertDtbUserBookFootPrint" parameterType="DtbUserBookFootPrint" useGeneratedKeys="true" keyProperty="footPrintId">
        insert into dtb_user_book_foot_print
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bookId != null">book_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="seeQuantity != null">see_quantity,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bookId != null">#{bookId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="seeQuantity != null">#{seeQuantity},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateDtbUserBookFootPrint" parameterType="DtbUserBookFootPrint">
        update dtb_user_book_foot_print
        <trim prefix="SET" suffixOverrides=",">
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="seeQuantity != null">see_quantity = #{seeQuantity},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where foot_print_id = #{footPrintId}
    </update>

    <delete id="deleteDtbUserBookFootPrintByFootPrintId" parameterType="Long">
        delete from dtb_user_book_foot_print where foot_print_id = #{footPrintId}
    </delete>

    <delete id="deleteDtbUserBookFootPrintByFootPrintIds" parameterType="String">
        delete from dtb_user_book_foot_print where foot_print_id in
        <foreach item="footPrintId" collection="array" open="(" separator="," close=")">
            #{footPrintId}
        </foreach>
    </delete>
</mapper>
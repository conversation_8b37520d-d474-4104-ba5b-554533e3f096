package cn.dutp.book.service.impl;

import cn.dutp.book.domain.DtbUserResource;
import cn.dutp.book.domain.vo.CheckResourceVO;
import cn.dutp.book.mapper.DtbUserResourceMapper;
import cn.dutp.book.service.IDtbUserResourceService;
import cn.dutp.common.core.utils.StringUtils;
import cn.dutp.common.encrypt.utils.ShaUtils;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.util.Date;
import java.util.List;

/**
 * 用户资源库Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Service
public class DtbUserResourceServiceImpl extends ServiceImpl<DtbUserResourceMapper, DtbUserResource> implements IDtbUserResourceService {
    private static final Logger log = LoggerFactory.getLogger(DtbUserResourceServiceImpl.class);

    @Autowired
    private DtbUserResourceMapper dtbUserResourceMapper;

    // 从Nacos配置中读取存储路径
    @Value("${file.resource.base-path}")
    private String baseStoragePath;

    // 从Nacos配置中读取存储路径
    @Value("${file.resource.preview-path}")
    private String basePreviewPath;

    /**
     * 查询用户资源库
     *
     * @param resourceId 用户资源库主键
     * @return 用户资源库
     */
    @Override
    public DtbUserResource selectDtbUserResourceByResourceId(Long resourceId) {
        return this.getById(resourceId);
    }

    /**
     * 查询用户资源库列表
     *
     * @param dtbUserResource 用户资源库
     * @return 用户资源库
     */
    @Override
    public List<DtbUserResource> selectDtbUserResourceList(DtbUserResource dtbUserResource) {
        LambdaQueryWrapper<DtbUserResource> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotEmpty(dtbUserResource.getUserId())) {
            lambdaQueryWrapper.eq(DtbUserResource::getUserId
                    , dtbUserResource.getUserId());
        }
        if (ObjectUtil.isNotEmpty(dtbUserResource.getFileName())) {
            lambdaQueryWrapper.like(DtbUserResource::getFileName
                    , dtbUserResource.getFileName());
        }
        if (ObjectUtil.isNotEmpty(dtbUserResource.getFileUrl())) {
            lambdaQueryWrapper.eq(DtbUserResource::getFileUrl
                    , dtbUserResource.getFileUrl());
        }
        if (ObjectUtil.isNotEmpty(dtbUserResource.getFileType())) {
            lambdaQueryWrapper.eq(DtbUserResource::getFileType
                    , dtbUserResource.getFileType());
        }
        if (ObjectUtil.isNotEmpty(dtbUserResource.getFileSize())) {
            lambdaQueryWrapper.eq(DtbUserResource::getFileSize
                    , dtbUserResource.getFileSize());
        }
        if (ObjectUtil.isNotEmpty(dtbUserResource.getQuestionId())) {
            lambdaQueryWrapper.eq(DtbUserResource::getQuestionId
                    , dtbUserResource.getQuestionId());
        }
        if (ObjectUtil.isNotEmpty(dtbUserResource.getFolderId())) {
            lambdaQueryWrapper.eq(DtbUserResource::getFolderId
                    , dtbUserResource.getFolderId());
        }

        if (ObjectUtil.isNotEmpty(dtbUserResource.getDelFlag())) {
            lambdaQueryWrapper.eq(DtbUserResource::getDelFlag
                    , dtbUserResource.getDelFlag());
        }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增用户资源库
     *
     * @param dtbUserResource 用户资源库
     * @return 结果
     */
    @Override
    public boolean insertDtbUserResource(DtbUserResource dtbUserResource) {
        // 先保存获取resourceId
        boolean saved = this.save(dtbUserResource);
        if (!saved) {
            return false;
        }

        // 检查是否为需要下载的特殊文件类型（虚拟仿真、AR/VR、3D模型）
        if (dtbUserResource.getFileType() != null &&
                ("4".equals(dtbUserResource.getFileType()) ||
                        "5".equals(dtbUserResource.getFileType()))) {

            // 下载文件并存储到服务器
            String fileUrl = dtbUserResource.getFileUrl();

            String checkType = "zip";

            if (StringUtils.isNotEmpty(fileUrl) && fileUrl.contains(checkType)) {
                try {
                    // 下载文件逻辑，使用resourceId作为文件名
                    String newFileUrl = downloadAndSaveFile(fileUrl, dtbUserResource.getFileName(),
                            dtbUserResource.getFileType(), dtbUserResource.getResourceId());
                    // 更新为新的文件URL
                    dtbUserResource.setFileUrl(newFileUrl);
                    // 更新记录
                    this.updateById(dtbUserResource);
                } catch (Exception e) {
                    // 处理下载异常
                    log.error("文件下载失败: " + fileUrl, e);
                }
            }
        }

        return true;
    }

    /**
     * 下载并保存文件到服务器
     *
     * @param fileUrl    文件URL
     * @param fileName   文件名
     * @param fileType   文件类型
     * @param resourceId 资源ID
     * @return 保存后的文件路径
     */
    private String downloadAndSaveFile(String fileUrl, String fileName, String fileType, Long resourceId) throws Exception {
        log.info("开始下载文件: URL={}, 文件名={}, 资源ID={}", fileUrl, fileName, resourceId);

        // 根据文件类型确定具体的存储路径
        String specificPath;

        // 使用resourceId作为文件名
        specificPath = resourceId.toString();

        // 完整存储路径
        String fullStoragePath = baseStoragePath + File.separator + specificPath;

        String previewPath = basePreviewPath + File.separator + specificPath;

        log.info("文件存储路径: {}", fullStoragePath);

        // 创建保存目录
        File directory = new File(fullStoragePath);
        if (!directory.exists()) {
            log.info("创建存储目录: {}", fullStoragePath);
            directory.mkdirs();
        }

        // 处理文件名
        String extension = "";
        if (StringUtils.isEmpty(fileName)) {
            // 如果没有文件名，从URL中提取
            fileName = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
        }

        // 提取文件扩展名
        if (fileName.contains(".")) {
            extension = fileName.substring(fileName.lastIndexOf("."));
        }

        String newFileName = fileName;

        File targetFile = new File(directory, newFileName);

        // 下载文件
        log.info("开始从URL下载文件: {}", fileUrl);
        URL url = new URL(fileUrl);
        long startTime = System.currentTimeMillis();
        long fileSize = 0;
        try (InputStream inputStream = url.openStream();
             FileOutputStream outputStream = new FileOutputStream(targetFile)) {

            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
                fileSize += bytesRead;

                // 每下载5MB记录一次进度
                if (fileSize % (5 * 1024 * 1024) < 1024) {
                    log.info("文件下载进度: 已下载 {} MB", String.format("%.2f", fileSize / (1024.0 * 1024.0)));
                }
            }
        }
        long endTime = System.currentTimeMillis();
        log.info("文件下载完成: 大小={} 字节, 耗时={} 秒", fileSize, (endTime - startTime) / 1000.0);

        // 检查是否为ZIP文件，如果是则尝试解压，解压失败不影响整体流程
        if (extension.toLowerCase().endsWith(".zip")) {
            log.info("检测到ZIP文件，准备解压: {}", targetFile.getAbsolutePath());
            String extractDir = fullStoragePath;
            try {
                log.info("开始解压ZIP文件到: {}", extractDir);
                long unzipStartTime = System.currentTimeMillis();
                extractZipFile(targetFile, extractDir);
                long unzipEndTime = System.currentTimeMillis();
                log.info("ZIP文件解压完成，耗时: {} 秒", (unzipEndTime - unzipStartTime) / 1000.0);

                // 解压成功后删除原始ZIP文件
                if (targetFile.exists()) {
                    log.info("尝试删除原始ZIP文件: {}", targetFile.getAbsolutePath());
                    boolean deleted = targetFile.delete();
                    if (!deleted) {
                        log.warn("无法删除原始ZIP文件: " + targetFile.getAbsolutePath());
                    } else {
                        log.info("成功删除原始ZIP文件: " + targetFile.getAbsolutePath());
                    }
                }
                // 返回解压后的文件夹路径而不是原始文件路径
                log.info("返回解压后的文件夹路径: {}", fullStoragePath);
                return previewPath;
            } catch (Exception e) {
                // 记录错误但继续处理，不影响整体流程
                log.error("ZIP文件解压失败: " + targetFile.getAbsolutePath() + ", 错误: " + e.getMessage());
            }
        } else {
            previewPath = fileUrl;
        }

        // 返回新的文件路径（非ZIP文件或ZIP解压失败时使用）
        log.info("文件处理完成，返回路径: {}", previewPath);
        return previewPath;
    }

    /**
     * 解压ZIP文件
     *
     * @param zipFile ZIP文件
     * @param destDir 目标解压目录
     */
    private void extractZipFile(File zipFile, String destDir) throws Exception {
        File destDirFile = new File(destDir);
        if (!destDirFile.exists()) {
            destDirFile.mkdirs();
        }

        // 添加对ZIP文件有效性的检查
        if (!zipFile.exists() || zipFile.length() == 0) {
            log.warn("ZIP文件不存在或大小为0: " + zipFile.getAbsolutePath());
            return;
        }

        try {
            // 尝试使用Java自带的ZipFile来解压
            java.util.zip.ZipFile zip = new java.util.zip.ZipFile(zipFile);

            try {
                java.util.Enumeration<? extends java.util.zip.ZipEntry> entries = zip.entries();
                while (entries.hasMoreElements()) {
                    java.util.zip.ZipEntry entry = entries.nextElement();
                    String entryName = entry.getName();

                    // 安全检查：防止路径遍历攻击
                    if (entryName.contains("..")) {
                        log.warn("跳过可能的路径遍历条目: " + entryName);
                        continue;
                    }

                    File entryDestination = new File(destDir, entryName);

                    // 如果是目录，创建目录
                    if (entry.isDirectory()) {
                        entryDestination.mkdirs();
                    } else {
                        // 确保父目录存在
                        if (entryDestination.getParentFile() != null) {
                            entryDestination.getParentFile().mkdirs();
                        }

                        // 提取文件
                        try (InputStream in = zip.getInputStream(entry);
                             FileOutputStream out = new FileOutputStream(entryDestination)) {
                            byte[] buffer = new byte[1024];
                            int len;
                            while ((len = in.read(buffer)) > 0) {
                                out.write(buffer, 0, len);
                            }
                        } catch (Exception e) {
                            log.warn("解压单个文件失败: " + entryName + ", 错误: " + e.getMessage());
                        }
                    }
                }
            } finally {
                zip.close();
            }
        } catch (java.util.zip.ZipException ze) {
            // ZIP文件格式错误，尝试使用备选方法
            log.warn("标准ZIP解压方法失败，尝试使用备选方法: " + ze.getMessage());

            // 这里可以实现一个备选的ZIP解压方法
            // 例如使用Apache Commons Compress等第三方库
            log.error("ZIP文件格式不正确，无法解压: " + zipFile.getAbsolutePath());

            // 如果需要的话，您可以在此添加其他解压方法的实现
        }
    }

    /**
     * 修改用户资源库
     *
     * @param dtbUserResource 用户资源库
     * @return 结果
     */
    @Override
    public boolean updateDtbUserResource(DtbUserResource dtbUserResource) {
        return this.updateById(dtbUserResource);
    }

    /**
     * 批量删除用户资源库
     *
     * @param resourceIds 需要删除的用户资源库主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbUserResourceByResourceIds(List<Long> resourceIds) {
        return this.removeByIds(resourceIds);
    }

    @Override
    public int recycle(List<Long> resourceIds) {
        // 批量更新delFlag为1
        return this.update(new LambdaUpdateWrapper<DtbUserResource>()
                .in(DtbUserResource::getResourceId, resourceIds)
                .set(DtbUserResource::getDelFlag, 1).set(DtbUserResource::getUpdateTime, new Date())) ? 1 : 0;
    }

    @Override
    public int restore(List<Long> resourceIds) {

        int count = 0;
        // 获取要恢复的资源列表
        List<DtbUserResource> resources = dtbUserResourceMapper.selectUserResourceByIds(resourceIds);

        for (DtbUserResource resource : resources) {
            // 检查文件夹状态
            if (resource.getFolderId() != null && resource.getFolderId() > 0) {
                DtbUserResource folder = this.getById(resource.getFolderId());
                if (folder != null && "2".equals(folder.getDelFlag())) {
                    // 如果文件夹已删除,将folderId设为0
                    resource.setFolderId(0L);
                }
            }
            // 恢复文件
            resource.setDelFlag("0");

            count += dtbUserResourceMapper.updateDtbUserResource(resource) ? 1 : 0;
        }

        // 批量更新
        return count;
    }

    @Override
    public List<DtbUserResource> selectDtbUserResourceListByRecycle(DtbUserResource dtbUserResource) {
        return dtbUserResourceMapper.selectDtbUserResourceListByRecycle(dtbUserResource);

    }

    @Override
    public int permanentDeleteDtbUserResourceByResourceIds(List<Long> resourceIds) {
        int count = 0;
        // 获取要删除的资源列表
        List<DtbUserResource> resources = dtbUserResourceMapper.selectUserResourceByIds(resourceIds);

        for (DtbUserResource resource : resources) {
            // 设置删除标志为2
            resource.setDelFlag("2");
            count += dtbUserResourceMapper.updateDtbUserResource(resource) ? 1 : 0;
        }
        return count;
    }

    @Override
    public CheckResourceVO checkResourceIsExists(MultipartFile file) {
        CheckResourceVO checkResourceVO = new CheckResourceVO();
        checkResourceVO.setExist(false);
        Long userId = SecurityUtils.getUserId();
        try {
            String calculateHash = ShaUtils.sha256(file.getBytes());
            checkResourceVO.setHash(calculateHash);
            String url = dtbUserResourceMapper.queryByHash(calculateHash, userId);
            if (ObjectUtil.isNotEmpty(url)) {
                checkResourceVO.setExist(true);
                checkResourceVO.setUrl(url);
                log.info("userId: {},上传重复图片, url: {}, calculateHash: {}", userId, url, calculateHash);
            }
        } catch (Exception e) {
            log.error("计算文件哈希值失败, ", e);
        }
        return checkResourceVO;
    }


}

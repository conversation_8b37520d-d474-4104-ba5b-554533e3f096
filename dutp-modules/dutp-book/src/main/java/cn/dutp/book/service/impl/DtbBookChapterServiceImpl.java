package cn.dutp.book.service.impl;

import cn.dutp.book.domain.*;
import cn.dutp.book.domain.vo.DtbBookChapterCatalogVO;
import cn.dutp.book.domain.vo.DtbBookChapterTreeVO;
import cn.dutp.book.domain.vo.DtbBookChapterVO;
import cn.dutp.book.domain.vo.ResourceVO;
import cn.dutp.book.mapper.*;
import cn.dutp.book.service.IDtbBookChapterService;
import cn.dutp.book.service.IDtbBookResourceService;
import cn.dutp.common.core.constant.HttpStatus;
import cn.dutp.common.core.domain.UploadFileDto;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.utils.AliyunOssStsUtil;
import cn.dutp.common.core.utils.DocxToJsonConverter;
import cn.dutp.common.core.utils.JsonToDocxConverter;
import cn.dutp.common.core.utils.PdfConver;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.mongo.service.MongoService;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBook;
import cn.dutp.domain.DtbBookGroup;
import cn.dutp.message.api.RemoteUserMessageService;
import cn.dutp.message.api.domain.DutpUserMessage;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static cn.dutp.book.service.impl.DtbBookChapterContentServiceImpl.BOOK_CHAPTER_CONTENT;
import static cn.dutp.common.core.utils.TreeUtil.makeTree;
import static cn.dutp.common.core.utils.zipUtil.generateZip;

/**
 * 数字教材章节目录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-30
 */
@Slf4j
@Service
public class DtbBookChapterServiceImpl extends ServiceImpl<DtbBookChapterMapper, DtbBookChapter> implements IDtbBookChapterService {
    @Autowired
    private DtbBookChapterMapper dtbBookChapterMapper;

    @Autowired
    private DtbBookChapterCatalogMapper chapterCatalogMapper;

    @Autowired
    private DtbBookChapterEditorMapper bookChapterEditorMapper;

    @Autowired
    private DtbBookBookMapper bookMapper;

    @Autowired
    private RemoteUserMessageService remoteUserMessageService;

    @Autowired
    private DtbBookGroupMapper bookGroupMapper;

    @Autowired
    private DtbBookChapterAuditLogMapper auditLogMapper;

    @Autowired
    private MongoService mongoService;

    @Autowired
    private DocxToJsonConverter docxToJsonConverter;

    @Autowired
    private PdfConver pdfToJsonConverter;

    @Autowired
    private DutpTaskMapper taskMapper;

    @Autowired
    private IDtbBookResourceService dtbBookResourceService;

    @Autowired
    private DtbBookCommonMapper commonMapper;

    @Autowired
    private DtbUserBookMapper dtbUserBookMapper;

    @Autowired
    private AliyunOssStsUtil aliyunOssStsUtil;

    @Autowired
    private DtbBookTemplateMapper templateMapper;
    // 导出教材的位置
    @Value("${file.export.path}")
    private String fileExportPath;

    ExecutorService customExecutor = Executors.newFixedThreadPool(10);

    /**
     * 查询数字教材章节目录
     *
     * @param chapterId 数字教材章节目录主键
     * @return 数字教材章节目录
     */
    @Override
    public DtbBookChapter selectDtbBookChapterByChapterId(Long chapterId) {
        DtbBookChapter bookChapter = this.getOne(new LambdaQueryWrapper<DtbBookChapter>()
                .select(DtbBookChapter::getChapterId, DtbBookChapter::getBookId, DtbBookChapter::getFree,
                        DtbBookChapter::getChapterName)
                .eq(DtbBookChapter::getChapterId, chapterId));
        if (ObjectUtil.isNotEmpty(bookChapter)) {
            List<DtbBookChapterEditor> chapterEditorList = bookChapterEditorMapper.selectList(new LambdaQueryWrapper<DtbBookChapterEditor>()
                    .select(DtbBookChapterEditor::getRoleType, DtbBookChapterEditor::getUserId)
                    .eq(DtbBookChapterEditor::getChapterId, chapterId));
            if (ObjectUtil.isNotEmpty(chapterEditorList)) {
                // 编辑者
                List<Long> editorIds = new ArrayList<>();
                // 查看者
                List<Long> viewerIds = new ArrayList<>();
                for (DtbBookChapterEditor dtbBookChapterEditor : chapterEditorList) {
                    if (dtbBookChapterEditor.getRoleType() == 1) {
                        editorIds.add(dtbBookChapterEditor.getUserId());
                    } else if (dtbBookChapterEditor.getRoleType() == 2) {
                        viewerIds.add(dtbBookChapterEditor.getUserId());
                    }
                }
                bookChapter.setEditorIds(editorIds);
                bookChapter.setViewerIds(viewerIds);
            }
        }
        return bookChapter;
    }

    /**
     * 查询数字教材章节目录列表
     *
     * @param dtbBookChapter 数字教材章节目录
     * @return 数字教材章节目录
     */
    @Override
    public List<DtbBookChapterTreeVO> selectDtbBookChapterList(DtbBookChapter dtbBookChapter) {
        Long bookId = dtbBookChapter.getBookId();
        if (ObjectUtil.isEmpty(bookId)) {
            return new ArrayList<>();
        }
        List<DtbBookChapterTreeVO> dtbBookChapterTreeVOList = dtbBookChapterMapper.adminQueryBookChapterTreeByBookId(bookId);
        if (ObjectUtil.isEmpty(dtbBookChapterTreeVOList)) {
            return new ArrayList<>();
        }
        Long toUserId = bookGroupMapper.queryGroupUserByType(1, bookId);
        Long userId = SecurityUtils.getUserId();
        dtbBookChapterTreeVOList.forEach(item -> {
            // 判断当前登录人对此章节的权限
            List<DtbBookChapterEditor> chapterEditorList = bookChapterEditorMapper.selectList(new LambdaQueryWrapper<DtbBookChapterEditor>()
                    .select(DtbBookChapterEditor::getRoleType)
                    .eq(DtbBookChapterEditor::getChapterId, item.getChapterId())
                    .eq(DtbBookChapterEditor::getUserId, userId));
            int[] roleTypeList = chapterEditorList.stream().mapToInt(DtbBookChapterEditor::getRoleType).toArray();
            for (int roleType : roleTypeList) {
                if (roleType == 1) {
                    item.setIsEditor(true);
                } else if (roleType == 2) {
                    item.setIsViewer(true);
                }
            }
            List<DtbBookChapterTreeVO> dtbBookChapterCatalogList = chapterCatalogMapper.queryBookChapterCatalogTreeByChapterId(item.getChapterId());
            // 构建树
            List<DtbBookChapterTreeVO> dtbBookChapterCatalogs = makeTree(dtbBookChapterCatalogList,
                    x -> x.getParentId() == 0l, (x, y) ->
                            x.getCatalogId().equals(y.getParentId()),
                    DtbBookChapterTreeVO::setChildren);
            // 第一级增加父Id
            dtbBookChapterCatalogs.forEach(o -> o.setParentId(item.getChapterId()));
            item.setChildren(dtbBookChapterCatalogs);
            if (item.getChapterStatus() == 2) {
                // 查询是否进行撤销操作
                DtbBookChapterAuditLog chapterAuditLog = new DtbBookChapterAuditLog();
                chapterAuditLog.setBookId(item.getBookId());
                chapterAuditLog.setChapterId(item.getChapterId());
                chapterAuditLog.setAuditorId(toUserId);
                chapterAuditLog.setChapterStatus(1);
                chapterAuditLog.setAuditType(2);
                chapterAuditLog.setVersionId(item.getCurrentVersionId());
                DtbBookChapterAuditLog auditLog = auditLogMapper.selectWaitAuditLog(chapterAuditLog);
                if (ObjectUtil.isNotEmpty(auditLog)) {
                    item.setRevoked(1);
                } else {
                    item.setRevoked(2);
                }
            }
        });
        return dtbBookChapterTreeVOList;
    }

    /**
     * 查询数字教材章节目录列表 给待办中心用的 涉及到修正数据
     *
     * @param dtbBookChapter 数字教材章节目录
     * @return 数字教材章节目录
     */
    @Override
    public List<DtbBookChapterTreeVO> selectProcessChapterList(DtbBookChapter dtbBookChapter) {
        Long bookId = dtbBookChapter.getBookId();
        if (ObjectUtil.isEmpty(bookId)) {
            return new ArrayList<>();
        }
        List<DtbBookChapterTreeVO> dtbBookChapterTreeVOList = dtbBookChapterMapper.adminQueryProcessChapterTreeByBookId(bookId);
        if (ObjectUtil.isEmpty(dtbBookChapterTreeVOList)) {
            return new ArrayList<>();
        }
        Long toUserId = bookGroupMapper.queryGroupUserByType(1, bookId);
        Long userId = SecurityUtils.getUserId();
        dtbBookChapterTreeVOList.forEach(item -> {
            // 判断当前登录人对此章节的权限
            List<DtbBookChapterEditor> chapterEditorList = bookChapterEditorMapper.selectList(new LambdaQueryWrapper<DtbBookChapterEditor>()
                    .select(DtbBookChapterEditor::getRoleType)
                    .eq(DtbBookChapterEditor::getChapterId, item.getChapterId())
                    .eq(DtbBookChapterEditor::getUserId, userId));
            int[] roleTypeList = chapterEditorList.stream().mapToInt(DtbBookChapterEditor::getRoleType).toArray();
            for (int roleType : roleTypeList) {
                if (roleType == 1) {
                    item.setIsEditor(true);
                } else if (roleType == 2) {
                    item.setIsViewer(true);
                }
            }
            List<DtbBookChapterTreeVO> dtbBookChapterCatalogList = chapterCatalogMapper.queryBookChapterCatalogTreeByChapterId(item.getChapterId());
            // 构建树
            List<DtbBookChapterTreeVO> dtbBookChapterCatalogs = makeTree(dtbBookChapterCatalogList,
                    x -> x.getParentId() == 0l, (x, y) ->
                            x.getCatalogId().equals(y.getParentId()),
                    DtbBookChapterTreeVO::setChildren);
            // 第一级增加父Id
            dtbBookChapterCatalogs.forEach(o -> o.setParentId(item.getChapterId()));
            item.setChildren(dtbBookChapterCatalogs);
            if (item.getChapterStatus() == 2) {
                // 查询是否进行撤销操作
                DtbBookChapterAuditLog chapterAuditLog = new DtbBookChapterAuditLog();
                chapterAuditLog.setBookId(item.getBookId());
                chapterAuditLog.setChapterId(item.getChapterId());
                chapterAuditLog.setAuditorId(toUserId);
                chapterAuditLog.setChapterStatus(1);
                chapterAuditLog.setAuditType(2);
                chapterAuditLog.setVersionId(item.getCurrentVersionId());
                DtbBookChapterAuditLog auditLog = auditLogMapper.selectWaitAuditLog(chapterAuditLog);
                if (ObjectUtil.isNotEmpty(auditLog)) {
                    item.setRevoked(1);
                } else {
                    item.setRevoked(2);
                }
            }
        });
        return dtbBookChapterTreeVOList;
    }

    /**
     * 新增数字教材章节目录
     *
     * @param dtbBookChapter 数字教材章节目录
     * @return 结果
     */
    @Override
    public boolean insertDtbBookChapter(DtbBookChapter dtbBookChapter) {
        // 查询版本ID
        Long bookId = dtbBookChapter.getBookId();
        DtbBook book = bookMapper.selectOne(new LambdaQueryWrapper<DtbBook>()
                .select(DtbBook::getCurrentVersionId)
                .eq(DtbBook::getBookId, bookId));
        Integer sort = dtbBookChapterMapper.queryMaxSort(bookId);
        dtbBookChapter.setSort(sort);
        dtbBookChapter.setVersionId(book.getCurrentVersionId());
        Long templateId = dtbBookChapterMapper.queryTemplateIdByBookId(bookId);
        if (ObjectUtil.isEmpty(templateId)) {
            templateId = templateMapper.queryDefaultTemplateId();
        }
        dtbBookChapter.setTemplateId(templateId);
        boolean save = this.save(dtbBookChapter);
        // Long chapterId = dtbBookChapter.getChapterId();
        // String chapterName = dtbBookChapter.getChapterName();

        // 更新总页数
        // DtbBookChapter bookChapter = new DtbBookChapter();
        // bookChapter.setChapterId(chapterId);
        // bookChapter.setChapterTotalPage(1);
        // dtbBookChapterMapper.updateById(bookChapter);

        // Update update = new Update();
        // update.set("chapterId", chapterId);
        // update.set("content", "{\"type\":\"doc\",\"content\":[{\"type\":\"page\",\"attrs\":{\"id\":\"i71qalfs\",\"extend\":false,\"class\":\"bellCss\",\"HTMLAttributes\":null,\"pageNumber\":1,\"force\":false,\"slots\":{}},\"content\":[{\"type\":\"paragraph\",\"attrs\":{\"id\":\"41dvqcik\",\"extend\":false,\"class\":\"bellCss\",\"columnCount\":0,\"indent\":null,\"textAlign\":\"center\",\"lineHeight\":1,\"margin\":{},\"backgroundImage\":\"\",\"containerColor\":\"\",\"backgroundSize\":\"\",\"backgroundBorder\":\"\"},\"content\":[{\"type\":\"text\",\"marks\":[{\"type\":\"textStyle\",\"attrs\":{\"fontFamily\":\"helvetica neue, helvetica, pingfang sc, hiragino sans gb, microsoft yahei, simsun, sans-serif\",\"fontSize\":\"20px\",\"color\":\"rgb(51,54,57)\",\"letterSpacing\":\"normal\"}},{\"type\":\"bold\"}],\"text\":\"" + chapterName + "\"}]}]}]}");
        // mongoService.updateOne(BOOK_CHAPTER_CONTENT, new Query(Criteria.where("chapterId").is(chapterId)), update, DtbBookChapterContent.class);

        if (save) {
            List<Long> editorIds = dtbBookChapter.getEditorIds();
            // 新增编辑发送消息
            sentMessageForEditor(editorIds, dtbBookChapter.getChapterId());
            // 添加编写者和查看者
            updateChapterUser(dtbBookChapter);
        }
        return save;
    }

    /**
     * 修改数字教材章节目录
     *
     * @param dtbBookChapter 数字教材章节目录
     * @return 结果
     */
    @Override
    public boolean updateDtbBookChapter(DtbBookChapter dtbBookChapter) {
        boolean success = this.updateById(dtbBookChapter);
        if (success) {

            List<DtbBookChapterEditor> chapterEditorList = bookChapterEditorMapper.selectList(new LambdaQueryWrapper<DtbBookChapterEditor>()
                    .select(DtbBookChapterEditor::getUserId)
                    .eq(DtbBookChapterEditor::getChapterId, dtbBookChapter.getChapterId())
                    .eq(DtbBookChapterEditor::getRoleType, 1));
            List<Long> editorIds = dtbBookChapter.getEditorIds();
            if (ObjectUtil.isNotEmpty(chapterEditorList) && ObjectUtil.isNotEmpty(editorIds)) {
                List<Long> oldEditorIds = chapterEditorList.stream().map(DtbBookChapterEditor::getUserId).collect(Collectors.toList());
                List<Long> sentMessageEditors = editorIds.stream().filter(o -> !oldEditorIds.contains(o)).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(sentMessageEditors)) {
                    // 新增编辑发送消息
                    sentMessageForEditor(sentMessageEditors, dtbBookChapter.getChapterId());
                }
            }
            // 删除编写者和查看者
            bookChapterEditorMapper.deleteAllByChapterId(dtbBookChapter.getChapterId());

            // 添加编写者和查看者
            updateChapterUser(dtbBookChapter);
        }
        return success;
    }

    /**
     * 发送消息新增编辑
     *
     * @param sentMessageEditors
     * @param chapterId
     */
    private void sentMessageForEditor(List<Long> sentMessageEditors, Long chapterId) {
        if (ObjectUtil.isEmpty(sentMessageEditors)) {
            return;
        }
        Long fromUserId = SecurityUtils.getUserId();
        DtbBookChapter chapter = dtbBookChapterMapper.queryBookByChapterId(chapterId);
        for (Long userId : sentMessageEditors) {
            DutpUserMessage dutpUserMessage = new DutpUserMessage();
            dutpUserMessage.setContent("您好，您已有本章编写权限。【章节名称：" + chapter.getChapterName() + "; 教材名称：" +
                    chapter.getBookName() + ";教材编号：" + chapter.getBookNo() + "】。");
            dutpUserMessage.setTitle("教材编写任务分配提醒");
            dutpUserMessage.setFromUserId(fromUserId);
            dutpUserMessage.setToUserId(userId);
            dutpUserMessage.setMessageType(1);
            dutpUserMessage.setFromUserType(1);
            dutpUserMessage.setToUserType(1);
            remoteUserMessageService.addMessage(dutpUserMessage);
        }
    }

    /**
     * 更新章节编写者和查看者 用户
     *
     * @param dtbBookChapter
     */
    private void updateChapterUser(DtbBookChapter dtbBookChapter) {
        List<Long> editorIds = dtbBookChapter.getEditorIds();
        if (ObjectUtil.isNotEmpty(editorIds)) {
            bookChapterEditorMapper.batchSave(editorIds, dtbBookChapter.getBookId(), dtbBookChapter.getChapterId(), 1);
        }
        List<Long> viewerIds = dtbBookChapter.getViewerIds();
        if (ObjectUtil.isNotEmpty(viewerIds)) {
            bookChapterEditorMapper.batchSave(viewerIds, dtbBookChapter.getBookId(), dtbBookChapter.getChapterId(), 2);
        }
    }

    /**
     * 批量删除数字教材章节目录
     *
     * @param chapterIds 需要删除的数字教材章节目录主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookChapterByChapterIds(List<Long> chapterIds) {
        Long userId = SecurityUtils.getUserId();
        int row = 1;
        Date nowDate = new Date();
        for (Long chapterId : chapterIds) {
            DtbBookChapter dtbBookChapter = new DtbBookChapter();
            dtbBookChapter.setChapterId(chapterId);
            dtbBookChapter.setDelUserId(userId);
            dtbBookChapter.setUpdateTime(nowDate);
            row *= dtbBookChapterMapper.delChapter(dtbBookChapter);
        }
        return row >= 1;
    }

    /**
     * 获取数字教材章节目录列表
     *
     * @param dtbBookChapter
     * @return
     */
    @Override
    public List<DtbBookChapterTreeVO> queryBookChapterList(DtbBookChapter dtbBookChapter) {
        Long chapterId = dtbBookChapter.getChapterId();
        Long bookId = dtbBookChapterMapper.queryBookIdByChapterId(chapterId);
        if (ObjectUtil.isEmpty(bookId)) {
            // throw new ServiceException("章节不存在");
            return new ArrayList<>();
        }
        List<DtbBookChapterTreeVO> dtbBookChapterTreeVOList = dtbBookChapterMapper.queryBookChapterTreeByBookId(bookId);
        if (ObjectUtil.isEmpty(dtbBookChapterTreeVOList)) {
            return new ArrayList<>();
        }
        dtbBookChapterTreeVOList.forEach(item -> {
            List<DtbBookChapterTreeVO> dtbBookChapterCatalogList = chapterCatalogMapper.queryBookChapterCatalogTreeByChapterId(item.getChapterId());
            // 构建树
            List<DtbBookChapterTreeVO> dtbBookChapterCatalogs = makeTree(dtbBookChapterCatalogList,
                    x -> x.getParentId() == 0l, (x, y) ->
                            x.getCatalogId().equals(y.getParentId()),
                    DtbBookChapterTreeVO::setChildren);
            // 第一级增加父Id
            dtbBookChapterCatalogs.forEach(o -> o.setParentId(item.getChapterId()));
            item.setChildren(dtbBookChapterCatalogs);
        });
        return dtbBookChapterTreeVOList;
    }

    @Override
    public List<DtbBookChapter> listForRecycle(DtbBookChapter dtbBookChapter) {
        return dtbBookChapterMapper.listForRecycle(dtbBookChapter);
    }

    @Override
    public int recycleChapter(DtbBookChapter dtbBookChapter) {
        return dtbBookChapterMapper.recycleChapter(dtbBookChapter);
    }

    @Override
    public List<DtbBookChapter> listForSort(DtbBookChapter dtbBookChapter) {
        return dtbBookChapterMapper.listForSort(dtbBookChapter);
    }

    @Override
    public int updateChapterSort(List<DtbBookChapter> dtbBookChapterList) {
        int row = 1;
        for (DtbBookChapter dtbBookChapter : dtbBookChapterList) {
            row = dtbBookChapterMapper.updateById(dtbBookChapter);
        }
        return row;
    }

    @Override
    public int edit(DtbBookChapter dtbBookChapter) {
        Integer chapterStatus = dtbBookChapter.getChapterStatus();

        if (ObjectUtil.isNotEmpty(chapterStatus)) {
            DtbBookChapter bookChapter = getOne(new LambdaQueryWrapper<DtbBookChapter>()
                    .select(DtbBookChapter::getChapterId, DtbBookChapter::getChapterStatus, DtbBookChapter::getBookId,
                            DtbBookChapter::getChapterName)
                    .eq(DtbBookChapter::getChapterId, dtbBookChapter.getChapterId()));

            if (chapterStatus == 1 && (bookChapter.getChapterStatus() == 0 || bookChapter.getChapterStatus() == 3)) {
                // 提交
                // 添加消息
                Long userId = SecurityUtils.getUserId();
                DtbBook book = bookMapper.selectOne(new LambdaQueryWrapper<DtbBook>()
                        .select(DtbBook::getBookNo, DtbBook::getBookName, DtbBook::getCurrentVersionId)
                        .eq(DtbBook::getBookId, bookChapter.getBookId()));
                Long toUserId = bookGroupMapper.queryGroupUserByType(1, bookChapter.getBookId());
                if (ObjectUtil.isEmpty(toUserId)) {
                    throw new ServiceException("书稿联系人为空，不能提交");
                }
                Integer count = bookChapterEditorMapper.selectCount(new LambdaQueryWrapper<DtbBookChapterEditor>()
                        .eq(DtbBookChapterEditor::getRoleType, 1)
                        .eq(DtbBookChapterEditor::getChapterId, bookChapter.getChapterId()));
                if (ObjectUtil.isEmpty(count) || count <= 0) {
                    throw new ServiceException("编写者不能为空");
                }

                // 添加审核
                DtbBookChapterAuditLog chapterAuditLog = new DtbBookChapterAuditLog();
                chapterAuditLog.setBookId(bookChapter.getBookId());
                chapterAuditLog.setChapterId(dtbBookChapter.getChapterId());
                chapterAuditLog.setAuditorId(toUserId);
                chapterAuditLog.setChapterStatus(1);
                chapterAuditLog.setAuditType(1);
                chapterAuditLog.setPromoterUserId(userId);
                chapterAuditLog.setVersionId(book.getCurrentVersionId());
                auditLogMapper.insert(chapterAuditLog);

                DutpUserMessage dutpUserMessage = new DutpUserMessage();
                dutpUserMessage.setContent("您好，您有一条新的章节提交申请待处理。【章节名称：" + bookChapter.getChapterName() + "; 教材名称：" +
                        book.getBookName() + ";教材编号：" + book.getBookNo() + "】。");
                dutpUserMessage.setTitle("章节审核提醒");
                dutpUserMessage.setFromUserId(userId);
                dutpUserMessage.setToUserId(toUserId);
                dutpUserMessage.setMessageType(1);
                dutpUserMessage.setFromUserType(1);
                dutpUserMessage.setToUserType(1);
                dutpUserMessage.setBusinessId(chapterAuditLog.getLogId());
                remoteUserMessageService.addMessage(dutpUserMessage);

                // 更新进度条
                dtbBookChapter.setCompleteRate(new BigDecimal("100"));
            } else if (chapterStatus == 0 && bookChapter.getChapterStatus() == 1) {
                // 取消
                Long userId = SecurityUtils.getUserId();
                DtbBook book = bookMapper.selectOne(new LambdaQueryWrapper<DtbBook>()
                        .select(DtbBook::getBookNo, DtbBook::getBookName, DtbBook::getCurrentVersionId)
                        .eq(DtbBook::getBookId, bookChapter.getBookId()));
                Long toUserId = bookGroupMapper.queryGroupUserByType(1, bookChapter.getBookId());
                // 取消审核记录
                DtbBookChapterAuditLog chapterAuditLog = new DtbBookChapterAuditLog();
                chapterAuditLog.setBookId(bookChapter.getBookId());
                chapterAuditLog.setChapterId(dtbBookChapter.getChapterId());
                chapterAuditLog.setAuditorId(toUserId);
                chapterAuditLog.setChapterStatus(1);
                chapterAuditLog.setAuditType(1);
                // chapterAuditLog.setPromoterUserId(userId);
                chapterAuditLog.setVersionId(book.getCurrentVersionId());
                DtbBookChapterAuditLog auditLog = auditLogMapper.selectWaitAuditLog(chapterAuditLog);
                if (ObjectUtil.isEmpty(auditLog)) {
                    log.error("取消审核记录失败,查询条件 {}", chapterAuditLog);
                    throw new ServiceException("取消审核记录失败，没有找到审核记录");
                } else {
                    auditLog.setChapterStatus(4);
                    auditLogMapper.updateById(auditLog);
                }
                // 添加消息
                DutpUserMessage dutpUserMessage = new DutpUserMessage();
                dutpUserMessage.setContent("您好，章节提交申请已取消。【章节名称：" + bookChapter.getChapterName() + "; 教材名称：" +
                        book.getBookName() + ";教材编号：" + book.getBookNo() + "】。");
                dutpUserMessage.setTitle("章节审核提醒");
                dutpUserMessage.setFromUserId(userId);
                dutpUserMessage.setToUserId(toUserId);
                dutpUserMessage.setMessageType(1);
                dutpUserMessage.setFromUserType(1);
                dutpUserMessage.setToUserType(1);
                remoteUserMessageService.addMessage(dutpUserMessage);


            } else {
                log.error("当前用户：{}，违规更改章节状态为：{}, 当前章节状态为：{}", SecurityUtils.getUserId(), chapterStatus, bookChapter.getChapterStatus());
                throw new ServiceException("操作失败,当前章节已经改变，请刷新页面，重新操作");
            }
        }
        return dtbBookChapterMapper.updateById(dtbBookChapter);
    }

    @Override
    public List<DtbBookChapter> listForSelect(DtbBookChapter dtbBookChapter) {
        return dtbBookChapterMapper.dtbBookChapterMapper(dtbBookChapter);
    }

    /**
     * 获取数字教材章节目录列表
     *
     * @param dtbBookChapter
     * @return
     */
    @Override
    public List<DtbBookChapter> queryBookChapterListByBookDetail(DtbBookChapter dtbBookChapter) {
        if (ObjectUtil.isEmpty(dtbBookChapter.getBookId())) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<DtbBookChapter> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DtbBookChapter::getBookId, dtbBookChapter.getBookId())
                .eq(DtbBookChapter::getDelFlag, 0)
                .orderByAsc(DtbBookChapter::getSort);
        List<DtbBookChapter> dtbBookChapterTreeVOList = dtbBookChapterMapper.queryBookChapterDataList(dtbBookChapter);
        if (ObjectUtil.isEmpty(dtbBookChapterTreeVOList)) {
            return new ArrayList<>();
        }
        dtbBookChapterTreeVOList.forEach(item -> {
            LambdaQueryWrapper<DtbBookChapterCatalog> catalogLambdaQueryWrapper = new LambdaQueryWrapper<>();
            catalogLambdaQueryWrapper.eq(DtbBookChapterCatalog::getChapterId, item.getChapterId());
            List<DtbBookChapterCatalog> dtbBookChapterCatalogList = chapterCatalogMapper.selectList(catalogLambdaQueryWrapper);
            dtbBookChapterCatalogList.forEach(catalogItem -> {
                catalogItem.setFree(item.getFree());
                catalogItem.setChapterName(catalogItem.getTitle());
            });
            item.setChildren(dtbBookChapterCatalogList);
        });
        return dtbBookChapterTreeVOList;
    }


    @Override
    public boolean updateCharpterFree(List<DtbBookChapter> dtbBookChapterList) {
        return this.updateBatchById(dtbBookChapterList);
    }

    @Override
    public AjaxResult getBookChapters(Long bookId) {
        Long userId = SecurityUtils.getUserId();
        DtbBook dtbBook = bookMapper.selectById(bookId);


        LambdaQueryWrapper<DtbUserBook> dtbUserBookLambdaQueryWrapper = new LambdaQueryWrapper<>();
        // 如果是副教材 查询关联的主教材是否购买
        if (dtbBook.getMasterFlag() == 3) {
            dtbUserBookLambdaQueryWrapper.eq(DtbUserBook::getBookId, dtbBook.getMasterBookId())
                    .eq(DtbUserBook::getUserId, userId)
                    .gt(DtbUserBook::getExpireDate, new Date());
        } else {
            dtbUserBookLambdaQueryWrapper.eq(DtbUserBook::getBookId, bookId)
                    .eq(DtbUserBook::getUserId, userId)
                    .gt(DtbUserBook::getExpireDate, new Date());
        }
        DtbUserBook dtbUserBook = dtbUserBookMapper.selectOne(dtbUserBookLambdaQueryWrapper);
        Long versionId;
        Integer hasBuy = 0;
        Integer free = 1; // 1付费 2免费
        // 没买
        if (ObjectUtil.isNotEmpty(userId) && userId.longValue() != 0l && ObjectUtil.isNotNull(dtbUserBook)) {
            if (dtbBook.getMasterFlag() == 3) {
                versionId = dtbBook.getCurrentVersionId();
            } else {
                versionId = dtbUserBook.getVersionId();
            }
            hasBuy = 1;
            free = 2;
        } else {
            versionId = dtbBook.getCurrentVersionId();
        }
        List<DtbBookChapterVO> chapters = dtbBookChapterMapper.getBookChapterByReader(bookId, versionId);
        for (DtbBookChapterVO dtbBookChapterVO : chapters) {
            List<DtbBookChapterCatalogVO> catalogVOS = chapterCatalogMapper.getBookChapterCatalogByReader(dtbBookChapterVO.getChapterId());
            DtbBookChapter chapter = dtbBookChapterMapper.selectById(dtbBookChapterVO.getChapterId());
            // 设置章节的页数
            dtbBookChapterVO.setChapterTotalPages(chapter.getChapterTotalPage());
            dtbBookChapterVO.setCatalogs(catalogVOS);
            dtbBookChapterVO.setHasBuy(hasBuy);
            if (chapter.getFree() == 2) { // 免费
                dtbBookChapterVO.setFree(2);
            } else {
                dtbBookChapterVO.setFree(free);
            }
        }
        chapters.forEach(dtbBookChapterVO -> {
            List<DtbBookChapterCatalogVO> catalogVOS = dtbBookChapterVO.getCatalogs();
            List<DtbBookChapterCatalogVO> dtbBookChapterCatalogs = makeTree(catalogVOS,
                    x -> x.getParentId() == 0l, (x, y) ->
                            x.getCatalogId().equals(y.getParentId()),
                    DtbBookChapterCatalogVO::setChildren);
            dtbBookChapterVO.setCatalogs(dtbBookChapterCatalogs);
        });
        return AjaxResult.success(chapters);
    }


    /**
     * 简版阅读器
     *
     * @param bookId
     * @return
     */
    @Override
    public AjaxResult getBookChaptersSimple(Long bookId) {
        DtbBook dtbBook = bookMapper.selectById(bookId);

        Integer hasBuy = 1;
        Integer free = 2; // 1付费 2免费
        Long versionId = dtbBook.getCurrentVersionId();

        List<DtbBookChapterVO> chapters = dtbBookChapterMapper.getBookChapterByReader(bookId, versionId);
        for (DtbBookChapterVO dtbBookChapterVO : chapters) {
            List<DtbBookChapterCatalogVO> catalogVOS = chapterCatalogMapper.getBookChapterCatalogByReader(dtbBookChapterVO.getChapterId());
            DtbBookChapter chapter = dtbBookChapterMapper.selectById(dtbBookChapterVO.getChapterId());
            // 设置章节的页数
            dtbBookChapterVO.setChapterTotalPages(chapter.getChapterTotalPage());
            dtbBookChapterVO.setCatalogs(catalogVOS);
            dtbBookChapterVO.setHasBuy(hasBuy);
            dtbBookChapterVO.setFree(2);
        }
        chapters.forEach(dtbBookChapterVO -> {
            List<DtbBookChapterCatalogVO> catalogVOS = dtbBookChapterVO.getCatalogs();
            List<DtbBookChapterCatalogVO> dtbBookChapterCatalogs = makeTree(catalogVOS,
                    x -> x.getParentId() == 0l, (x, y) ->
                            x.getCatalogId().equals(y.getParentId()),
                    DtbBookChapterCatalogVO::setChildren);
            dtbBookChapterVO.setCatalogs(dtbBookChapterCatalogs);
        });
        return AjaxResult.success(chapters);
    }

    @Override
    public DtbBookChapter chapterInfo(Long chapterId) {
        DtbBookChapter bookChapter = dtbBookChapterMapper.chapterInfo(chapterId);
        if (ObjectUtil.isEmpty(bookChapter)) {
            throw new ServiceException("章节不存在");
        }
        return bookChapter;
    }

    @Override
    public List<DtbBookChapter> queryChapterList(Long bookId) {
        DtbBookChapter dtbBookChapter = new DtbBookChapter();
        dtbBookChapter.setBookId(bookId);
        return dtbBookChapterMapper.dtbBookChapterMapper(dtbBookChapter);
    }

    @Override
    public List<DtbBookChapter> queryChapterListLastVersion(Long bookId) {
        DtbBookChapter dtbBookChapter = new DtbBookChapter();
        dtbBookChapter.setBookId(bookId);
        return dtbBookChapterMapper.dtbBookChapterMapperLastVersion(dtbBookChapter);
    }

    @Override
    public int updateChapterTemplate(DtbBookChapter chapter) {
        if (ObjectUtil.isEmpty(chapter.getTemplateId())) {
            throw new ServiceException("templateId不能为空");
        }
        Long chapterId = chapter.getChapterId();
        if (ObjectUtil.isEmpty(chapterId)) {
            throw new ServiceException("chapterId不能为空");
        }

        Long versionId = dtbBookChapterMapper.queryVersionIddByChapterId(chapterId);
        chapter.setVersionId(versionId);
        if (ObjectUtil.isEmpty(versionId)) {
            throw new ServiceException("chapterId不存在");
        }
        return dtbBookChapterMapper.updateChapterTemplate(chapter);
    }

    @Override
    public int importChapter(MultipartFile file, Long chapterId) {
        if (ObjectUtil.isEmpty(file) || ObjectUtil.isEmpty(chapterId)) {
            throw new ServiceException("word文件或者chapterId为空");
        }

        String fileName = file.getOriginalFilename();
        if (!fileName.endsWith(".docx")) {
            throw new ServiceException("文件格式不正确，请上传.docx文件");
        }

        InputStream wordFileInputStream = null;
        try {
            wordFileInputStream = file.getInputStream();
        } catch (IOException e) {
            log.info("读取文件流异常，", e);
            throw new ServiceException("读取文件错误");
        }

        DtbBookChapter chapter = dtbBookChapterMapper.queryBookByChapterId(chapterId);

        if (ObjectUtil.isEmpty(chapter)) {
            throw new ServiceException("章节不存在");
        }

        Long userId = SecurityUtils.getUserId();
        // 生成任务
        DutpTask dutpTask = new DutpTask();
        dutpTask.setTaskType(4);
        dutpTask.setTaskContent("导入章节内容");
        dutpTask.setRemark("章节名称: " + chapter.getChapterName());
        dutpTask.setDataId(chapterId);
        dutpTask.setUserId(userId);
        dutpTask.setTaskRate(0);
        dutpTask.setStartTime(new Date());
        dutpTask.setTaskState(1);
        int success = taskMapper.insert(dutpTask);


        // 新开线程执行任务
        InputStream finalWordFileInputStream = wordFileInputStream;

        CompletableFuture.runAsync(() -> {
            List<Map<String, Object>> docList = null;
            try {
                docList = docxToJsonConverter.analyzeWord(finalWordFileInputStream);
            } catch (ServiceException e) {
                throw e;
            } catch (Exception e) {
                log.error("导入教材章节失败，chapterId: {}, 异常：", chapterId, e);
            }
            if (ObjectUtil.isEmpty(docList)) {
                throw new ServiceException("解析失败");
            }
            Map<String, Object> doc = docList.get(0);
            // 生成小标题
            generateChapterLog(doc, chapter);

            String jsonStr = JSONUtil.toJsonStr(doc);
            // log.info("导入json字符串长度：{},{}", jsonStr.length(), jsonStr);
            Update update = new Update();
            update.set("chapterId", chapterId);
            update.set("content", jsonStr);
            mongoService.updateOne(BOOK_CHAPTER_CONTENT, new Query(Criteria.where("chapterId").is(chapterId)), update, DtbBookChapterContent.class);

            // 扫描json资源
            List<ResourceVO> resourceVOList = new ArrayList<>();
            List<Object> pageList = (List<Object>) doc.get("content");
            analysisNode(resourceVOList, pageList, userId, chapterId);

            // 保存资源到数据库
            dtbBookResourceService.batchUploadResource(resourceVOList, userId, chapterId);

            dutpTask.setTaskState(2);
            updateTaskAndSentMessage("您好，章节导入任务已完成。【章节名称：" + chapter.getChapterName() + "；教材名称：" + chapter.getBookName() + "；教材编号：" + chapter.getBookNo() + "】。", userId, dutpTask);
        }, customExecutor).exceptionally(ex -> {
            log.error("导入教材章节失败，chapterId: {}, 异常：{}", chapterId, ex);
            // 结束任务
            dutpTask.setTaskState(3);
            updateTaskAndSentMessage("您好，章节导入任务已完成。【章节名称：" + chapter.getChapterName() + "；教材名称：" + chapter.getBookName() + "；教材编号：" + chapter.getBookNo() + "】。", userId, dutpTask);
            return null;
        });

        return success;
    }

    @Override
    public int importChapterPdf(MultipartFile file, Long chapterId) {
        if (ObjectUtil.isEmpty(file) || ObjectUtil.isEmpty(chapterId)) {
            throw new ServiceException("PDF文件或者chapterId为空");
        }

        String fileName = file.getOriginalFilename();
        if (!fileName.endsWith(".pdf")) {
            throw new ServiceException("文件格式不正确，请上传.pdf文件");
        }

        DtbBookChapter chapter = dtbBookChapterMapper.queryBookByChapterId(chapterId);

        if (ObjectUtil.isEmpty(chapter)) {
            throw new ServiceException("章节不存在");
        }

        Long userId = SecurityUtils.getUserId();
        // 生成任务
        DutpTask dutpTask = new DutpTask();
        dutpTask.setTaskType(4);
        dutpTask.setTaskContent("导入章节内容");
        dutpTask.setRemark("章节名称: " + chapter.getChapterName());
        dutpTask.setDataId(chapterId);
        dutpTask.setUserId(userId);
        dutpTask.setTaskRate(0);
        dutpTask.setStartTime(new Date());
        dutpTask.setTaskState(1);
        int success = taskMapper.insert(dutpTask);
        // 1. 先将 MultipartFile 转换为 File
        File tempFile = null;
        try {
            tempFile = File.createTempFile("upload_", ".pdf");
            file.transferTo(tempFile);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("导入教材章节失败，chapterId: {}, 异常：", chapterId, e);
        }

        // 新开线程执行任务
        File finalTempFile = tempFile;
        CompletableFuture.runAsync(() -> {
//            List<JSONObject> pdfList = null;
            JsonObject pdfStr = new JsonObject();
            String jsonResponse = "";
            try {
                // 2. 调用 PdfToJsonHttpClient 上传 PDF
                pdfStr = pdfToJsonConverter.convertPdf(finalTempFile, "html");
            } catch (ServiceException e) {
                throw e;
            } catch (Exception e) {
                log.error("导入教材章节失败，chapterId: {}, 异常：", chapterId, e);
            }

            // 生成小标题
            generateChapterLog(pdfStr, chapter);
            String jsonStr = pdfStr.toString();
            log.info("导入json字符串长度：{},{}", jsonStr.length(), jsonStr);
            Update update = new Update();
            update.set("chapterId", chapterId);
            update.set("content", jsonStr);
            mongoService.updateOne(BOOK_CHAPTER_CONTENT, new Query(Criteria.where("chapterId").is(chapterId)), update, DtbBookChapterContent.class);

            // 扫描json资源
            List<ResourceVO> resourceVOList = new ArrayList<>();
            List<Object> pageList = (List<Object>) pdfStr.get("content");
            analysisNode(resourceVOList, pageList, userId, chapterId);

            // 保存资源到数据库
            dtbBookResourceService.batchUploadResource(resourceVOList, userId, chapterId);

            dutpTask.setTaskState(2);
            updateTaskAndSentMessage("您好，章节导入任务已完成。【章节名称：" + chapter.getChapterName() + "；教材名称：" + chapter.getBookName() + "；教材编号：" + chapter.getBookNo() + "】。", userId, dutpTask);
        }, customExecutor).exceptionally(ex -> {
            log.error("导入教材章节失败，chapterId: {}, 异常：{}", chapterId, ex);
            // 结束任务
            dutpTask.setTaskState(3);
            updateTaskAndSentMessage("您好，章节导入任务已完成。【章节名称：" + chapter.getChapterName() + "；教材名称：" + chapter.getBookName() + "；教材编号：" + chapter.getBookNo() + "】。", userId, dutpTask);
            return null;
        });

        return success;
    }

    /**
     * 分析节点
     *
     * @param resourceList
     * @param nodeList
     */
    private void analysisNode(List<ResourceVO> resourceList, List<Object> nodeList, Long userId, Long chapterId) {
        for (Object parentNode : nodeList) {
            Map<String, Object> node = (Map<String, Object>) parentNode;
            String type = (String) node.get("type");

            if (type.equals("text")) {
                // 文字

            } else if (type.equals("imageLayout") || type.equals("imageInLine") || type.equals("imageIcon") || type.equals("imageGallery")) {
                // 图片
                if (type.equals("imageGallery")) {
                    // 画廊
                    List<Object> imgList = (List<Object>) node.get("imgList");
                    if (ObjectUtil.isNotEmpty(imgList)) {
                        for (Object img : imgList) {
                            Map<String, Object> imgNode = (Map<String, Object>) img;
                            generateResource(resourceList, "1", userId, chapterId, imgNode);
                        }
                    }
                } else {
                    // 非画廊
                    Map<String, Object> attrs = (Map<String, Object>) node.get("attrs");
                    generateResource(resourceList, "1", userId, chapterId, attrs);
                }
            } else if (type.equals("bubbleInline")) {
                // 气泡

            } else if (type.equals("links")) {
                // 链接

            } else if (type.equals("image")) {
                // TODO 先默认这个为公式
                // 公式

            } else if (type.equals("resourceCover")) {
                // 资源封面组件
                Map<String, Object> attrs = (Map<String, Object>) node.get("attrs");
                Integer rcType = (Integer) attrs.get("rcType");
                if (rcType == 0) {
                    // 3D

                } else if (rcType == 1 || rcType == 2) {
                    // AR/VR

                } else if (rcType == 3) {
                    // 仿真

                } else if (rcType == 4) {
                    // 游戏

                } else if (rcType == 5) {
                    // 教学资源

                } else if (rcType == 6) {
                    // 扩展阅读

                } else if (rcType == 7) {
                    // TODO 实训
                    generateResource(resourceList, "9", userId, chapterId, attrs);

                }
            } else if (type.equals("questions")) {
                // 试题

            }
            // TODO 二期 脚注

            else if (type.equals("audio")) {
                // 音频
                Map<String, Object> attrs = (Map<String, Object>) node.get("attrs");
                generateResource(resourceList, "2", userId, chapterId, attrs);
            } else if (type.equals("video")) {
                // 视频
                Map<String, Object> attrs = (Map<String, Object>) node.get("attrs");
                generateResource(resourceList, "3", userId, chapterId, attrs);

            } else if (type.equals("codeBlock")) {
                // 代码块

            }
            // TODO 二期 互动

            List<Object> childrenNodeList = (List<Object>) node.get("content");
            if (ObjectUtil.isNotEmpty(childrenNodeList)) {
                analysisNode(resourceList, childrenNodeList, userId, chapterId);
            }
        }
    }

    /**
     * 生成资源
     *
     * @param resourceList
     * @param userId
     * @param chapterId
     * @param imgNode
     */
    private void generateResource(List<ResourceVO> resourceList, String fileType, Long userId, Long chapterId, Map<String, Object> imgNode) {
        String src = (String) imgNode.get("src");
        String name = (String) imgNode.get("name");
        Long fileSize = (Long) imgNode.get("size");
        ResourceVO resourceVO = new ResourceVO();
        resourceVO.setFileName(name);
        resourceVO.setFileUrl(src);
        resourceVO.setFileSize(fileSize);
        resourceVO.setFileType(fileType);
        resourceVO.setUserId(userId);
        resourceVO.setChapterId(chapterId);
        resourceList.add(resourceVO);
    }

    /**
     * 生成小节
     *
     * @param doc
     * @param chapter
     */
    private void generateChapterLog(Map<String, Object> doc, DtbBookChapter chapter) {
        Long bookId = chapter.getBookId();
        Long versionId = chapter.getVersionId();
        Long chapterId = chapter.getChapterId();
        List<String> hasDomIdList = new ArrayList<>();
        List<Map<String, Object>> levelList = new ArrayList<>(6);
        List<Long> catalogIdList = new ArrayList<>(6);

        // 删除没有存在的id
        LambdaQueryWrapper<DtbBookChapterCatalog> lambdaDeleteQueryWrapper = new LambdaQueryWrapper<DtbBookChapterCatalog>()
                .eq(DtbBookChapterCatalog::getChapterId, chapterId);
        // if (ObjectUtil.isNotEmpty(hasDomIdList)) {
        //     lambdaDeleteQueryWrapper.notIn(DtbBookChapterCatalog::getDomId, hasDomIdList);
        // }
        chapterCatalogMapper.delete(lambdaDeleteQueryWrapper);

        // 有效长度
        int effectiveLength = 0;
        int pageNumber = 0;
        List<Object> pageList = (List<Object>) doc.get("content");

        for (Object page : pageList) {
            Map<String, Object> pageObj = (Map<String, Object>) page;
            List<Object> contentList = (List<Object>) pageObj.get("content");
            if (ObjectUtil.isEmpty(contentList)) {
                continue;
            }
            pageNumber++;
            for (Object content : contentList) {
                Map<String, Object> contentObj = (Map<String, Object>) content;
                String type = (String) contentObj.get("type");
                if ("heading".equals(type)) {
                    Map<String, Object> attrs = (Map<String, Object>) contentObj.get("attrs");
                    Integer level = (Integer) attrs.get("level");
                    String domId = (String) attrs.get("id");
                    // 要操作的index
                    int index = -1;
                    Long parentId = 0L;
                    if (effectiveLength == 0) {
                        levelList.add(attrs);
                        index = 0;
                        parentId = 0l;
                        effectiveLength = 1;
                    } else {

                        for (int i = 0; i < effectiveLength; i++) {
                            Map<String, Object> hLabelObjectAttrs = levelList.get(i);
                            Integer iterLevel = (Integer) hLabelObjectAttrs.get("level");
                            // 当前level 比循环中的level小于等于 就替换当前值
                            if (iterLevel >= level) {
                                index = i;
                                effectiveLength = i + 1;
                                if (i == 0) {
                                    parentId = 0l;
                                } else {
                                    parentId = catalogIdList.get(i - 1);
                                    ;
                                }

                                levelList.set(i, attrs);
                                break;
                            }
                        }

                        if (index == -1) {
                            if (effectiveLength == levelList.size()) {
                                // 末尾追加
                                index = levelList.size();
                                levelList.add(index, attrs);
                            } else {
                                // 中间替换
                                index = effectiveLength;
                                levelList.set(index, attrs);
                            }
                            effectiveLength++;
                            // 父Id为数组最后一个的catalogId
                            parentId = catalogIdList.get(index - 1);
                        }
                    }
                    String text = (String) ((Map<String, Object>) ((List<Object>) contentObj.get("content")).get(0)).get("text");
                    // 更新数据库
                    // Long catalogId = chapterCatalogMapper.checkIsExist(domId);
                    DtbBookChapterCatalog dtbBookChapterCatalog = new DtbBookChapterCatalog();
                    dtbBookChapterCatalog.setTitle(text);
                    dtbBookChapterCatalog.setParentId(parentId);
                    dtbBookChapterCatalog.setPageNumber(pageNumber);
                    // if (ObjectUtil.isNotEmpty(catalogId)) {
                    //     dtbBookChapterCatalog.setCatalogId(catalogId);
                    // chapterCatalogMapper.updateById(dtbBookChapterCatalog);
                    // } else {
                    dtbBookChapterCatalog.setChapterId(chapterId);
                    dtbBookChapterCatalog.setBookId(bookId);
                    dtbBookChapterCatalog.setVersionId(versionId);
                    dtbBookChapterCatalog.setDomId(domId);
                    chapterCatalogMapper.insert(dtbBookChapterCatalog);
                    // }
                    if (effectiveLength <= catalogIdList.size()) {
                        catalogIdList.set(index, dtbBookChapterCatalog.getCatalogId());
                    } else {
                        catalogIdList.add(index, dtbBookChapterCatalog.getCatalogId());
                    }

                    hasDomIdList.add(domId);
                    log.info("level: {}, Text: {}, ParentId: {}, domID: {}", level, text, parentId, domId);
                }
            }
        }
        // 更新总页数
        DtbBookChapter bookChapter = new DtbBookChapter();
        bookChapter.setChapterId(chapterId);
        bookChapter.setChapterTotalPage(pageList.size());
        dtbBookChapterMapper.updateById(bookChapter);
    }

    /**
     * 生成小节 (JsonObject版本)
     *
     * @param pdf
     * @param chapter
     */
    private void generateChapterLog(JsonObject pdf, DtbBookChapter chapter) {
        Long bookId = chapter.getBookId();
        Long versionId = chapter.getVersionId();
        Long chapterId = chapter.getChapterId();
        List<String> hasDomIdList = new ArrayList<>();
        List<JsonObject> levelList = new ArrayList<>(6);
        List<Long> catalogIdList = new ArrayList<>(6);

        // 删除没有存在的id
        LambdaQueryWrapper<DtbBookChapterCatalog> lambdaDeleteQueryWrapper = new LambdaQueryWrapper<DtbBookChapterCatalog>()
                .eq(DtbBookChapterCatalog::getChapterId, chapterId);
        // if (ObjectUtil.isNotEmpty(hasDomIdList)) {
        //     lambdaDeleteQueryWrapper.notIn(DtbBookChapterCatalog::getDomId, hasDomIdList);
        // }
        chapterCatalogMapper.delete(lambdaDeleteQueryWrapper);

        // 有效长度
        int effectiveLength = 0;
        int pageNumber = 0;
        JsonArray pageList = pdf.getAsJsonArray("content");

        for (int i = 0; i < pageList.size(); i++) {
            JsonObject pageObj = pageList.get(i).getAsJsonObject();
            JsonArray contentList = pageObj.getAsJsonArray("content");
            if (contentList == null || contentList.size() == 0) {
                continue;
            }
            pageNumber++;
            for (int j = 0; j < contentList.size(); j++) {
                JsonObject contentObj = contentList.get(j).getAsJsonObject();
                String type = contentObj.get("type").getAsString();
                if ("heading".equals(type)) {
                    JsonObject attrs = contentObj.getAsJsonObject("attrs");
                    Integer level = attrs.get("level").getAsInt();
                    String domId = attrs.get("id").getAsString();
                    // 要操作的index
                    int index = -1;
                    Long parentId = 0L;
                    if (effectiveLength == 0) {
                        levelList.add(attrs);
                        index = 0;
                        parentId = 0L;
                        effectiveLength = 1;
                    } else {
                        for (int k = 0; k < effectiveLength; k++) {
                            JsonObject hLabelObjectAttrs = levelList.get(k);
                            Integer iterLevel = hLabelObjectAttrs.get("level").getAsInt();
                            // 当前level 比循环中的level小于等于 就替换当前值
                            if (iterLevel >= level) {
                                index = k;
                                effectiveLength = k + 1;
                                if (k == 0) {
                                    parentId = 0L;
                                } else {
                                    parentId = catalogIdList.get(k - 1);
                                }

                                levelList.set(k, attrs);
                                break;
                            }
                        }

                        if (index == -1) {
                            if (effectiveLength == levelList.size()) {
                                // 末尾追加
                                index = levelList.size();
                                levelList.add(index, attrs);
                            } else {
                                // 中间替换
                                index = effectiveLength;
                                levelList.set(index, attrs);
                            }
                            effectiveLength++;
                            // 父Id为数组最后一个的catalogId
                            parentId = catalogIdList.get(index - 1);
                        }
                    }

                    // 获取文本内容
                    String text = contentObj.getAsJsonArray("content").get(0).getAsJsonObject().get("text").getAsString();

                    // 更新数据库
                    // Long catalogId = chapterCatalogMapper.checkIsExist(domId);
                    DtbBookChapterCatalog dtbBookChapterCatalog = new DtbBookChapterCatalog();
                    dtbBookChapterCatalog.setTitle(text);
                    dtbBookChapterCatalog.setParentId(parentId);
                    dtbBookChapterCatalog.setPageNumber(pageNumber);
                    // if (ObjectUtil.isNotEmpty(catalogId)) {
                    //     dtbBookChapterCatalog.setCatalogId(catalogId);
                    // chapterCatalogMapper.updateById(dtbBookChapterCatalog);
                    // } else {
                    dtbBookChapterCatalog.setChapterId(chapterId);
                    dtbBookChapterCatalog.setBookId(bookId);
                    dtbBookChapterCatalog.setVersionId(versionId);
                    dtbBookChapterCatalog.setDomId(domId);
                    chapterCatalogMapper.insert(dtbBookChapterCatalog);
                    // }
                    if (effectiveLength <= catalogIdList.size()) {
                        catalogIdList.set(index, dtbBookChapterCatalog.getCatalogId());
                    } else {
                        catalogIdList.add(index, dtbBookChapterCatalog.getCatalogId());
                    }

                    hasDomIdList.add(domId);
                    log.info("level: {}, Text: {}, ParentId: {}, domID: {}", level, text, parentId, domId);
                }
            }
        }
        // 更新总页数
        DtbBookChapter bookChapter = new DtbBookChapter();
        bookChapter.setChapterId(chapterId);
        bookChapter.setChapterTotalPage(pageList.size());
        dtbBookChapterMapper.updateById(bookChapter);
    }

    /**
     * 更新任务结果和发送消息
     *
     * @param content
     * @param userId
     * @param dutpTask
     */
    private void updateTaskAndSentMessage(String content, Long userId, DutpTask dutpTask) {
        // 更新任务结果
        Long taskId = dutpTask.getTaskId();
        DutpTask task = new DutpTask();
        task.setTaskId(taskId);
        task.setTaskRate(100);
        task.setUrl(dutpTask.getUrl());
        task.setEndTime(new Date());
        task.setTaskState(dutpTask.getTaskState());
        taskMapper.updateById(task);

        // 发送消息
        DutpUserMessage dutpUserMessage = new DutpUserMessage();
        dutpUserMessage.setContent(content);
        dutpUserMessage.setTitle("任务提醒");
        dutpUserMessage.setToUserId(userId);
        dutpUserMessage.setMessageType(1);
        dutpUserMessage.setFromUserType(1);
        dutpUserMessage.setToUserType(1);
        remoteUserMessageService.addMessage(dutpUserMessage);
    }

    /**
     * 首页章节查询
     *
     * @param dtbBook
     * @return
     */
    @Override
//    public List<DtbBook> homepageChapterSearch(DtbBook dtbBook) {
//        LambdaQueryWrapper<DtbBook> dtbBookLambdaQueryWrapper = new LambdaQueryWrapper<>();
//        // 添加非空检查
//        if (ObjectUtil.isNotNull(dtbBook.getIsbn())
//                && ObjectUtil.isNotNull(dtbBook.getBookName())
//                && ObjectUtil.isNotNull(dtbBook.getAuthorValue())) {
//            dtbBookLambdaQueryWrapper.or(wrapper ->
//                    wrapper.like(DtbBook::getIsbn, dtbBook.getIsbn())
//                            .or()
//                            .like(DtbBook::getBookName, dtbBook.getBookName())
//                            .or()
//                            .like(DtbBook::getAuthorValue, dtbBook.getAuthorValue())
//            ).and(wrapper -> wrapper.eq(DtbBook::getShelfState, DutpConstant.NUM_TWO));
//        }
//        List<DtbBook> bookList = bookMapper.selectList(dtbBookLambdaQueryWrapper);
//        bookList.forEach(item -> {
//            LambdaQueryWrapper<DtbUserBook> dtbUserBookLambdaQueryWrapper = new LambdaQueryWrapper<>();
//            dtbUserBookLambdaQueryWrapper.eq(DtbUserBook::getBookId, item.getBookId());
//            dtbUserBookLambdaQueryWrapper.eq(DtbUserBook::getUserId, ObjectUtil.isNotEmpty(SecurityUtils.getLoginUser()) ? SecurityUtils.getLoginUser().getUserid() : 0);
//            List<DtbUserBook> dtbUserBookList = dtbUserBookMapper.selectList(dtbUserBookLambdaQueryWrapper);
//            DtbBookChapter dtbBookChapter = new DtbBookChapter();
//            dtbBookChapter.setBookId(item.getBookId());
//            List<DtbBookChapter> dtbBookChapterList = queryBookChapterListByBookDetail(dtbBookChapter);
//            List<Map<String, String>> chapterMapList = new ArrayList<>();
//            dtbBookChapterList.forEach(chapter -> {
//                Map<String, String> chapterMap = new HashMap<>();
//                chapterMap.put("chapterName", chapter.getChapterName());
//                chapterMap.put("chapterId", String.valueOf(chapter.getChapterId()));
//                if (ObjectUtil.isNotEmpty(dtbUserBookList)) {
//                    Date expireDate = dtbUserBookList.get(0).getExpireDate();
//                    LocalDate expireLocalDate = expireDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
//                    LocalDate currentDate = LocalDate.now();
//                    boolean isExpired = currentDate.isAfter(expireLocalDate);
//                    if (isExpired) {
//                        chapterMap.put("free", String.valueOf(chapter.getFree()));
//                    } else {
//                        chapterMap.put("free", String.valueOf(DutpConstant.NUM_TWO));
//                    }
//                } else {
//                    chapterMap.put("free", String.valueOf(chapter.getFree()));
//                }
//                chapterMapList.add(chapterMap);
//            });
//            item.setChapterList(chapterMapList);
//        });
//        return bookList;
//    }
    public TableDataInfo homepageChapterSearch(DtbBook dtbBook) {
        Long total = dtbBookChapterMapper.homepageChapterSearchPagetotal(dtbBook.getChapterName());
        List<DtbBookChapter> chapterPage = dtbBookChapterMapper.homepageChapterSearchByPage(
                SecurityUtils.getUserId(),
                dtbBook.getChapterName(),
                dtbBook.getPageSize(),
                (dtbBook.getPageNum() - 1) * dtbBook.getPageSize());
        // 按照bookId分组
        Map<Long, List<DtbBookChapter>> bookIdGroup = chapterPage.stream()
                .collect(Collectors.groupingBy(DtbBookChapter::getBookId));

        List<DtbBook> resList = new ArrayList<>();
        bookIdGroup.forEach((bookId, chapters) -> {
            DtbBook book = new DtbBook();
            book.setBookId(bookId);
            book.setBookName(chapters.get(0).getBookName());
            book.setAuthorLabel(chapters.get(0).getAuthorLabel());
            book.setAuthorValue(chapters.get(0).getAuthorValue());

            List<Map<String, String>> chapterMapList = chapters.stream()
                    .sorted(Comparator.comparingInt(DtbBookChapter::getSort))
                    .map(chapter -> {
                        Map<String, String> chapterMap = new HashMap<>();
                        chapterMap.put("chapterName", chapter.getChapterName());
                        chapterMap.put("chapterId", String.valueOf(chapter.getChapterId()));
                        chapterMap.put("free", String.valueOf(chapter.getFree()));
                        return chapterMap;
                    })
                    .collect(Collectors.toList());

            book.setChapterList(chapterMapList);
            resList.add(book);
        });
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(resList);
        rspData.setMsg("查询成功");
        rspData.setTotal(total);
        return rspData;
    }

    @Override
    public void export(DtbBookChapter dtbBookChapter) {
        List<Long> chapterIdList = dtbBookChapter.getChapterIdList();
        if (ObjectUtil.isEmpty(chapterIdList)) {
            throw new ServiceException("导出章节不能为空");
        }
        // 鉴权
        Long userId = SecurityUtils.getUserId();
        String roleKey = commonMapper.queryRole(userId);
        Long bookId = dtbBookChapterMapper.queryBookIdByChapterId(chapterIdList.get(0));
        DtbBook book = bookMapper.selectOne(new LambdaQueryWrapper<DtbBook>()
                .select(DtbBook::getBookId, DtbBook::getBookName, DtbBook::getBookNo, DtbBook::getPublishStatus, DtbBook::getLastVersionId, DtbBook::getCurrentVersionId)
                .eq(DtbBook::getBookId, bookId));
        // 修正版本不允许导出
        if (!book.getCurrentVersionId().equals(book.getLastVersionId())) {
            throw new ServiceException("修正版本教材不允许导出");
        }
        // 作者编辑端鉴权
        if ("writer".equals(roleKey) || "editor".equals(roleKey)) {
            Integer count = bookGroupMapper.selectCount(new LambdaQueryWrapper<DtbBookGroup>()
                    .eq(DtbBookGroup::getBookId, bookId)
                    .eq(DtbBookGroup::getUserId, userId)
                    .in(DtbBookGroup::getRoleType, 5, 6));
            if (ObjectUtil.isEmpty(count)) {
                count = 0;
            }

            if (count == 0) {
                // 不是编辑角色
                if (book.getPublishStatus() == 2) {
                    // 已经通过一次三审三校了
                    throw new ServiceException("无权限，不允许导出");
                }

                // 书稿联系人和主编
                Integer isEditor = bookGroupMapper.selectCount(new LambdaQueryWrapper<DtbBookGroup>()
                        .eq(DtbBookGroup::getBookId, bookId)
                        .eq(DtbBookGroup::getUserId, userId)
                        .in(DtbBookGroup::getRoleType, 1, 2));
                if (ObjectUtil.isEmpty(isEditor)) {
                    isEditor = 0;
                }
                // 有编写和预览章节权限的人
                List<DtbBookChapterEditor> chapterEditorList = bookChapterEditorMapper.selectList(new LambdaQueryWrapper<DtbBookChapterEditor>()
                        .select(DtbBookChapterEditor::getChapterId)
                        .eq(DtbBookChapterEditor::getUserId, userId)
                        .in(DtbBookChapterEditor::getChapterId, chapterIdList));
                if (ObjectUtil.isEmpty(chapterEditorList) && isEditor == 0) {
                    throw new ServiceException("无权限，不允许导出");
                }
                if (ObjectUtil.isNotEmpty(chapterEditorList) && isEditor == 0) {
                    chapterIdList = chapterEditorList.stream().map(DtbBookChapterEditor::getChapterId).collect(Collectors.toList());
                }
            }
        }
        String nowDay = DateUtil.format(new Date(), "yyyy-MM-dd");
        for (Long chapterId : chapterIdList) {
            // 生成任务
            DutpTask dutpTask = new DutpTask();
            dutpTask.setTaskType(10);
            dutpTask.setTaskContent("章节导出");
            dutpTask.setDataId(chapterId);
            dutpTask.setUserId(userId);
            dutpTask.setTaskRate(0);
            dutpTask.setStartTime(new Date());
            dutpTask.setTaskState(1);
            taskMapper.insert(dutpTask);
            // 开启异步线程
            // List<Long> finalChapterIdList = chapterIdList;
            CompletableFuture.runAsync(() -> {
                String path = fileExportPath + File.separator + dutpTask.getTaskId() + "_exportBook" + File.separator;
                // for (Long chapterId : finalChapterIdList) {
                DtbBookChapter chapter = dtbBookChapterMapper.selectOne(new LambdaQueryWrapper<DtbBookChapter>()
                        .select(DtbBookChapter::getChapterId, DtbBookChapter::getChapterName)
                        .eq(DtbBookChapter::getChapterId, chapterId));
                // 查询章节内容
                Query query = new Query(Criteria.where("chapterId").is(chapterId));
                DtbBookChapterContent chapterContent = mongoService.findOne(BOOK_CHAPTER_CONTENT, query, DtbBookChapterContent.class);
                String contentJson = chapterContent.getContent();

                // 生成word
                try {
                    JsonToDocxConverter.analyzeJson(contentJson, path + chapter.getChapterName() + ".docx");
                } catch (Exception e) {
                    log.info("导出章节失败：{}, 原因：", chapterId, e);
                }
                // TODO 生成pdf
                // }
                // 打包zip
                File file = generateZip(path, fileExportPath + File.separator + dutpTask.getTaskId() + File.separator + book.getBookName() + "_章节导出_" + nowDay + ".zip");
                UploadFileDto uploadFileDto = aliyunOssStsUtil.uploadFile(file);
                log.info("上传完后的文件url: {}", uploadFileDto);
                // 结束任务
                dutpTask.setUrl(uploadFileDto.getFileUrl() + "!&&&!" + book.getBookName() + "_章节导出_" + nowDay + ".zip");
                dutpTask.setTaskState(2);
                updateTaskAndSentMessage("您好，教材导出任务已完成，请到【教材管理-任务中心】内下载。【教材名称：" + book.getBookName() + "；教材编号：" + book.getBookNo() + "】。", userId, dutpTask);
            }, customExecutor).exceptionally(ex -> {
                log.error("导出教材章节失败: 异常：", ex);
                // 结束任务
                dutpTask.setTaskState(3);
                updateTaskAndSentMessage("您好，教材导出任务已完成，请到【教材管理-任务中心】内下载。【教材名称：" + book.getBookName() + "；教材编号：" + book.getBookNo() + "】。", userId, dutpTask);
                return null;
            });
        }


    }

    @Override
    public List<DtbBookChapterTreeVO> getChapterCatalog(Long chapterId) {
        Long chapter = dtbBookChapterMapper.queryChapter(chapterId);
        if (ObjectUtil.isEmpty(chapter)) {
            return new ArrayList<>();
        }
        List<DtbBookChapterTreeVO> dtbBookChapterCatalogList = chapterCatalogMapper.queryBookChapterCatalogTreeByChapterId(chapterId);
        // 构建树
        dtbBookChapterCatalogList = makeTree(dtbBookChapterCatalogList,
                x -> x.getParentId() == 0l, (x, y) ->
                        x.getCatalogId().equals(y.getParentId()),
                DtbBookChapterTreeVO::setChildren);
        return dtbBookChapterCatalogList;
    }
}

package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 用户心理测试答题明细对象 mooc_psychology_health_test_answer
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Data
@TableName("mooc_psychology_health_test_answer")
public class MoocPsychologyHealthTestAnswer extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long answerId;

    /**
     * 用户id
     */
    @Excel(name = "用户id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 题干id
     */
    @Excel(name = "题干id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long questionId;

    /**
     * 选择的选项id
     */
    @Excel(name = "选择的选项id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long optionId;

    /**
     * 结果id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long resultId;


    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("answerId", getAnswerId())
                .append("userId", getUserId())
                .append("questionId", getQuestionId())
                .append("optionId", getOptionId())
                .append("resultId", getResultId())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("delFlag", getDelFlag())
                .toString();
    }
}

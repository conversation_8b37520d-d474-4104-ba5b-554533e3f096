package cn.dutp.book.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.dutp.book.domain.DtbBookShareChapter;
import cn.dutp.book.mapper.DtbBookShareChapterMapper;
import cn.dutp.book.service.IDtbBookShareChapterService;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.book.mapper.DtbBookShareMapper;
import cn.dutp.book.domain.DtbBookShare;
import cn.dutp.book.service.IDtbBookShareService;

/**
 * 教材分享记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Service
public class DtbBookShareServiceImpl extends ServiceImpl<DtbBookShareMapper, DtbBookShare> implements IDtbBookShareService
{
    @Autowired
    private DtbBookShareMapper dtbBookShareMapper;

    @Autowired
    private IDtbBookShareChapterService dtbBookShareChapterService;

    /**
     * 查询教材分享记录
     *
     * @param shareId 教材分享记录主键
     * @return 教材分享记录
     */
    @Override
    public DtbBookShare selectDtbBookShareByShareId(Long shareId)
    {
        return this.getById(shareId);
    }

    /**
     * 查询教材分享记录列表
     *
     * @param dtbBookShare 教材分享记录
     * @return 教材分享记录
     */
    @Override
    public List<DtbBookShare> selectDtbBookShareList(DtbBookShare dtbBookShare)
    {
        Long userId = SecurityUtils.getUserId();
        dtbBookShare.setUserId(userId);
        return dtbBookShareMapper.selectDtbBookShareList(dtbBookShare);
    }

    /**
     * 新增教材分享记录
     *
     * @param dtbBookShare 教材分享记录
     * @return 结果
     */
    @Override
    public boolean insertDtbBookShare(DtbBookShare dtbBookShare)
    {
        dtbBookShare.setUserId(SecurityUtils.getUserId());
        // 保存的时候判断开始日期是否是今天 如果是今天 status=2 (生效中)  否则 status=1 (待生效) 通过定时任务生效
        Date now = new Date();
        Date startDate = dtbBookShare.getStartDate();
        Date endDate = dtbBookShare.getEndDate();

        if (ObjectUtil.isNotEmpty(startDate)) {
            if (now.before(startDate)) {
                // 当前时间在开始时间之前 - 未生效
                dtbBookShare.setStatus(1);
            } else if (ObjectUtil.isNotEmpty(endDate) && now.after(endDate)) {
                // 当前时间在结束时间之后 - 已过期
                dtbBookShare.setStatus(3);
            } else {
                // 当前时间在有效期内 - 已生效
                dtbBookShare.setStatus(2);
            }
        }

        // 保存分享章节中间表
        List<DtbBookShareChapter> chapterList = dtbBookShare.getChapterList();
        if (ObjectUtil.isNotEmpty(chapterList)){
            for (DtbBookShareChapter chapter : chapterList) {
                chapter.setShareId(dtbBookShare.getShareId());
            }
            dtbBookShareChapterService.saveBatch(chapterList);
        }
        return this.save(dtbBookShare);
    }

    /**
     * 修改教材分享记录
     *
     * @param dtbBookShare 教材分享记录
     * @return 结果
     */
    @Override
    public boolean updateDtbBookShare(DtbBookShare dtbBookShare)
    {
        // 保存的时候判断开始日期是否是今天 如果是今天 status=2 (生效中)  否则 status=1 (待生效) 通过定时任务生效
//        if (ObjectUtil.isNotEmpty(dtbBookShare.getStartDate())){
//            if (DateUtil.isSameDay(dtbBookShare.getStartDate(), new Date())){
//                dtbBookShare.setStatus(2);
//            }
//        }
        Date now = new Date();
        Date startDate = dtbBookShare.getStartDate();
        Date endDate = dtbBookShare.getEndDate();

        if (ObjectUtil.isNotEmpty(startDate)) {
            if (now.before(startDate)) {
                // 当前时间在开始时间之前 - 未生效
                dtbBookShare.setStatus(1);
            } else if (ObjectUtil.isNotEmpty(endDate) && now.after(endDate)) {
                // 当前时间在结束时间之后 - 已过期
                dtbBookShare.setStatus(3);
            } else {
                // 当前时间在有效期内 - 已生效
                dtbBookShare.setStatus(2);
            }
        }
        return this.updateById(dtbBookShare);
    }

    /**
     * 批量删除教材分享记录
     *
     * @param shareIds 需要删除的教材分享记录主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookShareByShareIds(List<Long> shareIds)
    {
        return this.removeByIds(shareIds);
    }

    /**
     * 验证教材是否设置了分享码
     * @param shareId
     * @return
     */
    @Override
    public AjaxResult getIsShareCode(Long shareId) {
        DtbBookShare bookShare = this.selectDtbBookShareByShareId(shareId);
        String code = bookShare.getCode();
        if (ObjectUtil.isEmpty(code)){
            return AjaxResult.success(false);
        }else{
            return AjaxResult.success(true);
        }
    }

    /**
     * 验证分享码
     * @param dtbBookShare
     * @return
     */
    @Override
    public AjaxResult checkShareCode(DtbBookShare dtbBookShare) {
        Long shareId = dtbBookShare.getShareId();
        String inputCode = dtbBookShare.getCode();

        DtbBookShare bookShare = this.selectDtbBookShareByShareId(shareId);
        boolean checkFlag = false;
        if (inputCode.equalsIgnoreCase(bookShare.getCode())){
            checkFlag = true;
        }else {
            checkFlag = false;
        }
        Map<String,Object> retMap = new HashMap<>();
        retMap.put("checkFlag",checkFlag);
        retMap.put("bookId", bookShare.getBookId() != null ? String.valueOf(bookShare.getBookId()) : "");
        return AjaxResult.success(retMap);
    }
}

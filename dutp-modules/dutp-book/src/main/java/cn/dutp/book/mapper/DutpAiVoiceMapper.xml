<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DutpAiVoiceMapper">
    
    <resultMap type="cn.dutp.book.domain.DutpAiVoice" id="DutpAiVoiceResult">
        <result property="voiceId"    column="voice_id"    />
        <result property="name"    column="name"    />
        <result property="type"    column="type"    />
        <result property="vcn"    column="vcn"    />
        <result property="promptId"    column="prompt_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDutpAiVoiceVo">
        select voice_id, name, type, vcn, prompt_id, del_flag, create_by, create_time, update_by, update_time from dutp_ai_voice
    </sql>

    <select id="selectDutpAiVoiceList" parameterType="cn.dutp.book.domain.DutpAiVoice" resultMap="DutpAiVoiceResult">
        <include refid="selectDutpAiVoiceVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="vcn != null  and vcn != ''"> and vcn = #{vcn}</if>
            <if test="promptId != null "> and prompt_id = #{promptId}</if>
        </where>
    </select>
    
    <select id="selectDutpAiVoiceByVoiceId" parameterType="Long" resultMap="DutpAiVoiceResult">
        <include refid="selectDutpAiVoiceVo"/>
        where voice_id = #{voiceId}
    </select>

    <insert id="insertDutpAiVoice" parameterType="cn.dutp.book.domain.DutpAiVoice">
        insert into dutp_ai_voice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="voiceId != null">voice_id,</if>
            <if test="name != null">name,</if>
            <if test="type != null">type,</if>
            <if test="vcn != null">vcn,</if>
            <if test="promptId != null">prompt_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="voiceId != null">#{voiceId},</if>
            <if test="name != null">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="vcn != null">#{vcn},</if>
            <if test="promptId != null">#{promptId},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDutpAiVoice" parameterType="cn.dutp.book.domain.DutpAiVoice">
        update dutp_ai_voice
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="vcn != null">vcn = #{vcn},</if>
            <if test="promptId != null">prompt_id = #{promptId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where voice_id = #{voiceId}
    </update>

    <delete id="deleteDutpAiVoiceByVoiceId" parameterType="Long">
        delete from dutp_ai_voice where voice_id = #{voiceId}
    </delete>

    <delete id="deleteDutpAiVoiceByVoiceIds" parameterType="String">
        delete from dutp_ai_voice where voice_id in 
        <foreach item="voiceId" collection="array" open="(" separator="," close=")">
            #{voiceId}
        </foreach>
    </delete>
</mapper>
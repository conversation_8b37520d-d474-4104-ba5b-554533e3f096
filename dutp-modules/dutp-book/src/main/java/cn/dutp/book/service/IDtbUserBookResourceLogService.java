package cn.dutp.book.service;

import java.util.List;
import cn.dutp.book.domain.DtbUserBookResourceLog;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * DUTP-DTB_017学生观看数字教材资源记录Service接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IDtbUserBookResourceLogService extends IService<DtbUserBookResourceLog>
{
    /**
     * 查询DUTP-DTB_017学生观看数字教材资源记录
     *
     * @param logId DUTP-DTB_017学生观看数字教材资源记录主键
     * @return DUTP-DTB_017学生观看数字教材资源记录
     */
    public DtbUserBookResourceLog selectDtbUserBookResourceLogByLogId(Long logId);

    /**
     * 查询DUTP-DTB_017学生观看数字教材资源记录列表
     *
     * @param dtbUserBookResourceLog DUTP-DTB_017学生观看数字教材资源记录
     * @return DUTP-DTB_017学生观看数字教材资源记录集合
     */
    public List<DtbUserBookResourceLog> selectDtbUserBookResourceLogList(DtbUserBookResourceLog dtbUserBookResourceLog);

    /**
     * 新增DUTP-DTB_017学生观看数字教材资源记录
     *
     * @param dtbUserBookResourceLog DUTP-DTB_017学生观看数字教材资源记录
     * @return 结果
     */
    public boolean insertDtbUserBookResourceLog(DtbUserBookResourceLog dtbUserBookResourceLog);

    /**
     * 修改DUTP-DTB_017学生观看数字教材资源记录
     *
     * @param dtbUserBookResourceLog DUTP-DTB_017学生观看数字教材资源记录
     * @return 结果
     */
    public boolean updateDtbUserBookResourceLog(DtbUserBookResourceLog dtbUserBookResourceLog);

    /**
     * 批量删除DUTP-DTB_017学生观看数字教材资源记录
     *
     * @param logIds 需要删除的DUTP-DTB_017学生观看数字教材资源记录主键集合
     * @return 结果
     */
    public boolean deleteDtbUserBookResourceLogByLogIds(List<Long> logIds);

}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbArVrResourceMapper">
    
    <resultMap type="DtbArVrResource" id="DtbArVrResourceResult">
        <result property="resourceId"    column="resource_id"    />
        <result property="name"    column="name"    />
        <result property="coverUrl"    column="cover_url"    />
        <result property="resourceUrl"    column="resource_url"    />
        <result property="description"    column="description"    />
        <result property="folderId"    column="folder_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectDtbArVrResourceVo">
        select resource_id, name, cover_url, resource_url, description, folder_id, create_by, create_time, update_by, update_time, del_flag from dtb_ar_vr_resource
    </sql>

    <select id="selectDtbArVrResourceList" parameterType="DtbArVrResource" resultMap="DtbArVrResourceResult">
        <include refid="selectDtbArVrResourceVo"/>
        <where>
            del_flag = '0'
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="coverUrl != null  and coverUrl != ''"> and cover_url = #{coverUrl}</if>
            <if test="resourceUrl != null  and resourceUrl != ''"> and resource_url = #{resourceUrl}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="folderId != null">
                <choose>
                    <when test="folderId == 0">
                        and (folder_id = 0 or folder_id is null)
                    </when>
                    <otherwise>
                        and folder_id = #{folderId}
                    </otherwise>
                </choose>
            </if>
        </where>
        order by update_time desc
    </select>
    
    <select id="selectDtbArVrResourceByResourceId" parameterType="Long" resultMap="DtbArVrResourceResult">
        <include refid="selectDtbArVrResourceVo"/>
        where resource_id = #{resourceId}
    </select>

    <insert id="insertDtbArVrResource" parameterType="DtbArVrResource" useGeneratedKeys="true" keyProperty="resourceId">
        insert into dtb_ar_vr_resource
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="coverUrl != null">cover_url,</if>
            <if test="resourceUrl != null and resourceUrl != ''">resource_url,</if>
            <if test="description != null">description,</if>
            <if test="folderId != null">folder_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="coverUrl != null">#{coverUrl},</if>
            <if test="resourceUrl != null and resourceUrl != ''">#{resourceUrl},</if>
            <if test="description != null">#{description},</if>
            <if test="folderId != null">#{folderId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateDtbArVrResource" parameterType="DtbArVrResource">
        update dtb_ar_vr_resource
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="coverUrl != null">cover_url = #{coverUrl},</if>
            <if test="resourceUrl != null and resourceUrl != ''">resource_url = #{resourceUrl},</if>
            <if test="description != null">description = #{description},</if>
            <if test="folderId != null">folder_id = #{folderId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where resource_id = #{resourceId}
    </update>

    <delete id="deleteDtbArVrResourceByResourceId" parameterType="Long">
        delete from dtb_ar_vr_resource where resource_id = #{resourceId}
    </delete>

    <delete id="deleteDtbArVrResourceByResourceIds" parameterType="String">
        delete from dtb_ar_vr_resource where resource_id in 
        <foreach item="resourceId" collection="array" open="(" separator="," close=")">
            #{resourceId}
        </foreach>
    </delete>
</mapper>
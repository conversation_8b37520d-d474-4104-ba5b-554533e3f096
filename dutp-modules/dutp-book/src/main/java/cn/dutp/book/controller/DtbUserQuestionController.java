package cn.dutp.book.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.book.domain.DtbBookQuestionAnswer;
import cn.dutp.book.domain.DtbUserQuestion;
import cn.dutp.book.service.IDtbUserQuestionService;
import cn.dutp.common.security.utils.SecurityUtils;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.core.utils.PageUtils;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 数字教材习题Controller
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@RestController
@RequestMapping("/userQuestion")
public class DtbUserQuestionController extends BaseController
{
    @Autowired
    private IDtbUserQuestionService dtbUserQuestionService;

    /**
     * 查询数字教材习题列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbUserQuestion dtbUserQuestion)
    {
        startPage();
        List<DtbUserQuestion> list = dtbUserQuestionService.selectDtbUserQuestionList(dtbUserQuestion);
        return getDataTable(list);
    }
    /**
     * 查询数字教材习题列表(包含选项)
     */
    @GetMapping("/listWithOptions")
    public TableDataInfo listWithOptions(DtbUserQuestion dtbUserQuestion)
    {


        //如果没有提供目录id，那么只拉取本用户的
        if(dtbUserQuestion.getFolderId() == null){
            dtbUserQuestion.setUserId(SecurityUtils.getUserId());
        }

        startPage();
        List<DtbUserQuestion> list = dtbUserQuestionService.selectDtbUserQuestionListWithOptions(dtbUserQuestion);
        return getDataTable(list);
    }

    /**
     * 导出数字教材习题列表
     */
    @RequiresPermissions("book:userQuestion:export")
    @Log(title = "导出数字教材习题", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbUserQuestion dtbUserQuestion)
    {
        List<DtbUserQuestion> list = dtbUserQuestionService.selectDtbUserQuestionList(dtbUserQuestion);
        ExcelUtil<DtbUserQuestion> util = new ExcelUtil<DtbUserQuestion>(DtbUserQuestion.class);
        util.exportExcel(response, list, "数字教材习题数据");
    }

    /**
     * 获取数字教材习题详细信息
     */
    @GetMapping(value = "/{questionId}")
    public AjaxResult getInfo(@PathVariable("questionId") Long questionId)
    {
        return success(dtbUserQuestionService.selectDtbUserQuestionByQuestionId(questionId));
    }

    /**
     * 新增数字教材习题
     */
    @RequiresPermissions("book:userQuestion:add")
    @Log(title = "新增数字教材习题", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbUserQuestion dtbUserQuestion)
    {

        dtbUserQuestion.setUserId(SecurityUtils.getUserId());
        return toAjax(dtbUserQuestionService.insertDtbUserQuestion(dtbUserQuestion));
    }



    /**
     * 新增数字教材习题
     */
    @RequiresPermissions("book:userQuestion:add")
    @Log(title = "批量导入数字教材习题", businessType = BusinessType.INSERT)
    @PostMapping("/import")
    public AjaxResult importQuestions(@RequestBody List<DtbUserQuestion> dtbUserQuestionList)
    {

        return AjaxResult.success(dtbUserQuestionService.importQuestions(dtbUserQuestionList));
    }

    /**
     * 修改数字教材习题
     */
    @RequiresPermissions("book:userQuestion:edit")
    @Log(title = "修改数字教材习题", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbUserQuestion dtbUserQuestion)
    {
        return toAjax(dtbUserQuestionService.updateDtbUserQuestion(dtbUserQuestion));
    }

    /**
     * 删除数字教材习题
     */
    @RequiresPermissions("book:userQuestion:remove")
    @Log(title = "删除数字教材习题", businessType = BusinessType.DELETE)
    @DeleteMapping("/{questionIds}")
    public AjaxResult remove(@PathVariable Long[] questionIds)
    {
        return toAjax(dtbUserQuestionService.deleteDtbUserQuestionByQuestionIds(Arrays.asList(questionIds)));
    }

    /**
     * 将数字教材习题移入回收站
     */
    @RequiresPermissions("book:userQuestion:remove")
    @Log(title = "移入回收站", businessType = BusinessType.UPDATE)
    @PutMapping("/recycle/{questionIds}")
    public AjaxResult moveToRecycleBin(@PathVariable Long[] questionIds)
    {
        return toAjax(dtbUserQuestionService.moveToRecycleBin(Arrays.asList(questionIds)));
    }

    /**
     * 从回收站恢复数字教材习题
     */
    @RequiresPermissions("book:userQuestion:remove")
    @Log(title = "从回收站恢复", businessType = BusinessType.UPDATE)
    @PutMapping("/restore/{questionIds}")
    public AjaxResult restoreFromRecycleBin(@PathVariable Long[] questionIds)
    {
        return toAjax(dtbUserQuestionService.restoreFromRecycleBin(Arrays.asList(questionIds)));
    }


    /**
     * 查询回收站中的数字教材习题列表
     */
    @RequiresPermissions("book:userQuestion:remove")
    @GetMapping("/recycleBin/list")
    public TableDataInfo recycleBinList(DtbUserQuestion dtbUserQuestion)
    {
        startPage();
        //如果没有提供目录id，那么只拉取本用户的
        if(dtbUserQuestion.getFolderId() == null){
            dtbUserQuestion.setUserId(SecurityUtils.getUserId());
        }
        List<DtbUserQuestion> list = dtbUserQuestionService.recycleBinList(dtbUserQuestion);
        return getDataTable(list);
    }

    /**
     * 检查题目是否被试卷管理
     */
    @PostMapping("/checkPaperReference")
    public AjaxResult checkPaperReference(@RequestBody Long[] questionIds)
    {
        return success(dtbUserQuestionService.checkPaperReference(questionIds));
    }

    /**
     * 获取题目信息及用户答案
     */
    @GetMapping(value = "/getUserAnswerInfo")
    public AjaxResult getUserAnswerInfo(DtbUserQuestion dtbUserQuestion)
    {
        return success(dtbUserQuestionService.getUserAnswerInfo(dtbUserQuestion));
    }

}

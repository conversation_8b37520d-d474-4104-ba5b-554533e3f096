<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbUserBookResourceLogMapper">
    
    <resultMap type="DtbUserBookResourceLog" id="DtbUserBookResourceLogResult">
        <result property="logId"    column="log_id"    />
        <result property="userId"    column="user_id"    />
        <result property="bookId"    column="book_id"    />
        <result property="resourceId"    column="resource_id"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="lastTime"    column="last_time"    />
        <result property="chapterId"    column="chapter_id"    />
        <result property="progressRate"    column="progress_rate"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDtbUserBookResourceLogVo">
        select log_id, user_id, book_id, resource_id, file_url, last_time, chapter_id, progress_rate, create_by, create_time, update_by, update_time from dtb_user_book_resource_log
    </sql>

    <select id="selectDtbUserBookResourceLogList" parameterType="DtbUserBookResourceLog" resultMap="DtbUserBookResourceLogResult">
        <include refid="selectDtbUserBookResourceLogVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="bookId != null "> and book_id = #{bookId}</if>
            <if test="resourceId != null "> and resource_id = #{resourceId}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="lastTime != null "> and last_time = #{lastTime}</if>
            <if test="chapterId != null "> and chapter_id = #{chapterId}</if>
            <if test="progressRate != null "> and progress_rate = #{progressRate}</if>
        </where>
    </select>
    
    <select id="selectDtbUserBookResourceLogByLogId" parameterType="Long" resultMap="DtbUserBookResourceLogResult">
        <include refid="selectDtbUserBookResourceLogVo"/>
        where log_id = #{logId}
    </select>

    <insert id="insertDtbUserBookResourceLog" parameterType="DtbUserBookResourceLog" useGeneratedKeys="true" keyProperty="logId">
        insert into dtb_user_book_resource_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="bookId != null">book_id,</if>
            <if test="resourceId != null">resource_id,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="lastTime != null">last_time,</if>
            <if test="chapterId != null">chapter_id,</if>
            <if test="progressRate != null">progress_rate,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="bookId != null">#{bookId},</if>
            <if test="resourceId != null">#{resourceId},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="lastTime != null">#{lastTime},</if>
            <if test="chapterId != null">#{chapterId},</if>
            <if test="progressRate != null">#{progressRate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDtbUserBookResourceLog" parameterType="DtbUserBookResourceLog">
        update dtb_user_book_resource_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="resourceId != null">resource_id = #{resourceId},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="lastTime != null">last_time = #{lastTime},</if>
            <if test="chapterId != null">chapter_id = #{chapterId},</if>
            <if test="progressRate != null">progress_rate = #{progressRate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where log_id = #{logId}
    </update>

    <delete id="deleteDtbUserBookResourceLogByLogId" parameterType="Long">
        delete from dtb_user_book_resource_log where log_id = #{logId}
    </delete>

    <delete id="deleteDtbUserBookResourceLogByLogIds" parameterType="String">
        delete from dtb_user_book_resource_log where log_id in 
        <foreach item="logId" collection="array" open="(" separator="," close=")">
            #{logId}
        </foreach>
    </delete>
</mapper>
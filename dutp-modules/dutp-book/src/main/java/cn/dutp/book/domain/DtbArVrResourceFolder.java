package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * AR/VR资源库文件夹对象 dtb_ar_vr_folder
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@TableName("dtb_ar_vr_folder")
public class DtbArVrResourceFolder extends BaseEntity {
    private static final long serialVersionUID = 1L;
    
    /**
     * 文件夹ID
     */
    @TableId(type = IdType.AUTO)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long folderId;

    /**
     * 文件夹名称
     */
    @Excel(name = "文件夹名称")
    private String folderName;

    /**
     * 父级ID
     */
    @Excel(name = "父级ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("folderId", getFolderId())
                .append("folderName", getFolderName())
                .append("parentId", getParentId())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("delFlag", getDelFlag())
                .toString();
    }
} 
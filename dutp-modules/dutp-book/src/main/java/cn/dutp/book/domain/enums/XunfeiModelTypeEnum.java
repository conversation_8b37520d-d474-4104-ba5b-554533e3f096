package cn.dutp.book.domain.enums;

import lombok.Getter;

/**
 * 大模型类型
 *
 * <AUTHOR>
 * @date 2024-11-04
 */
@Getter
public enum XunfeiModelTypeEnum {
    // 设备类型

    lite("lite","指向Lite版本"),
    generalv3("generalv3","指向Pro版本"),
    pro128("pro-128k","指向Pro-128K版本"),
    generalv35("generalv3.5","指向Max版本"),
    max32("max-32k","指向Max-32K版本"),
    ultra("4.0Ultra","指向4.0 Ultra版本");


    private final String code;
    private final String desc;

    XunfeiModelTypeEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

}

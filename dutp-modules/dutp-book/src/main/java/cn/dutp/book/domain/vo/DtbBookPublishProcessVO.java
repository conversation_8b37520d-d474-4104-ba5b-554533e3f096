package cn.dutp.book.domain.vo;

import cn.dutp.book.domain.DtbBookPublishProcess;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class DtbBookPublishProcessVO implements Serializable {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long rootStepId;

    private String rootStepName;

    /**
     * 审批详情
     */
    private List<DtbBookPublishProcess> dtbBookPublishProcessList;
}

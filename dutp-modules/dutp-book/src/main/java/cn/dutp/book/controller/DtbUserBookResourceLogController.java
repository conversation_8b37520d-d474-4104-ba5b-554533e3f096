package cn.dutp.book.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.book.domain.DtbUserBookResourceLog;
import cn.dutp.book.service.IDtbUserBookResourceLogService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * DUTP-DTB_017学生观看数字教材资源记录Controller
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@RestController
@RequestMapping("/book/log")
public class DtbUserBookResourceLogController extends BaseController
{
    @Autowired
    private IDtbUserBookResourceLogService dtbUserBookResourceLogService;

    /**
     * 查询DUTP-DTB_017学生观看数字教材资源记录列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbUserBookResourceLog dtbUserBookResourceLog)
    {
        startPage();
        List<DtbUserBookResourceLog> list = dtbUserBookResourceLogService.selectDtbUserBookResourceLogList(dtbUserBookResourceLog);
        return getDataTable(list);
    }

    /**
     * 导出DUTP-DTB_017学生观看数字教材资源记录列表
     */
    @RequiresPermissions("book:log:export")
    @Log(title = "导出DUTP-DTB_017学生观看数字教材资源记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbUserBookResourceLog dtbUserBookResourceLog)
    {
        List<DtbUserBookResourceLog> list = dtbUserBookResourceLogService.selectDtbUserBookResourceLogList(dtbUserBookResourceLog);
        ExcelUtil<DtbUserBookResourceLog> util = new ExcelUtil<DtbUserBookResourceLog>(DtbUserBookResourceLog.class);
        util.exportExcel(response, list, "DUTP-DTB_017学生观看数字教材资源记录数据");
    }

    /**
     * 获取DUTP-DTB_017学生观看数字教材资源记录详细信息
     */
    @RequiresPermissions("book:log:query")
    @GetMapping(value = "/{logId}")
    public AjaxResult getInfo(@PathVariable("logId") Long logId)
    {
        return success(dtbUserBookResourceLogService.selectDtbUserBookResourceLogByLogId(logId));
    }

    /**
     * 新增DUTP-DTB_017学生观看数字教材资源记录
     */
    @RequiresPermissions("book:log:add")
    @Log(title = "新增DUTP-DTB_017学生观看数字教材资源记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbUserBookResourceLog dtbUserBookResourceLog)
    {
        return toAjax(dtbUserBookResourceLogService.insertDtbUserBookResourceLog(dtbUserBookResourceLog));
    }

    /**
     * 修改DUTP-DTB_017学生观看数字教材资源记录
     */
    @RequiresPermissions("book:log:edit")
    @Log(title = "修改DUTP-DTB_017学生观看数字教材资源记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbUserBookResourceLog dtbUserBookResourceLog)
    {
        return toAjax(dtbUserBookResourceLogService.updateDtbUserBookResourceLog(dtbUserBookResourceLog));
    }

    /**
     * 删除DUTP-DTB_017学生观看数字教材资源记录
     */
    @RequiresPermissions("book:log:remove")
    @Log(title = "删除DUTP-DTB_017学生观看数字教材资源记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{logIds}")
    public AjaxResult remove(@PathVariable Long[] logIds)
    {
        return toAjax(dtbUserBookResourceLogService.deleteDtbUserBookResourceLogByLogIds(Arrays.asList(logIds)));
    }
}

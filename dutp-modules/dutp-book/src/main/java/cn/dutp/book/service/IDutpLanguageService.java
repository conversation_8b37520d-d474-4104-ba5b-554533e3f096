package cn.dutp.book.service;

import cn.dutp.book.domain.DutpLanguage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 平台语种Service接口
 *
 * <AUTHOR>
 * @date 2024-12-27
 */
public interface IDutpLanguageService extends IService<DutpLanguage>
{

    /**
     * 查询平台语种列表
     *
     * @param dutpLanguage 平台语种
     * @return 平台语种集合
     */
    List<DutpLanguage> selectDutpLanguageList(DutpLanguage dutpLanguage);


}

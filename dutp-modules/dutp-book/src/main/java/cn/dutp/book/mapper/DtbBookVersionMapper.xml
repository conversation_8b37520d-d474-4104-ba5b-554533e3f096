<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbBookVersionMapper">
    
    <resultMap type="DtbBookVersion" id="DtbBookVersionResult">
        <result property="versionId"    column="version_id"    />
        <result property="versionNo"    column="version_no"    />
        <result property="bookId"    column="book_id"    />
        <result property="versionIntroduce"    column="version_introduce"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectDtbBookVersionVo">
        select version_id, version_no, book_id, version_introduce, create_by, create_time, update_by, update_time, del_flag from dtb_book_version
    </sql>

    <select id="selectDtbBookVersionList" parameterType="DtbBookVersion" resultMap="DtbBookVersionResult">
        <include refid="selectDtbBookVersionVo"/>
        <where>  
            <if test="versionNo != null  and versionNo != ''"> and version_no = #{versionNo}</if>
            <if test="bookId != null "> and book_id = #{bookId}</if>
            <if test="versionIntroduce != null  and versionIntroduce != ''"> and version_introduce = #{versionIntroduce}</if>
        </where>
    </select>
    
    <select id="selectDtbBookVersionByVersionId" parameterType="Long" resultMap="DtbBookVersionResult">
        <include refid="selectDtbBookVersionVo"/>
        where version_id = #{versionId}
    </select>
    <select id="versionList" resultType="cn.dutp.book.domain.DtbBookVersion">
        SELECT
            v.version_no,
            v.version_id,
            t.book_id,
            t.state,
            t.step_id,
            t.promoter_user_id,
            s.step_name,
            b.record_no,
            b.master_flag,
            b.book_organize,
            t.process_id,
            u.nick_name AS promoter_user_name
        FROM
            (
                SELECT
                    p.book_id,
                    p.process_id,
                    p.record_no,
                    p.step_id,
                    p.state,
                    p.version_id,
                    p.promoter_user_id,
                    ROW_NUMBER() OVER ( PARTITION BY p.version_id ORDER BY p.create_time DESC ) AS row_num
                FROM
                    dtb_book_publish_process p
            ) AS t
                INNER JOIN dtb_book_version v ON t.version_id = v.version_id
                INNER JOIN dtb_book_publish_step s ON t.step_id = s.step_id
                INNER JOIN sys_user u ON u.user_id = t.promoter_user_id
                LEFT JOIN dtb_book b ON b.book_id = t.book_id
        WHERE
            t.row_num = 1 and t.book_id = #{bookId} and v.del_flag = '0'
            <if test="state != null">and t.state = #{state}</if>
            <if test="stepId != null">and t.step_id = #{stepId}</if>
    </select>

    <insert id="insertDtbBookVersion" parameterType="DtbBookVersion" useGeneratedKeys="true" keyProperty="versionId">
        insert into dtb_book_version
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="versionNo != null">version_no,</if>
            <if test="bookId != null">book_id,</if>
            <if test="versionIntroduce != null">version_introduce,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="versionNo != null">#{versionNo},</if>
            <if test="bookId != null">#{bookId},</if>
            <if test="versionIntroduce != null">#{versionIntroduce},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateDtbBookVersion" parameterType="DtbBookVersion">
        update dtb_book_version
        <trim prefix="SET" suffixOverrides=",">
            <if test="versionNo != null">version_no = #{versionNo},</if>
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="versionIntroduce != null">version_introduce = #{versionIntroduce},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where version_id = #{versionId}
    </update>

    <delete id="deleteDtbBookVersionByVersionId" parameterType="Long">
        delete from dtb_book_version where version_id = #{versionId}
    </delete>

    <delete id="deleteDtbBookVersionByVersionIds" parameterType="String">
        delete from dtb_book_version where version_id in 
        <foreach item="versionId" collection="array" open="(" separator="," close=")">
            #{versionId}
        </foreach>
    </delete>
</mapper>
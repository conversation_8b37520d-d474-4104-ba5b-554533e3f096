package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 发布流程审核人对象 dtb_book_publish_process_audit_user
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@Data
@TableName("dtb_book_publish_process_audit_user")
public class DtbBookPublishProcessAuditUser extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long auditUserId;

    /**
     * 审核人ID
     */
    @Excel(name = "审核人ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 实际处理人ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long handleUserId;

    /**
     * 部门ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Excel(name = "部门ID")
    private Long deptId;

    /**
     * 1未处理2通过3驳回
     */
    @Excel(name = "1未处理2通过3驳回")
    private Integer state;

    /**
     * 驳回理由
     */
    @Excel(name = "驳回理由")
    private String reason;

    /**
     * 流程ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Excel(name = "流程ID")
    private Long processId;
}

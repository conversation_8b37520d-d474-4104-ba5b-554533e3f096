package cn.dutp.book.task;


import cn.dutp.book.domain.DtbBookChapter;
import cn.dutp.book.domain.DtbBookChapterContent;
import cn.dutp.book.domain.DtbBookChapterResource;
import cn.dutp.book.domain.DutpTask;
import cn.dutp.book.mapper.DtbBookChapterMapper;
import cn.dutp.book.mapper.DutpTaskMapper;
import cn.dutp.book.service.IDtbBookChapterResourceService;
import cn.dutp.book.service.IDutpTaskService;
import cn.dutp.common.mongo.service.MongoService;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.WebSocketHttpHeaders;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.client.WebSocketClient;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.net.URI;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static cn.dutp.book.service.impl.DtbBookChapterContentServiceImpl.BOOK_CHAPTER_CONTENT;

/**
 * 题注更新任务
 */
@EnableScheduling
@EnableAsync
@Component("statisticalChapterCaptionTask")
@Slf4j
public class statisticalChapterCaptionTask {

    @Autowired
    private DtbBookChapterMapper chapterMapper;

    @Autowired
    private MongoService mongoService;

    @Autowired
    private DutpTaskMapper taskMapper;

    @Autowired
    private IDtbBookChapterResourceService bookChapterResourceService;

    @Autowired
    private IDutpTaskService taskService;

    /**
     * 每天0点20秒执行
     */
    @Async
    @Scheduled(cron = "40 0 0 * * ?")
    public void statisticalChapterData() {


        log.info("题注更新任务开始执行");
        List<DutpTask> taskList = taskMapper.selectList(new LambdaQueryWrapper<DutpTask>()
                .select(DutpTask::getDataId, DutpTask::getTaskId)
                .eq(DutpTask::getTaskType, 6));
        if (ObjectUtil.isNotEmpty(taskList)) {

            for (DutpTask task : taskList) {
                taskService.editCaptionStyle(task);
            }
        }
        log.info("题注更新任务执行结束");
    }


}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbUserBookNoteAttachmentMapper">
    
    <resultMap type="DtbUserBookNoteAttachment" id="DtbUserBookNoteAttachmentResult">
        <result property="attachmentId"    column="attachment_id"    />
        <result property="noteId"    column="note_id"    />
        <result property="attachmentType"    column="attachment_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDtbUserBookNoteAttachmentVo">
        select attachment_id, note_id, attachment_type, create_by, create_time, update_by, update_time from dtb_user_book_note_attachment
    </sql>

    <select id="selectDtbUserBookNoteAttachmentList" parameterType="DtbUserBookNoteAttachment" resultMap="DtbUserBookNoteAttachmentResult">
        <include refid="selectDtbUserBookNoteAttachmentVo"/>
        <where>  
            <if test="noteId != null "> and note_id = #{noteId}</if>
            <if test="attachmentType != null "> and attachment_type = #{attachmentType}</if>
        </where>
    </select>
    
    <select id="selectDtbUserBookNoteAttachmentByAttachmentId" parameterType="Long" resultMap="DtbUserBookNoteAttachmentResult">
        <include refid="selectDtbUserBookNoteAttachmentVo"/>
        where attachment_id = #{attachmentId}
    </select>

    <insert id="insertDtbUserBookNoteAttachment" parameterType="DtbUserBookNoteAttachment" useGeneratedKeys="true" keyProperty="attachmentId">
        insert into dtb_user_book_note_attachment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="noteId != null">note_id,</if>
            <if test="attachmentType != null">attachment_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="noteId != null">#{noteId},</if>
            <if test="attachmentType != null">#{attachmentType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDtbUserBookNoteAttachment" parameterType="DtbUserBookNoteAttachment">
        update dtb_user_book_note_attachment
        <trim prefix="SET" suffixOverrides=",">
            <if test="noteId != null">note_id = #{noteId},</if>
            <if test="attachmentType != null">attachment_type = #{attachmentType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where attachment_id = #{attachmentId}
    </update>

    <delete id="deleteDtbUserBookNoteAttachmentByAttachmentId" parameterType="Long">
        delete from dtb_user_book_note_attachment where attachment_id = #{attachmentId}
    </delete>

    <delete id="deleteDtbUserBookNoteAttachmentByAttachmentIds" parameterType="String">
        delete from dtb_user_book_note_attachment where attachment_id in 
        <foreach item="attachmentId" collection="array" open="(" separator="," close=")">
            #{attachmentId}
        </foreach>
    </delete>
</mapper>
package cn.dutp.book.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.book.domain.DtbUserBookStudyData;
import cn.dutp.book.service.IDtbUserBookStudyDataService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * DUTP-DTB_024学生学习数据Controller
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@RestController
@RequestMapping("/book/data")
public class DtbUserBookStudyDataController extends BaseController
{
    @Autowired
    private IDtbUserBookStudyDataService dtbUserBookStudyDataService;

    /**
     * 查询DUTP-DTB_024学生学习数据列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbUserBookStudyData dtbUserBookStudyData)
    {
        startPage();
        List<DtbUserBookStudyData> list = dtbUserBookStudyDataService.selectDtbUserBookStudyDataList(dtbUserBookStudyData);
        return getDataTable(list);
    }

    /**
     * 导出DUTP-DTB_024学生学习数据列表
     */
    @RequiresPermissions("book:data:export")
    @Log(title = "导出DUTP-DTB_024学生学习数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbUserBookStudyData dtbUserBookStudyData)
    {
        List<DtbUserBookStudyData> list = dtbUserBookStudyDataService.selectDtbUserBookStudyDataList(dtbUserBookStudyData);
        ExcelUtil<DtbUserBookStudyData> util = new ExcelUtil<DtbUserBookStudyData>(DtbUserBookStudyData.class);
        util.exportExcel(response, list, "DUTP-DTB_024学生学习数据数据");
    }

    /**
     * 获取DUTP-DTB_024学生学习数据详细信息
     */
    @RequiresPermissions("book:data:query")
    @GetMapping(value = "/{dataId}")
    public AjaxResult getInfo(@PathVariable("dataId") Long dataId)
    {
        return success(dtbUserBookStudyDataService.selectDtbUserBookStudyDataByDataId(dataId));
    }

    /**
     * 新增DUTP-DTB_024学生学习数据
     */
    @RequiresPermissions("book:data:add")
    @Log(title = "新增DUTP-DTB_024学生学习数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbUserBookStudyData dtbUserBookStudyData)
    {
        return toAjax(dtbUserBookStudyDataService.insertDtbUserBookStudyData(dtbUserBookStudyData));
    }

    /**
     * 修改DUTP-DTB_024学生学习数据
     */
    @RequiresPermissions("book:data:edit")
    @Log(title = "修改DUTP-DTB_024学生学习数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbUserBookStudyData dtbUserBookStudyData)
    {
        return toAjax(dtbUserBookStudyDataService.updateDtbUserBookStudyData(dtbUserBookStudyData));
    }

    /**
     * 删除DUTP-DTB_024学生学习数据
     */
    @RequiresPermissions("book:data:remove")
    @Log(title = "删除DUTP-DTB_024学生学习数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{dataIds}")
    public AjaxResult remove(@PathVariable Long[] dataIds)
    {
        return toAjax(dtbUserBookStudyDataService.deleteDtbUserBookStudyDataByDataIds(Arrays.asList(dataIds)));
    }
}

package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import com.baomidou.mybatisplus.annotation.TableField;

/**
 * 试卷小题对象 dtb_test_paper_question
 *
 * <AUTHOR>
 * @date 2025-02-08
 */
@Data
public class DtbTestPaperQuestion extends BaseEntity
        {
private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long paperQuestionId;

    /** $column.columnComment */
            @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
            @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long paperId;

    /** $column.columnComment */
            @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
            @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long collectionId;

    /** $column.columnComment */
            @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
            @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long questionId;

    /** 排序 */
            @Excel(name = "排序")
    private Integer sort;

    /** 小题分数 */
    @Excel(name = "小题分数")
    private Integer questionScore;


    /** 小题内容 */
    @TableField(exist = false)
    private DtbUserQuestion questionContent;

    


@Override

public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("paperQuestionId", getPaperQuestionId())
            .append("paperId", getPaperId())
            .append("collectionId", getCollectionId())
            .append("questionId", getQuestionId())
            .append("sort", getSort())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
        .toString();
        }
        }

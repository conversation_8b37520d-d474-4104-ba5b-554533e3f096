package cn.dutp.book.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.book.domain.vo.BookQuestionRecycleVO;
import cn.dutp.book.domain.vo.DtbBookQuestionDetailVo;
import cn.dutp.common.core.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.book.domain.DtbBookQuestion;
import cn.dutp.book.service.IDtbBookQuestionService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 数字教材习题Controller
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@RestController
@RequestMapping("/bookQuestion")
public class DtbBookQuestionController extends BaseController
{
    @Autowired
    private IDtbBookQuestionService dtbBookQuestionService;

    /**
     * 查询数字教材习题列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbBookQuestion dtbBookQuestion)
    {
        startPage();
        List<DtbBookQuestion> list = dtbBookQuestionService.selectDtbBookQuestionList(dtbBookQuestion);
        return getDataTable(list);
    }


    /**
     * 查询数字教材习题列表(包含选项)
     */
    @GetMapping("/listWithOptions")
    public TableDataInfo listWithOptions(DtbBookQuestion dtbBookQuestion)
    {


        //如果没有提供目录id或bookId，那么不允许查询
        if(dtbBookQuestion.getFolderId() == null && dtbBookQuestion.getBookId() == null){
            throw new ServiceException("请指定书名或目录进行查询");
        }

        startPage();
        List<DtbBookQuestionDetailVo> list = dtbBookQuestionService.selectDtbUserQuestionListWithOptions(dtbBookQuestion);
        return getDataTable(list);
    }

    /**
     * 导出数字教材习题列表
     */
    @RequiresPermissions("book:bookQuestion:export")
    @Log(title = "导出数字教材习题", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbBookQuestion dtbBookQuestion)
    {
        List<DtbBookQuestion> list = dtbBookQuestionService.selectDtbBookQuestionList(dtbBookQuestion);
        ExcelUtil<DtbBookQuestion> util = new ExcelUtil<DtbBookQuestion>(DtbBookQuestion.class);
        util.exportExcel(response, list, "数字教材习题数据");
    }

    /**
     * 获取数字教材习题详细信息
     */
    @RequiresPermissions("book:bookQuestion:query")
    @GetMapping(value = "/{bookQuestionId}")
    public AjaxResult getInfo(@PathVariable("bookQuestionId") Long bookQuestionId)
    {
        return success(dtbBookQuestionService.selectDtbBookQuestionByBookQuestionId(bookQuestionId));
    }

    /**
     * 新增数字教材习题
     */
    @RequiresPermissions("book:bookQuestion:add")
    @Log(title = "新增数字教材习题", businessType = BusinessType.INSERT)
    @PostMapping
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult add(@RequestBody DtbBookQuestion dtbBookQuestion)
    {
        return success(dtbBookQuestionService.insertDtbBookQuestion(dtbBookQuestion));
    }


    /**
     * 新增数字教材习题
     */
    @RequiresPermissions("book:bookQuestion:add")
    @Log(title = "新增数字教材习题", businessType = BusinessType.INSERT)
    @PostMapping("/addBatch")
    public AjaxResult addBatch(@RequestBody List<DtbBookQuestion> dtbBookQuestions )
    {
        //循环加入
        for(DtbBookQuestion dtbBookQuestion : dtbBookQuestions){
            dtbBookQuestionService.insertDtbBookQuestion(dtbBookQuestion);
        }
        return success(dtbBookQuestions);
    }

    /**
     * 修改数字教材习题
     */
    @RequiresPermissions("book:bookQuestion:edit")
    @Log(title = "修改数字教材习题", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookQuestion dtbBookQuestion)
    {
        return success(dtbBookQuestionService.updateDtbBookQuestion(dtbBookQuestion));
    }

    /**
     * 删除数字教材习题
     */
    @RequiresPermissions("book:bookQuestion:remove")
    @Log(title = "删除数字教材习题", businessType = BusinessType.DELETE)
    @DeleteMapping("/{bookQuestionIds}")
    public AjaxResult remove(@PathVariable Long[] bookQuestionIds)
    {
        return toAjax(dtbBookQuestionService.deleteDtbBookQuestionByBookQuestionIds(Arrays.asList(bookQuestionIds)));
    }

    /**
     * 将数字教材习题移入回收站
     */
    @RequiresPermissions("book:bookQuestion:remove")
    @Log(title = "移入回收站", businessType = BusinessType.UPDATE)
    @PutMapping("/recycle/{bookQuestionIds}")
    public AjaxResult moveToRecycleBin(@PathVariable Long[] bookQuestionIds)
    {
        return toAjax(dtbBookQuestionService.moveToRecycleBin(Arrays.asList(bookQuestionIds)));
    }

    /**
     * 从回收站恢复数字教材习题
     */
    @RequiresPermissions("book:bookQuestion:remove")
    @Log(title = "从回收站恢复", businessType = BusinessType.UPDATE)
    @PutMapping("/restore/{bookQuestionIds}")
    public AjaxResult restoreFromRecycleBin(@PathVariable Long[] bookQuestionIds)
    {
        return toAjax(dtbBookQuestionService.restoreFromRecycleBin(Arrays.asList(bookQuestionIds)));
    }

    /**
     * 查询回收站中的数字教材习题列表
     */
    @RequiresPermissions("book:bookQuestion:remove")
    @GetMapping("/recycleBin/list")
    public TableDataInfo recycleBinList(DtbBookQuestion dtbBookQuestion)
    {
        startPage();
    
        List<BookQuestionRecycleVO> list = dtbBookQuestionService.recycleBinList(dtbBookQuestion);
        return getDataTable(list);
    }


    /**
     * 新增数字教材习题
     */
    @RequiresPermissions("book:bookQuestion:add")
    @Log(title = "批量导入数字教材习题", businessType = BusinessType.INSERT)
    @PostMapping("/import")
    public AjaxResult importQuestions(@RequestBody List<DtbBookQuestion> dtbBookQuestions)
    {

        return AjaxResult.success(dtbBookQuestionService.importQuestions(dtbBookQuestions));
    }



 
}


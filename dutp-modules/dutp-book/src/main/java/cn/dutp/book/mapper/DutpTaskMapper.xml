<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DutpTaskMapper">

    <resultMap type="cn.dutp.book.domain.DutpTask" id="DutpTaskResult">
        <result property="taskId" column="task_id"/>
        <result property="taskType" column="task_type"/>
        <result property="taskContent" column="task_content"/>
        <result property="dataId" column="data_id"/>
        <result property="userId" column="user_id"/>
        <result property="taskRate" column="task_rate"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="taskState" column="task_state"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <sql id="selectDutpTaskVo">
        select task_id,
               task_type,
               task_content,
               data_id,
               user_id,
               task_rate,
               create_by,
               create_time,
               update_by,
               update_time,
               del_flag
        from dutp_task
    </sql>

    <select id="selectTaskList" resultType="cn.dutp.book.domain.DutpTask">
        select
        COALESCE(b.book_no,b2.book_no,null) as 'bookNo',
        COALESCE(b.book_name,b2.book_name,null) as 'bookName',
        u.user_name as 'userName',
        u.phonenumber as 'userPhone',
        t.task_id as 'taskId',
        t.data_id as 'dataId',
        t.user_id as 'userId',
        t.task_type as 'taskType',
        t.task_content as 'taskContent',
        t.start_time as 'startTime',
        t.end_time as 'endTime',
        t.remark,
        t.task_state as 'taskState',
        t.create_time as 'createTime'
        from dutp_task t
        left join dtb_book b on t.data_id = b.book_id
        left join dtb_book_chapter bc on t.data_id = bc.chapter_id
        left join dtb_book b2 on bc.book_id = b2.book_id
        left join sys_user u on t.user_id = u.user_id
        where t.del_flag = '0' and t.user_id = #{userId}
        <if test="param.taskType != null and param.taskType != ''">
            and t.task_type = #{param.taskType}
        </if>
        <if test="param.taskState != null">
            and t.task_state = #{param.taskState}
        </if>
        <if test="param.bookName != null and param.bookName != ''">
            and (b.book_name like concat('%', #{param.bookName}, '%') or b2.book_name like concat('%',
            #{param.bookName}, '%'))
        </if>
        order by t.create_time desc

    </select>

</mapper>
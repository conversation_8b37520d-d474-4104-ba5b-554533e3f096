package cn.dutp.book.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import cn.dutp.book.domain.DtbBookType;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
/**
 * 中图分类Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Repository
public interface DtbBookTypeMapper extends BaseMapper<DtbBookType>
{

    @Select("SELECT parent_id FROM dtb_book_type WHERE type_id = #{parentId} and del_flag = '0'")
    Long getParentIdByTypeId(Long parentId);

    int checkIsRepeatByTypeName(@Param("typeId") Long typeId, @Param("typeName") String typeName);

    int checkIsRepeatByTypeCode(@Param("typeId") Long typeId, @Param("typeCode") String typeCode);

    @Select("SELECT type_id FROM dtb_book_type WHERE parent_id = #{parentId} and del_flag = '0'")
    List<Long> getTypeIdListByParentId(long parentId);

    @Select("select count(*) from dtb_book_type where parent_id = #{typeId} and del_flag = '0'")
    int checkCountByTypeId(Long typeId);

    @Select("select count(*) from dtb_book where book_type = #{typeId} and del_flag = '0'")
    int checkBookCountByTypeId(Long typeId);

    @Select("select type_id, parent_id, type_name from dtb_book_type where type_id = #{typeId} and del_flag = '0'")
    DtbBookType getBookTypeById(Long typeId);
}

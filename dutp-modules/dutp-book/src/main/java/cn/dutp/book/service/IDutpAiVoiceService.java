package cn.dutp.book.service;

import cn.dutp.book.domain.DutpAiVoice;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
/**
 * 语音音色Service接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
public interface IDutpAiVoiceService extends IService<DutpAiVoice>
{
    /**
     * 查询语音音色
     *
     * @param voiceId 语音音色主键
     * @return 语音音色
     */
    public DutpAiVoice selectDutpAiVoiceByVoiceId(Long voiceId);

    /**
     * 查询语音音色列表
     *
     * @param dutpAiVoice 语音音色
     * @return 语音音色集合
     */
    public List<DutpAiVoice> selectDutpAiVoiceList(DutpAiVoice dutpAiVoice);

    /**
     * 新增语音音色
     *
     * @param dutpAiVoice 语音音色
     * @return 结果
     */
    public boolean insertDutpAiVoice(DutpAiVoice dutpAiVoice);

    /**
     * 修改语音音色
     *
     * @param dutpAiVoice 语音音色
     * @return 结果
     */
    public boolean updateDutpAiVoice(DutpAiVoice dutpAiVoice);

    /**
     * 批量删除语音音色
     *
     * @param voiceIds 需要删除的语音音色主键集合
     * @return 结果
     */
    public boolean deleteDutpAiVoiceByVoiceIds(List<Long> voiceIds);

}

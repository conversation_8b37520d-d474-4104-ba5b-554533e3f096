package cn.dutp.book.domain.vo;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 笔记附件对象 dtb_user_book_note_attachment
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
public class DtbUserBookNoteAttachmentVO
{
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long attachmentId;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long noteId;
    private Integer attachmentType;
    private String attachmentUrl;
    private Double attachmentSize;
    private String attachmentName;
}

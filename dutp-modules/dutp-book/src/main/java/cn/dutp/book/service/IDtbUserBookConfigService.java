package cn.dutp.book.service;

import java.util.List;
import cn.dutp.book.domain.DtbUserBookConfig;
import cn.dutp.common.core.web.domain.AjaxResult;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 教材配置Service接口
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
public interface IDtbUserBookConfigService extends IService<DtbUserBookConfig>
{
    /**
     * 查询教材配置
     *
     * @param configId 教材配置主键
     * @return 教材配置
     */
    public DtbUserBookConfig selectDtbUserBookConfigByConfigId(Long configId);

    /**
     * 查询教材配置列表
     *
     * @param dtbUserBookConfig 教材配置
     * @return 教材配置集合
     */
    public List<DtbUserBookConfig> selectDtbUserBookConfigList(DtbUserBookConfig dtbUserBookConfig);

    /**
     * 新增教材配置
     *
     * @param dtbUserBookConfig 教材配置
     * @return 结果
     */
    public boolean insertDtbUserBookConfig(DtbUserBookConfig dtbUserBookConfig);

    /**
     * 修改教材配置
     *
     * @param dtbUserBookConfig 教材配置
     * @return 结果
     */
    public AjaxResult updateDtbUserBookConfig(DtbUserBookConfig dtbUserBookConfig);

    /**
     * 批量删除教材配置
     *
     * @param configIds 需要删除的教材配置主键集合
     * @return 结果
     */
    public boolean deleteDtbUserBookConfigByConfigIds(List<Long> configIds);

}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbUserBookMarkMapper">
    
    <resultMap type="DtbUserBookMark" id="DtbUserBookMarkResult">
        <result property="markId"    column="mark_id"    />
        <result property="pageNumber"    column="page_number"    />
        <result property="userId"    column="user_id"    />
        <result property="bookId"    column="book_id"    />
        <result property="chapterId"    column="chapter_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectDtbUserBookMarkVo">
        select mark_id, page_number, user_id, book_id, chapter_id, create_by, create_time, update_by, update_time, del_flag from dtb_user_book_mark
    </sql>

    <select id="selectDtbUserBookMarkList" parameterType="DtbUserBookMark" resultMap="DtbUserBookMarkResult">
        <include refid="selectDtbUserBookMarkVo"/>
        <where>  
            <if test="pageNumber != null "> and page_number = #{pageNumber}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="bookId != null "> and book_id = #{bookId}</if>
            <if test="chapterId != null "> and chapter_id = #{chapterId}</if>
        </where>
    </select>
    
    <select id="selectDtbUserBookMarkByMarkId" parameterType="Long" resultMap="DtbUserBookMarkResult">
        <include refid="selectDtbUserBookMarkVo"/>
        where mark_id = #{markId}
    </select>

    <insert id="insertDtbUserBookMark" parameterType="DtbUserBookMark" useGeneratedKeys="true" keyProperty="markId">
        insert into dtb_user_book_mark
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pageNumber != null">page_number,</if>
            <if test="userId != null">user_id,</if>
            <if test="bookId != null">book_id,</if>
            <if test="chapterId != null">chapter_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pageNumber != null">#{pageNumber},</if>
            <if test="userId != null">#{userId},</if>
            <if test="bookId != null">#{bookId},</if>
            <if test="chapterId != null">#{chapterId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateDtbUserBookMark" parameterType="DtbUserBookMark">
        update dtb_user_book_mark
        <trim prefix="SET" suffixOverrides=",">
            <if test="pageNumber != null">page_number = #{pageNumber},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="chapterId != null">chapter_id = #{chapterId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where mark_id = #{markId}
    </update>

    <delete id="deleteDtbUserBookMarkByMarkId" parameterType="Long">
        delete from dtb_user_book_mark where mark_id = #{markId}
    </delete>

    <delete id="deleteDtbUserBookMarkByMarkIds" parameterType="String">
        delete from dtb_user_book_mark where mark_id in 
        <foreach item="markId" collection="array" open="(" separator="," close=")">
            #{markId}
        </foreach>
    </delete>
    <select id="selectReaderUserBookMarkList" resultType="cn.dutp.book.domain.vo.DtbUserBookMarkVO">
        SELECT
            mark.mark_id,
            mark.chapter_id,
            mark.page_number,
            mark.page_content,
            mark.create_time,
            mark.book_id,
            c.chapter_name
        FROM
            dtb_user_book_mark mark
        INNER JOIN dtb_book_chapter c ON mark.chapter_id = c.chapter_id
        WHERE
            1 = 1
        AND mark.del_flag = 0
        AND mark.book_id = #{bookId}
        AND user_id = #{userId}
        ORDER BY
            mark.page_number ASC
    </select>
</mapper>
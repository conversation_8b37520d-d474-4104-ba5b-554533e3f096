package cn.dutp.book.uitls.xunfei;

import cn.dutp.domain.DutpAiPrompt;
import cn.dutp.common.security.utils.SecurityUtils;
import com.alibaba.nacos.shaded.com.google.gson.JsonArray;
import com.alibaba.nacos.shaded.com.google.gson.JsonObject;

import java.util.HashMap;
import java.util.Map;
/**
 * 请求大模型工具类
 */
public class ChatAiUtil {

    /**
     * 组装http请求体
     */
    public static String buildHttpBody(DutpAiPrompt dutpAiPrompt) throws Exception {
        JsonObject requestBody = new JsonObject();

        // 填充 model 和 user 字段
        requestBody.addProperty("model", dutpAiPrompt.getModelType());
        requestBody.addProperty("user", SecurityUtils.getUserId().toString());

        // 构建 messages 数组
        JsonArray messages = new JsonArray();
        JsonObject systemMessage = new JsonObject();
        systemMessage.addProperty("role", "system");
        systemMessage.addProperty("content", dutpAiPrompt.getPrompt());
        messages.add(systemMessage);

        JsonObject userMessage = new JsonObject();
        userMessage.addProperty("role", "user");
//        userMessage.addProperty("content", dutpAiPrompt.getQuestion());
        messages.add(userMessage);

        requestBody.add("messages", messages);

        // 添加可选参数
        requestBody.addProperty("temperature", 0.5);
        requestBody.addProperty("top_k", 4);
        requestBody.addProperty("stream", false);
        requestBody.addProperty("max_tokens", 1024);
        requestBody.addProperty("presence_penalty", 1);
        requestBody.addProperty("frequency_penalty", 1);

        // 构建 tools 数组
        JsonArray tools = new JsonArray();
        JsonObject functionTool = new JsonObject();
        functionTool.addProperty("type", "function");
        JsonObject functionDetails = new JsonObject();
        functionDetails.addProperty("name", "str2int");
        functionDetails.addProperty("description", "");
        JsonObject functionParameters = new JsonObject(); // 如果有参数定义，可以在这里添加
        functionDetails.add("parameters", functionParameters);
        functionTool.add("function", functionDetails);
        tools.add(functionTool);

        JsonObject webSearchTool = new JsonObject();
        webSearchTool.addProperty("type", "web_search");
        JsonObject webSearchDetails = new JsonObject();
        webSearchDetails.addProperty("enable", true);
        webSearchTool.add("web_search", webSearchDetails);
        tools.add(webSearchTool);

        requestBody.add("tools", tools);

        // 构建 response_format 对象
        JsonObject responseFormat = new JsonObject();
        responseFormat.addProperty("type", "json_object");
        requestBody.add("response_format", responseFormat);

        // 构建 suppress_plugin 数组
        JsonArray suppressPlugin = new JsonArray();
        suppressPlugin.add("knowledge");
        requestBody.add("suppress_plugin", suppressPlugin);

        return requestBody.toString();
    }

    /**
     * 组装http请求头
     */
    public static Map<String, String> buildHttpHeader(String apiPassword){
        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("Authorization", "Bearer " + apiPassword);
        return header;
    }
}

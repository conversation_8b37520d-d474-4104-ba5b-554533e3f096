<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbBookChapterEditorMapper">

    <insert id="batchSave">
        insert into dtb_book_chapter_editor(book_id, chapter_id, user_id, role_type) values
        <foreach item="item" collection="userIds" separator=",">
            (#{bookId}, #{chapterId}, #{item}, #{roleType})
        </foreach>
    </insert>

    <select id="queryBookChapterEditor" resultType="cn.dutp.book.domain.DtbBookChapterEditor">
        SELECT e.user_id,
               e.role_type
        FROM dtb_book_chapter c
                 INNER JOIN dtb_book b ON b.book_id = c.book_id
            AND c.version_id = b.current_version_id
                 INNER JOIN dtb_book_chapter_editor e ON c.chapter_id = e.chapter_id
        WHERE c.book_id = #{bookId}
          AND c.del_flag = '0'
          and c.sort = #{sort}
    </select>
    <select id="queryUserByBookId" resultType="java.lang.Long">
        SELECT DISTINCT e.user_id
        FROM dtb_book_chapter_editor e
                 INNER JOIN dtb_book_chapter c ON c.chapter_id = e.chapter_id
                 INNER JOIN dtb_book b ON b.book_id = c.book_id
            AND c.version_id = b.current_version_id
        where b.book_id = #{bookId}
          and e.role_type = #{roleType}
    </select>
    <select id="queryUserList" resultType="cn.dutp.book.domain.DtbBookChapterEditor">
        SELECT
            e.user_id,
            e.chapter_id
        FROM
            dtb_book_chapter_editor e
                INNER JOIN dtb_book_chapter c ON c.chapter_id = e.chapter_id
                INNER JOIN dtb_book b ON b.book_id = c.book_id
                AND c.version_id = b.current_version_id
        WHERE
            c.del_flag = '0'
          AND b.book_id = #{bookId}
          AND e.role_type = 1
    </select>

</mapper>
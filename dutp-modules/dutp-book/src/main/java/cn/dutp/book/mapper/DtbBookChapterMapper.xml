<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbBookChapterMapper">

    <update id="updateChapterTemplate">
        UPDATE dtb_book_chapter c set c.template_id = #{templateId} WHERE c.version_id = #{versionId}
    </update>

    <select id="adminQueryBookChapterTreeByBookId" resultType="cn.dutp.book.domain.vo.DtbBookChapterTreeVO">
        SELECT
            c.book_id,
            c.chapter_id,
            c.chapter_id AS id,
            c.chapter_name AS NAME,
            c.free,
            c.complete_rate,
            c.chapter_status,
            c.back_apply,
            b.publish_status,
            b.current_step_id,
            b.current_version_id,
            0 AS parent_id,
            GROUP_CONCAT( u.nick_name ) AS editor
        FROM
            dtb_book_chapter c
                INNER JOIN dtb_book b ON c.book_id = b.book_id
                AND b.current_version_id = c.version_id
                LEFT JOIN dtb_book_chapter_editor e ON c.chapter_id = e.chapter_id
                AND e.role_type = 1
                LEFT JOIN sys_user u ON u.user_id = e.user_id
        WHERE
            c.book_id = #{bookId}
          AND c.del_flag = '0'
        GROUP BY
            c.chapter_id
        ORDER BY
            sort asc
    </select>

    <select id="adminQueryProcessChapterTreeByBookId" resultType="cn.dutp.book.domain.vo.DtbBookChapterTreeVO">
        SELECT
            c.book_id,
            c.chapter_id,
            c.chapter_id AS id,
            c.chapter_name AS NAME,
            c.free,
            c.complete_rate,
            c.chapter_status,
            c.back_apply,
            b.publish_status,
            b.current_step_id,
            b.current_version_id,
            b.last_version_id,
            0 AS parent_id,
            GROUP_CONCAT( u.nick_name ) AS editor
        FROM
            dtb_book_chapter c
                INNER JOIN dtb_book b ON c.book_id = b.book_id
                AND b.last_version_id = c.version_id
                LEFT JOIN dtb_book_chapter_editor e ON c.chapter_id = e.chapter_id
                AND e.role_type = 1
                LEFT JOIN sys_user u ON u.user_id = e.user_id
        WHERE
            c.book_id = #{bookId}
          AND c.del_flag = '0'
        GROUP BY
            c.chapter_id
        ORDER BY
            sort asc
    </select>
    <select id="listForRecycle" resultType="cn.dutp.book.domain.DtbBookChapter">
        SELECT
            c.book_id,
            c.chapter_id,
            c.chapter_name,
            c.update_time
        FROM
            dtb_book_chapter c
                INNER JOIN dtb_book b ON c.book_id = b.book_id
                AND b.current_version_id = c.version_id
        WHERE
            c.book_id = #{bookId}
          AND c.del_flag = '2' and c.del_user_id = #{delUserId}
        ORDER BY
            sort asc
    </select>

    <select id="listForSort" resultType="cn.dutp.book.domain.DtbBookChapter">
        SELECT
            c.sort,
            c.free,
            c.chapter_id,
            c.chapter_name
        FROM
            dtb_book_chapter c
                INNER JOIN dtb_book b ON c.book_id = b.book_id
                AND b.current_version_id = c.version_id
        WHERE
            c.book_id = #{bookId}
          AND c.del_flag = '0'
        ORDER BY
            sort asc
    </select>

    <select id="dtbBookChapterMapper" resultType="cn.dutp.book.domain.DtbBookChapter">
        SELECT
            c.chapter_id,
            c.chapter_name,
            c.sort
        FROM
            dtb_book_chapter c
                INNER JOIN dtb_book b ON c.book_id = b.book_id
                AND b.current_version_id = c.version_id
        WHERE
            c.book_id = #{bookId}
          AND c.del_flag = '0'
        ORDER BY
            sort asc
    </select>

    <select id="dtbBookChapterMapperLastVersion" resultType="cn.dutp.book.domain.DtbBookChapter">
        SELECT
            c.chapter_id,
            c.chapter_name,
            c.sort
        FROM
            dtb_book_chapter c
                INNER JOIN dtb_book b ON c.book_id = b.book_id
                AND b.last_version_id = c.version_id
        WHERE
            c.book_id = #{bookId}
          AND c.del_flag = '0'
        ORDER BY
            sort asc
    </select>
    <select id="listForFinalizedSubmit" resultType="cn.dutp.book.domain.DtbBookChapter">
        SELECT
            c.chapter_id,
            c.chapter_name,
            c.chapter_status
        FROM
            dtb_book_chapter c
                INNER JOIN dtb_book b ON c.book_id = b.book_id
                AND b.current_version_id = c.version_id
        WHERE
            c.book_id = #{bookId}
          AND c.del_flag = '0'
        ORDER BY
            sort asc
    </select>

    <select id="queryBookChapterTreeByBookId" resultType="cn.dutp.book.domain.vo.DtbBookChapterTreeVO">
        select c.book_id, c.chapter_id, c.chapter_id as id, c.chapter_name as name, 0 as parent_id
        from dtb_book_chapter c
            INNER JOIN dtb_book b ON c.book_id = b.book_id
            AND b.current_version_id = c.version_id
               where c.book_id = #{bookId} and c.del_flag = '0' order by c.sort desc
    </select>
    <select id="queryCopyBookChapterMapper" resultType="cn.dutp.book.domain.DtbBookChapter">
        SELECT
            c.chapter_name,
            c.chapter_total_page,
            c.chapter_status,
            c.sort,
            c.free,
            c.template_id
        FROM
            dtb_book_chapter c
                INNER JOIN dtb_book b ON c.book_id = b.book_id
                AND b.current_version_id = c.version_id
        WHERE
            c.book_id = #{bookId}
          AND c.del_flag = '0'
    </select>

    <select id="queryChapterId" resultType="java.lang.Long">
        SELECT
            c.chapter_id
        FROM
            dtb_book_chapter c
                INNER JOIN dtb_book b ON c.book_id = b.book_id
                AND b.current_version_id = c.version_id
        WHERE
            c.book_id = #{bookId}
          AND c.del_flag = '0'
          AND c.sort = #{sort}
        limit 1
    </select>
    <select id="chapterInfo" resultType="cn.dutp.book.domain.DtbBookChapter">
        SELECT
            c.chapter_id,
            c.chapter_name,
            b.book_name,
            c.chapter_status
        FROM
            dtb_book_chapter c
                INNER JOIN dtb_book b ON c.book_id = b.book_id
        WHERE
            c.chapter_id = #{chapterId}
          AND c.del_flag = '0'
    </select>
    <select id="queryMaxSort" resultType="java.lang.Integer">
        SELECT
            c.sort + 2
        FROM
            dtb_book_chapter c
                INNER JOIN dtb_book b ON c.book_id = b.book_id
                AND b.current_version_id = c.version_id
        WHERE
            b.book_id = #{bookId}
          AND c.del_flag = '0'
        ORDER BY
            c.sort DESC
            LIMIT 1
    </select>
    <select id="queryBookByChapterId" resultType="cn.dutp.book.domain.DtbBookChapter">
        SELECT
            c.chapter_name,
            c.chapter_id,
            b.book_name,
            b.book_no,
            b.book_id,
            c.version_id
        FROM
            dtb_book_chapter c
                INNER JOIN dtb_book b ON c.book_id = b.book_id
        WHERE
            c.chapter_id = #{chapterId}
          AND c.del_flag = '0'

    </select>
    <select id="queryTemplateIdByBookId" resultType="java.lang.Long">
        SELECT
            c.template_id
        FROM
            dtb_book_chapter c
        WHERE
            c.book_id = #{bookId}
          AND c.del_flag = '0'
        limit 1
    </select>
    <select id="queryBookChapterDataList"  parameterType="cn.dutp.book.domain.DtbBookChapter" resultType="cn.dutp.book.domain.DtbBookChapter">
        SELECT
            c.chapter_name,
            c.chapter_object_id,
            c.book_id,
            c.sort,
            c.version_id,
            c.chapter_total_page,
            c.free,
            c.chapter_status,
            c.back_apply,
            c.frozen,
            c.state,
            c.complete_rate,
            c.del_user_id,
            c.template_id,
            c.remark,
            c.chapter_id
        FROM
            dtb_book_chapter c
                INNER JOIN dtb_book b ON c.book_id = b.book_id
                AND b.current_version_id = c.version_id
        WHERE
            c.book_id = #{bookId}
          AND c.del_flag = '0'
        ORDER BY
            c.sort
    </select>
    <select id="queryBookChapterListByBookId" resultType="cn.dutp.book.domain.DtbBookChapter">
        select b.book_id, c.chapter_id, c.complete_rate, c.chapter_name
        FROM
            dtb_book_chapter c
                INNER JOIN dtb_book b ON c.book_id = b.book_id
                AND b.current_version_id = c.version_id
        WHERE
            c.book_id = #{bookId}
          AND c.del_flag = '0'
        order by sort
    </select>

    <select id="selectByBookAndVersion" parameterType="java.util.List" resultType="cn.dutp.book.domain.DtbBookChapter">
        select
            c.chapter_id,
            c.chapter_name,
            c.book_id
        from
            dtb_book_chapter c
        left join
            dtb_book b on b.book_id = c.book_id
        where
            c.del_flag = 0 and c.chapter_status = 2
        and
        <foreach collection="allBook" item="item" separator="OR">
            (c.version_id = #{item.currentVersionId} and c.book_id = #{item.bookId})
        </foreach>

    </select>

    <select id="homepageChapterSearchByPage" resultType="cn.dutp.book.domain.DtbBookChapter">
        SELECT
        c.chapter_id,
        c.chapter_name,
        c.book_id,
        c.sort,
        c.chapter_total_page,
        c.version_id,
        CASE WHEN ub.user_id IS NOT NULL THEN 2 ELSE c.free END AS free,
        c.chapter_status,
        c.back_apply,
        c.frozen,
        c.state,
        c.complete_rate,
        c.del_user_id,
        c.template_id,
        b.book_name,
        b.author_label,
        b.author_value
        FROM dtb_book AS b
        INNER JOIN dtb_book_chapter AS c
        ON b.book_id = c.book_id
        AND b.current_version_id = c.version_id
        LEFT JOIN dtb_user_book ub
        ON ub.book_id = c.book_id
        AND ub.user_id = #{userId}
        AND ub.del_flag = 0
        AND ub.expire_date >= NOW()
        INNER JOIN (
        SELECT b2.book_id
        FROM dtb_book AS b2
        INNER JOIN dtb_book_chapter AS c2
        ON b2.book_id = c2.book_id
        AND b2.current_version_id = c2.version_id
        WHERE
        c2.del_flag = 0
        AND b2.del_flag = 0
        AND b2.master_flag != 3
        AND b2.shelf_state = 1
        AND b2.book_organize = 1
        <if test="chapterName != null and chapterName != ''">
            AND c2.chapter_name LIKE CONCAT('%',#{chapterName}, '%')
        </if>
        GROUP BY b2.book_id
        LIMIT #{pageSize} OFFSET #{offset}
        ) AS sub ON b.book_id = sub.book_id
        <where>
            <if test="chapterName != null and chapterName != ''">
                c.chapter_name LIKE CONCAT('%',#{chapterName}, '%')
            </if>
        </where>
    </select>

    <select id="homepageChapterSearchPagetotal" resultType="Long">
        SELECT
         COUNT(DISTINCT b.book_id)
        FROM
         dtb_book AS b
        INNER JOIN dtb_book_chapter AS c ON b.book_id = c.book_id
         AND b.current_version_id = c.version_id
        <where>
            c.del_flag = 0
            AND b.del_flag = 0
            AND b.master_flag != 3
            AND b.shelf_state = 1
            AND b.book_organize = 1
            <if test="chapterName != null and chapterName != ''">
                AND c.chapter_name LIKE CONCAT('%',#{chapterName}, '%')
            </if>
        </where>
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbBookQuestionMapper">

    <resultMap type="DtbBookQuestion" id="DtbBookQuestionResult">
        <result property="bookQuestionId"    column="book_question_id"    />
        <result property="questionType"    column="question_type"    />
        <result property="chapterId"    column="chapter_id"    />
        <result property="bookId"    column="book_id"    />
        <result property="sort"    column="sort"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="folderId"    column="folder_id"    />
        <result property="userQuestionId"    column="user_question_id"    />
    </resultMap>

    <sql id="selectDtbBookQuestionVo">
        select book_question_id, question_type, chapter_id, book_id, sort, create_by, create_time, update_by, update_time, del_flag, folder_id, user_question_id from dtb_book_question
    </sql>

    <select id="selectDtbBookQuestionList" parameterType="DtbBookQuestion" resultMap="DtbBookQuestionResult">
        <include refid="selectDtbBookQuestionVo"/>
        <where>  
            <if test="questionType != null "> and question_type = #{questionType}</if>
            <if test="chapterId != null "> and chapter_id = #{chapterId}</if>
            <if test="bookId != null "> and book_id = #{bookId}</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="folderId != null "> and folder_id = #{folderId}</if>
            <if test="userQuestionId != null "> and user_question_id = #{userQuestionId}</if>
        </where>
    </select>
    
    <select id="selectDtbBookQuestionByBookQuestionId" parameterType="Long" resultMap="DtbBookQuestionResult">
        <include refid="selectDtbBookQuestionVo"/>
        where book_question_id = #{bookQuestionId}
    </select>

    <insert id="insertDtbBookQuestion" parameterType="DtbBookQuestion" useGeneratedKeys="true" keyProperty="bookQuestionId">
        insert into dtb_book_question
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="questionType != null">question_type,</if>
            <if test="chapterId != null">chapter_id,</if>
            <if test="bookId != null">book_id,</if>
            <if test="sort != null">sort,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="folderId != null">folder_id,</if>
            <if test="userQuestionId != null">user_question_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="questionType != null">#{questionType},</if>
            <if test="chapterId != null">#{chapterId},</if>
            <if test="bookId != null">#{bookId},</if>
            <if test="sort != null">#{sort},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="folderId != null">#{folderId},</if>
            <if test="userQuestionId != null">#{userQuestionId},</if>
         </trim>
    </insert>

    <update id="updateDtbBookQuestion" parameterType="DtbBookQuestion">
        update dtb_book_question
        <trim prefix="SET" suffixOverrides=",">
            <if test="questionType != null">question_type = #{questionType},</if>
            <if test="chapterId != null">chapter_id = #{chapterId},</if>
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="folderId != null">folder_id = #{folderId},</if>
            <if test="userQuestionId != null">user_question_id = #{userQuestionId},</if>
        </trim>
        where book_question_id = #{bookQuestionId}
    </update>

    <delete id="deleteDtbBookQuestionByBookQuestionId" parameterType="Long">
        delete from dtb_book_question where book_question_id = #{bookQuestionId}
    </delete>

    <select id="selectQuestionByBookId" resultType="integer">
        SELECT
        q.question_type
        FROM
        dtb_book_chapter c
        INNER JOIN dtb_book b ON b.book_id = c.book_id
        AND c.version_id = b.current_version_id
        INNER JOIN dtb_book_question q ON c.chapter_id = q.chapter_id
        AND q.del_flag = '0'
        WHERE
        c.del_flag = '0'
        and b.book_id = #{bookId}
        <if test="chapterId != null"> and c.chapter_id = #{chapterId}</if>
    </select>
    <select id="queryBookQuestion" resultType="cn.dutp.book.domain.DtbBookQuestion">
        SELECT
            q.question_type,
            q.sort,
            q.user_question_id
        FROM
            dtb_book_chapter c
                INNER JOIN dtb_book b ON b.book_id = c.book_id
                AND c.version_id = b.current_version_id
                INNER JOIN dtb_book_question q ON c.chapter_id = q.chapter_id
                AND q.del_flag = '0'
        WHERE
            c.del_flag = '0'
          AND b.book_id = #{bookId}
          AND c.sort = #{sort}
          AND q.folder_id = #{folderId}
    </select>

    <delete id="deleteDtbBookQuestionByBookQuestionIds" parameterType="String">
        delete from dtb_book_question where book_question_id in 
        <foreach item="bookQuestionId" collection="array" open="(" separator="," close=")">
            #{bookQuestionId}
        </foreach>
    </delete>

    <!-- 基础的题目结果映射 -->
    <resultMap type="DtbBookQuestionDetailVo" id="DtbBookQuestionVoResult">
        <result property="bookQuestionId" column="book_question_id" />
        <result property="questionId" column="question_id" />
        <result property="questionType" column="question_type" />
        <result property="questionContent" column="question_content" />
        <result property="rightAnswer" column="right_answer" />
        <result property="analysis" column="analysis" />
        <result property="userId" column="user_id" />
        <result property="disorder" column="disorder" />
        <result property="sort" column="sort" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delFlag" column="del_flag" />
        <result property="folderId" column="folder_id" />
        <result property="codeContent" column="code_content" />
        <result property="chapterId" column="chapter_id" />
        <result property="bookId" column="book_id" />
        <result property="userQuestionId" column="user_question_id" />
        <result property="questionRemark" column="question_remark" />
    </resultMap>

    <!-- 题目选项结果映射 -->
    <resultMap id="QuestionOptionResult" type="DtbUserQuestionOption">
        <result property="optionId" column="option_id" />
        <result property="optionContent" column="option_content" />
        <result property="questionId" column="question_id" />
        <result property="rightFlag" column="right_flag" />
        <result property="optionPosition" column="option_position" />
        <result property="sort" column="sort" />
    </resultMap>

    <!-- 包含选项的完整题目结果映射 -->
    <resultMap type="DtbBookQuestionDetailVo" id="DtbBookQuestionWithOptionsResult" extends="DtbBookQuestionVoResult">
        <collection property="options" 
                    column="question_id" 
                    select="selectQuestionOptions"
                    fetchType="eager" />
    </resultMap>

    <!-- 查询题目选项的SQL -->
    <select id="selectQuestionOptions" resultMap="QuestionOptionResult">
        SELECT 
            option_id, option_content, question_id, right_flag, 
            option_position, sort
        FROM dtb_user_question_option
        WHERE question_id = #{question_id}
        AND (del_flag = '0' OR del_flag IS NULL)
        ORDER BY sort
    </select>

    <!-- 主查询SQL，使用包含选项的结果映射 -->
    <select id="selectDtbUserQuestionListWithOptions" parameterType="cn.dutp.book.domain.vo.DtbBookQuestionDetailVo" resultMap="DtbBookQuestionWithOptionsResult">
        SELECT 
            q.question_id, q.question_type, q.question_content, q.right_answer,
            q.analysis, q.user_id, q.disorder, q.sort,
            q.create_by, q.create_time, q.update_by, q.update_time,
            q.del_flag, q.folder_id, q.code_content,
            bq.book_question_id, bq.chapter_id, bq.book_id, bq.user_question_id,q.question_remark
        FROM dtb_user_question q
        INNER JOIN dtb_book_question bq ON q.question_id = bq.user_question_id
        <where>
            <if test="questionType != null">AND q.question_type = #{questionType}</if>
            <if test="userQuestion !=null and userQuestion.questionContent != null">AND q.question_content like concat('%',#{userQuestion.questionContent},'%')</if>
            <if test="folderId != null">AND bq.folder_id = #{folderId}</if>
            <if test="chapterId != null">AND bq.chapter_id = #{chapterId}</if>
            <if test="bookId != null">AND bq.book_id = #{bookId}</if>
            AND q.del_flag = '0'
            AND bq.del_flag = '0'
        </where>
        ORDER BY bq.sort desc, q.create_time desc
    </select>

    <!-- 将题目移动到回收站 -->
    <update id="moveToRecycleBin">
        update dtb_book_question 
        set del_flag = '1'
        where book_question_id in
        <foreach item="bookQuestionId" collection="list" open="(" separator="," close=")">
            #{bookQuestionId}
        </foreach>
    </update>

    <!-- 从回收站恢复题目 -->
    <update id="restoreByIds">
        update dtb_book_question 
        set del_flag = '0'
        where book_question_id in
        <foreach item="bookQuestionId" collection="list" open="(" separator="," close=")">
            #{bookQuestionId}
        </foreach>
    </update>

    <!-- 查询回收站中的题目列表 -->
    <select id="recycleBinList" parameterType="DtbBookQuestion" resultType="cn.dutp.book.domain.vo.BookQuestionRecycleVO">
        SELECT 
            bq.book_question_id,
            bq.question_type,
            bq.book_id,
            bq.chapter_id,
            bq.folder_id,
            bq.update_time,
            q.question_content,
            f.folder_name
        FROM dtb_book_question bq
        LEFT JOIN dtb_user_question q ON bq.user_question_id = q.question_id
        LEFT JOIN dtb_book_question_folder f ON bq.folder_id = f.folder_id
        <where>
            bq.del_flag = '1'
            <if test="questionType != null"> AND bq.question_type = #{questionType}</if>
            <if test="chapterId != null"> AND bq.chapter_id = #{chapterId}</if>
            <if test="bookId != null"> AND bq.book_id = #{bookId}</if>
            <if test="folderId != null"> AND bq.folder_id = #{folderId}</if>
        </where>
        ORDER BY bq.update_time DESC
    </select>
    <select id="selectSchoolQuantity" resultType="java.lang.Long">
        SELECT
            count( DISTINCT oi.school_id )
        FROM
            dtb_book_order_item oi
                LEFT JOIN dtb_book_order o ON o.order_id = oi.order_id
                AND o.order_status = 'paid'
        WHERE
            oi.book_id = #{bookId}
          AND oi.item_status = 'normal'
    </select>

    <!-- 将题目彻底删除（更新del_flag为2） -->
    <update id="updateDelFlagToTwo">
        update dtb_book_question 
        set del_flag = '2'
        where book_question_id in
        <foreach item="bookQuestionId" collection="list" open="(" separator="," close=")">
            #{bookQuestionId}
        </foreach>
    </update>

</mapper>
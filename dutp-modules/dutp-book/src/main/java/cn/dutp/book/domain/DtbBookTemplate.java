package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * DUTP-DTB-029教材模板对象 dtb_book_template
 *
 * <AUTHOR>
 * @date 2025-02-17
 */
@Data
@TableName("dtb_book_template")
public class DtbBookTemplate extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long templateId;

    /**
     * 模板名称
     */
    @Excel(name = "模板名称")
    private String modal;

    /**
     * 模板背景图
     */
    @Excel(name = "模板背景图")
    private String imgUrl;

    /**
     * 风格类型
     */
    @Excel(name = "风格类型")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long templateTypeId;

    /**
     * 页眉背景图
     */
    @Excel(name = "页眉背景图")
    private String headerUrl;

    /**
     * 内容背景图
     */
    @Excel(name = "内容背景图")
    private String contentUrl;

    /**
     * 页脚
     */
    @Excel(name = "页脚")
    private String footerUrl;

    /**
     * 章头背景图
     */
    @Excel(name = "章头背景图")
    private String chapterHeaderUrl;

    /**
     * 章头字体颜色
     */
    @Excel(name = "章头字体颜色")
    private String chapterFontColor;

    /**
     * 章头高度
     */
    @Excel(name = "章头高度")
    private String chapterHeaderHeight;

    /**
     * 节头背景图
     */
    @Excel(name = "节头背景图")
    private String jointHeaderUrl;

    /**
     * 节头字体颜色
     */
    @Excel(name = "节头字体颜色")
    private String jointFontColor;

    /**
     * 节头高度
     */
    @Excel(name = "节头高度")
    private String jointHeight;

    /**
     * 状态：1启用2禁用
     */
    @Excel(name = "状态：1启用2禁用")
    private Integer status;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Integer sort;

    /**
     * 是否是默认的0不是1是
     */
    @Excel(name = "是否是默认的0不是1是")
    private Integer isDefault;

    /**
     * 资源背景图
     */
    @Excel(name = "资源背景图")
    private String orderTemplateBgUrl;

    /**
     * 资源控件名称与标题的间距
     */
    @Excel(name = "资源控件名称与标题的间距")
    private String orderTemplateMarginLeft;

    /**
     * 资源字体颜色
     */
    @Excel(name = "资源字体颜色")
    private String orderTemplateColor;

    /**
     * 页码字体颜色
     */
    @Excel(name = "页码字体颜色")
    private String pagesFontColor;

    /**
     * light亮色  dark暗色
     */
    private String theme;

    /**
     * 页码对齐方式
     */
    @Excel(name = "页码对齐方式")
    private String pagesAlign;

    /**
     * 页码位置
     */
    @Excel(name = "页码位置")
    private String pagesPosition;

    /**
     * 是否使用
     */
    @TableField(exist = false)
    private Boolean isUse;

    /**
     * 教材ID
     */
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

}

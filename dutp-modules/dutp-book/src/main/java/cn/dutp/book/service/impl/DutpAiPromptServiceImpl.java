package cn.dutp.book.service.impl;

import java.util.List;

import cn.dutp.domain.DutpAiPrompt;
import cn.dutp.book.mapper.DutpAiPromptMapper;
import cn.dutp.book.service.IDutpAiPromptService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 模型指令Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Service
public class DutpAiPromptServiceImpl extends ServiceImpl<DutpAiPromptMapper, DutpAiPrompt> implements IDutpAiPromptService
{
    @Autowired
    private DutpAiPromptMapper dutpAiPromptMapper;

    /**
     * 查询模型指令
     *
     * @param promptId 模型指令主键
     * @return 模型指令
     */
    @Override
    public DutpAiPrompt selectDutpAiPromptByPromptId(Long promptId)
    {
        return this.getById(promptId);
    }

    /**
     * 查询模型指令列表
     *
     * @param dutpAiPrompt 模型指令
     * @return 模型指令
     */
    @Override
    public List<DutpAiPrompt> selectDutpAiPromptList(DutpAiPrompt dutpAiPrompt)
    {
        LambdaQueryWrapper<DutpAiPrompt> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dutpAiPrompt.getPrompt())) {
                lambdaQueryWrapper.eq(DutpAiPrompt::getPrompt
                ,dutpAiPrompt.getPrompt());
            }
                if(ObjectUtil.isNotEmpty(dutpAiPrompt.getAbility())) {
                lambdaQueryWrapper.eq(DutpAiPrompt::getAbility
                ,dutpAiPrompt.getAbility());
            }
                if(ObjectUtil.isNotEmpty(dutpAiPrompt.getXfType())) {
                lambdaQueryWrapper.eq(DutpAiPrompt::getXfType
                ,dutpAiPrompt.getXfType());
            }
                if(ObjectUtil.isNotEmpty(dutpAiPrompt.getModelType())) {
                lambdaQueryWrapper.eq(DutpAiPrompt::getModelType
                ,dutpAiPrompt.getModelType());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增模型指令
     *
     * @param dutpAiPrompt 模型指令
     * @return 结果
     */
    @Override
    public boolean insertDutpAiPrompt(DutpAiPrompt dutpAiPrompt)
    {
        return this.save(dutpAiPrompt);
    }

    /**
     * 修改模型指令
     *
     * @param dutpAiPrompt 模型指令
     * @return 结果
     */
    @Override
    public boolean updateDutpAiPrompt(DutpAiPrompt dutpAiPrompt)
    {
        return this.updateById(dutpAiPrompt);
    }

    /**
     * 批量删除模型指令
     *
     * @param promptIds 需要删除的模型指令主键
     * @return 结果
     */
    @Override
    public boolean deleteDutpAiPromptByPromptIds(List<Long> promptIds)
    {
        return this.removeByIds(promptIds);
    }

    @Override
    public DutpAiPrompt selectDutpAiPromptByAbility(Integer ability) {
        QueryWrapper<DutpAiPrompt> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DutpAiPrompt::getAbility, ability);
        return dutpAiPromptMapper.selectOne(queryWrapper);
    }

}

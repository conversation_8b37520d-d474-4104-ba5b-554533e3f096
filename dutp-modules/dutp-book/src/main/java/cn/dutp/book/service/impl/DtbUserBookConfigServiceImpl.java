package cn.dutp.book.service.impl;

import java.util.List;

import cn.dutp.common.core.constant.HttpStatus;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.book.mapper.DtbUserBookConfigMapper;
import cn.dutp.book.domain.DtbUserBookConfig;
import cn.dutp.book.service.IDtbUserBookConfigService;

/**
 * 教材配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Service
public class DtbUserBookConfigServiceImpl extends ServiceImpl<DtbUserBookConfigMapper, DtbUserBookConfig> implements IDtbUserBookConfigService
{
    @Autowired
    private DtbUserBookConfigMapper dtbUserBookConfigMapper;

    /**
     * 查询教材配置
     *
     * @param configId 教材配置主键
     * @return 教材配置
     */
    @Override
    public DtbUserBookConfig selectDtbUserBookConfigByConfigId(Long configId)
    {
        return this.getById(configId);
    }

    /**
     * 查询教材配置列表
     *
     * @param dtbUserBookConfig 教材配置
     * @return 教材配置
     */
    @Override
    public List<DtbUserBookConfig> selectDtbUserBookConfigList(DtbUserBookConfig dtbUserBookConfig)
    {
        LambdaQueryWrapper<DtbUserBookConfig> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbUserBookConfig.getUserId())) {
                lambdaQueryWrapper.eq(DtbUserBookConfig::getUserId
                ,dtbUserBookConfig.getUserId());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookConfig.getBookId())) {
                lambdaQueryWrapper.eq(DtbUserBookConfig::getBookId
                ,dtbUserBookConfig.getBookId());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookConfig.getTheme())) {
                lambdaQueryWrapper.eq(DtbUserBookConfig::getTheme
                ,dtbUserBookConfig.getTheme());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookConfig.getFontFamily())) {
                lambdaQueryWrapper.eq(DtbUserBookConfig::getFontFamily
                ,dtbUserBookConfig.getFontFamily());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookConfig.getFontSize())) {
                lambdaQueryWrapper.eq(DtbUserBookConfig::getFontSize
                ,dtbUserBookConfig.getFontSize());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookConfig.getColumnQuantity())) {
                lambdaQueryWrapper.eq(DtbUserBookConfig::getColumnQuantity
                ,dtbUserBookConfig.getColumnQuantity());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookConfig.getLineHeight())) {
                lambdaQueryWrapper.eq(DtbUserBookConfig::getLineHeight
                ,dtbUserBookConfig.getLineHeight());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookConfig.getReadRate())) {
                lambdaQueryWrapper.eq(DtbUserBookConfig::getReadRate
                ,dtbUserBookConfig.getReadRate());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookConfig.getReadMode())) {
                lambdaQueryWrapper.eq(DtbUserBookConfig::getReadMode
                ,dtbUserBookConfig.getReadMode());
            }

                if(ObjectUtil.isNotEmpty(dtbUserBookConfig.getLastSeeDate())) {
                lambdaQueryWrapper.eq(DtbUserBookConfig::getLastSeeDate
                ,dtbUserBookConfig.getLastSeeDate());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookConfig.getLastChapterId())) {
                lambdaQueryWrapper.eq(DtbUserBookConfig::getLastChapterId
                ,dtbUserBookConfig.getLastChapterId());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookConfig.getLastPageNumber())) {
                lambdaQueryWrapper.eq(DtbUserBookConfig::getLastPageNumber
                ,dtbUserBookConfig.getLastPageNumber());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增教材配置
     *
     * @param dtbUserBookConfig 教材配置
     * @return 结果
     */
    @Override
    public boolean insertDtbUserBookConfig(DtbUserBookConfig dtbUserBookConfig)
    {
        return this.save(dtbUserBookConfig);
    }

    /**
     * 修改教材配置
     *
     * @param dtbUserBookConfig 教材配置
     * @return 结果
     */
    @Override
    public AjaxResult updateDtbUserBookConfig(DtbUserBookConfig dtbUserBookConfig)
    {
        DtbUserBookConfig dbconfig = this.getById(dtbUserBookConfig.getConfigId());
        if (dbconfig.getUserId().longValue() != SecurityUtils.getUserId().longValue()) {
            return AjaxResult.error(HttpStatus.FORBIDDEN, "用户没有权限");
        }
        this.updateById(dtbUserBookConfig);
        return AjaxResult.success();
    }

    /**
     * 批量删除教材配置
     *
     * @param configIds 需要删除的教材配置主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbUserBookConfigByConfigIds(List<Long> configIds)
    {
        return this.removeByIds(configIds);
    }

}

package cn.dutp.book.domain.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;


@Data
public class DtbUserQuestionVO {
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long questionId;
    private Integer questionType;
    private String questionContent;
    private String rightAnswer;
    private String analysis;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;
    private Integer disorder;
    private Integer sort;
}

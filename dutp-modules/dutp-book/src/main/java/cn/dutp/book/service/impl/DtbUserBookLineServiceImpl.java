package cn.dutp.book.service.impl;

import cn.dutp.book.domain.DtbUserBookLine;
import cn.dutp.book.domain.vo.DtbUserBookLineVO;
import cn.dutp.book.mapper.DtbBookBookMapper;
import cn.dutp.book.mapper.DtbUserBookLineMapper;
import cn.dutp.book.service.IDtbUserBookLineService;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBook;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * DUTP-DTB_020划线Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@Service
public class DtbUserBookLineServiceImpl extends ServiceImpl<DtbUserBookLineMapper, DtbUserBookLine> implements IDtbUserBookLineService {
    @Autowired
    private DtbUserBookLineMapper dtbUserBookLineMapper;

    @Autowired
    private DtbBookBookMapper bookMapper;

    /**
     * 查询DUTP-DTB_020划线
     *
     * @param lineId DUTP-DTB_020划线主键
     * @return DUTP-DTB_020划线
     */
    @Override
    public DtbUserBookLine selectDtbUserBookLineByLineId(Long lineId) {
        return this.getById(lineId);
    }

    /**
     * 查询DUTP-DTB_020划线列表
     *
     * @param dtbUserBookLine DUTP-DTB_020划线
     * @return DUTP-DTB_020划线
     */
    @Override
    public List<DtbUserBookLine> selectDtbUserBookLineList(DtbUserBookLine dtbUserBookLine) {
        LambdaQueryWrapper<DtbUserBookLine> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotEmpty(dtbUserBookLine.getBookId())) {
            lambdaQueryWrapper.eq(DtbUserBookLine::getBookId
                    , dtbUserBookLine.getBookId());
        }
        if (ObjectUtil.isNotEmpty(dtbUserBookLine.getChapterId())) {
            lambdaQueryWrapper.eq(DtbUserBookLine::getChapterId
                    , dtbUserBookLine.getChapterId());
        }
        if (ObjectUtil.isNotEmpty(dtbUserBookLine.getPageNumber())) {
            lambdaQueryWrapper.eq(DtbUserBookLine::getPageNumber
                    , dtbUserBookLine.getPageNumber());
        }
        if (ObjectUtil.isNotEmpty(dtbUserBookLine.getUserId())) {
            lambdaQueryWrapper.eq(DtbUserBookLine::getUserId
                    , dtbUserBookLine.getUserId());
        }
        if (ObjectUtil.isNotEmpty(dtbUserBookLine.getWord())) {
            lambdaQueryWrapper.eq(DtbUserBookLine::getWord
                    , dtbUserBookLine.getWord());
        }
        if (ObjectUtil.isNotEmpty(dtbUserBookLine.getColor())) {
            lambdaQueryWrapper.eq(DtbUserBookLine::getColor
                    , dtbUserBookLine.getColor());
        }
        if (ObjectUtil.isNotEmpty(dtbUserBookLine.getFromWordId())) {
            lambdaQueryWrapper.eq(DtbUserBookLine::getFromWordId
                    , dtbUserBookLine.getFromWordId());
        }
        if (ObjectUtil.isNotEmpty(dtbUserBookLine.getEndWordId())) {
            lambdaQueryWrapper.eq(DtbUserBookLine::getEndWordId
                    , dtbUserBookLine.getEndWordId());
        }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增DUTP-DTB_020划线
     *
     * @param dtbUserBookLine DUTP-DTB_020划线
     * @return 结果
     */
    @Override
    public AjaxResult insertDtbUserBookLine(DtbUserBookLine dtbUserBookLine) {
        LambdaQueryWrapper<DtbUserBookLine> lineLambdaQueryWrapper = new LambdaQueryWrapper<>();
        lineLambdaQueryWrapper.eq(DtbUserBookLine::getBookId, dtbUserBookLine.getBookId())
                .eq(DtbUserBookLine::getUserId, SecurityUtils.getUserId())
                .eq(DtbUserBookLine::getFromWordId, dtbUserBookLine.getFromWordId())
                .eq(DtbUserBookLine::getChapterId, dtbUserBookLine.getChapterId())
                .eq(DtbUserBookLine::getPageNumber, dtbUserBookLine.getPageNumber())
                .eq(DtbUserBookLine::getEndWordId, dtbUserBookLine.getEndWordId());
        DtbUserBookLine dbBookLine = dtbUserBookLineMapper.selectOne(lineLambdaQueryWrapper);
        if (ObjectUtil.isEmpty(dbBookLine)) {
            this.save(dtbUserBookLine);
        } else {
            DtbUserBookLine updatLineData = new DtbUserBookLine();
            updatLineData.setLineId(dbBookLine.getLineId());
            updatLineData.setColor(dtbUserBookLine.getColor());
            updatLineData.setLineStyle(dtbUserBookLine.getLineStyle());
            dtbUserBookLineMapper.updateById(updatLineData);
        }
        return AjaxResult.success();
    }

    /**
     * 修改DUTP-DTB_020划线
     *
     * @param dtbUserBookLine DUTP-DTB_020划线
     * @return 结果
     */
    @Override
    public boolean updateDtbUserBookLine(DtbUserBookLine dtbUserBookLine) {
        return this.updateById(dtbUserBookLine);
    }

    /**
     * 批量删除DUTP-DTB_020划线
     *
     * @param lineIds 需要删除的DUTP-DTB_020划线主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbUserBookLineByLineIds(List<Long> lineIds) {
        return this.removeByIds(lineIds);
    }

    @Override
    public AjaxResult selectReaderUserBookLineList(DtbUserBookLine bookLine) {
        List<DtbUserBookLineVO> vos = dtbUserBookLineMapper.selectReaderUserBookLineList(bookLine);
        return AjaxResult.success(vos);
    }

    @Override
    public AjaxResult deleteReaderDtbUserBookLine(DtbUserBookLine dtbUserBookLine) {
        if (ObjectUtil.isNotEmpty(dtbUserBookLine.getLineId())) {
            dtbUserBookLineMapper.deleteById(dtbUserBookLine.getLineId());
        } else {
            LambdaQueryWrapper<DtbUserBookLine> lineLambdaQueryWrapper = new LambdaQueryWrapper<>();
            lineLambdaQueryWrapper.eq(DtbUserBookLine::getUserId, SecurityUtils.getUserId())
                    .eq(DtbUserBookLine::getChapterId, dtbUserBookLine.getChapterId())
                    .eq(DtbUserBookLine::getBookId, dtbUserBookLine.getBookId())
                    .eq(DtbUserBookLine::getPageNumber, dtbUserBookLine.getPageNumber())
                    .eq(DtbUserBookLine::getFromWordId, dtbUserBookLine.getFromWordId())
                    .eq(DtbUserBookLine::getEndWordId, dtbUserBookLine.getEndWordId());
            List<DtbUserBookLine> dtbUserBookLines = dtbUserBookLineMapper.selectList(lineLambdaQueryWrapper);
            List<Long> ids = dtbUserBookLines.stream().map(DtbUserBookLine::getLineId).collect(Collectors.toList());
            dtbUserBookLineMapper.deleteBatchIds(ids);
        }
        return AjaxResult.success();
    }

    @Override
    public void exportBookLine(HttpServletResponse response, DtbUserBookLine dtbUserBookLine) {
        if (dtbUserBookLine.getBookId() == null) {
            throw new ServiceException("bookId不能为空");
        }
        List<DtbUserBookLine> userBookLineList = dtbUserBookLineMapper.exportBookLine(dtbUserBookLine);
        DtbBook book = bookMapper.selectOne(new LambdaQueryWrapper<DtbBook>()
                .select(DtbBook::getBookName)
                .eq(DtbBook::getBookId, dtbUserBookLine.getBookId()));
        Map<Long, List<DtbUserBookLine>> collect = userBookLineList.stream().collect(Collectors.groupingBy(DtbUserBookLine::getChapterId));
        XWPFDocument document = new XWPFDocument();
        collect.forEach((k, v) -> {
            int idx = 1;
            for (DtbUserBookLine userBookLine : v) {
                if (idx == 1) {
                    XWPFParagraph paragraph = document.createParagraph();
                    XWPFRun run = paragraph.createRun();
                    run.setText(userBookLine.getChapterName());
                    run.setBold(true);
                }
                XWPFParagraph paragraph = document.createParagraph();
                XWPFRun run = paragraph.createRun();
                run.setText(idx + ". 划线内容");
                XWPFParagraph paragraph1 = document.createParagraph();
                XWPFRun run1 = paragraph1.createRun();
                run1.setText("  " + userBookLine.getWord());
                XWPFParagraph paragraph2 = document.createParagraph();
                XWPFRun run2 = paragraph2.createRun();
                run2.setText("  时间:" + DateUtil.format(userBookLine.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                document.createParagraph();
                idx++;
            }
        });
        response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(book.getBookName() + ".docx", StandardCharsets.UTF_8.name()));
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        try (OutputStream out = response.getOutputStream()) {
            document.write(out);
            out.flush();
        } catch (IOException e) {
            log.info("导出划线word失败，", e);
        } finally {
            try {
                document.close();
            } catch (IOException e) {
                log.info("导出划线word失败，", e);

            }
        }

    }

}

package cn.dutp.book.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.book.domain.DtbBookShareChapter;
import cn.dutp.book.service.IDtbBookShareChapterService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 教材分享目录Controller
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@RestController
@RequestMapping("/shareChapter")
public class DtbBookShareChapterController extends BaseController
{
    @Autowired
    private IDtbBookShareChapterService dtbBookShareChapterService;

    /**
     * 查询教材分享目录列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbBookShareChapter dtbBookShareChapter)
    {
        startPage();
        List<DtbBookShareChapter> list = dtbBookShareChapterService.selectDtbBookShareChapterList(dtbBookShareChapter);
        return getDataTable(list);
    }

    /**
     * 导出教材分享目录列表
     */
//    @RequiresPermissions("book:shareChapter:export")
    @Log(title = "导出教材分享目录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbBookShareChapter dtbBookShareChapter)
    {
        List<DtbBookShareChapter> list = dtbBookShareChapterService.selectDtbBookShareChapterList(dtbBookShareChapter);
        ExcelUtil<DtbBookShareChapter> util = new ExcelUtil<DtbBookShareChapter>(DtbBookShareChapter.class);
        util.exportExcel(response, list, "教材分享目录数据");
    }

    /**
     * 获取教材分享目录详细信息
     */
//    @RequiresPermissions("book:shareChapter:query")
    @GetMapping(value = "/{shareChapterId}")
    public AjaxResult getInfo(@PathVariable("shareChapterId") Long shareChapterId)
    {
        return success(dtbBookShareChapterService.selectDtbBookShareChapterByShareChapterId(shareChapterId));
    }

    /**
     * 新增教材分享目录
     */
//    @RequiresPermissions("book:shareChapter:add")
    @Log(title = "新增教材分享目录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookShareChapter dtbBookShareChapter)
    {
        return toAjax(dtbBookShareChapterService.insertDtbBookShareChapter(dtbBookShareChapter));
    }

    /**
     * 修改教材分享目录
     */
//    @RequiresPermissions("book:shareChapter:edit")
    @Log(title = "修改教材分享目录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookShareChapter dtbBookShareChapter)
    {
        return toAjax(dtbBookShareChapterService.updateDtbBookShareChapter(dtbBookShareChapter));
    }

    /**
     * 删除教材分享目录
     */
//    @RequiresPermissions("book:shareChapter:remove")
    @Log(title = "删除教材分享目录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{shareChapterIds}")
    public AjaxResult remove(@PathVariable Long[] shareChapterIds)
    {
        return toAjax(dtbBookShareChapterService.deleteDtbBookShareChapterByShareChapterIds(Arrays.asList(shareChapterIds)));
    }
}

package cn.dutp.book.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.book.domain.DtbTestPaper;
import cn.dutp.book.service.IDtbTestPaperService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.book.domain.DtbTestPaperQuestionCollection;
import cn.dutp.book.domain.DtbTestPaperQuestion;

/**
 * 试卷Controller
 *
 * <AUTHOR>
 * @date 2025-02-08
 */
@RestController
@RequestMapping("/paper")
public class DtbTestPaperController extends BaseController
{
    @Autowired
    private IDtbTestPaperService dtbTestPaperService;

    /**
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbTestPaper dtbTestPaper)
    {
        startPage();
        List<DtbTestPaper> list = dtbTestPaperService.selectDtbTestPaperList(dtbTestPaper);
        return getDataTable(list);
    }

    /**
     * 导出试卷列表
     */
    @RequiresPermissions("book:paper:export")
    @Log(title = "导出试卷", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbTestPaper dtbTestPaper)
    {
        List<DtbTestPaper> list = dtbTestPaperService.selectDtbTestPaperList(dtbTestPaper);
        ExcelUtil<DtbTestPaper> util = new ExcelUtil<DtbTestPaper>(DtbTestPaper.class);
        util.exportExcel(response, list, "试卷数据");
    }

    /**
     * 获取试卷详细信息
     */
    @RequiresPermissions("book:paper:query")
    @GetMapping(value = "/{paperId}")
    public AjaxResult getInfo(@PathVariable("paperId") Long paperId)
    {
        return success(dtbTestPaperService.selectDtbTestPaperByPaperId(paperId));
    }

    /**
     * 新增试卷
     */
    @RequiresPermissions("book:paper:add")
    @Log(title = "新增试卷", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbTestPaper dtbTestPaper)
    {
        // 计算试卷总分和题目数量
        calculatePaperInfo(dtbTestPaper);
        return toAjax(dtbTestPaperService.insertDtbTestPaper(dtbTestPaper));
    }

    /**
     * 修改试卷
     */
    @RequiresPermissions("book:paper:edit")
    @Log(title = "修改试卷", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbTestPaper dtbTestPaper)
    {
        // 计算试卷总分和题目数量
        calculatePaperInfo(dtbTestPaper);
        return toAjax(dtbTestPaperService.updateDtbTestPaper(dtbTestPaper));
    }

    /**
     * 计算试卷信息（总分和题目数量）
     */
    private void calculatePaperInfo(DtbTestPaper dtbTestPaper) {
        List<DtbTestPaperQuestionCollection> collections = dtbTestPaper.getDtbTestPaperQuestionCollectionList();
        if (collections != null && !collections.isEmpty()) {
            int totalQuestions = 0;
            int totalScore = 0;
            
            for (DtbTestPaperQuestionCollection collection : collections) {
                List<DtbTestPaperQuestion> questions = collection.getQuestionList();
                if (questions != null) {
                    // 设置题型集合关联的试卷ID
                    collection.setPaperId(dtbTestPaper.getPaperId());
                    // 统计题目数量
                    totalQuestions += questions.size();
                    
                    for (DtbTestPaperQuestion question : questions) {
                        // 设置小题关联的试卷ID和题型集合ID
                        question.setPaperId(dtbTestPaper.getPaperId());
                        question.setCollectionId(collection.getCollectionId());
                        totalScore += question.getQuestionScore() != null ? 
                        question.getQuestionScore() : 0;
                    }
                }
            }
            
            // 更新试卷的总分和题目数量
            dtbTestPaper.setQuestionQuantity(totalQuestions);
            dtbTestPaper.setTotalScore(totalScore);
        }
    }

    /**
     * 删除试卷
     */
    @RequiresPermissions("book:paper:remove")
    @Log(title = "删除试卷", businessType = BusinessType.DELETE)
    @DeleteMapping("/{paperIds}")
    public AjaxResult remove(@PathVariable Long[] paperIds)
    {
        return toAjax(dtbTestPaperService.deleteDtbTestPaperByPaperIds(Arrays.asList(paperIds)));
    }

    /**
     * 获取试卷的题目组和题目信息
     */
    @GetMapping("/questions/{paperId}")
    public AjaxResult getTestPaperQuestions(@PathVariable("paperId") Long paperId)
    {
        List<DtbTestPaperQuestionCollection> collections = dtbTestPaperService.selectQuestionCollectionsByPaperId(paperId);
        return success(collections);
    }

    /**
     * 将试卷移入回收站
     */
    @RequiresPermissions("book:paper:recycle")
    @Log(title = "移入回收站", businessType = BusinessType.UPDATE)
    @PutMapping("/recycle/{paperIds}")
    public AjaxResult moveToRecycleBin(@PathVariable Long[] paperIds)
    {
        return toAjax(dtbTestPaperService.moveToRecycleBin(Arrays.asList(paperIds)));
    }

    /**
     * 查询回收站试卷列表
     */
    @RequiresPermissions("book:paper:recycle")
    @GetMapping("/recycle/list")
    public TableDataInfo recycleList(DtbTestPaper dtbTestPaper)
    {
        startPage();
        List<DtbTestPaper> list = dtbTestPaperService.selectRecycleBinList(dtbTestPaper);
        return getDataTable(list);
    }

    /**
     * 从回收站恢复试卷
     */
    @RequiresPermissions("book:paper:recycle")
    @Log(title = "恢复试卷", businessType = BusinessType.UPDATE)
    @PutMapping("/recycle/restore/{paperIds}")
    public AjaxResult restoreFromRecycleBin(@PathVariable Long[] paperIds)
    {
        return toAjax(dtbTestPaperService.restoreFromRecycleBin(Arrays.asList(paperIds)));
    }

    /**
     * 复制试卷
     */
    @RequiresPermissions("book:paper:copy")
    @Log(title = "复制试卷", businessType = BusinessType.INSERT)
    @PostMapping("/copy/{paperId}")
    public AjaxResult copy(@PathVariable Long paperId) {
        try {
            DtbTestPaper newPaper = dtbTestPaperService.copyTestPaper(paperId);
            return success(newPaper);
        } catch (Exception e) {
            return error("复制试卷失败：" + e.getMessage());
        }
    }


    /**
     * 编辑试卷前的检测
     */
    @RequiresPermissions("book:paper:edit")
    @GetMapping("/checkBeforeEdit/{paperId}")
    public AjaxResult checkBeforeEdit(@PathVariable Long paperId)
    {
        try {
            boolean canEdit = dtbTestPaperService.checkBeforeEdit(paperId);
            return success(canEdit);
        } catch (Exception e) {
            return error("检测编辑失败：" + e.getMessage());
        }
    }

}

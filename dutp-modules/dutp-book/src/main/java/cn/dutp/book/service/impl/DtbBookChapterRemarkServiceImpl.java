package cn.dutp.book.service.impl;

import cn.dutp.book.domain.DtbBookChapter;
import cn.dutp.book.domain.DtbBookChapterRemark;
import cn.dutp.book.mapper.DtbBookChapterMapper;
import cn.dutp.book.mapper.DtbBookChapterRemarkMapper;
import cn.dutp.book.mapper.DtbBookCommonMapper;
import cn.dutp.book.service.IDtbBookChapterRemarkService;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 章节标注Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-06
 */
@Service
public class DtbBookChapterRemarkServiceImpl extends ServiceImpl<DtbBookChapterRemarkMapper, DtbBookChapterRemark> implements IDtbBookChapterRemarkService {
    @Autowired
    private DtbBookChapterRemarkMapper dtbBookChapterRemarkMapper;

    @Autowired
    private DtbBookChapterMapper chapterMapper;

    @Autowired
    private DtbBookCommonMapper commonMapper;

    /**
     * 查询章节标注
     *
     * @param remarkId 章节标注主键
     * @return 章节标注
     */
    @Override
    public DtbBookChapterRemark selectDtbBookChapterRemarkByRemarkId(Long remarkId) {
        DtbBookChapterRemark byId = this.getById(remarkId);
        if (byId.getAuthorId().equals(SecurityUtils.getUserId())) {
            return byId;
        }
        throw new ServiceException("没有权限");
    }

    /**
     * 查询章节标注列表
     *
     * @param dtbBookChapterRemark 章节标注
     * @return 章节标注
     */
    @Override
    public List<DtbBookChapterRemark> selectDtbBookChapterRemarkList(DtbBookChapterRemark dtbBookChapterRemark) {
        LambdaQueryWrapper<DtbBookChapterRemark> lambdaQueryWrapper = new LambdaQueryWrapper<>();

        if (ObjectUtil.isNotEmpty(dtbBookChapterRemark.getChapterId())) {
            lambdaQueryWrapper.eq(DtbBookChapterRemark::getChapterId
                    , dtbBookChapterRemark.getChapterId());
        }
        lambdaQueryWrapper.eq(DtbBookChapterRemark::getState, 1);
        lambdaQueryWrapper.select(DtbBookChapterRemark::getRemarkId, DtbBookChapterRemark::getChapterId,
                DtbBookChapterRemark::getRemarkContent, DtbBookChapterRemark::getDataId,
                DtbBookChapterRemark::getCreateTime, DtbBookChapterRemark::getAuthorId);
        List<DtbBookChapterRemark> list = this.list(lambdaQueryWrapper);
        for (DtbBookChapterRemark bookChapterRemark : list) {
            bookChapterRemark.setNickname(commonMapper.queryNickNameByUserId(bookChapterRemark.getAuthorId()));
        }
        return list;
    }

    /**
     * 新增章节标注
     *
     * @param dtbBookChapterRemark 章节标注
     * @return 结果
     */
    @Override
    public boolean insertDtbBookChapterRemark(DtbBookChapterRemark dtbBookChapterRemark) {
        Long chapterId = dtbBookChapterRemark.getChapterId();
        DtbBookChapter chapter = chapterMapper.selectOne(new LambdaQueryWrapper<DtbBookChapter>()
                .select(DtbBookChapter::getBookId)
                .eq(DtbBookChapter::getChapterId, chapterId));
        dtbBookChapterRemark.setBookId(chapter.getBookId());
        dtbBookChapterRemark.setAuthorId(SecurityUtils.getUserId());
        dtbBookChapterRemark.setState(1);
        return this.save(dtbBookChapterRemark);
    }

    /**
     * 修改章节标注
     *
     * @param dtbBookChapterRemark 章节标注
     * @return 结果
     */
    @Override
    public boolean updateDtbBookChapterRemark(DtbBookChapterRemark dtbBookChapterRemark) {
        return this.updateById(dtbBookChapterRemark);
    }

    /**
     * 批量删除章节标注
     *
     * @param remarkIds 需要删除的章节标注主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookChapterRemarkByRemarkIds(List<Long> remarkIds) {
        return this.removeByIds(remarkIds);
    }

}

package cn.dutp.book.controller;

import cn.dutp.book.domain.DtbBookTestPaper;
import cn.dutp.book.service.IDtbBookTestPaperService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 教材跟试卷关系Controller
 *
 * <AUTHOR>
 * @date 2025-02-20
 */
@RestController
@RequestMapping("/bookPaper")
public class DtbBookTestPaperController extends BaseController {
    @Autowired
    private IDtbBookTestPaperService dtbBookTestPaperService;

    /**
     * 查询教材跟试卷关系列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbBookTestPaper dtbBookTestPaper) {
        startPage();
        List<DtbBookTestPaper> list = dtbBookTestPaperService.selectDtbBookTestPaperList(dtbBookTestPaper);
        return getDataTable(list);
    }

    /**
     * 导出教材跟试卷关系列表
     */
    @RequiresPermissions("book:paper:export")
    @Log(title = "导出教材跟试卷关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbBookTestPaper dtbBookTestPaper) {
        List<DtbBookTestPaper> list = dtbBookTestPaperService.selectDtbBookTestPaperList(dtbBookTestPaper);
        ExcelUtil<DtbBookTestPaper> util = new ExcelUtil<DtbBookTestPaper>(DtbBookTestPaper.class);
        util.exportExcel(response, list, "教材跟试卷关系数据");
    }

    /**
     * 获取教材跟试卷关系详细信息
     */
    @RequiresPermissions("book:paper:query")
    @GetMapping(value = "/{bookPaperId}")
    public AjaxResult getInfo(@PathVariable("bookPaperId") Long bookPaperId) {
        return success(dtbBookTestPaperService.selectDtbBookTestPaperByBookPaperId(bookPaperId));
    }

    /**
     * 根据试卷id获取考试详情信息
     */
    @GetMapping(value = "/getPaperAnswer")
    public AjaxResult getPaperAnswer(DtbBookTestPaper dtbBookTestPaper) {
        return dtbBookTestPaperService.getPaperAnswer(dtbBookTestPaper);
    }

    /**
     * 新增教材跟试卷关系
     */
    @Log(title = "新增教材跟试卷关系", businessType = BusinessType.INSERT)
    @PostMapping("/addPaper")
    public AjaxResult addPaper(@RequestBody DtbBookTestPaper dtbBookTestPaper) {
        return toAjax(dtbBookTestPaperService.addPaper(dtbBookTestPaper));
    }

    /**
     * 新增教材跟试卷关系
     */
    @Operation(
            summary = "考试保存接口",
            description = "考试结果保存",
            tags = "考试保存接口",
            parameters = {
                    @Parameter(name = "bookPaperId", description = "主键ID", required = false),
                    @Parameter(name = "bookId", description = "小题ID", required = true),
                    @Parameter(name = "paperId", description = "试卷ID", required = false),
                    @Parameter(name = "chapterId", description = "章节ID", required = false),
                    @Parameter(name = "domId", description = "节点domID", required = false),
                    @Parameter(name = "dtbBookQuestionAnswerList", description = "题目答案明细", required = false),
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "成功"),
            }
    )
    @Log(title = "考试保存接口", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookTestPaper dtbBookTestPaper) {
        return dtbBookTestPaperService.insertDtbBookTestPaper(dtbBookTestPaper);
    }

    /**
     * 获取考试和作业列表 按章节id筛选
     */
    @GetMapping(value = "/getPaperList")
    public AjaxResult getPaperList(DtbBookTestPaper dtbBookTestPaper){
        return dtbBookTestPaperService.getPaperList(dtbBookTestPaper);
    }

    /**
     * 修改教材跟试卷关系
     */
    @RequiresPermissions("book:paper:edit")
    @Log(title = "修改教材跟试卷关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookTestPaper dtbBookTestPaper) {
        return toAjax(dtbBookTestPaperService.updateDtbBookTestPaper(dtbBookTestPaper));
    }

    /**
     * 删除教材跟试卷关系
     */
    @RequiresPermissions("book:paper:remove")
    @Log(title = "删除教材跟试卷关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{bookPaperIds}")
    public AjaxResult remove(@PathVariable Long[] bookPaperIds) {
        return toAjax(dtbBookTestPaperService.deleteDtbBookTestPaperByBookPaperIds(Arrays.asList(bookPaperIds)));
    }


}

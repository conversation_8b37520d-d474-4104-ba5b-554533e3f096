package cn.dutp.book.domain;

    import java.math.BigDecimal;
import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 教材分享点评附件对象 dtb_book_share_comment_attachment
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Data
@TableName("dtb_book_share_comment_attachment")
public class DtbBookShareCommentAttachment extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** $column.columnComment */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long attachmentId;

    /** $column.columnComment */
        @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long commentId;

    /** 1图片2音频 */
        @Excel(name = "1图片2音频")
    private Integer attachmentType;

    /** 附件地址 */
        @Excel(name = "附件地址")
    private String attachmentUrl;

    /** 附件文件大小 */
        @Excel(name = "附件文件大小")
    private BigDecimal attachmentSize;

    /** 附件名 */
        @Excel(name = "附件名")
    private String attachmentName;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("attachmentId", getAttachmentId())
            .append("commentId", getCommentId())
            .append("attachmentType", getAttachmentType())
            .append("attachmentUrl", getAttachmentUrl())
            .append("attachmentSize", getAttachmentSize())
            .append("attachmentName", getAttachmentName())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
        .toString();
        }
        }

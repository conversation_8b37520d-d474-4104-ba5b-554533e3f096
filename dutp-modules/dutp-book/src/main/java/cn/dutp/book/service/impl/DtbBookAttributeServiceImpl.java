package cn.dutp.book.service.impl;

import cn.dutp.book.domain.DtbBookAttribute;
import cn.dutp.book.mapper.DtbBookAttributeMapper;
import cn.dutp.book.service.IDtbBookAttributeService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数字教材简介Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@Service
public class DtbBookAttributeServiceImpl extends ServiceImpl<DtbBookAttributeMapper, DtbBookAttribute> implements IDtbBookAttributeService {
    @Autowired
    private DtbBookAttributeMapper dtbBookAttributeMapper;

    /**
     * 查询数字教材简介
     *
     * @param bookId 数字教材简介主键
     * @return 数字教材简介
     */
    @Override
    public DtbBookAttribute selectDtbBookAttributeByBookId(Long bookId) {
        DtbBookAttribute bookAttribute = this.getOne(new LambdaQueryWrapper<DtbBookAttribute>()
                .select(DtbBookAttribute::getBookId, DtbBookAttribute::getAttributeId,
                        DtbBookAttribute::getCopyright, DtbBookAttribute::getDeclaration,
                        DtbBookAttribute::getRecommend, DtbBookAttribute::getIntroduce)
                .eq(DtbBookAttribute::getBookId, bookId));
        if (ObjectUtil.isEmpty(bookAttribute)) {
            bookAttribute = new DtbBookAttribute();
            bookAttribute.setBookId(bookId);
        }
        return bookAttribute;
    }

    /**
     * 新增数字教材简介
     *
     * @param dtbBookAttribute 数字教材简介
     * @return 结果
     */
    @Override
    public Long insertDtbBookAttribute(DtbBookAttribute dtbBookAttribute) {
        this.save(dtbBookAttribute);
        return dtbBookAttribute.getAttributeId();
    }

    /**
     * 修改数字教材简介
     *
     * @param dtbBookAttribute 数字教材简介
     * @return 结果
     */
    @Override
    public boolean updateDtbBookAttribute(DtbBookAttribute dtbBookAttribute) {
        return this.updateById(dtbBookAttribute);
    }

    /**
     * 学生教师端获取推荐教材
     *
     * @param dtbBookAttribute 数字教材简介
     * @return 结果
     */
    @Override
    public List<DtbBookAttribute> getRecommendedTextbooks(DtbBookAttribute dtbBookAttribute) {
        LambdaQueryWrapper<DtbBookAttribute> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ObjectUtil.isNotEmpty(dtbBookAttribute.getBookId()), DtbBookAttribute::getBookId, dtbBookAttribute.getBookId());
        return baseMapper.selectList(wrapper);
    }
}

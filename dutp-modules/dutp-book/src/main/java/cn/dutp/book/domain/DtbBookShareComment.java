package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.List;

/**
 * 教材分享点评对象 dtb_book_share_comment
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Data
@TableName("dtb_book_share_comment")
public class DtbBookShareComment extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** 点评id */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long commentId;

    /** 笔记记录人 */
        @Excel(name = "笔记记录人")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long shareId;

    /** 教材ID */
        @Excel(name = "教材ID")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /** 章节ID */
        @Excel(name = "章节ID")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    /** 页码 */
        @Excel(name = "页码")
    private Long pageNumber;

    /** 点评内容 */
        @Excel(name = "点评内容")
    private String commentContent;

    /** 开始文字ID */
        @Excel(name = "开始文字ID")
    private String fromWordId;

    /** 结束文字ID */
        @Excel(name = "结束文字ID")
    private String endWordId;

    /** 原文内容 */
        @Excel(name = "原文内容")
    private String bookContent;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /**
     * 章节名称
     */
    @TableField(exist = false)
    private String chapterName;
    @TableField(exist = false)
    private List<DtbBookShareCommentAttachment> attachments;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("commentId", getCommentId())
            .append("shareId", getShareId())
            .append("bookId", getBookId())
            .append("chapterId", getChapterId())
            .append("pageNumber", getPageNumber())
            .append("commentContent", getCommentContent())
            .append("fromWordId", getFromWordId())
            .append("endWordId", getEndWordId())
            .append("bookContent", getBookContent())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
        .toString();
        }
        }

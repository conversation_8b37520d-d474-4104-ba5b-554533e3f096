package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * DUTP-DTB_010数字教材选择题选项对象 dtb_user_question_option
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@TableName("dtb_user_question_option")
public class DtbUserQuestionOption extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** $column.columnComment */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long optionId;

    /** 选项内容 */
        @Excel(name = "选项内容")
    private String optionContent;

    /** 小题ID */
        @Excel(name = "小题ID")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long questionId;

    /** 是否是正确选项1不是2是 */
        @Excel(name = "是否是正确选项1不是2是")
    private Integer rightFlag;

    /** 连线选项位置，1左侧2右侧 */
        @Excel(name = "连线选项位置，1左侧2右侧")
    private Integer optionPosition;

    /** 序号 */
        @Excel(name = "序号")
    private Integer sort;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("optionId", getOptionId())
            .append("optionContent", getOptionContent())
            .append("questionId", getQuestionId())
            .append("rightFlag", getRightFlag())
            .append("optionPosition", getOptionPosition())
            .append("sort", getSort())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
        .toString();
        }
        }

package cn.dutp.book.controller;

import cn.dutp.book.domain.DtbUserBook;
import cn.dutp.book.service.IDtbUserBookService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.log.enums.OperatorType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * DUTP-DTB_014学生/教师书架Controller
 *
 * <AUTHOR>
 * @date 2024-12-04
 */
@RestController
@RequestMapping("/userBook")
public class DtbUserBookController extends BaseController
{
    @Autowired
    private IDtbUserBookService dtbUserBookService;

    /**
     * 查询DUTP-DTB_014学生/教师书架列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbUserBook dtbUserBook)
    {
        startPage();
        List<DtbUserBook> list = dtbUserBookService.selectDtbUserBookList(dtbUserBook);
        return getDataTable(list);
    }

    /**
     * 导出DUTP-DTB_014学生/教师书架列表
     */
    @RequiresPermissions("book:book:export")
    @Log(title = "DUTP-DTB_014学生/教师书架", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbUserBook dtbUserBook)
    {
        List<DtbUserBook> list = dtbUserBookService.selectDtbUserBookList(dtbUserBook);
        ExcelUtil<DtbUserBook> util = new ExcelUtil<DtbUserBook>(DtbUserBook.class);
        util.exportExcel(response, list, "DUTP-DTB_014学生/教师书架数据");
    }

    /**
     * 获取DUTP-DTB_014学生/教师书架详细信息
     */
    @RequiresPermissions("book:book:query")
    @GetMapping(value = "/{userBookId}")
    public AjaxResult getInfo(@PathVariable("userBookId") Long userBookId)
    {
        return success(dtbUserBookService.selectDtbUserBookByUserBookId(userBookId));
    }

    /**
     * 学生教师端我的教材查询
     */
    @GetMapping(value = "/getInfoEducation")
    public AjaxResult getInfoEducation(DtbUserBook dtbUserBook)
    {
        return success(dtbUserBookService.getInfoEducation(dtbUserBook));
    }

    /**
     * 新增DUTP-DTB_014学生/教师书架
     */
    @Log(title = "DUTP-DTB_014学生/教师书架", operatorType = OperatorType.READER, businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbUserBook dtbUserBook)
    {
        return toAjax(dtbUserBookService.insertDtbUserBook(dtbUserBook));
    }

    /**
     * 修改DUTP-DTB_014学生/教师书架
     */
    @RequiresPermissions("book:book:edit")
    @Log(title = "DUTP-DTB_014学生/教师书架", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbUserBook dtbUserBook)
    {
        return toAjax(dtbUserBookService.updateDtbUserBook(dtbUserBook));
    }

    /**
     * 学生教师端批量修改学生/教师书架
     */
    @Log(title = "学生教师端批量修改学生/教师书架",  operatorType = OperatorType.READER, businessType = BusinessType.UPDATE)
    @PutMapping("/editEducation")
    public AjaxResult editEducation(@RequestBody List<DtbUserBook> dtbUserBookList)
    {
        return toAjax(dtbUserBookService.editEducation(dtbUserBookList));
    }

    /**
     * 删除DUTP-DTB_014学生/教师书架
     */
    @RequiresPermissions("book:book:remove")
    @Log(title = "DUTP-DTB_014学生/教师书架", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userBookIds}")
    public AjaxResult remove(@PathVariable Long[] userBookIds)
    {
        return toAjax(dtbUserBookService.deleteDtbUserBookByUserBookIds(Arrays.asList(userBookIds)));
    }
    /**
     * 兑换购书码状态变更
     */
    @Log(title = "兑换购书码状态变更",  operatorType = OperatorType.READER, businessType = BusinessType.UPDATE)
    @PutMapping("/editStatus")
    public AjaxResult editStatus(@RequestBody DtbUserBook dtbUserBook)
    {
        return toAjax(dtbUserBookService.editStatus(dtbUserBook));
    }

    /**
     * 新增DUTP-DTB_014学生/教师书架
     */
    @Log(title = "DUTP-DTB_014学生/教师书架", operatorType = OperatorType.READER, businessType = BusinessType.INSERT)
    @PostMapping("/addBatchUserBook")
    public AjaxResult addBatchUserBook(@RequestBody List<DtbUserBook> insList)
    {
        return toAjax(dtbUserBookService.saveBatch(insList));
    }
}

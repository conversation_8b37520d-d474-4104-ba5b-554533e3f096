package cn.dutp.book.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.book.mapper.DtbBookShareChapterMapper;
import cn.dutp.book.domain.DtbBookShareChapter;
import cn.dutp.book.service.IDtbBookShareChapterService;

/**
 * 教材分享目录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Service
public class DtbBookShareChapterServiceImpl extends ServiceImpl<DtbBookShareChapterMapper, DtbBookShareChapter> implements IDtbBookShareChapterService
{
    @Autowired
    private DtbBookShareChapterMapper dtbBookShareChapterMapper;

    /**
     * 查询教材分享目录
     *
     * @param shareChapterId 教材分享目录主键
     * @return 教材分享目录
     */
    @Override
    public DtbBookShareChapter selectDtbBookShareChapterByShareChapterId(Long shareChapterId)
    {
        return this.getById(shareChapterId);
    }

    /**
     * 查询教材分享目录列表
     *
     * @param dtbBookShareChapter 教材分享目录
     * @return 教材分享目录
     */
    @Override
    public List<DtbBookShareChapter> selectDtbBookShareChapterList(DtbBookShareChapter dtbBookShareChapter)
    {
        LambdaQueryWrapper<DtbBookShareChapter> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbBookShareChapter.getShareId())) {
                lambdaQueryWrapper.eq(DtbBookShareChapter::getShareId
                ,dtbBookShareChapter.getShareId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookShareChapter.getChapterId())) {
                lambdaQueryWrapper.eq(DtbBookShareChapter::getChapterId
                ,dtbBookShareChapter.getChapterId());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增教材分享目录
     *
     * @param dtbBookShareChapter 教材分享目录
     * @return 结果
     */
    @Override
    public boolean insertDtbBookShareChapter(DtbBookShareChapter dtbBookShareChapter)
    {
        return this.save(dtbBookShareChapter);
    }

    /**
     * 修改教材分享目录
     *
     * @param dtbBookShareChapter 教材分享目录
     * @return 结果
     */
    @Override
    public boolean updateDtbBookShareChapter(DtbBookShareChapter dtbBookShareChapter)
    {
        return this.updateById(dtbBookShareChapter);
    }

    /**
     * 批量删除教材分享目录
     *
     * @param shareChapterIds 需要删除的教材分享目录主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookShareChapterByShareChapterIds(List<Long> shareChapterIds)
    {
        return this.removeByIds(shareChapterIds);
    }

}

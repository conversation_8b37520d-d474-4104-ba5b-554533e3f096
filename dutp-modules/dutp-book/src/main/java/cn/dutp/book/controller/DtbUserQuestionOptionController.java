package cn.dutp.book.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.book.domain.DtbUserQuestionOption;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.book.service.IDtbUserQuestionOptionService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * DUTP-DTB_010数字教材选择题选项Controller
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@RestController
@RequestMapping("/userQuestionOption")
public class DtbUserQuestionOptionController extends BaseController
{
    @Autowired
    private IDtbUserQuestionOptionService dtbBookQuestionOptionService;

    /**
     * 查询DUTP-DTB_010数字教材选择题选项列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbUserQuestionOption dtbUserQuestionOption)
    {
        startPage();
        List<DtbUserQuestionOption> list = dtbBookQuestionOptionService.selectDtbBookQuestionOptionList(dtbUserQuestionOption);
        return getDataTable(list);
    }

    /**
     * 导出DUTP-DTB_010数字教材选择题选项列表
     */
    @RequiresPermissions("book:option:export")
    @Log(title = "导出DUTP-DTB_010数字教材选择题选项", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbUserQuestionOption dtbUserQuestionOption)
    {
        List<DtbUserQuestionOption> list = dtbBookQuestionOptionService.selectDtbBookQuestionOptionList(dtbUserQuestionOption);
        ExcelUtil<DtbUserQuestionOption> util = new ExcelUtil<DtbUserQuestionOption>(DtbUserQuestionOption.class);
        util.exportExcel(response, list, "DUTP-DTB_010数字教材选择题选项数据");
    }

    /**
     * 获取DUTP-DTB_010数字教材选择题选项详细信息
     */
    @RequiresPermissions("book:option:query")
    @GetMapping(value = "/{optionId}")
    public AjaxResult getInfo(@PathVariable("optionId") Long optionId)
    {
        return success(dtbBookQuestionOptionService.selectDtbBookQuestionOptionByOptionId(optionId));
    }

    /**
     * 新增DUTP-DTB_010数字教材选择题选项
     */
    @RequiresPermissions("book:option:add")
    @Log(title = "新增DUTP-DTB_010数字教材选择题选项", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbUserQuestionOption dtbUserQuestionOption)
    {
        return toAjax(dtbBookQuestionOptionService.insertDtbBookQuestionOption(dtbUserQuestionOption));
    }

    /**
     * 修改DUTP-DTB_010数字教材选择题选项
     */
    @RequiresPermissions("book:option:edit")
    @Log(title = "修改DUTP-DTB_010数字教材选择题选项", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbUserQuestionOption dtbUserQuestionOption)
    {
        return toAjax(dtbBookQuestionOptionService.updateDtbBookQuestionOption(dtbUserQuestionOption));
    }

    /**
     * 删除DUTP-DTB_010数字教材选择题选项
     */
    @RequiresPermissions("book:option:remove")
    @Log(title = "删除DUTP-DTB_010数字教材选择题选项", businessType = BusinessType.DELETE)
    @DeleteMapping("/{optionIds}")
    public AjaxResult remove(@PathVariable Long[] optionIds)
    {
        return toAjax(dtbBookQuestionOptionService.deleteDtbBookQuestionOptionByOptionIds(Arrays.asList(optionIds)));
    }
}

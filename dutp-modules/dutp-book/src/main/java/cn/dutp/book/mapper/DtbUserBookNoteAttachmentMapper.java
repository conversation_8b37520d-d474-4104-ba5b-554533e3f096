package cn.dutp.book.mapper;

import java.util.List;

import cn.dutp.book.domain.vo.DtbUserBookNoteAttachmentVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import cn.dutp.book.domain.DtbUserBookNoteAttachment;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
/**
 * 笔记附件Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Repository
public interface DtbUserBookNoteAttachmentMapper extends BaseMapper<DtbUserBookNoteAttachment>
{
    @Select("SELECT " +
            "attachment_id," +
            "note_id," +
            "attachment_type," +
            "attachment_url," +
            "attachment_size," +
            "attachment_name " +
            "FROM " +
            "dtb_user_book_note_attachment " +
            "WHERE " +
            "1 = 1 " +
            "AND note_id = #{noteId}")
    List<DtbUserBookNoteAttachmentVO> selectNodeAttachmentList(@Param("noteId") Long noteId);
}

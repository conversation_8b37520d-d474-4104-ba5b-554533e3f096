package cn.dutp.book.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.book.domain.DutpAiUserConfig;
import cn.dutp.book.service.IDutpAiUserConfigService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 【ai请求配置表】Controller
 *
 * <AUTHOR>
 * @date 2025-04-02
 */
@RestController
@RequestMapping("/experimentConfig")
public class DutpAiUserConfigController extends BaseController
{
    @Autowired
    private IDutpAiUserConfigService dutpAiUserConfigService;

    /**
     * 查询【请填写功能名称】列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DutpAiUserConfig dutpAiUserConfig)
    {
        startPage();
        List<DutpAiUserConfig> list = dutpAiUserConfigService.selectDutpAiUserConfigList(dutpAiUserConfig);
        return getDataTable(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @RequiresPermissions("system:config:export")
    @Log(title = "导出【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DutpAiUserConfig dutpAiUserConfig)
    {
        List<DutpAiUserConfig> list = dutpAiUserConfigService.selectDutpAiUserConfigList(dutpAiUserConfig);
        ExcelUtil<DutpAiUserConfig> util = new ExcelUtil<DutpAiUserConfig>(DutpAiUserConfig.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @RequiresPermissions("system:config:query")
    @GetMapping(value = "/{aiConfigId}")
    public AjaxResult getInfo(@PathVariable("aiConfigId") Long aiConfigId)
    {
        return success(dutpAiUserConfigService.selectDutpAiUserConfigByAiConfigId(aiConfigId));
    }

    /**
     * 新增【请填写功能名称】
     */
    @RequiresPermissions("system:config:add")
    @Log(title = "新增【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DutpAiUserConfig dutpAiUserConfig)
    {
        return toAjax(dutpAiUserConfigService.insertDutpAiUserConfig(dutpAiUserConfig));
    }

    /**
     * 修改【请填写功能名称】
     */
    @RequiresPermissions("system:config:edit")
    @Log(title = "修改【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DutpAiUserConfig dutpAiUserConfig)
    {
        return toAjax(dutpAiUserConfigService.updateDutpAiUserConfig(dutpAiUserConfig));
    }

    /**
     * 删除【请填写功能名称】
     */
    @RequiresPermissions("system:config:remove")
    @Log(title = "删除【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{aiConfigIds}")
    public AjaxResult remove(@PathVariable Long[] aiConfigIds)
    {
        return toAjax(dutpAiUserConfigService.deleteDutpAiUserConfigByAiConfigIds(Arrays.asList(aiConfigIds)));
    }
}

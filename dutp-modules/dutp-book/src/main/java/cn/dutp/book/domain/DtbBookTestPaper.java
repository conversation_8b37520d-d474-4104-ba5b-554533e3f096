package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.List;

/**
 * 教材跟试卷关系对象 dtb_book_test_paper
 *
 * <AUTHOR>
 * @date 2025-02-17
 */
@Data
@TableName("dtb_book_test_paper")
public class DtbBookTestPaper extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookPaperId;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long paperId;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    /**
     * 节点domid
     */
    private String domId;


    // 考试题目明细
    @TableField(exist = false)
    private List<DtbBookQuestionAnswer> dtbBookQuestionAnswerList;


    // 考试答卷明细
    @TableField(exist = false)
    private BigDecimal totalScore;
    @TableField(exist = false)
    private String paperTitle;
    @TableField(exist = false)
    private Integer questionQuantity;
    @TableField(exist = false)
    private Integer paperType;
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Integer state;

    @TableField(exist = false)
    private Integer isFree;

    private Integer pageNumber;
    @TableField(exist = false)
    private String chapterName;
    @TableField(exist = false)
    private Long versionId;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("bookPaperId", getBookPaperId())
                .append("bookId", getBookId())
                .append("paperId", getPaperId())
                .append("chapterId", getChapterId())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

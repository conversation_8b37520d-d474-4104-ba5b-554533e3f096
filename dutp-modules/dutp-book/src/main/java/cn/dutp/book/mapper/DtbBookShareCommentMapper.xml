<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbBookShareCommentMapper">


    <select id="selectBookShareCommentList" resultType="cn.dutp.book.domain.vo.DtbBookShareCommentVo">
        SELECT
            comment.comment_id,
            comment.chapter_id,
            comment.share_id,
            comment.page_number,
            comment.comment_content,
            comment.from_word_id,
            comment.end_word_id,
            comment.create_time,
            comment.book_content,
            c.chapter_name
        FROM
        dtb_book_share_comment comment
        INNER JOIN dtb_book_chapter c ON comment.chapter_id = c.chapter_id
        WHERE
        1 = 1
        AND comment.del_flag = 0
        AND comment.book_id = #{bookId}
        AND comment.share_id = #{shareId}
        <if test="chapterId !=null">
            AND comment.chapter_id = #{chapterId}
        </if>
        <if test="sort == 1">
            ORDER BY
            comment.page_number ASC
        </if>
        <if test="sort == 2">
            ORDER BY
            comment.create_time DESC
        </if>
    </select>

    <select id="exportBookShare" resultType="cn.dutp.book.domain.DtbBookShareComment">
        SELECT
        comment.comment_id,
        comment.share_id,
        comment.chapter_id,
        comment.comment_content,
        comment.create_time,
        comment.book_content,
        c.chapter_name
        FROM
        dtb_book_share_comment comment
        INNER JOIN dtb_book_chapter c ON comment.chapter_id = c.chapter_id
        WHERE
        comment.del_flag = 0
        AND comment.book_id = #{bookId}
        AND comment.share_id = #{bookId}
        <if test = "chapterId !=null" >
            AND comment.chapter_id = #{chapterId}
        </if >
        order by c.sort
    </select>

</mapper>
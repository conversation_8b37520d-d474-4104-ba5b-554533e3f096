<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbBookTypeMapper">
    
    <resultMap type="DtbBookType" id="DtbBookTypeResult">
        <result property="typeId"    column="type_id"    />
        <result property="typeName"    column="type_name"    />
        <result property="typeCode"    column="type_code"    />
        <result property="parentId"    column="parent_id"    />
        <result property="sort"    column="sort"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectDtbBookTypeVo">
        select type_id, type_name, type_code, parent_id, sort, create_by, create_time, update_by, update_time, del_flag from dtb_book_type
    </sql>

    <select id="selectDtbBookTypeList" parameterType="DtbBookType" resultMap="DtbBookTypeResult">
        <include refid="selectDtbBookTypeVo"/>
        <where>  
            <if test="typeName != null  and typeName != ''"> and type_name like concat('%', #{typeName}, '%')</if>
            <if test="typeCode != null  and typeCode != ''"> and type_code = #{typeCode}</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="sort != null "> and sort = #{sort}</if>
        </where>
    </select>
    
    <select id="selectDtbBookTypeByTypeId" parameterType="Long" resultMap="DtbBookTypeResult">
        <include refid="selectDtbBookTypeVo"/>
        where type_id = #{typeId}
    </select>

    <select id="checkIsRepeatByTypeName" resultType="java.lang.Integer">
        select count(*) from dtb_book_type where type_name = #{typeName} and del_flag = '0'
        <if test="typeId != null">and type_id != #{typeId}</if>
    </select>

    <select id="checkIsRepeatByTypeCode" resultType="java.lang.Integer">
        select count(*) from dtb_book_type where type_code = #{typeCode} and del_flag = '0'
        <if test="typeId != null">and type_id != #{typeId}</if>
    </select>

    <insert id="insertDtbBookType" parameterType="DtbBookType" useGeneratedKeys="true" keyProperty="typeId">
        insert into dtb_book_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="typeName != null">type_name,</if>
            <if test="typeCode != null">type_code,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="sort != null">sort,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="typeName != null">#{typeName},</if>
            <if test="typeCode != null">#{typeCode},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="sort != null">#{sort},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateDtbBookType" parameterType="DtbBookType">
        update dtb_book_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="typeName != null">type_name = #{typeName},</if>
            <if test="typeCode != null">type_code = #{typeCode},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where type_id = #{typeId}
    </update>

    <delete id="deleteDtbBookTypeByTypeId" parameterType="Long">
        delete from dtb_book_type where type_id = #{typeId}
    </delete>

    <delete id="deleteDtbBookTypeByTypeIds" parameterType="String">
        delete from dtb_book_type where type_id in 
        <foreach item="typeId" collection="array" open="(" separator="," close=")">
            #{typeId}
        </foreach>
    </delete>
</mapper>
package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 心理测试题目选项对象 mooc_psychology_health_scale_question_option
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Data
@TableName("mooc_psychology_health_scale_question_option")
public class MoocPsychologyHealthScaleQuestionOption extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long optionId;

    /**
     * 选项内容
     */
    @Excel(name = "选项内容")
    private String optionContent;

    /**
     * 题目id
     */
    @Excel(name = "题目id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long questionId;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Long sort;

    /**
     * 分数
     */
    @Excel(name = "分数")
    private Long score;

    /**
     * 跳转到题目id
     */
    @Excel(name = "跳转到题目id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long jumpQuestionId;

    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long jumpId;


    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 排序
     */
    @TableField(exist = false)
    private Long optionSort;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("optionId", getOptionId())
                .append("optionContent", getOptionContent())
                .append("questionId", getQuestionId())
                .append("sort", getSort())
                .append("score", getScore())
                .append("jumpQuestionId", getJumpQuestionId())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("delFlag", getDelFlag())
                .toString();
    }
}

package cn.dutp.book.service;

import cn.dutp.book.domain.DtbUserBookLine;
import cn.dutp.common.core.web.domain.AjaxResult;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * DUTP-DTB_020划线Service接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IDtbUserBookLineService extends IService<DtbUserBookLine> {
    /**
     * 查询DUTP-DTB_020划线
     *
     * @param lineId DUTP-DTB_020划线主键
     * @return DUTP-DTB_020划线
     */
    public DtbUserBookLine selectDtbUserBookLineByLineId(Long lineId);

    /**
     * 查询DUTP-DTB_020划线列表
     *
     * @param dtbUserBookLine DUTP-DTB_020划线
     * @return DUTP-DTB_020划线集合
     */
    public List<DtbUserBookLine> selectDtbUserBookLineList(DtbUserBookLine dtbUserBookLine);

    /**
     * 新增DUTP-DTB_020划线
     *
     * @param dtbUserBookLine DUTP-DTB_020划线
     * @return 结果
     */
    public AjaxResult insertDtbUserBookLine(DtbUserBookLine dtbUserBookLine);

    /**
     * 修改DUTP-DTB_020划线
     *
     * @param dtbUserBookLine DUTP-DTB_020划线
     * @return 结果
     */
    public boolean updateDtbUserBookLine(DtbUserBookLine dtbUserBookLine);

    /**
     * 批量删除DUTP-DTB_020划线
     *
     * @param lineIds 需要删除的DUTP-DTB_020划线主键集合
     * @return 结果
     */
    public boolean deleteDtbUserBookLineByLineIds(List<Long> lineIds);

    AjaxResult selectReaderUserBookLineList(DtbUserBookLine bookLine);

    AjaxResult deleteReaderDtbUserBookLine(DtbUserBookLine dtbUserBookLine);

    void exportBookLine(HttpServletResponse response, DtbUserBookLine dtbUserBookLine);
}

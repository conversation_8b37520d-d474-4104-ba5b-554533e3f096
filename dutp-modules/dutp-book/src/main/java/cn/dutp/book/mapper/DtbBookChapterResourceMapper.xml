<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbBookChapterResourceMapper">

    <resultMap type="DtbBookChapterResource" id="DtbBookChapterResourceResult">
        <result property="chapterResourceId" column="chapter_resource_id"/>
        <result property="chapterId" column="chapter_id"/>
        <result property="bookId" column="book_id"/>
        <result property="resourceId" column="resource_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectDtbBookChapterResourceVo">
        select chapter_resource_id,
               chapter_id,
               book_id,
               resource_id,
               create_by,
               create_time,
               update_by,
               update_time
        from dtb_book_chapter_resource
    </sql>

    <select id="selectDtbBookChapterResourceList" parameterType="DtbBookChapterResource"
            resultMap="DtbBookChapterResourceResult">
        <include refid="selectDtbBookChapterResourceVo"/>
        <where>
            <if test="chapterId != null ">and chapter_id = #{chapterId}</if>
            <if test="bookId != null ">and book_id = #{bookId}</if>
            <if test="resourceId != null ">and resource_id = #{resourceId}</if>
        </where>
    </select>

    <select id="selectDtbBookChapterResourceByChapterResourceId" parameterType="Long"
            resultMap="DtbBookChapterResourceResult">
        <include refid="selectDtbBookChapterResourceVo"/>
        where chapter_resource_id = #{chapterResourceId}
    </select>
    <select id="queryBookChapterResource" resultType="cn.dutp.book.domain.DtbBookChapterResource">
        SELECT
            r.resource_id
        FROM
            dtb_book_chapter_resource r
                INNER JOIN dtb_book_chapter c.chapter_id = r.chapter_id
	INNER JOIN dtb_book b ON b.book_id = c.book_id
            AND c.version_id = b.current_version_id
        WHERE
            r.book_id = #{bookId} and c.del_flag = '0'
          AND c.sort = #{sort}
    </select>

    <insert id="insertDtbBookChapterResource" parameterType="DtbBookChapterResource" useGeneratedKeys="true"
            keyProperty="chapterResourceId">
        insert into dtb_book_chapter_resource
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="chapterId != null">chapter_id,</if>
            <if test="bookId != null">book_id,</if>
            <if test="resourceId != null">resource_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="chapterId != null">#{chapterId},</if>
            <if test="bookId != null">#{bookId},</if>
            <if test="resourceId != null">#{resourceId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateDtbBookChapterResource" parameterType="DtbBookChapterResource">
        update dtb_book_chapter_resource
        <trim prefix="SET" suffixOverrides=",">
            <if test="chapterId != null">chapter_id = #{chapterId},</if>
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="resourceId != null">resource_id = #{resourceId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where chapter_resource_id = #{chapterResourceId}
    </update>

    <delete id="deleteDtbBookChapterResourceByChapterResourceId" parameterType="Long">
        delete
        from dtb_book_chapter_resource
        where chapter_resource_id = #{chapterResourceId}
    </delete>

    <delete id="deleteDtbBookChapterResourceByChapterResourceIds" parameterType="String">
        delete from dtb_book_chapter_resource where chapter_resource_id in
        <foreach item="chapterResourceId" collection="array" open="(" separator="," close=")">
            #{chapterResourceId}
        </foreach>
    </delete>
</mapper>
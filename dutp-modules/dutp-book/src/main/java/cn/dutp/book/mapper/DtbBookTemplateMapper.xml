<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbBookTemplateMapper">
    

    <select id="selectDtbBookTemplateByChapterId" resultType="cn.dutp.book.domain.vo.DtbBookTemplateVO">
        select
            dbt.template_id as id,
            dbt.modal,
            dbt.img_url,
            dbt.header_url,
            dbt.content_url,
            dbt.footer_url,
            dbt.chapter_header_url,
            dbt.chapter_font_color,
            dbt.chapter_header_height,
            dbt.joint_header_url,
            dbt.joint_font_color,
            dbt.joint_height,
            dbt.order_template_bg_url,
            dbt.order_template_margin_left,
            dbt.order_template_color,
            dbt.pages_font_color,
            dbt.pages_align,
            dbt.theme,
            dbt.pages_position
        from dtb_book_template dbt
            inner join dtb_book_chapter dbc on dbt.template_id = dbc.template_id
        where dbt.del_flag = 0 and dbc.chapter_id = #{chapterId}
    </select>

</mapper>
package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 试卷对象 dtb_test_paper
 *
 * <AUTHOR>
 * @date 2025-02-08
 */
@Data
@TableName("dtb_test_paper")
public class DtbTestPaper extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long paperId;

    /**
     * 试卷标题
     */
    @Excel(name = "试卷标题")
    private String paperTitle;

    /**
     * 小题数
     */
    @Excel(name = "小题数")
    private Integer questionQuantity;

    /**
     * 总分
     */
    @Excel(name = "总分")
    private Integer totalScore;

    /**
     * 1试卷2作业
     */
    @Excel(name = "1试卷2作业")
    private Integer paperType;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 页码[弃用]
     */
    private Integer pageNumber;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long sysUserId;

    /**
     * 小题集合
     */
    @TableField(exist = false)
    List<DtbTestPaperQuestionCollection> dtbTestPaperQuestionCollectionList;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("paperId", getPaperId())
                .append("paperTitle", getPaperTitle())
                .append("questionQuantity", getQuestionQuantity())
                .append("totalScore", getTotalScore())
                .append("paperType", getPaperType())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("delFlag", getDelFlag())
                .toString();
    }
}

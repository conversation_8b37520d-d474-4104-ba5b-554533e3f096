package cn.dutp.book.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.book.mapper.DutpAiUserConfigMapper;
import cn.dutp.book.domain.DutpAiUserConfig;
import cn.dutp.book.service.IDutpAiUserConfigService;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-02
 */
@Service
public class DutpAiUserConfigServiceImpl extends ServiceImpl<DutpAiUserConfigMapper, DutpAiUserConfig> implements IDutpAiUserConfigService
{
    @Autowired
    private DutpAiUserConfigMapper dutpAiUserConfigMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param aiConfigId 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public DutpAiUserConfig selectDutpAiUserConfigByAiConfigId(Long aiConfigId)
    {
        return this.getById(aiConfigId);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param dutpAiUserConfig 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<DutpAiUserConfig> selectDutpAiUserConfigList(DutpAiUserConfig dutpAiUserConfig)
    {
        LambdaQueryWrapper<DutpAiUserConfig> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dutpAiUserConfig.getUserId())) {
                lambdaQueryWrapper.eq(DutpAiUserConfig::getUserId
                ,dutpAiUserConfig.getUserId());
            }
                if(ObjectUtil.isNotEmpty(dutpAiUserConfig.getAiExperimentCount())) {
                lambdaQueryWrapper.eq(DutpAiUserConfig::getAiExperimentCount
                ,dutpAiUserConfig.getAiExperimentCount());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param dutpAiUserConfig 【请填写功能名称】
     * @return 结果
     */
    @Override
    public boolean insertDutpAiUserConfig(DutpAiUserConfig dutpAiUserConfig)
    {
        return this.save(dutpAiUserConfig);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param dutpAiUserConfig 【请填写功能名称】
     * @return 结果
     */
    @Override
    public boolean updateDutpAiUserConfig(DutpAiUserConfig dutpAiUserConfig)
    {
        return this.updateById(dutpAiUserConfig);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param aiConfigIds 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public boolean deleteDutpAiUserConfigByAiConfigIds(List<Long> aiConfigIds)
    {
        return this.removeByIds(aiConfigIds);
    }

}

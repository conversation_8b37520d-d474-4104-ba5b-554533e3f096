<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbBookChapterDataMapper">

    <resultMap type="DtbBookChapterData" id="DtbBookChapterDataResult">
        <result property="dataId" column="data_id"/>
        <result property="bookId" column="book_id"/>
        <result property="chapterId" column="chapter_id"/>
        <result property="wordQuantity" column="word_quantity"/>
        <result property="imageQuanity" column="image_quanity"/>
        <result property="bubbleQuantity" column="bubble_quantity"/>
        <result property="outsideLinkQuantity" column="outside_link_quantity"/>
        <result property="formulaQuantity" column="formula_quantity"/>
        <result property="threeDimenQuantity" column="three_dimen_quantity"/>
        <result property="avrQuantity" column="avr_quantity"/>
        <result property="simulationQuantity" column="simulation_quantity"/>
        <result property="questionQuantity" column="question_quantity"/>
        <result property="gameQuantity" column="game_quantity"/>
        <result property="footnoteQuantity" column="footnote_quantity"/>
        <result property="resouceQuantity" column="resouce_quantity"/>
        <result property="audioQuantity" column="audio_quantity"/>
        <result property="videoQuantity" column="video_quantity"/>
        <result property="extQuantity" column="ext_quantity"/>
        <result property="codeQuantity" column="code_quantity"/>
        <result property="audioTotalDuration" column="audio_total_duration"/>
        <result property="interactionVoteQuantity" column="interaction_vote_quantity"/>
        <result property="videoTotalDuration" column="video_total_duration"/>
        <result property="interactionWordCloudQuantity" column="interaction_word_cloud_quantity"/>
        <result property="interactionDiscussQuantity" column="interaction_discuss_quantity"/>
        <result property="interactionImageWaterfallQuantity" column="interaction_image_waterfall_quantity"/>
        <result property="insideLinkQuantity" column="inside_link_quantity"/>
        <result property="studyTotalTime" column="study_total_time"/>
        <result property="studyVideoTime" column="study_video_time"/>
        <result property="studyNoteQuantity" column="study_note_quantity"/>
        <result property="studyHighlightQuantity" column="study_highlight_quantity"/>
        <result property="studyDiscussQuantity" column="study_discuss_quantity"/>
        <result property="studyUserQuantity" column="study_user_quantity"/>
        <result property="studyQuestionQuantity" column="study_question_quantity"/>
        <result property="studyQuestionRate" column="study_question_rate"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectDtbBookChapterDataVo">
        select data_id,
               book_id,
               chapter_id,
               word_quantity,
               image_quanity,
               bubble_quantity,
               outside_link_quantity,
               formula_quantity,
               three_dimen_quantity,
               avr_quantity,
               simulation_quantity,
               question_quantity,
               game_quantity,
               footnote_quantity,
               resouce_quantity,
               audio_quantity,
               video_quantity,
               ext_quantity,
               code_quantity,
               audio_total_duration,
               interaction_vote_quantity,
               video_total_duration,
               interaction_word_cloud_quantity,
               interaction_discuss_quantity,
               interaction_image_waterfall_quantity,
               inside_link_quantity,
               study_total_time,
               study_video_time,
               study_note_quantity,
               study_highlight_quantity,
               study_discuss_quantity,
               study_user_quantity,
               study_question_quantity,
               study_question_rate,
               create_by,
               create_time,
               update_by,
               update_time
        from dtb_book_chapter_data
    </sql>


    <select id="selectDtbBookChapterDataByDataId" parameterType="Long" resultMap="DtbBookChapterDataResult">
        <include refid="selectDtbBookChapterDataVo"/>
        where data_id = #{dataId}
    </select>
    <select id="selectDtbBookChapterDataList" resultType="cn.dutp.book.domain.DtbBookChapterData">
        SELECT
            c.chapter_name,
            d.study_video_time,
            d.study_user_quantity,
            d.study_total_time,
            d.study_note_quantity,
            d.study_highlight_quantity,
            d.study_discuss_quantity,
            d.study_question_quantity,
            d.study_question_rate
        FROM
            dtb_book_chapter c
                INNER JOIN dtb_book b on b.book_id = c.book_id and c.version_id = b.current_version_id
                LEFT JOIN dtb_book_chapter_data d ON c.chapter_id = d.chapter_id

        WHERE
            c.book_id = #{bookId}
          AND c.del_flag = '0'
        ORDER BY
            c.sort ASC
    </select>
    <select id="dataOverview" resultType="cn.dutp.book.domain.DtbBookChapterData">
        SELECT
            c.chapter_id,
            d.word_quantity,
            d.image_quanity,
            d.bubble_quantity,
            d.outside_link_quantity,
            d.formula_quantity,
            d.three_dimen_quantity,
            d.avr_quantity,
            d.simulation_quantity,
            d.question_quantity,
            d.game_quantity,
            d.footnote_quantity,
            d.resouce_quantity,
            d.audio_quantity,
            d.video_quantity,
            d.ext_quantity,
            d.code_quantity,
            d.audio_total_duration,
            d.interaction_vote_quantity,
            d.video_total_duration,
            d.interaction_word_cloud_quantity,
            d.interaction_discuss_quantity,
            d.interaction_image_waterfall_quantity,
            d.inside_link_quantity
        FROM
            dtb_book_chapter c
                INNER JOIN dtb_book b ON b.book_id = c.book_id
                AND c.version_id = b.current_version_id
                LEFT JOIN dtb_book_chapter_data d ON c.chapter_id = d.chapter_id
        WHERE
            c.book_id = #{bookId}
          AND c.del_flag = '0'
        <if test="chapterId != null"> and c.chapter_id = #{chapterId}</if>
    </select>
    <select id="dataOverviewOne" resultType="cn.dutp.book.domain.DtbBookChapterData">
        SELECT
        c.chapter_id,
        d.word_quantity,
        d.image_quanity,
        d.bubble_quantity,
        d.outside_link_quantity,
        d.formula_quantity,
        d.three_dimen_quantity,
        d.avr_quantity,
        d.simulation_quantity,
        d.question_quantity,
        d.game_quantity,
        d.footnote_quantity,
        d.resouce_quantity,
        d.audio_quantity,
        d.video_quantity,
        d.ext_quantity,
        d.code_quantity,
        d.audio_total_duration,
        d.interaction_vote_quantity,
        d.video_total_duration,
        d.interaction_word_cloud_quantity,
        d.interaction_discuss_quantity,
        d.interaction_image_waterfall_quantity,
        d.inside_link_quantity
        FROM
        dtb_book_chapter c
        INNER JOIN dtb_book b ON b.book_id = c.book_id
        AND c.version_id = b.current_version_id
        LEFT JOIN dtb_book_chapter_data d ON c.chapter_id = d.chapter_id
        WHERE
        c.book_id = #{bookId}
        AND c.del_flag = '0'
        and c.chapter_id = #{chapterId}
    </select>

    <select id="queryChapterDataByChapterSortAndBookId" resultType="cn.dutp.book.domain.DtbBookChapterData">
        SELECT
            d.*
        FROM
            dtb_book_chapter c
                INNER JOIN dtb_book b ON b.book_id = c.book_id
                AND c.version_id = b.current_version_id
                INNER JOIN dtb_book_chapter_data d ON c.chapter_id = d.chapter_id
        WHERE
            c.book_id = #{bookId}
          AND c.del_flag = '0'
          and c.sort = #{sort}
    </select>
    <select id="queryChapterDataByChapterId" resultType="cn.dutp.book.domain.DtbBookChapterData">
        SELECT
            d.data_id,
            d.chapter_id,
            d.word_quantity,
            d.image_quanity,
            d.bubble_quantity,
            d.outside_link_quantity,
            d.formula_quantity,
            d.three_dimen_quantity,
            d.avr_quantity,
            d.simulation_quantity,
            d.question_quantity,
            d.game_quantity,
            d.footnote_quantity,
            d.resouce_quantity,
            d.audio_quantity,
            d.video_quantity,
            d.ext_quantity,
            d.code_quantity,
            d.audio_total_duration,
            d.interaction_vote_quantity,
            d.video_total_duration,
            d.interaction_word_cloud_quantity,
            d.interaction_discuss_quantity,
            d.interaction_image_waterfall_quantity,
            d.inside_link_quantity,
            d.study_user_quantity,
            d.study_video_time,
            d.study_total_time,
            d.study_note_quantity,
            d.study_highlight_quantity,
            d.study_discuss_quantity,
            d.study_question_quantity,
            d.study_question_rate
        FROM
            dtb_book_chapter_data d
        WHERE
           d.chapter_id = #{chapterId}
    </select>


    <insert id="insertDtbBookChapterData" parameterType="DtbBookChapterData" useGeneratedKeys="true"
            keyProperty="dataId">
        insert into dtb_book_chapter_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bookId != null">book_id,</if>
            <if test="chapterId != null">chapter_id,</if>
            <if test="wordQuantity != null">word_quantity,</if>
            <if test="imageQuanity != null">image_quanity,</if>
            <if test="bubbleQuantity != null">bubble_quantity,</if>
            <if test="outsideLinkQuantity != null">outside_link_quantity,</if>
            <if test="formulaQuantity != null">formula_quantity,</if>
            <if test="threeDimenQuantity != null">three_dimen_quantity,</if>
            <if test="avrQuantity != null">avr_quantity,</if>
            <if test="simulationQuantity != null">simulation_quantity,</if>
            <if test="questionQuantity != null">question_quantity,</if>
            <if test="gameQuantity != null">game_quantity,</if>
            <if test="footnoteQuantity != null">footnote_quantity,</if>
            <if test="resouceQuantity != null">resouce_quantity,</if>
            <if test="audioQuantity != null">audio_quantity,</if>
            <if test="videoQuantity != null">video_quantity,</if>
            <if test="extQuantity != null">ext_quantity,</if>
            <if test="codeQuantity != null">code_quantity,</if>
            <if test="audioTotalDuration != null">audio_total_duration,</if>
            <if test="interactionVoteQuantity != null">interaction_vote_quantity,</if>
            <if test="videoTotalDuration != null">video_total_duration,</if>
            <if test="interactionWordCloudQuantity != null">interaction_word_cloud_quantity,</if>
            <if test="interactionDiscussQuantity != null">interaction_discuss_quantity,</if>
            <if test="interactionImageWaterfallQuantity != null">interaction_image_waterfall_quantity,</if>
            <if test="insideLinkQuantity != null">inside_link_quantity,</if>
            <if test="studyTotalTime != null">study_total_time,</if>
            <if test="studyVideoTime != null">study_video_time,</if>
            <if test="studyNoteQuantity != null">study_note_quantity,</if>
            <if test="studyHighlightQuantity != null">study_highlight_quantity,</if>
            <if test="studyDiscussQuantity != null">study_discuss_quantity,</if>
            <if test="studyUserQuantity != null">study_user_quantity,</if>
            <if test="studyQuestionQuantity != null">study_question_quantity,</if>
            <if test="studyQuestionRate != null">study_question_rate,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bookId != null">#{bookId},</if>
            <if test="chapterId != null">#{chapterId},</if>
            <if test="wordQuantity != null">#{wordQuantity},</if>
            <if test="imageQuanity != null">#{imageQuanity},</if>
            <if test="bubbleQuantity != null">#{bubbleQuantity},</if>
            <if test="outsideLinkQuantity != null">#{outsideLinkQuantity},</if>
            <if test="formulaQuantity != null">#{formulaQuantity},</if>
            <if test="threeDimenQuantity != null">#{threeDimenQuantity},</if>
            <if test="avrQuantity != null">#{avrQuantity},</if>
            <if test="simulationQuantity != null">#{simulationQuantity},</if>
            <if test="questionQuantity != null">#{questionQuantity},</if>
            <if test="gameQuantity != null">#{gameQuantity},</if>
            <if test="footnoteQuantity != null">#{footnoteQuantity},</if>
            <if test="resouceQuantity != null">#{resouceQuantity},</if>
            <if test="audioQuantity != null">#{audioQuantity},</if>
            <if test="videoQuantity != null">#{videoQuantity},</if>
            <if test="extQuantity != null">#{extQuantity},</if>
            <if test="codeQuantity != null">#{codeQuantity},</if>
            <if test="audioTotalDuration != null">#{audioTotalDuration},</if>
            <if test="interactionVoteQuantity != null">#{interactionVoteQuantity},</if>
            <if test="videoTotalDuration != null">#{videoTotalDuration},</if>
            <if test="interactionWordCloudQuantity != null">#{interactionWordCloudQuantity},</if>
            <if test="interactionDiscussQuantity != null">#{interactionDiscussQuantity},</if>
            <if test="interactionImageWaterfallQuantity != null">#{interactionImageWaterfallQuantity},</if>
            <if test="insideLinkQuantity != null">#{insideLinkQuantity},</if>
            <if test="studyTotalTime != null">#{studyTotalTime},</if>
            <if test="studyVideoTime != null">#{studyVideoTime},</if>
            <if test="studyNoteQuantity != null">#{studyNoteQuantity},</if>
            <if test="studyHighlightQuantity != null">#{studyHighlightQuantity},</if>
            <if test="studyDiscussQuantity != null">#{studyDiscussQuantity},</if>
            <if test="studyUserQuantity != null">#{studyUserQuantity},</if>
            <if test="studyQuestionQuantity != null">#{studyQuestionQuantity},</if>
            <if test="studyQuestionRate != null">#{studyQuestionRate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateDtbBookChapterData" parameterType="DtbBookChapterData">
        update dtb_book_chapter_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="chapterId != null">chapter_id = #{chapterId},</if>
            <if test="wordQuantity != null">word_quantity = #{wordQuantity},</if>
            <if test="imageQuanity != null">image_quanity = #{imageQuanity},</if>
            <if test="bubbleQuantity != null">bubble_quantity = #{bubbleQuantity},</if>
            <if test="outsideLinkQuantity != null">outside_link_quantity = #{outsideLinkQuantity},</if>
            <if test="formulaQuantity != null">formula_quantity = #{formulaQuantity},</if>
            <if test="threeDimenQuantity != null">three_dimen_quantity = #{threeDimenQuantity},</if>
            <if test="avrQuantity != null">avr_quantity = #{avrQuantity},</if>
            <if test="simulationQuantity != null">simulation_quantity = #{simulationQuantity},</if>
            <if test="questionQuantity != null">question_quantity = #{questionQuantity},</if>
            <if test="gameQuantity != null">game_quantity = #{gameQuantity},</if>
            <if test="footnoteQuantity != null">footnote_quantity = #{footnoteQuantity},</if>
            <if test="resouceQuantity != null">resouce_quantity = #{resouceQuantity},</if>
            <if test="audioQuantity != null">audio_quantity = #{audioQuantity},</if>
            <if test="videoQuantity != null">video_quantity = #{videoQuantity},</if>
            <if test="extQuantity != null">ext_quantity = #{extQuantity},</if>
            <if test="codeQuantity != null">code_quantity = #{codeQuantity},</if>
            <if test="audioTotalDuration != null">audio_total_duration = #{audioTotalDuration},</if>
            <if test="interactionVoteQuantity != null">interaction_vote_quantity = #{interactionVoteQuantity},</if>
            <if test="videoTotalDuration != null">video_total_duration = #{videoTotalDuration},</if>
            <if test="interactionWordCloudQuantity != null">interaction_word_cloud_quantity =
                #{interactionWordCloudQuantity},
            </if>
            <if test="interactionDiscussQuantity != null">interaction_discuss_quantity =
                #{interactionDiscussQuantity},
            </if>
            <if test="interactionImageWaterfallQuantity != null">interaction_image_waterfall_quantity =
                #{interactionImageWaterfallQuantity},
            </if>
            <if test="insideLinkQuantity != null">inside_link_quantity = #{insideLinkQuantity},</if>
            <if test="studyTotalTime != null">study_total_time = #{studyTotalTime},</if>
            <if test="studyVideoTime != null">study_video_time = #{studyVideoTime},</if>
            <if test="studyNoteQuantity != null">study_note_quantity = #{studyNoteQuantity},</if>
            <if test="studyHighlightQuantity != null">study_highlight_quantity = #{studyHighlightQuantity},</if>
            <if test="studyDiscussQuantity != null">study_discuss_quantity = #{studyDiscussQuantity},</if>
            <if test="studyUserQuantity != null">study_user_quantity = #{studyUserQuantity},</if>
            <if test="studyQuestionQuantity != null">study_question_quantity = #{studyQuestionQuantity},</if>
            <if test="studyQuestionRate != null">study_question_rate = #{studyQuestionRate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where data_id = #{dataId}
    </update>

    <delete id="deleteDtbBookChapterDataByDataId" parameterType="Long">
        delete
        from dtb_book_chapter_data
        where data_id = #{dataId}
    </delete>

    <delete id="deleteDtbBookChapterDataByDataIds" parameterType="String">
        delete from dtb_book_chapter_data where data_id in
        <foreach item="dataId" collection="array" open="(" separator="," close=")">
            #{dataId}
        </foreach>
    </delete>
</mapper>
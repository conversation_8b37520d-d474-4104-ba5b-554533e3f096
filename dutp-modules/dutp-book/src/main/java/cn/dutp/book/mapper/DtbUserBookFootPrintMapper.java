package cn.dutp.book.mapper;

import java.util.List;

import cn.dutp.book.domain.vo.DtbUserBookFootPrintVo;
import cn.dutp.system.api.domain.DutpUser;
import org.springframework.stereotype.Repository;
import cn.dutp.book.domain.DtbUserBookFootPrint;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
/**
 * DUTP-DTB_018足迹Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
@Repository
public interface DtbUserBookFootPrintMapper extends BaseMapper<DtbUserBookFootPrint>
{
    /**
     * 足迹查询
     *
     * @param dtbUserBookFootPrint
     * @return 结果
     */
    public List<DtbUserBookFootPrintVo> selectDtbUserBookFootPrintList(DtbUserBookFootPrint dtbUserBookFootPrint);
}

package cn.dutp.book.service;

import java.util.List;
import cn.dutp.book.domain.DtbUserBookNoteAttachment;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 笔记附件Service接口
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface IDtbUserBookNoteAttachmentService extends IService<DtbUserBookNoteAttachment>
{
    /**
     * 查询笔记附件
     *
     * @param attachmentId 笔记附件主键
     * @return 笔记附件
     */
    public DtbUserBookNoteAttachment selectDtbUserBookNoteAttachmentByAttachmentId(Long attachmentId);

    /**
     * 查询笔记附件列表
     *
     * @param dtbUserBookNoteAttachment 笔记附件
     * @return 笔记附件集合
     */
    public List<DtbUserBookNoteAttachment> selectDtbUserBookNoteAttachmentList(DtbUserBookNoteAttachment dtbUserBookNoteAttachment);

    /**
     * 新增笔记附件
     *
     * @param dtbUserBookNoteAttachment 笔记附件
     * @return 结果
     */
    public boolean insertDtbUserBookNoteAttachment(DtbUserBookNoteAttachment dtbUserBookNoteAttachment);

    /**
     * 修改笔记附件
     *
     * @param dtbUserBookNoteAttachment 笔记附件
     * @return 结果
     */
    public boolean updateDtbUserBookNoteAttachment(DtbUserBookNoteAttachment dtbUserBookNoteAttachment);

    /**
     * 批量删除笔记附件
     *
     * @param attachmentIds 需要删除的笔记附件主键集合
     * @return 结果
     */
    public boolean deleteDtbUserBookNoteAttachmentByAttachmentIds(List<Long> attachmentIds);

}

package cn.dutp.book.controller;


import cn.dutp.book.domain.DutpLanguage;
import cn.dutp.book.service.IDutpLanguageService;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 平台语种Controller
 *
 * <AUTHOR>
 * @date 2024-12-27
 */
@RestController
@RequestMapping("/language")
public class DutpLanguageController extends BaseController
{
    @Autowired
    private IDutpLanguageService dutpLanguageService;

    /**
     * 查询平台语种列表
     */
    @GetMapping("/listNotPage")
    public AjaxResult list(DutpLanguage dutpLanguage)
    {
        List<DutpLanguage> list = dutpLanguageService.selectDutpLanguageList(dutpLanguage);
        return success(list);
    }

}

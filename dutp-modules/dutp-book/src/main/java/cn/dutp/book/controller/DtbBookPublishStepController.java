package cn.dutp.book.controller;


import cn.dutp.book.domain.DtbBookPublishStep;
import cn.dutp.book.service.IDtbBookPublishStepService;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * DUTP-DTB-027数字教材初版步骤Controller
 *
 * <AUTHOR>
 * @date 2024-12-27
 */
@RestController
@RequestMapping("/step")
public class DtbBookPublishStepController extends BaseController {
    @Autowired
    private IDtbBookPublishStepService dtbBookPublishStepService;

    /**
     * 查询DUTP-DTB-027数字教材初版步骤列表 下拉框使用
     */
    @GetMapping("/listNotPage")
    public AjaxResult list(DtbBookPublishStep dtbBookPublishStep) {
        List<DtbBookPublishStep> list = dtbBookPublishStepService.selectDtbBookPublishStepList(dtbBookPublishStep);
        return success(list);
    }

    @GetMapping("/{stepId}")
    public AjaxResult getStepById(@PathVariable("stepId") Long stepId) {
        return success(dtbBookPublishStepService.getStepById(stepId));
    }

}

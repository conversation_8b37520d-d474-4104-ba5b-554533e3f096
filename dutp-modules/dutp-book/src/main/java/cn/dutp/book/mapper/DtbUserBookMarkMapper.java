package cn.dutp.book.mapper;

import java.util.List;

import cn.dutp.book.domain.vo.DtbUserBookMarkVO;
import org.springframework.stereotype.Repository;
import cn.dutp.book.domain.DtbUserBookMark;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
/**
 * DUTP-DTB_021书签Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Repository
public interface DtbUserBookMarkMapper extends BaseMapper<DtbUserBookMark>
{

    List<DtbUserBookMarkVO> selectReaderUserBookMarkList(DtbUserBookMark dtbUserBookMark);
}

package cn.dutp.book.service;

import cn.dutp.book.domain.DtbBookPublishProcessChapterBackup;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
/**
 * 发布流程章节备份Service接口
 *
 * <AUTHOR>
 * @date 2025-06-22
 */
public interface IDtbBookPublishProcessChapterBackupService extends IService<DtbBookPublishProcessChapterBackup>
{
    /**
     * 查询发布流程章节备份
     *
     * @param backupId 发布流程章节备份主键
     * @return 发布流程章节备份
     */
    public DtbBookPublishProcessChapterBackup selectDtbBookPublishProcessChapterBackupByBackupId(Long backupId);

    /**
     * 查询发布流程章节备份列表
     *
     * @param dtbBookPublishProcessChapterBackup 发布流程章节备份
     * @return 发布流程章节备份集合
     */
    public List<DtbBookPublishProcessChapterBackup> selectDtbBookPublishProcessChapterBackupList(DtbBookPublishProcessChapterBackup dtbBookPublishProcessChapterBackup);

    /**
     * 新增发布流程章节备份
     *
     * @param dtbBookPublishProcessChapterBackup 发布流程章节备份
     * @return 结果
     */
    public boolean insertDtbBookPublishProcessChapterBackup(DtbBookPublishProcessChapterBackup dtbBookPublishProcessChapterBackup);

    /**
     * 修改发布流程章节备份
     *
     * @param dtbBookPublishProcessChapterBackup 发布流程章节备份
     * @return 结果
     */
    public boolean updateDtbBookPublishProcessChapterBackup(DtbBookPublishProcessChapterBackup dtbBookPublishProcessChapterBackup);

    /**
     * 批量删除发布流程章节备份
     *
     * @param backupIds 需要删除的发布流程章节备份主键集合
     * @return 结果
     */
    public boolean deleteDtbBookPublishProcessChapterBackupByBackupIds(List<Long> backupIds);

}

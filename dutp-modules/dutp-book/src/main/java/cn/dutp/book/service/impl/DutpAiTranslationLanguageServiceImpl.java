package cn.dutp.book.service.impl;

import java.util.List;

import cn.dutp.book.domain.DutpAiTranslationLanguage;
import cn.dutp.book.mapper.DutpAiTranslationLanguageMapper;
import cn.dutp.book.service.IDutpAiTranslationLanguageService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 翻译语种Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Service
public class DutpAiTranslationLanguageServiceImpl extends ServiceImpl<DutpAiTranslationLanguageMapper, DutpAiTranslationLanguage> implements IDutpAiTranslationLanguageService {
    @Autowired
    private DutpAiTranslationLanguageMapper dutpAiTranslationLanguageMapper;

    /**
     * 查询翻译语种列表
     *
     * @param dutpAiTranslationLanguage 翻译语种
     * @return 翻译语种
     */
    @Override
    public List<DutpAiTranslationLanguage> selectDutpAiTranslationLanguageList(DutpAiTranslationLanguage dutpAiTranslationLanguage) {
        // 使用的ai模型
        Integer modelType = dutpAiTranslationLanguage.getModelType();

        LambdaQueryWrapper<DutpAiTranslationLanguage> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(DutpAiTranslationLanguage::getLanguage, DutpAiTranslationLanguage::getParameter);
        return this.list(lambdaQueryWrapper);
    }

}

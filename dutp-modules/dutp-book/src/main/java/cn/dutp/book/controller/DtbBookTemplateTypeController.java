package cn.dutp.book.controller;

import cn.dutp.book.domain.DtbBookTemplateType;
import cn.dutp.book.service.IDtbBookTemplateTypeService;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 教材模板分类Controller
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@RestController
@RequestMapping("/bookTemplateType")
public class DtbBookTemplateTypeController extends BaseController {
    @Autowired
    private IDtbBookTemplateTypeService dtbBookTemplateTypeService;

    /**
     * 查询教材模板分类列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbBookTemplateType dtbBookTemplateType) {
        startPage();
        List<DtbBookTemplateType> list = dtbBookTemplateTypeService.selectDtbBookTemplateTypeList(dtbBookTemplateType);
        return getDataTable(list);
    }


    /**
     * 查询教材模板分类列表 (无分页)
     */
    @GetMapping("/listOfNoPage")
    public AjaxResult listOfNoPage(DtbBookTemplateType dtbBookTemplateType) {
        List<DtbBookTemplateType> list = dtbBookTemplateTypeService.selectDtbBookTemplateTypeList(dtbBookTemplateType);
        return success(list);
    }

    /**
     * 获取教材模板分类详细信息
     */
    @RequiresPermissions("book:bookTemplateType:query")
    @GetMapping(value = "/{typeId}")
    public AjaxResult getInfo(@PathVariable("typeId") Long typeId) {
        return success(dtbBookTemplateTypeService.selectDtbBookTemplateTypeByTypeId(typeId));
    }

    /**
     * 新增教材模板分类
     */
    @RequiresPermissions("book:bookTemplateType:add")
    @Log(title = "新增教材模板分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookTemplateType dtbBookTemplateType) {
        return toAjax(dtbBookTemplateTypeService.insertDtbBookTemplateType(dtbBookTemplateType));
    }

    /**
     * 修改教材模板分类
     */
    @RequiresPermissions("book:bookTemplateType:edit")
    @Log(title = "修改教材模板分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookTemplateType dtbBookTemplateType) {
        return toAjax(dtbBookTemplateTypeService.updateDtbBookTemplateType(dtbBookTemplateType));
    }

    /**
     * 删除教材模板分类
     */
    @RequiresPermissions("book:bookTemplateType:remove")
    @Log(title = "删除教材模板分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{typeIds}")
    public AjaxResult remove(@PathVariable Long[] typeIds) {
        return toAjax(dtbBookTemplateTypeService.deleteDtbBookTemplateTypeByTypeIds(Arrays.asList(typeIds)));
    }
}

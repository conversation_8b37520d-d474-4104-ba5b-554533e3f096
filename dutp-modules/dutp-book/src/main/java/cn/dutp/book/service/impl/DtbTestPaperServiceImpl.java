package cn.dutp.book.service.impl;

import cn.dutp.book.domain.*;
import cn.dutp.book.mapper.*;
import cn.dutp.book.service.IDtbTestPaperService;
import cn.dutp.book.service.IDtbUserQuestionService;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.utils.StringUtils;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBook;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 试卷Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-08
 */
@Slf4j
@Service
public class DtbTestPaperServiceImpl extends ServiceImpl<DtbTestPaperMapper, DtbTestPaper> implements IDtbTestPaperService {
    @Autowired
    private DtbTestPaperMapper dtbTestPaperMapper;

    @Autowired
    private IDtbUserQuestionService dtbUserQuestionService;

    @Autowired
    private DtbTestPaperQuestionCollectionMapper dtbTestPaperQuestionCollectionMapper;

    @Autowired
    private DtbTestPaperQuestionMapper dtbTestPaperQuestionMapper;

    @Autowired
    private DtbBookTestPaperMapper dtbBookTestPaperMapper;

    @Autowired
    private DtbBookBookMapper dtbBookMapper;


    /**
     * 查询试卷
     *
     * @param paperId 试卷主键
     * @return 试卷
     */
    @Override
    public DtbTestPaper selectDtbTestPaperByPaperId(Long paperId) {
        // 获取原始数据
        DtbTestPaper paper = dtbTestPaperMapper.selectDtbTestPaperByPaperId(paperId);

        // 如果数据为空，直接返回
        if (paper == null) {
            return null;
        }

        try {
            // 创建一个新的对象，使用 BeanUtils 复制属性
            DtbTestPaper result = new DtbTestPaper();
            BeanUtils.copyProperties(result, paper);

            // 手动处理集合数据，避免懒加载问题
            if (paper.getDtbTestPaperQuestionCollectionList() != null) {
                List<DtbTestPaperQuestionCollection> collections = new ArrayList<>();
                for (DtbTestPaperQuestionCollection collection : paper.getDtbTestPaperQuestionCollectionList()) {
                    DtbTestPaperQuestionCollection newCollection = new DtbTestPaperQuestionCollection();
                    BeanUtils.copyProperties(newCollection, collection);
                    collections.add(newCollection);
                }
                result.setDtbTestPaperQuestionCollectionList(collections);
            }

            return result;
        } catch (Exception e) {
            // 处理异常
            log.error("查询试卷失败，试卷ID：{}，错误信息：{}", paperId, e.getMessage());
            throw new ServiceException("查询试卷失败，试卷ID：" + paperId + "，错误信息：" + e.getMessage());
        }
    }

    /**
     * 查询试卷列表
     *
     * @param dtbTestPaper 试卷
     * @return 试卷
     */
    @Override
    public List<DtbTestPaper> selectDtbTestPaperList(DtbTestPaper dtbTestPaper) {
        LambdaQueryWrapper<DtbTestPaper> lambdaQueryWrapper = new LambdaQueryWrapper<>();

        lambdaQueryWrapper.orderByAsc(DtbTestPaper::getCreateTime);

        lambdaQueryWrapper.eq(DtbTestPaper::getSysUserId, SecurityUtils.getUserId());

        if (ObjectUtil.isNotEmpty(dtbTestPaper.getPaperTitle())) {
            lambdaQueryWrapper.like(DtbTestPaper::getPaperTitle, dtbTestPaper.getPaperTitle());
        }
        if (ObjectUtil.isNotEmpty(dtbTestPaper.getQuestionQuantity())) {
            lambdaQueryWrapper.eq(DtbTestPaper::getQuestionQuantity, dtbTestPaper.getQuestionQuantity());
        }
        if (ObjectUtil.isNotEmpty(dtbTestPaper.getTotalScore())) {
            lambdaQueryWrapper.eq(DtbTestPaper::getTotalScore, dtbTestPaper.getTotalScore());
        }
        if (ObjectUtil.isNotEmpty(dtbTestPaper.getPaperType())) {
            lambdaQueryWrapper.eq(DtbTestPaper::getPaperType, dtbTestPaper.getPaperType());
        }
        lambdaQueryWrapper.orderByDesc(DtbTestPaper::getCreateBy);
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 计算试卷总分
     */
    private int calculateTotalScore(List<DtbTestPaperQuestionCollection> collections) {
        int totalScore = 0;
        if (collections != null && !collections.isEmpty()) {
            for (DtbTestPaperQuestionCollection collection : collections) {
                List<DtbTestPaperQuestion> questions = collection.getQuestionList();
                if (questions != null && !questions.isEmpty()) {
                    for (DtbTestPaperQuestion question : questions) {
                        totalScore += question.getQuestionScore() != null ? question.getQuestionScore() : 0;
                    }
                }
            }
        }
        return totalScore;
    }

    /**
     * 验证试卷是否可以编辑或删除
     *
     * @param paperId       试卷ID
     * @param operationType 操作类型："edit" 或 "delete"
     * @return 如果可以操作返回true，否则抛出异常
     */
    private boolean validatePaperOperation(Long paperId, String operationType) {
        // 查询试卷是否被教材引用
        LambdaQueryWrapper<DtbBookTestPaper> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DtbBookTestPaper::getPaperId, paperId);
        List<DtbBookTestPaper> bookPapers = dtbBookTestPaperMapper.selectList(wrapper);

        if (!bookPapers.isEmpty()) {
            // 检查关联的教材是否已出版
            for (DtbBookTestPaper bookPaper : bookPapers) {
                DtbBook book = dtbBookMapper.selectById(bookPaper.getBookId());
                if (book != null) {
                    // 如果是编辑操作，检查教材是否已出版
                    if ("edit".equals(operationType) && book.getPublishStatus() == 2) {
                        throw new RuntimeException("试卷已被已出版的教材使用，不能编辑");
                    }
                    // 如果是删除操作，不允许删除被教材引用的试卷
                    if ("delete".equals(operationType)) {
                        throw new RuntimeException("试卷已被教材引用，不能删除");
                    }
                }
            }
        }
        return true;
    }

    /**
     * 新增试卷
     *
     * @param dtbTestPaper 试卷
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertDtbTestPaper(DtbTestPaper dtbTestPaper) {

        // 计算总分
        int totalScore = calculateTotalScore(dtbTestPaper.getDtbTestPaperQuestionCollectionList());
        dtbTestPaper.setTotalScore(totalScore);

        dtbTestPaper.setSysUserId(SecurityUtils.getUserId());

        // 1. 插入试卷主表
        int rows = baseMapper.insert(dtbTestPaper);

        // 2. 处理题型集合和题目
        insertPaperDetails(dtbTestPaper);

        return rows > 0;
    }

    /**
     * 修改试卷
     *
     * @param dtbTestPaper 试卷
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDtbTestPaper(DtbTestPaper dtbTestPaper) {
        // 验证是否可以编辑
        validatePaperOperation(dtbTestPaper.getPaperId(), "edit");

        // 计算总分
        int totalScore = calculateTotalScore(dtbTestPaper.getDtbTestPaperQuestionCollectionList());
        dtbTestPaper.setTotalScore(totalScore);

        // 1. 更新试卷主表
        int rows = baseMapper.updateById(dtbTestPaper);

        // 2. 删除原有的题型集合和题目
        deleteExistingPaperDetails(dtbTestPaper.getPaperId());

        // 3. 重新插入题型集合和题目
        insertPaperDetails(dtbTestPaper);

        return rows > 0;
    }

    /**
     * 删除试卷相关的题型集合和题目
     */
    private void deleteExistingPaperDetails(Long paperId) {
        // 删除试卷相关的题目
        LambdaQueryWrapper<DtbTestPaperQuestion> questionWrapper = new LambdaQueryWrapper<>();
        questionWrapper.eq(DtbTestPaperQuestion::getPaperId, paperId);
        dtbTestPaperQuestionMapper.delete(questionWrapper);

        // 删除试卷相关的题型集合
        LambdaQueryWrapper<DtbTestPaperQuestionCollection> collectionWrapper = new LambdaQueryWrapper<>();
        collectionWrapper.eq(DtbTestPaperQuestionCollection::getPaperId, paperId);
        dtbTestPaperQuestionCollectionMapper.delete(collectionWrapper);
    }

    /**
     * 插入试卷的题型集合和题目
     */
    private void insertPaperDetails(DtbTestPaper dtbTestPaper) {
        List<DtbTestPaperQuestionCollection> collections = dtbTestPaper.getDtbTestPaperQuestionCollectionList();
        if (collections != null && !collections.isEmpty()) {
            for (DtbTestPaperQuestionCollection collection : collections) {
                // 设置试卷ID
                collection.setPaperId(dtbTestPaper.getPaperId());
                // 插入题型集合并获取插入后的ID
                dtbTestPaperQuestionCollectionMapper.insert(collection);
                Long collectionId = collection.getCollectionId();

                List<DtbTestPaperQuestion> questions = collection.getQuestionList();
                if (questions != null && !questions.isEmpty()) {
                    for (DtbTestPaperQuestion question : questions) {
                        // 设置试卷ID和题型集合ID
                        question.setPaperId(dtbTestPaper.getPaperId());
                        question.setCollectionId(collectionId);
                        // 插入题目
                        dtbTestPaperQuestionMapper.insert(question);
                    }
                }
            }
        }
    }

    /**
     * 批量删除试卷
     *
     * @param paperIds 需要删除的试卷主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbTestPaperByPaperIds(List<Long> paperIds) {

        try {
            return dtbTestPaperMapper.deleteBatchByIds(paperIds, SecurityUtils.getUserId()) > 0;
        } catch (Exception e) {
            log.error("批量删除试卷失败，试卷IDs：{}，错误信息：{}", paperIds, e.getMessage());
            return false;
        }
    }


    /**
     * 查询试卷的题目组和题目信息
     *
     * @param paperId 试卷ID
     * @return 试卷题目组和题目列表
     */
    @Override
    public List<DtbTestPaperQuestionCollection> selectQuestionCollectionsByPaperId(Long paperId) {
        if (paperId == null) {
            return new ArrayList<>();
        }

        try {
            // 1. 使用dtbTestPaperQuestionCollectionMapper查询题目组信息
            LambdaQueryWrapper<DtbTestPaperQuestionCollection> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DtbTestPaperQuestionCollection::getPaperId, paperId)
                    .orderByAsc(DtbTestPaperQuestionCollection::getSort);
            List<DtbTestPaperQuestionCollection> collections = dtbTestPaperQuestionCollectionMapper.selectList(queryWrapper);

            // 2. 查询每个题目组下的题目信息
            if (StringUtils.isNotEmpty(collections)) {
                for (DtbTestPaperQuestionCollection collection : collections) {
                    if (collection.getCollectionId() == null) {
                        continue;
                    }

                    // 使用dtbTestPaperQuestionMapper查询题目列表
                    LambdaQueryWrapper<DtbTestPaperQuestion> questionWrapper = new LambdaQueryWrapper<>();
                    questionWrapper.eq(DtbTestPaperQuestion::getCollectionId, collection.getCollectionId());
                    List<DtbTestPaperQuestion> questions = dtbTestPaperQuestionMapper.selectList(questionWrapper);

                    // 查询每个题目的具体内容
                    if (StringUtils.isNotEmpty(questions)) {
                        questions.removeIf(question -> {
                            if (question.getQuestionId() != null) {
                                DtbUserQuestion questionContent = dtbUserQuestionService.selectDtbUserQuestionByQuestionId(question.getQuestionId());
                                if (questionContent == null){
                                    return true;
                                }
                                if (!"0".equals(questionContent.getDelFlag())) {
                                    return true;
                                }
                                question.setQuestionContent(questionContent);
                            }
                            return false;
                        });
                    }
                    collection.setQuestionList(questions != null ? questions : new ArrayList<>());
                }
            }

            return collections != null ? collections : new ArrayList<>();

        } catch (Exception e) {
            log.error("查询试卷题目组和题目信息失败，试卷ID：{}，错误信息：{}", paperId, e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 将试卷移入回收站
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean moveToRecycleBin(List<Long> paperIds) {

        // 验证每个试卷是否可以删除
        for (Long paperId : paperIds) {
            validatePaperOperation(paperId, "delete");
        }

        if (StringUtils.isEmpty(paperIds)) {
            return false;
        }
        try {
            return dtbTestPaperMapper.moveToRecycleBin(paperIds, SecurityUtils.getUsername()) > 0;
        } catch (Exception e) {
            log.error("移入回收站失败，试卷IDs：{}，错误信息：{}", paperIds, e.getMessage());
            return false;
        }
    }

    /**
     * 从回收站恢复试卷
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean restoreFromRecycleBin(List<Long> paperIds) {
        if (StringUtils.isEmpty(paperIds)) {
            return false;
        }
        try {
            return dtbTestPaperMapper.restoreFromRecycleBin(paperIds, SecurityUtils.getUsername()) > 0;
        } catch (Exception e) {
            log.error("从回收站恢复失败，试卷IDs：{}，错误信息：{}", paperIds, e.getMessage());
            return false;
        }
    }

    @Override
    public List<DtbTestPaper> selectRecycleBinList(DtbTestPaper dtbTestPaper) {
        try {
            dtbTestPaper.setSysUserId(SecurityUtils.getUserId());
            return dtbTestPaperMapper.selectRecycleBinList(dtbTestPaper);
        } catch (Exception e) {
            log.error("查询回收站列表失败，错误信息：{}", e.getMessage());
            return Collections.emptyList();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DtbTestPaper copyTestPaper(Long paperId) {
        // 验证权限
        DtbTestPaper originalPaper = this.selectDtbTestPaperByPaperId(paperId);
        if (originalPaper == null) {
            throw new RuntimeException("试卷不存在或无权限访问");
        }

        // 2. 复制试卷基本信息
        DtbTestPaper newPaper = new DtbTestPaper();
        newPaper.setPaperTitle(originalPaper.getPaperTitle() + " - 副本");
        newPaper.setPaperType(originalPaper.getPaperType());
        newPaper.setQuestionQuantity(originalPaper.getQuestionQuantity());
        newPaper.setTotalScore(originalPaper.getTotalScore());
        newPaper.setSysUserId(originalPaper.getSysUserId());
        // 设置新的创建时间
        newPaper.setCreateTime(new Date());


        // 3. 保存新试卷
        this.save(newPaper);

        // 4. 获取并复制题型集合和题目
        List<DtbTestPaperQuestionCollection> collections =
                selectQuestionCollectionsByPaperId(paperId);

        if (collections != null && !collections.isEmpty()) {
            for (DtbTestPaperQuestionCollection collection : collections) {
                // 复制题型集合
                DtbTestPaperQuestionCollection newCollection = new DtbTestPaperQuestionCollection();
                newCollection.setPaperId(newPaper.getPaperId());
                newCollection.setSort(collection.getSort());
                newCollection.setQuestionType(collection.getQuestionType());
                dtbTestPaperQuestionCollectionMapper.insert(newCollection);

                // 复制题目
                List<DtbTestPaperQuestion> questions = collection.getQuestionList();
                if (questions != null && !questions.isEmpty()) {
                    for (DtbTestPaperQuestion question : questions) {
                        DtbTestPaperQuestion newQuestion = new DtbTestPaperQuestion();
                        newQuestion.setPaperId(newPaper.getPaperId());
                        newQuestion.setCollectionId(newCollection.getCollectionId());
                        newQuestion.setQuestionId(question.getQuestionId());
                        newQuestion.setQuestionScore(question.getQuestionScore());
                        dtbTestPaperQuestionMapper.insert(newQuestion);
                    }
                }
            }
        }

        return newPaper;
    }

    @Override
    public boolean checkBeforeEdit(Long paperId) {
        // 查询引用该试卷的所有教材
        int bookCount = dtbBookMapper.countPublishedBooksByPaperId(paperId);

        if (bookCount > 0) {
            return false;
        }
        return true;
    }
}

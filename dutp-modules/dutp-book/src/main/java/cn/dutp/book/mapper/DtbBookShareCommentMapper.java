package cn.dutp.book.mapper;

import cn.dutp.book.domain.DtbBookShareComment;
import cn.dutp.book.domain.form.ReaderCommonForm;
import cn.dutp.book.domain.vo.DtbBookShareCommentVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

import java.util.List;
/**
 * 教材分享点评Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Repository
public interface DtbBookShareCommentMapper extends BaseMapper<DtbBookShareComment>
{
    List<DtbBookShareCommentVo> selectBookShareCommentList(ReaderCommonForm readerForm);

    List<DtbBookShareComment> exportBookShare(DtbBookShareComment dtbBookShareComment);
}

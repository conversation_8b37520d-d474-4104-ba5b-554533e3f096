package cn.dutp.book.mapper;

import cn.dutp.book.domain.DtbBookChapter;
import cn.dutp.book.domain.vo.DtbBookChapterTreeVO;
import cn.dutp.book.domain.vo.DtbBookChapterVO;
import cn.dutp.domain.DtbBook;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 数字教材章节目录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-30
 */
@Repository
public interface DtbBookChapterMapper extends BaseMapper<DtbBookChapter> {

    @Select("select book_id from dtb_book_chapter where chapter_id = #{chapterId} and del_flag = '0'")
    Long queryBookIdByChapterId(Long chapterId);

    List<DtbBookChapterTreeVO> queryBookChapterTreeByBookId(Long bookId);

    List<DtbBookChapter> queryBookChapterListByBookId(Long bookId);

    List<DtbBookChapterTreeVO> adminQueryBookChapterTreeByBookId(Long bookId);

    List<DtbBookChapterTreeVO> adminQueryProcessChapterTreeByBookId(Long bookId);

    List<DtbBookChapter> listForRecycle(DtbBookChapter dtbBookChapter);

    @Update("update dtb_book_chapter set del_flag = '0' where chapter_id = #{chapterId}")
    int recycleChapter(DtbBookChapter dtbBookChapter);

    List<DtbBookChapter> listForSort(DtbBookChapter dtbBookChapter);

    List<DtbBookChapter> dtbBookChapterMapper(DtbBookChapter dtbBookChapter);
    List<DtbBookChapter> dtbBookChapterMapperLastVersion(DtbBookChapter dtbBookChapter);

    List<DtbBookChapter> listForFinalizedSubmit(DtbBook dtbBook);

    @Select("SELECT chapter_id,chapter_name,sort,free " +
            "FROM dtb_book_chapter " +
            "WHERE book_id = #{bookId} " +
            "AND version_id = #{versionId} " +
            "AND del_flag = 0 ORDER BY sort ")
    List<DtbBookChapterVO> getBookChapterByReader(@Param("bookId") Long bookId, @Param("versionId") Long versionId);

    @Select("SELECT chapter_id,chapter_name,sort,free " +
            "FROM dtb_book_chapter " +
            "WHERE book_id = #{bookId} " +
            "AND version_id = #{versionId} " +
            "ORDER BY sort ")
    List<DtbBookChapterVO> getBookChapterByReaderSimple(@Param("bookId") Long bookId, @Param("versionId") Long versionId);

    @Select("SELECT " +
            "   bc.chapter_id," +
            "   bc.chapter_name," +
            "   bc.sort," +
            "   bc.free " +
            "FROM dtb_book_chapter bc " +
            "   INNER JOIN (SELECT chapter_id FROM dtb_book_share_chapter WHERE share_id = #{shareId}) sc ON sc.chapter_id = bc.chapter_id " +
            "WHERE bc.book_id = #{bookId} " +
            "AND bc.version_id = #{versionId} " +
            "ORDER BY bc.sort ")
    List<DtbBookChapterVO> getBookShareChapterByReader(@Param("bookId") Long bookId, @Param("versionId") Long versionId,@Param("shareId") Long shareId);

    @Update("update dtb_book_chapter set del_flag = '2', del_user_id = #{delUserId}, update_time = #{updateTime} where chapter_id = #{chapterId}")
    int delChapter(DtbBookChapter dtbBookChapter);

    @Select("select IFNULL(sum(chapter_total_page),0) startPageNumber from dtb_book_chapter where del_flag = 0 " +
            "and book_id = #{bookId} and sort < (select sort from dtb_book_chapter where chapter_id = #{chapterId})")
    int getBookChapterStartPageNumber(@Param("chapterId") Long chapterId, @Param("bookId") Long bookId);

    List<DtbBookChapter> queryCopyBookChapterMapper(DtbBookChapter dtbBookChapter);

    Long queryChapterId(@Param("bookId") Long bookId, @Param("sort") Integer sort);

    DtbBookChapter chapterInfo(Long chapterId);

    Integer queryMaxSort(Long bookId);

    int updateChapterTemplate(DtbBookChapter chapter);

    DtbBookChapter queryBookByChapterId(Long chapterId);

    Long queryTemplateIdByBookId(Long bookId);

    @Select("select version_id from dtb_book_chapter where chapter_id = #{chapterId}")
    Long queryVersionIddByChapterId(Long chapterId);

    /**
     * 获取数字教材章节目录列表
     *
     * @param dtbBookChapter
     * @return
     */
    List<DtbBookChapter> queryBookChapterDataList(DtbBookChapter dtbBookChapter);

    @Select("select chapter_id from dtb_book_chapter where chapter_id = #{chapterId}")
    Long queryChapter(Long chapterId);

    List<DtbBookChapter> selectByBookAndVersion(@Param("allBook") List<DtbBook> allBook);

    @Select("select template_id from dtb_book_chapter where chapter_id = #{chapterId}")
    Long queryTemplateIdByChapterId(Long chapterId);

    List<DtbBookChapter> homepageChapterSearchByPage(
            @Param("userId") Long userId,
            @Param("chapterName") String chapterName,
            @Param("pageSize") int pageSize,
            @Param("offset") int offset);

    Long homepageChapterSearchPagetotal(@Param("chapterName") String chapterName);

    @Select("SELECT " +
                "chapter_id, " +
                "chapter_name, " +
                "chapter_object_id, " +
                "book_id, " +
                "sort, " +
                "chapter_total_page, " +
                "version_id, " +
                "free, " +
                "chapter_status, " +
                "back_apply, " +
                "frozen, " +
                "state, " +
                "complete_rate, " +
                "del_user_id, " +
                "template_id, " +
                "del_flag, " +
                "remark " +
            " from dtb_book_chapter where chapter_id = #{chapterId} ")
    DtbBookChapter queryBookChapterById(Long chapterId);

//    @Select("SELECT * from dtb_book_chapter where chapter_id = #{chapterId} ")
//    DtbBookChapter queryBookChapterById(Long chapterId,Long objectId,Long bookId,Integer free);

}

package cn.dutp.book.controller;

import cn.dutp.book.domain.DtbBookChapter;
import cn.dutp.book.domain.vo.DtbBookChapterTreeVO;
import cn.dutp.book.service.IDtbBookChapterService;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.Logical;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.hutool.core.util.ObjectUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;

/**
 * 数字教材章节目录Controller
 *
 * <AUTHOR>
 * @date 2024-11-30
 */
@RestController
@RequestMapping("/chapter")
public class DtbBookChapterController extends BaseController {
    @Autowired
    private IDtbBookChapterService dtbBookChapterService;

    /**
     * 查询数字教材章节目录列表
     */
    @GetMapping("/list")
    public AjaxResult list(DtbBookChapter dtbBookChapter) {
        List<DtbBookChapterTreeVO> list = dtbBookChapterService.selectDtbBookChapterList(dtbBookChapter);
        return success(list);
    }

    /**
     * 查询数字教材章节目录列表
     */
    @GetMapping("/processChapterList")
    public AjaxResult processChapterList(DtbBookChapter dtbBookChapter) {
        List<DtbBookChapterTreeVO> list = dtbBookChapterService.selectProcessChapterList(dtbBookChapter);
        return success(list);
    }

    /**
     * 查询数字教材章节信息
     */
    @GetMapping("/chapterInfo/{chapterId}")
    public AjaxResult chapterInfo(@PathVariable("chapterId") Long chapterId) {
        return success(dtbBookChapterService.chapterInfo(chapterId));
    }

    /**
     * 查询数字教材章节目录列表 为了下拉
     */
    @GetMapping("/listForSelect")
    public AjaxResult listForSelect(DtbBookChapter dtbBookChapter) {
        List<DtbBookChapter> list = dtbBookChapterService.listForSelect(dtbBookChapter);
        return success(list);
    }


    /**
     * 查询数字教材章节目录列表
     */
    @GetMapping("/listForSort")
    public AjaxResult listForSort(DtbBookChapter dtbBookChapter) {
        List<DtbBookChapter> list = dtbBookChapterService.listForSort(dtbBookChapter);
        return success(list);
    }


    /**
     * 查询数字教材章节回收站列表
     */
    @GetMapping("/listForRecycle")
    public AjaxResult listForRecycle(DtbBookChapter dtbBookChapter) {
        dtbBookChapter.setDelUserId(SecurityUtils.getUserId());
        List<DtbBookChapter> list = dtbBookChapterService.listForRecycle(dtbBookChapter);
        return success(list);
    }

    /**
     * 导出数字教材章节目录列表
     */
    @RequiresPermissions("book:book:export")
    @Log(title = "导出数字教材章节目录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@RequestBody DtbBookChapter dtbBookChapter) {
        // 导出
        dtbBookChapterService.export(dtbBookChapter);
    }

    /**
     * 获取数字教材章节目录详细信息
     */
    @GetMapping(value = "/{chapterId}")
    public AjaxResult getInfo(@PathVariable("chapterId") Long chapterId) {
        return success(dtbBookChapterService.selectDtbBookChapterByChapterId(chapterId));
    }

    /**
     * 获取数字教材章节目录
     */
    @GetMapping(value = "/chapterCatalogList/{chapterId}")
    public AjaxResult getChapterCatalog(@PathVariable("chapterId") Long chapterId) {
        return success(dtbBookChapterService.getChapterCatalog(chapterId));
    }

    /**
     * 获取数字教材章节列表
     */
    @GetMapping(value = "/queryChapterList/{bookId}")
    public AjaxResult queryChapterList(@PathVariable("bookId") Long bookId) {
        return success(dtbBookChapterService.queryChapterList(bookId));
    }

    /**
     * 获取数字教材章节列表
     */
    @GetMapping(value = "/queryChapterListLastVersion/{bookId}")
    public AjaxResult queryChapterListLastVersion(@PathVariable("bookId") Long bookId) {
        return success(dtbBookChapterService.queryChapterListLastVersion(bookId));
    }

    /**
     * 新增数字教材章节目录
     */
    @RequiresPermissions("book:book:addChapter")
    @Log(title = "新增数字教材章节目录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookChapter dtbBookChapter) {
        return toAjax(dtbBookChapterService.insertDtbBookChapter(dtbBookChapter));
    }

    /**
     * 恢复数字教材章节目录
     */
    @RequiresPermissions("book:book:recycle")
    @Log(title = "恢复数字教材章节目录", businessType = BusinessType.UPDATE)
    @PutMapping("/recycleChapter")
    public AjaxResult recycleChapter(@RequestBody DtbBookChapter dtbBookChapter) {
        return toAjax(dtbBookChapterService.recycleChapter(dtbBookChapter));
    }

    /**
     * 更新数字教材章节目录顺序
     */
    @RequiresPermissions("book:book:sort")
    @Log(title = "更新数字教材章节目录顺序", businessType = BusinessType.UPDATE)
    @PutMapping("/updateChapterSort")
    public AjaxResult updateChapterSort(@RequestBody List<DtbBookChapter> dtbBookChapterList) {
        return toAjax(dtbBookChapterService.updateChapterSort(dtbBookChapterList));
    }

    /**
     * 修改数字教材章节目录
     */
    @RequiresPermissions("book:book:editChapter")
    @Log(title = "修改数字教材章节目录", businessType = BusinessType.UPDATE)
    @PutMapping("/info")
    public AjaxResult editInfo(@RequestBody DtbBookChapter dtbBookChapter) {
        return toAjax(dtbBookChapterService.updateDtbBookChapter(dtbBookChapter));
    }

    /**
     * 修改数字教材章节目录
     */
    @RequiresPermissions(value = {
            "book:book:setting", "book:book:cancel", "book:book:submit"
    }, logical = Logical.OR)
    @Log(title = "修改数字教材章节目录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookChapter dtbBookChapter) {
        return toAjax(dtbBookChapterService.edit(dtbBookChapter));
    }

    /**
     * 删除数字教材章节目录
     */
    @RequiresPermissions("book:book:del")
    @Log(title = "删除数字教材章节目录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{chapterIds}")
    public AjaxResult remove(@PathVariable Long[] chapterIds) {
        return toAjax(dtbBookChapterService.deleteDtbBookChapterByChapterIds(Arrays.asList(chapterIds)));
    }

    /**
     * 查询数字教材章节目录列表
     */
    @GetMapping("/queryBookChapterList")
    public AjaxResult queryBookChapterList(DtbBookChapter dtbBookChapter) {
        List<DtbBookChapterTreeVO> list = dtbBookChapterService.queryBookChapterList(dtbBookChapter);
        return AjaxResult.success(list);
    }

    /**
     * 查询数字教材章节目录列表
     */
    @GetMapping("/queryBookChapterListByBookDetail")
    public AjaxResult queryBookChapterListByBookDetail(DtbBookChapter dtbBookChapter) {
        List<DtbBookChapter> list = dtbBookChapterService.queryBookChapterListByBookDetail(dtbBookChapter);
        return AjaxResult.success(list);
    }

    /**
     * 更新数字教材章节目录试读状态
     */
    @RequiresPermissions("book:purchaseCode:chapter")
    @Log(title = "更新数字教材章节目录试读状态", businessType = BusinessType.UPDATE)
    @PutMapping("/updateCharpterFree")
    public AjaxResult updateCharpterFree(@RequestBody List<DtbBookChapter> dtbBookChapterList) {
        return toAjax(dtbBookChapterService.updateCharpterFree(dtbBookChapterList));
    }


    /**
     * 更新教材模板
     */
    @Log(title = "更新教材模板", businessType = BusinessType.UPDATE)
    @PutMapping("/updateChapterTemplate")
    public AjaxResult updateChapterTemplate(@RequestBody DtbBookChapter chapter) {
        return toAjax(dtbBookChapterService.updateChapterTemplate(chapter));
    }

    /**
     * 导入数字教材章节内容
     */
    // @RequiresPermissions("book:book:importChapter")
    @Log(title = "导入数字教材章节内容", businessType = BusinessType.IMPORT)
    @PostMapping("/importChapter")
    public AjaxResult importChapter(MultipartFile file, Long chapterId) {
        if (ObjectUtil.isEmpty(file) || ObjectUtil.isEmpty(chapterId)) {
            throw new ServiceException("文件或者chapterId为空");
        }
        String fileName = file.getOriginalFilename();
        if (fileName.endsWith(".docx")) {
            return toAjax(dtbBookChapterService.importChapter(file, chapterId));
        } else if (fileName.endsWith(".pdf")) {
            return toAjax(dtbBookChapterService.importChapterPdf(file, chapterId));
        }else {
            throw new ServiceException("文件格式不正确，请上传.docx或者.pdf文件");
        }
    }

    /**
     * 导入数字教材章节内容(PDF)
     */
    // @RequiresPermissions("book:book:importChapter")
    @Log(title = "导入数字教材章节内容(PDF)", businessType = BusinessType.IMPORT)
    @PostMapping("/importChapterPdf")
    public AjaxResult importChapterPdf(MultipartFile file, Long chapterId) {
        return toAjax(dtbBookChapterService.importChapterPdf(file, chapterId));
    }
}

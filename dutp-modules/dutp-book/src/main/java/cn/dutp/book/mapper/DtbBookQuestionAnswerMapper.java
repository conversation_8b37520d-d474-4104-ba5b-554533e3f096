package cn.dutp.book.mapper;

import org.springframework.stereotype.Repository;
import cn.dutp.book.domain.DtbBookQuestionAnswer;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * 问题答案Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-07
 */
@Repository
public interface DtbBookQuestionAnswerMapper extends BaseMapper<DtbBookQuestionAnswer> {

    List<DtbBookQuestionAnswer> queryTestQuestionListByChapterId(Long chapterId);
}

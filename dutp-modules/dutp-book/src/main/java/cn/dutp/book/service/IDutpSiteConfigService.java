package cn.dutp.book.service;

import cn.dutp.domain.DutpSiteConfig;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 平台整体设置Service接口
 *
 * <AUTHOR>
 * @date 2024-12-10
 */
public interface IDutpSiteConfigService extends IService<DutpSiteConfig>
{
    /**
     * 查询平台整体设置
     *
     * @param configId 平台整体设置主键
     * @return 平台整体设置
     */
    public DutpSiteConfig selectDutpSiteConfigByConfigId(Long configId);

    /**
     * 查询平台整体设置列表
     *
     * @param dutpSiteConfig 平台整体设置
     * @return 平台整体设置集合
     */
    public List<DutpSiteConfig> selectDutpSiteConfigList(DutpSiteConfig dutpSiteConfig);

    /**
     * 新增平台整体设置
     *
     * @param dutpSiteConfig 平台整体设置
     * @return 结果
     */
    public boolean insertDutpSiteConfig(DutpSiteConfig dutpSiteConfig);

    /**
     * 修改平台整体设置
     *
     * @param dutpSiteConfig 平台整体设置
     * @return 结果
     */
    public boolean updateDutpSiteConfig(DutpSiteConfig dutpSiteConfig);

    /**
     * 批量删除平台整体设置
     *
     * @param configIds 需要删除的平台整体设置主键集合
     * @return 结果
     */
    public boolean deleteDutpSiteConfigByConfigIds(List<Long> configIds);

}

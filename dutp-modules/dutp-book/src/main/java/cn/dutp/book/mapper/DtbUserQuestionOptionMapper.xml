<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbUserQuestionOptionMapper">
    
    <resultMap type="DtbUserQuestionOption" id="DtbUserQuestionOptionResult">
        <result property="optionId"    column="option_id"    />
        <result property="optionContent"    column="option_content"    />
        <result property="questionId"    column="question_id"    />
        <result property="rightFlag"    column="right_flag"    />
        <result property="optionPosition"    column="option_position"    />
        <result property="sort"    column="sort"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectDtbUserQuestionOptionVo">
        select option_id, option_content, question_id, right_flag, option_position, sort, create_by, create_time, update_by, update_time, del_flag from dtb_book_question_option
    </sql>

    <select id="selectDtbUserQuestionOptionList" parameterType="DtbUserQuestionOption" resultMap="DtbUserQuestionOptionResult">
        <include refid="selectDtbUserQuestionOptionVo"/>
        <where>  
            <if test="optionContent != null  and optionContent != ''"> and option_content = #{optionContent}</if>
            <if test="questionId != null "> and question_id = #{questionId}</if>
            <if test="rightFlag != null "> and right_flag = #{rightFlag}</if>
            <if test="optionPosition != null "> and option_position = #{optionPosition}</if>
            <if test="sort != null "> and sort = #{sort}</if>
        </where>
    </select>
    
    <select id="selectDtbUserQuestionOptionByOptionId" parameterType="Long" resultMap="DtbUserQuestionOptionResult">
        <include refid="selectDtbUserQuestionOptionVo"/>
        where option_id = #{optionId}
    </select>

    <insert id="insertDtbUserQuestionOption" parameterType="DtbUserQuestionOption" useGeneratedKeys="true" keyProperty="optionId">
        insert into dtb_book_question_option
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="optionContent != null">option_content,</if>
            <if test="questionId != null">question_id,</if>
            <if test="rightFlag != null">right_flag,</if>
            <if test="optionPosition != null">option_position,</if>
            <if test="sort != null">sort,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="optionContent != null">#{optionContent},</if>
            <if test="questionId != null">#{questionId},</if>
            <if test="rightFlag != null">#{rightFlag},</if>
            <if test="optionPosition != null">#{optionPosition},</if>
            <if test="sort != null">#{sort},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateDtbUserQuestionOption" parameterType="DtbUserQuestionOption">
        update dtb_book_question_option
        <trim prefix="SET" suffixOverrides=",">
            <if test="optionContent != null">option_content = #{optionContent},</if>
            <if test="questionId != null">question_id = #{questionId},</if>
            <if test="rightFlag != null">right_flag = #{rightFlag},</if>
            <if test="optionPosition != null">option_position = #{optionPosition},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where option_id = #{optionId}
    </update>

    <delete id="deleteDtbUserQuestionOptionByOptionId" parameterType="Long">
        delete from dtb_book_question_option where option_id = #{optionId}
    </delete>

    <delete id="deleteDtbUserQuestionOptionByOptionIds" parameterType="String">
        delete from dtb_book_question_option where option_id in 
        <foreach item="optionId" collection="array" open="(" separator="," close=")">
            #{optionId}
        </foreach>
    </delete>
</mapper>
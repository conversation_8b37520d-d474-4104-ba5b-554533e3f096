<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.MoocPsychologyHealthUserResultMapper">

    <resultMap type="cn.dutp.book.domain.MoocPsychologyHealthScaleQuestion" id="MoocPsychologyHealthScaleResult">
        <result property="questionId" column="question_id"/>
        <result property="questionContent" column="question_content"/>
        <result property="chooseOptionId" column="chooseOptionId"/>
        <result property="sort" column="question_sort"/>
        <collection property="moocPsychologyHealthScaleQuestionOption" ofType="cn.dutp.book.domain.MoocPsychologyHealthScaleQuestionOption">
            <result property="optionId" column="option_id"/>
            <result property="optionContent" column="option_content"/>
            <result property="sort" column="option_sort"/>
        </collection>
    </resultMap>

    <resultMap type="cn.dutp.book.domain.MoocPsychologyHealthScaleFacet" id="MoocPsychologyHealthScaleFacetResult">
        <result property="facetId"    column="facet_id"    />
        <result property="scaleId"    column="scale_id"    />
        <result property="facetName"    column="facet_name"    />
        <result property="sort"    column="facet_sort"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <collection property="moocPsychologyHealthScaleQuestion" ofType="cn.dutp.book.domain.MoocPsychologyHealthScaleQuestion">
            <result property="questionId" column="question_id"/>
            <result property="questionContent" column="question_content"/>
            <result property="sort" column="question_sort"/>
            <result property="chooseOptionId" column="chooseOptionId"/>
            <collection property="moocPsychologyHealthScaleQuestionOption" ofType="cn.dutp.book.domain.MoocPsychologyHealthScaleQuestionOption">
                <result property="optionId" column="option_id"/>
                <result property="optionContent" column="option_content"/>
                <result property="sort" column="option_sort"/>
            </collection>
        </collection>
    </resultMap>

    <select id="getTestResultList" parameterType="cn.dutp.book.domain.MoocPsychologyHealthScale" resultType="cn.dutp.book.domain.MoocPsychologyHealthUserResult">
        select
            ur.user_id,
            ur.scale_id,
            ur.create_time,
            ur.result_id,
            ur.score,
            hc.chapter_id,
            b.book_id,
            b.book_name,
            bc.chapter_name,
            du.real_name,
            du.nick_name
        from
            mooc_psychology_health_user_result ur
        left join
            mooc_psychology_health_chapter hc on ur.scale_id = hc.scale_id and ur.health_chapter_id = hc.health_chapter_id and hc.del_flag = 0
        left join
            dutp_user du on ur.user_id = du.user_id and du.del_flag = 0
        left join
            dtb_book b on hc.book_id = b.book_id and b.del_flag = 0
        left join
            dtb_book_chapter bc on hc.chapter_id = bc.chapter_id and bc.del_flag = 0
        where
            ur.del_flag = 0 and ur.scale_id = #{scaleId}
            <if test="realName!= null and realName!= ''">
                and du.nick_name like concat('%', #{realName}, '%')
            </if>
        group by ur.user_id, ur.scale_id, ur.create_time, ur.result_id, hc.chapter_id, b.book_id, b.book_name, bc.chapter_name, du.real_name
    </select>

    <select id="getTestFacetResultDetail" parameterType="cn.dutp.book.domain.MoocPsychologyHealthUserResult" resultMap="MoocPsychologyHealthScaleFacetResult">
        select
            sf.facet_id,
            sf.facet_name,
            sf.sort as facet_sort,
            sq.question_id,
            sq.question_content,
            sq.sort as question_sort,
            so.option_id,
            so.option_content,
            so.sort as option_sort
        from
            mooc_psychology_health_scale_facet sf
        left join
            mooc_psychology_health_scale_question sq on sq.facet_id = sf.facet_id and sq.del_flag = 0
        left join
            mooc_psychology_health_scale_question_option so on so.question_id = sq.question_id and so.del_flag = 0
        left join
            mooc_psychology_health_user_result ur on ur.scale_id = sf.scale_id and ur.del_flag = 0
        where
            sf.del_flag = 0 and ur.del_flag = 0 and ur.result_id = #{resultId}
        order by sf.sort, sq.sort, so.sort
    </select>

    <select id="getTestResultDetail" parameterType="cn.dutp.book.domain.MoocPsychologyHealthUserResult" resultMap="MoocPsychologyHealthScaleResult">
        select
            ur.result_id,
            ur.user_id,
            ur.scale_id,
            ur.create_time,
            sq.question_id,
            sq.question_content,
            sq.sort as question_sort,
            so.option_id,
            so.option_content,
            so.sort as option_sort
        from
            mooc_psychology_health_scale_question sq
        left join
            mooc_psychology_health_scale_question_option so on so.question_id = sq.question_id and so.del_flag = 0
        left join
            mooc_psychology_health_user_result ur on ur.scale_id = sq.scale_id and ur.del_flag = 0
        where
            ur.del_flag = 0 and sq.del_flag = 0 and ur.result_id = #{resultId}
        order by sq.sort,so.sort
    </select>

</mapper>
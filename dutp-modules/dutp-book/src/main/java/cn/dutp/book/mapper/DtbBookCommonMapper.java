package cn.dutp.book.mapper;

import cn.dutp.domain.DutpAreaVo;
import cn.dutp.domain.DutpBookSchoolVo;
import cn.dutp.domain.DutpSubjectVo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * 通用Mapper接口 用于调用其他模块的sql
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Repository
public interface DtbBookCommonMapper {

    List<DutpBookSchoolVo> querySchoolByBookId(Long bookId);

    String querySchoolNameBySchoolId(Long schoolId);

    DutpSubjectVo querySubjectBySubjectId(Long subjectId);

    List<DutpAreaVo> queryAreaByBookId(Long bookId);

    int batchAddSchool(@Param("schoolList") List<DutpBookSchoolVo> schoolList, @Param("bookId") Long bookId);

    int batchAddArea(@Param("areaList") List<DutpAreaVo> areaList, @Param("bookId") Long bookId);

    @Delete("delete from dtb_book_school where book_id = #{bookId}")
    int delAllSchoolByBookId(Long bookId);

    @Delete("delete from dutp_book_area_relation where book_id = #{bookId}")
    int delAllAreaByBookId(Long bookId);

    @Select("select nick_name from sys_user where user_id = #{userId}")
    String queryNickNameByUserId(Long userId);

    List<Long> queryAdminUser();

    @Select("select dept_name from sys_dept where dept_id = #{deptId}")
    String queryDeptName(Long deptId);

    @Select("select school_id from dtb_book_school where book_id = #{bookId}")
    List<DutpBookSchoolVo> querySchoolIdListByBookId(Long bookId);

    @Select("select area_id from dutp_book_area_relation where book_id = #{bookId}")
    List<DutpAreaVo> queryAreaIdListByBookId(Long bookId);

    String queryRole(Long userId);

    List<Long> queryEduRoleUser(Long schoolId);
}

package cn.dutp.book.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.book.domain.DtbBookShareCommentAttachment;
import cn.dutp.book.service.IDtbBookShareCommentAttachmentService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 教材分享点评附件Controller
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@RestController
@RequestMapping("/shareAttachment")
public class DtbBookShareCommentAttachmentController extends BaseController
{
    @Autowired
    private IDtbBookShareCommentAttachmentService dtbBookShareCommentAttachmentService;

    /**
     * 查询教材分享点评附件列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbBookShareCommentAttachment dtbBookShareCommentAttachment)
    {
        startPage();
        List<DtbBookShareCommentAttachment> list = dtbBookShareCommentAttachmentService.selectDtbBookShareCommentAttachmentList(dtbBookShareCommentAttachment);
        return getDataTable(list);
    }

    /**
     * 导出教材分享点评附件列表
     */
    @RequiresPermissions("book:shareAttachment:export")
    @Log(title = "导出教材分享点评附件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbBookShareCommentAttachment dtbBookShareCommentAttachment)
    {
        List<DtbBookShareCommentAttachment> list = dtbBookShareCommentAttachmentService.selectDtbBookShareCommentAttachmentList(dtbBookShareCommentAttachment);
        ExcelUtil<DtbBookShareCommentAttachment> util = new ExcelUtil<DtbBookShareCommentAttachment>(DtbBookShareCommentAttachment.class);
        util.exportExcel(response, list, "教材分享点评附件数据");
    }

    /**
     * 获取教材分享点评附件详细信息
     */
    @RequiresPermissions("book:shareAttachment:query")
    @GetMapping(value = "/{attachmentId}")
    public AjaxResult getInfo(@PathVariable("attachmentId") Long attachmentId)
    {
        return success(dtbBookShareCommentAttachmentService.selectDtbBookShareCommentAttachmentByAttachmentId(attachmentId));
    }

    /**
     * 新增教材分享点评附件
     */
    @RequiresPermissions("book:shareAttachment:add")
    @Log(title = "新增教材分享点评附件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookShareCommentAttachment dtbBookShareCommentAttachment)
    {
        return toAjax(dtbBookShareCommentAttachmentService.insertDtbBookShareCommentAttachment(dtbBookShareCommentAttachment));
    }

    /**
     * 修改教材分享点评附件
     */
    @RequiresPermissions("book:shareAttachment:edit")
    @Log(title = "修改教材分享点评附件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookShareCommentAttachment dtbBookShareCommentAttachment)
    {
        return toAjax(dtbBookShareCommentAttachmentService.updateDtbBookShareCommentAttachment(dtbBookShareCommentAttachment));
    }

    /**
     * 删除教材分享点评附件
     */
    @RequiresPermissions("book:shareAttachment:remove")
    @Log(title = "删除教材分享点评附件", businessType = BusinessType.DELETE)
    @DeleteMapping("/{attachmentIds}")
    public AjaxResult remove(@PathVariable Long[] attachmentIds)
    {
        return toAjax(dtbBookShareCommentAttachmentService.deleteDtbBookShareCommentAttachmentByAttachmentIds(Arrays.asList(attachmentIds)));
    }
}

package cn.dutp.book.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.dutp.book.domain.DtbBookChapterData;
import cn.dutp.common.core.annotation.Excel;
import lombok.Data;


@Data
public class DtbBookApplicationUserDataVo {

    /**
     * 学习人数
     */
    @Excel(name = "学习学校数")
    private Long studySchoolQuantity;



    /**
     * 学习人数
     */
    @Excel(name = "学习教师数")
    private Long studyTeacherQuantity;


    /**
     * 学习人数
     */
    @Excel(name = "学习学生数")
    private Long studyStudentQuantity;


    private String type;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long BookId;
}

package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * AR/VR资源指派记录对象 dtb_ar_vr_resource_record
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Data
@TableName("dtb_ar_vr_resource_record")
public class DtbArVrResourceRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** 指派记录ID */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** AR/VR资源ID */
        @Excel(name = "AR/VR资源ID")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long resourceId;


    /** 教材资源ID */
        @Excel(name = "教材资源ID")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookResourceId;

    /** 教材ID */
        @Excel(name = "教材ID")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /** 文件夹ID */
        @Excel(name = "文件夹ID")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long folderId;

    /** 撤回标记（0未撤回 1已撤回） */
        @Excel(name = "撤回标记", readConverterExp = "0=未撤回,1=已撤回")
    private Integer isRecall;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** AR/VR资源名称 */
    @Excel(name = "AR/VR资源名称")
    private String resourceName;

    /** 教材名称 */
    @Excel(name = "教材名称")
    private String bookName;



@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("resourceId", getResourceId())
            .append("bookId", getBookId())
            .append("folderId", getFolderId())
            .append("isRecall", getIsRecall())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .append("resourceName", getResourceName())
            .append("bookName", getBookName())
        .toString();
        }
        }

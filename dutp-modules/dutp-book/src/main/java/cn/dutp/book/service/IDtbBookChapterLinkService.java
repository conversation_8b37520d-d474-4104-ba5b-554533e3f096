package cn.dutp.book.service;

import cn.dutp.book.domain.DtbBookChapterLink;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 外部链接扫描Service接口
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
public interface IDtbBookChapterLinkService extends IService<DtbBookChapterLink> {
    /**
     * 查询外部链接扫描
     *
     * @param linkId 外部链接扫描主键
     * @return 外部链接扫描
     */
    public DtbBookChapterLink selectDtbBookChapterLinkByLinkId(Long linkId);

    /**
     * 查询外部链接扫描列表
     *
     * @param dtbBookChapterLink 外部链接扫描
     * @return 外部链接扫描集合
     */
    public List<DtbBookChapterLink> selectDtbBookChapterLinkList(DtbBookChapterLink dtbBookChapterLink);

    /**
     * 新增外部链接扫描
     *
     * @param dtbBookChapterLink 外部链接扫描
     * @return 结果
     */
    public boolean insertDtbBookChapterLink(DtbBookChapterLink dtbBookChapterLink);

    /**
     * 修改外部链接扫描
     *
     * @param dtbBookChapterLink 外部链接扫描
     * @return 结果
     */
    public boolean updateDtbBookChapterLink(DtbBookChapterLink dtbBookChapterLink);

    /**
     * 批量删除外部链接扫描
     *
     * @param linkIds 需要删除的外部链接扫描主键集合
     * @return 结果
     */
    public boolean deleteDtbBookChapterLinkByLinkIds(List<Long> linkIds);

}

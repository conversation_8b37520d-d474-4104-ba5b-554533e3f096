package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.serialize.LongListToStringSerializer;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@IndexName("esdtbbook")
public class EsDtbBook extends BaseEntity {

    @IndexId
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 教材名称
     */
    private String bookName;

    /**
     * 作者的标题
     */
    private String authorLabel;

    /**
     * 作者的值，作者字段分成两部分。
     */
    private String authorValue;

    /**
     * 封面图片地址
     */
    private String cover;

    /**
     * ISBN序列号
     */
    private String isbn;

    /**
     * 教材编号
     */
    private String bookNo;

    /**
     * 出版日期
     */
    private String publishDate;

    /**
     * 教材中图分类,关联dtb_book_type
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookType;

    /**
     * 定价
     */
    private BigDecimal priceCounter;

    /**
     * 售价
     */
    private BigDecimal priceSale;

    /**
     * 教育层次Id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long topSubjectId;

    /**
     * 专业Id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long secondSubjectId;

    /**
     * 学科分类Id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long thirdSubjectId;

    /**
     * 学科专业Id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long forthSubjectId;

    private String shelfTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long readQuantity;

    private Integer masterFlag;

    /**
     * 分类IDS
     */
    @JsonSerialize(using = LongListToStringSerializer.class)
    private List<Long> subjectIds;

    private Integer pageNum;

    private Integer pageSize;

    /**
     * 上架状态1已上架2未上架3召回4即将上架
     */
    @Excel(name = "上架状态1已上架2未上架3召回4即将上架")
    private Integer shelfState;
}

package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;

import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 数字教材习题对象 dtb_user_question
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@TableName("dtb_user_question")
public class DtbUserQuestion extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long questionId;

    /**
     * 小题类型1单选 2多选 3填空 4排序 5连线 6简答 7判断 8编程-填空 9编程-简答
     */
    @Excel(name = "小题类型1单选 2多选 3填空 4排序 5连线 6简答 7判断 8编程-填空 9编程-简答")
    private Integer questionType;

    /**
     * 题干
     */
    @Excel(name = "题干")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private String questionContent;

    /**
     * 正确答案连线题JSON[{source:1,target:2}]
     */
    @Excel(name = "正确答案连线题JSON[{source:1,target:2}]")
    private String rightAnswer;

    /**
     * 解析
     */
    @Excel(name = "解析")
    private String analysis;

    /**
     * 教材ID
     */
    @Excel(name = "用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 是否乱序1不可乱序2可乱序
     */
    @Excel(name = "是否乱序1不可乱序2可乱序")
    private Integer disorder;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Integer sort;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 所属目录
     */
    @Excel(name = "所属目录")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long folderId;

    @TableField(exist = false)
    private String folderName;

    /**
     * 题目选项列表
     */
    @TableField(exist = false)
    private List<DtbUserQuestionOption> options;


    /**
     * 题干
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private String codeContent;

    /**题目的创建来源 默认0*/
    private Integer createSource ;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableField(exist = false)
    private Long bookQuestionId;
    /**
     * 章节ID
     */
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    /**
     * 教材ID
     */
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 用户答案
     */
    @TableField(exist = false)
    private DtbBookQuestionAnswer answerInfo;

    /**
     * 题目的描述
     */
    private String questionRemark;



    /**
     * 100表示完全正确0表示完全错误
     */
    @TableField(exist = false)
    @Excel(name = "100表示完全正确0表示完全错误")
    private Integer score;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("questionId", getQuestionId())
                .append("questionType", getQuestionType())
                .append("questionContent", getQuestionContent())
                .append("rightAnswer", getRightAnswer())
                .append("analysis", getAnalysis())
                .append("disorder", getDisorder())
                .append("sort", getSort())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("delFlag", getDelFlag())
                .append("folderId", getFolderId())
                .toString();
    }
}

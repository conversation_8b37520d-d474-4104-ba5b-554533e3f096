package cn.dutp.book.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class ChapterStatVO {
    @JsonProperty("_id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    private Integer count;

    private String chapterName;

}

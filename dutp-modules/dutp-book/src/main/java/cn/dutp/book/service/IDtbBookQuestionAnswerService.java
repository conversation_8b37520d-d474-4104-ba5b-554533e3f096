package cn.dutp.book.service;

import java.util.List;
import cn.dutp.book.domain.DtbBookQuestionAnswer;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 问题答案Service接口
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
public interface IDtbBookQuestionAnswerService extends IService<DtbBookQuestionAnswer>
{
    /**
     * 查询问题答案
     *
     * @param answerId 问题答案主键
     * @return 问题答案
     */
    public DtbBookQuestionAnswer selectDtbBookQuestionAnswerByAnswerId(Long answerId);

    /**
     * 查询问题答案列表
     *
     * @param dtbBookQuestionAnswer 问题答案
     * @return 问题答案集合
     */
    public List<DtbBookQuestionAnswer> selectDtbBookQuestionAnswerList(DtbBookQuestionAnswer dtbBookQuestionAnswer);

    /**
     * 新增问题答案
     *
     * @param dtbBookQuestionAnswer 问题答案
     * @return 结果
     */
    public boolean insertDtbBookQuestionAnswer(DtbBookQuestionAnswer dtbBookQuestionAnswer);

    /**
     * 修改问题答案
     *
     * @param dtbBookQuestionAnswer 问题答案
     * @return 结果
     */
    public boolean updateDtbBookQuestionAnswer(DtbBookQuestionAnswer dtbBookQuestionAnswer);

    /**
     * 批量删除问题答案
     *
     * @param answerIds 需要删除的问题答案主键集合
     * @return 结果
     */
    public boolean deleteDtbBookQuestionAnswerByAnswerIds(List<Long> answerIds);

}

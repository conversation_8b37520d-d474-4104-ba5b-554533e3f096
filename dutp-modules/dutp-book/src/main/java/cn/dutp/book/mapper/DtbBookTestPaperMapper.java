package cn.dutp.book.mapper;

import java.util.List;

import cn.dutp.book.domain.vo.DtbBookTestPaperDetailVO;
import cn.dutp.book.domain.vo.DtbBookTestPaperVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import cn.dutp.book.domain.DtbBookTestPaper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
/**
 * 教材跟试卷关系Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-17
 */
@Repository
public interface DtbBookTestPaperMapper extends BaseMapper<DtbBookTestPaper>
{

    @Select("select  " +
            "bqa.answer_id, " +
            "bqa.score, " +
            "bqa.answer_content, " +
            "duq.question_id, " +
            "duq.question_content, " +
            "duq.analysis, " +
            "duq.right_answer, " +
            "duq.code_content, " +
            "case when bqa.score = 100 then tpq.question_score else 0 end question_score " +
            "from dtb_book_test_paper_answer_detail pad  " +
            "inner join dtb_book_question_answer bqa on pad.question_answer_id = bqa.answer_id " +
            "inner join dtb_user_question duq on duq.question_id = bqa.user_question_id " +
            "left join dtb_test_paper_question tpq on tpq.question_id = duq.question_id and tpq.paper_id = #{paperId} " +
            "where paper_answer_id = #{paperAnswerId} ")
    List<DtbBookTestPaperDetailVO> getInfoByPaperId(@Param("paperId") Long paperId, @Param("paperAnswerId") Long paperAnswerId);

    List<DtbBookTestPaper> getPaperList(@Param("userId") Long userId,@Param("param") DtbBookTestPaper dtbBookTestPaper);

}

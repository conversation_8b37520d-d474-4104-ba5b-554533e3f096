package cn.dutp.book.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.book.mapper.DtbBookPublishProcessChapterMapper;
import cn.dutp.book.domain.DtbBookPublishProcessChapter;
import cn.dutp.book.service.IDtbBookPublishProcessChapterService;

/**
 * 流程章节关系表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Service
public class DtbBookPublishProcessChapterServiceImpl extends ServiceImpl<DtbBookPublishProcessChapterMapper, DtbBookPublishProcessChapter> implements IDtbBookPublishProcessChapterService
{
    @Autowired
    private DtbBookPublishProcessChapterMapper dtbBookPublishProcessChapterMapper;

    /**
     * 查询流程章节关系表
     *
     * @param processChapterId 流程章节关系表主键
     * @return 流程章节关系表
     */
    @Override
    public DtbBookPublishProcessChapter selectDtbBookPublishProcessChapterByProcessChapterId(Long processChapterId)
    {
        return this.getById(processChapterId);
    }

    /**
     * 查询流程章节关系表列表
     *
     * @param dtbBookPublishProcessChapter 流程章节关系表
     * @return 流程章节关系表
     */
    @Override
    public List<DtbBookPublishProcessChapter> selectDtbBookPublishProcessChapterList(DtbBookPublishProcessChapter dtbBookPublishProcessChapter)
    {
        LambdaQueryWrapper<DtbBookPublishProcessChapter> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbBookPublishProcessChapter.getChapterId())) {
                lambdaQueryWrapper.eq(DtbBookPublishProcessChapter::getChapterId
                ,dtbBookPublishProcessChapter.getChapterId());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增流程章节关系表
     *
     * @param dtbBookPublishProcessChapter 流程章节关系表
     * @return 结果
     */
    @Override
    public boolean insertDtbBookPublishProcessChapter(DtbBookPublishProcessChapter dtbBookPublishProcessChapter)
    {
        return this.save(dtbBookPublishProcessChapter);
    }

    /**
     * 修改流程章节关系表
     *
     * @param dtbBookPublishProcessChapter 流程章节关系表
     * @return 结果
     */
    @Override
    public boolean updateDtbBookPublishProcessChapter(DtbBookPublishProcessChapter dtbBookPublishProcessChapter)
    {
        return this.updateById(dtbBookPublishProcessChapter);
    }

    /**
     * 批量删除流程章节关系表
     *
     * @param processChapterIds 需要删除的流程章节关系表主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookPublishProcessChapterByProcessChapterIds(List<Long> processChapterIds)
    {
        return this.removeByIds(processChapterIds);
    }

}

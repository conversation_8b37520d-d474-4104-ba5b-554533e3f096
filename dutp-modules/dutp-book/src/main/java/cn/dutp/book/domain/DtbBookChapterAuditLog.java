package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 章节目录审核记录对象 dtb_book_chapter_audit_log
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Data
@TableName("dtb_book_chapter_audit_log")
public class DtbBookChapterAuditLog extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long logId;

    /**
     * 教材ID
     */
    @Excel(name = "教材ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 章节ID
     */
    @Excel(name = "章节ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    /**
     * 审核人ID，sys_user内userId
     */
    @Excel(name = "审核人ID，sys_user内userId")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long auditorId;

    /**
     * 状态 1已提交 2已通过 3已驳回 4已取消
     */
    @Excel(name = "状态 1已提交 2已通过 3已驳回 4已取消")
    private Integer chapterStatus;

    /**
     * 1章节提交2章节撤销申请
     */
    @Excel(name = "1章节提交2章节撤销申请")
    private Integer auditType;

    /**
     * 版本ID
     */
    @Excel(name = "版本ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long versionId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 发起人ID
     */
    @Excel(name = "发起人ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long promoterUserId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 撤销理由
     */
    private String revokedReason;

    /**
     * 审批时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /**
     * 章节名称
     */
    @TableField(exist = false)
    private String chapterName;

    /**
     * 教材名称
     */
    @TableField(exist = false)
    private String bookName;

    /**
     * 1公开教材2校本教材
     */
    @TableField(exist = false)
    private Integer bookOrganize;

    /**
     * ISBN序列号
     */
    @TableField(exist = false)
    private String isbn;

    /**
     * ISSN序列号
     */
    @TableField(exist = false)
    private String issn;

    /**
     * 教材编号
     */
    @TableField(exist = false)
    private String bookNo;

    /**
     * 发起人
     */
    @TableField(exist = false)
    private String nickName;

    /**
     * 审核人
     */
    @TableField(exist = false)
    private String auditNickName;
}

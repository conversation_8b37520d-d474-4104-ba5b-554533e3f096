package cn.dutp.book.service;

import cn.dutp.domain.DutpAiHistory;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
/**
 * ai请求记录Service接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
public interface IDutpAiHistoryService extends IService<DutpAiHistory>
{
    /**
     * 查询ai请求记录
     *
     * @param promptHistoryId ai请求记录主键
     * @return ai请求记录
     */
    public DutpAiHistory selectDutpAiHistoryByPromptHistoryId(Long promptHistoryId);

    /**
     * 查询ai请求记录列表
     *
     * @param dutpAiHistory ai请求记录
     * @return ai请求记录集合
     */
    public List<DutpAiHistory> selectDutpAiHistoryList(DutpAiHistory dutpAiHistory);

    /**
     * 新增ai请求记录
     *
     * @param dutpAiHistory ai请求记录
     * @return 结果
     */
    public boolean insertDutpAiHistory(DutpAiHistory dutpAiHistory);

    /**
     * 修改ai请求记录
     *
     * @param dutpAiHistory ai请求记录
     * @return 结果
     */
    public boolean updateDutpAiHistory(DutpAiHistory dutpAiHistory);

    /**
     * 批量删除ai请求记录
     *
     * @param promptHistoryIds 需要删除的ai请求记录主键集合
     * @return 结果
     */
    public boolean deleteDutpAiHistoryByPromptHistoryIds(List<Long> promptHistoryIds);

}

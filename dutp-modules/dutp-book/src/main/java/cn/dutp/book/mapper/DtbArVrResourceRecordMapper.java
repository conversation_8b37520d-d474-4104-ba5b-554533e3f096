package cn.dutp.book.mapper;

import org.springframework.stereotype.Repository;
import cn.dutp.book.domain.DtbArVrResourceRecord;
import cn.dutp.book.domain.vo.DtbArVrResourceRecordVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * AR/VR资源指派记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Repository
public interface DtbArVrResourceRecordMapper extends BaseMapper<DtbArVrResourceRecord>
{
    /**
     * 查询AR/VR资源指派记录VO列表
     *
     * @param dtbArVrResourceRecord AR/VR资源指派记录
     * @return AR/VR资源指派记录VO集合
     */
    List<DtbArVrResourceRecordVO> selectDtbArVrResourceRecordVOList(DtbArVrResourceRecord dtbArVrResourceRecord);
}

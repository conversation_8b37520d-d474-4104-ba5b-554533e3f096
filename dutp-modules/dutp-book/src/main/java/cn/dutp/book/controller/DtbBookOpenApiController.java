package cn.dutp.book.controller;

import cn.dutp.api.common.constant.DutpConstant;
import cn.dutp.book.domain.*;
import cn.dutp.book.service.*;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBook;
import cn.dutp.domain.DtbBookVo;
import cn.hutool.core.util.ObjectUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * DUTP-DTB_002数字教材Controller
 *
 * <AUTHOR>
 * @date 2024-11-14
 */
@RestController
@RequestMapping("/openApi")
public class DtbBookOpenApiController extends BaseController
{
    @Autowired
    private IDtbBookService dtbBookService;
    @Autowired
    private IDutpSiteConfigService dutpSiteConfigService;
    @Autowired
    private IDtbBookTypeService dtbBookTypeService;
    @Autowired
    private IDtbUserBookService dtbUserBookService;
    @Autowired
    private IDtbBookAttributeService dtbBookAttributeService;
    @Autowired
    private IDtbBookChapterService dtbBookChapterService;
    /**
     * 查询DUTP-DTB_002数字教材列表
     */
    @GetMapping("/cmsSearch")
    public AjaxResult list(DtbBook dtbBook)
    {
        List<DtbBookVo> list = dtbBookService.selectCmsDtbBookList(dtbBook);
        return AjaxResult.success(list);
    }

    /**
     * 查询DUTP-DTB_002数字教材列表
     */
    @GetMapping("/getSiteConfig/{configId}")
    public AjaxResult getSiteConfigById(@PathVariable("configId") long configId)
    {
        return AjaxResult.success(dutpSiteConfigService.selectDutpSiteConfigByConfigId(configId));
    }
    /**
     * 学生教师端多维交叉查询
     */
    @GetMapping("/miSearchEducation")
    public TableDataInfo miSearchEducation(DtbBook dtbBook)
    {
        startPage();
        List<DtbBook> list = dtbBookService.miSearchEducation(dtbBook);
        return getDataTable(list);
    }

    /**
     * 查询中图分类列表
     */
    @GetMapping("/listBookTypeEducation")
    public AjaxResult listBookTypeEducation(DtbBookType dtbBookType) {
        List<DtbBookType> list = dtbBookTypeService.selectDtbBookTypeList(dtbBookType);
        return success(list);
    }

    /**
     * 学生教师端教材查询
     */
    @GetMapping(value = "/getDtbBookInfo")
    public AjaxResult getDtbBookInfo(DtbUserBook book) {
        DtbBook dtbBook = dtbBookService.selectDtbBookByBookId(book.getBookId());
        if(ObjectUtil.isNotNull(dtbBook) && ObjectUtil.isNotNull(dtbBook.getDeputyBookList())){
            List<DtbBook> children = new ArrayList<>();
            dtbBook.getDeputyBookList().forEach(childrenBook -> {
                if(DutpConstant.NUM_ONE.equals(childrenBook.getShelfState())){
                    children.add(childrenBook);
                }
            });
            dtbBook.setDeputyBookList(children);
        }

        DtbUserBook dtbUserBookParm = new DtbUserBook();
        dtbUserBookParm.setBookId(book.getBookId());
        dtbUserBookParm.setUserId(ObjectUtil.isNotNull(SecurityUtils.getLoginUser())?SecurityUtils.getLoginUser().getUserid():0);
        List<DtbUserBook> dtbUserBook = dtbUserBookService.selectDtbUserBookList(dtbUserBookParm);
        if(!dtbUserBook.isEmpty()){
            dtbBook.setAddWay(dtbUserBook.get(0).getAddWay());
            Date expireDate = dtbUserBook.get(0).getExpireDate();
            LocalDate expireLocalDate = expireDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate currentDate = LocalDate.now();
            boolean isExpired = !expireLocalDate.isAfter(currentDate);
            dtbBook.setExpireDate(isExpired);
        }
        return success(dtbBook);
    }

    /**
     * 学生教师端获取推荐教材
     */
    @PostMapping(value = "/getRecommendedTextbooks")
    public AjaxResult getRecommendedTextbooks(@RequestBody DtbBookAttribute dtbBookAttribute)
    {
        return success(dtbBookAttributeService.getRecommendedTextbooks(dtbBookAttribute));
    }
    /**
     * 查询数字教材章节目录列表
     */
    @GetMapping("/queryBookChapterListByBookDetail")
    public AjaxResult queryBookChapterListByBookDetail(DtbBookChapter dtbBookChapter) {
        List<DtbBookChapter> list = dtbBookChapterService.queryBookChapterListByBookDetail(dtbBookChapter);
        return AjaxResult.success(list);
    }

    /**
     * 首页章节查询
     */
    @GetMapping("/homepageChapterSearch")
    public TableDataInfo homepageChapterSearch(DtbBook dtbBook) {
        return dtbBookChapterService.homepageChapterSearch(dtbBook);
    }
}

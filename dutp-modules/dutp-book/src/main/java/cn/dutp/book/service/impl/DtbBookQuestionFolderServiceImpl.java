package cn.dutp.book.service.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;

import cn.dutp.book.domain.vo.BookQuestionFoloderVo;
import cn.dutp.book.mapper.DtbBookBookMapper;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.book.mapper.DtbBookQuestionFolderMapper;
import cn.dutp.book.domain.DtbBookQuestionFolder;
import cn.dutp.book.service.IDtbBookQuestionFolderService;
import cn.dutp.book.service.IDtbUserQuestionFolderService;
import cn.dutp.domain.DtbBook;
import cn.dutp.book.domain.DtbUserQuestionFolder;
import cn.dutp.book.domain.vo.QuestionFolderVO;



/**
 * 题库目录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class DtbBookQuestionFolderServiceImpl extends ServiceImpl<DtbBookQuestionFolderMapper, DtbBookQuestionFolder> implements IDtbBookQuestionFolderService
{
    @Autowired
    private DtbBookQuestionFolderMapper dtbBookQuestionFolderMapper;

    @Autowired
    private IDtbUserQuestionFolderService userQuestionFolderService;

    @Autowired
    private DtbBookBookMapper dtbBookBookMapper;

    @Autowired
    private DtbBookBookMapper dtbBookMapper;

    /**
     * 查询题库目录
     *
     * @param folderId 题库目录主键
     * @return 题库目录
     */
    @Override
    public DtbBookQuestionFolder selectDtbBookQuestionFolderByFolderId(Long folderId)
    {
        return this.getById(folderId);
    }

    /**
     * 查询题库目录列表
     *
     * @param dtbBookQuestionFolder 题库目录
     * @return 题库目录
     */
    @Override
    public List<DtbBookQuestionFolder> selectDtbBookQuestionFolderList(DtbBookQuestionFolder dtbBookQuestionFolder)
    {
        LambdaQueryWrapper<DtbBookQuestionFolder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbBookQuestionFolder.getFolderName())) {
                lambdaQueryWrapper.like(DtbBookQuestionFolder::getFolderName
                ,dtbBookQuestionFolder.getFolderName());
            }
                if(ObjectUtil.isNotEmpty(dtbBookQuestionFolder.getBookId())) {
                lambdaQueryWrapper.eq(DtbBookQuestionFolder::getBookId
                ,dtbBookQuestionFolder.getBookId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookQuestionFolder.getParentId())) {
                lambdaQueryWrapper.eq(DtbBookQuestionFolder::getParentId
                ,dtbBookQuestionFolder.getParentId());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增题库目录
     *
     * @param dtbBookQuestionFolder 题库目录
     * @return 结果
     */
    @Override
    public boolean insertDtbBookQuestionFolder(DtbBookQuestionFolder dtbBookQuestionFolder)
    {
        return this.save(dtbBookQuestionFolder);
    }

    /**
     * 修改题库目录
     *
     * @param dtbBookQuestionFolder 题库目录
     * @return 结果
     */
    @Override
    public boolean updateDtbBookQuestionFolder(DtbBookQuestionFolder dtbBookQuestionFolder)
    {
        return this.updateById(dtbBookQuestionFolder);
    }

    /**
     * 批量删除题库目录
     *
     * @param folderIds 需要删除的题库目录主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookQuestionFolderByFolderIds(List<Long> folderIds)
    {
        return this.removeByIds(folderIds);
    }

    /**
     * 查询用户题库目录及相关用户题目信息
     *
     * @param userId 用户ID
     * @return 用户题库目录列表
     */
    @Override
    public List<BookQuestionFoloderVo> getPersonalQuestionLibrary(Long userId) {
        if (ObjectUtil.isEmpty(userId)) {
            return null;
        }

        // 获取用户的题库目录
        DtbUserQuestionFolder queryParam = new DtbUserQuestionFolder();
        queryParam.setUserId(userId);
        List<DtbUserQuestionFolder> userFolders = userQuestionFolderService.selectDtbUserQuestionFolderList(queryParam);

        // 获取用户可访问的教材列表
        DtbBook dtbBook = new DtbBook();
        List<DtbBook> accessibleBooks = dtbBookMapper.selectDtbBookListWithGroup(dtbBook, userId);
        
        // 创建结果集
        List<BookQuestionFoloderVo> result = new ArrayList<>();
        
        // 处理个人题库
        BookQuestionFoloderVo personalLibrary = new BookQuestionFoloderVo();
        personalLibrary.setFolderType("user");
        personalLibrary.setBookName("个人题库");
        List<QuestionFolderVO> personalFolders = new ArrayList<>();
        if (!userFolders.isEmpty()) {
            for (DtbUserQuestionFolder userFolder : userFolders) {
                QuestionFolderVO vo = new QuestionFolderVO();
                BeanUtils.copyProperties(userFolder, vo);
                vo.setType("user");
                personalFolders.add(vo);
            }
        }
        personalLibrary.setFolders(personalFolders);
        result.add(personalLibrary);

        // 只有在有可访问教材时才处理教材题库
        if (!accessibleBooks.isEmpty()) {
            List<Long> accessibleBookIds = accessibleBooks.stream()
                    .map(DtbBook::getBookId)
                    .collect(Collectors.toList());

            LambdaQueryWrapper<DtbBookQuestionFolder> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(DtbBookQuestionFolder::getBookId, accessibleBookIds);
            List<DtbBookQuestionFolder> bookFolders = this.list(wrapper);

            // 按bookId分组处理教材题库
            if (!bookFolders.isEmpty()) {
                Map<Long, List<DtbBookQuestionFolder>> bookFolderMap = bookFolders.stream()
                    .collect(Collectors.groupingBy(DtbBookQuestionFolder::getBookId));

                // 处理每本教材的题库
                for (Map.Entry<Long, List<DtbBookQuestionFolder>> entry : bookFolderMap.entrySet()) {
                    BookQuestionFoloderVo bookLibrary = new BookQuestionFoloderVo();
                    bookLibrary.setFolderType("book");
                    bookLibrary.setBookId(entry.getKey());
                    bookLibrary.setBookName(dtbBookBookMapper.queryBookByBookId(entry.getKey()).getBookName());
                    
                    List<QuestionFolderVO> bookFolderVOs = new ArrayList<>();
                    for (DtbBookQuestionFolder bookFolder : entry.getValue()) {
                        QuestionFolderVO vo = new QuestionFolderVO();
                        BeanUtils.copyProperties(bookFolder, vo);
                        vo.setType("book"); // 修改为字符串类型 "book"
                        vo.setUserId(userId);
                        bookFolderVOs.add(vo);
                    }
                    bookLibrary.setFolders(bookFolderVOs);
                    result.add(bookLibrary);
                }
            }
        }
        
        return result;
    }
}

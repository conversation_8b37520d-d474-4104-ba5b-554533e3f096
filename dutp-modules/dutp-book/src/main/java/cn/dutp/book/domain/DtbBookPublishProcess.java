package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】对象 dtb_book_publish_process
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@Data
@TableName("dtb_book_publish_process")
public class DtbBookPublishProcess extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long processId;

    /**
     * 教材ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Excel(name = "教材ID")
    private Long bookId;

    /**
     * 出版步骤ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Excel(name = "出版步骤ID")
    private Long stepId;

    /**
     * 0不需处理1未处理2通过3驳回
     */
    @Excel(name = "0不需处理1未处理2通过3驳回")
    private Integer state;

    /**
     * 版本ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Excel(name = "版本ID")
    private Long versionId;

    /**
     * 驳回理由
     */
    @Excel(name = "驳回理由")
    private String reason;

    /**
     * 0不是补录1补录
     */
    @Excel(name = "0不是补录1补录")
    private Integer additionFlag;

    /**
     * 流程操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "流程操作时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date processDate;

    /**
     * 上次流程ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Excel(name = "上次流程ID")
    private Long prevProcessId;

    /**
     * 实际处理人ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Excel(name = "实际处理人ID")
    private Long dealUserId;

    /**
     * 发起人ID
     */
    @Excel(name = "发起人ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long promoterUserId;

    /**
     * 录排登记号
     */
    @Excel(name = "录排登记号")
    @TableField(exist = false)
    private String recordNo;

    /**
     * 教材编号
     */
    @TableField(exist = false)
    private String bookNo;

    /**
     * 教材名称
     */
    @TableField(exist = false)
    private String bookName;

    @TableField(exist = false)
    private String isbn;

    @TableField(exist = false)
    private String issn;

    /**
     * 1其他2主教材3副教材
     */
    @TableField(exist = false)
    private Integer masterFlag;

    /**
     * 版本号第一个版本默认1.0.0
     */
    @TableField(exist = false)
    private String versionNo;

    /**
     * 当前节点
     */
    @TableField(exist = false)
    private String stepName;

    @TableField(exist = false)
    private Integer bookOrganize;

    /**
     * 下一节点审核人部门id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableField(exist = false)
    private Long auditDeptId;

    /**
     * 下一节点审核人id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableField(exist = false)
    private Long auditUserId;

    /**
     * 流程审核人id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableField(exist = false)
    private Long userId;

    /**
     * 主教材id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableField(exist = false)
    private Long masterBookId;

    /**
     * 流程审核人部门
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableField(exist = false)
    private Long deptId;

    /**
     * 实际审核人
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableField(exist = false)
    private String handleUserId;

    /**
     * 实际审核人名称
     */
    @TableField(exist = false)
    private String handleUserName;

    /**
     * 实际审核人部门名称
     */
    @TableField(exist = false)
    private String deptName;

    @TableField(exist = false)
    private String dealUserName;

    /**
     * 下一审核节点
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableField(exist = false)
    private Long nextStep;

    @TableField(exist = false)
    private Integer schoolFlag;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableField(exist = false)
    private Long rootStepId;

    @TableField(exist = false)
    private String rootStepName;

    @TableField(exist = false)
    private DtbBookPublishProcess nextProcess;

    @TableField(exist = false)
    private List<DtbBookPublishProcess> children;

    /**
     * 流程审核人部门
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableField(exist = false)
    private Long processDeptId;

}

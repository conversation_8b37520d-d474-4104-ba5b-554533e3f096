package cn.dutp.book.domain.vo;

import cn.dutp.book.domain.DtbBookChapterResource;
import cn.dutp.book.domain.DtbBookResource;
import cn.dutp.book.domain.DtbBookTestPaper;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
@Data
public class DtbBookResourceTreeVO
{
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long chapterId;

        private String chapterName;

        private List<DtbBookChapterResource> bookResourcesList;

}

package cn.dutp.book.controller;

import cn.dutp.book.service.IDtbBookPurchaseCodeService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.domain.DtbBookPurchaseCode;
import cn.dutp.domain.DtbBookPurchaseCodeExcel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 发行管理Controller
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@RestController
@RequestMapping("/purchaseCode")
public class DtbBookPurchaseCodeController extends BaseController {
    @Autowired
    private IDtbBookPurchaseCodeService dtbBookPurchaseCodeService;

    /**
     * 查询发行管理列表
     */
    @GetMapping("/purchaseCodeList")
    public TableDataInfo list(DtbBookPurchaseCode dtbBookPurchaseCode) {
        startPage();
        List<DtbBookPurchaseCode> list = dtbBookPurchaseCodeService.selectDtbBookPurchaseCodeList(dtbBookPurchaseCode);
        return getDataTable(list);
    }

    /**
     * 批量生成购书码
     */
    @RequiresPermissions("book:purchaseCode:batchPurchaseCode")
    @Log(title = "批量生成购书码", businessType = BusinessType.INSERT)
    @PostMapping("/batchPurchaseCode")
    public AjaxResult batchPurchaseCode(@RequestBody List<DtbBookPurchaseCode> dtbBookPurchaseCodeList) {
        return success(dtbBookPurchaseCodeService.batchPurchaseCode(dtbBookPurchaseCodeList));
    }

    /**
     * 批量导出购书码
     */
    @RequiresPermissions("book:purchaseCode:batchExportPurchaseCode")
    @Log(title = "批量导出购书码", businessType = BusinessType.EXPORT)
    @PostMapping("/batchExportPurchaseCode")
    public void batchExportPurchaseCode(@RequestBody List<DtbBookPurchaseCode> dtbBookPurchaseCodeList,HttpServletResponse response) {
        List<DtbBookPurchaseCodeExcel> list = dtbBookPurchaseCodeService.exprortCodeValid(dtbBookPurchaseCodeList);
        ExcelUtil<DtbBookPurchaseCodeExcel> util = new ExcelUtil<DtbBookPurchaseCodeExcel>(DtbBookPurchaseCodeExcel.class);
        util.exportExcel(response, list,"临时码"+ "("+dtbBookPurchaseCodeList.get(0).getBookName()+")");
    }


    /**
     * 查看购书码页面的购书码列表
     */
    @GetMapping("/codeList")
    public TableDataInfo codelList(DtbBookPurchaseCode dtbBookPurchaseCode) {
        startPage();
        List<DtbBookPurchaseCode> list = dtbBookPurchaseCodeService.selectCodelList(dtbBookPurchaseCode);
        return getDataTable(list);
    }

    /**
     * 解绑购书码
     */
    @RequiresPermissions("book:purchaseCode:codeUnbind")
    @Log(title = "解绑购书码", businessType = BusinessType.UPDATE)
    @PostMapping("/codeUnbind")
    public AjaxResult codeUnbind(@RequestBody DtbBookPurchaseCode dtbBookPurchaseCode)
    {
        return toAjax(dtbBookPurchaseCodeService.codeUnbind(dtbBookPurchaseCode));
    }

    /**
     * 批量解绑购书码
     */
    @RequiresPermissions("book:purchaseCode:codeBacthUnbind")
    @Log(title = "批量解绑购书码", businessType = BusinessType.UPDATE)
    @PostMapping("/codeBacthUnbind")
    public AjaxResult codeBacthUnbind(@RequestBody List<DtbBookPurchaseCode> dtbBookPurchaseCodeList)
    {
        return toAjax(dtbBookPurchaseCodeService.codeBacthUnbind(dtbBookPurchaseCodeList));
    }

    /**
     * 修改期限
     */
    @RequiresPermissions("book:purchaseCode:updateTimeLimit")
    @Log(title = "修改期限", businessType = BusinessType.UPDATE)
    @PutMapping("/updateTimeLimit")
    public AjaxResult updateTimeLimit(@RequestBody List<DtbBookPurchaseCode> dtbBookPurchaseCodeList) {
        return toAjax(dtbBookPurchaseCodeService.updateTimeLimit(dtbBookPurchaseCodeList));
    }

    /**
     * 更新数字教材章节目录试读状态
     */
    @RequiresPermissions("book:purchaseCode:deleteBacthPurchaseCode")
    @Log(title = "批量删除购书码", businessType = BusinessType.UPDATE)
    @PutMapping("/deleteBacthPurchaseCode")
    public AjaxResult deleteBacthPurchaseCode(@RequestBody List<DtbBookPurchaseCode> dtbBookPurchaseCodeList) {
        return toAjax(dtbBookPurchaseCodeService.deleteBacthPurchaseCode(dtbBookPurchaseCodeList));
    }
}

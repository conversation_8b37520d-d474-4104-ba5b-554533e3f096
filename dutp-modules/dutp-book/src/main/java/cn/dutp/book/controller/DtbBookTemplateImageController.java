package cn.dutp.book.controller;

import cn.dutp.book.domain.DtbBookTemplateImage;
import cn.dutp.book.service.IDtbBookTemplateImageService;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 教材模板图片Controller
 *
 * <AUTHOR>
 * @date 2025-04-25
 */
@RestController
@RequestMapping("/bookTemplateImage")
public class DtbBookTemplateImageController extends BaseController {
    @Autowired
    private IDtbBookTemplateImageService dtbBookTemplateImageService;

    /**
     * 查询教材模板图片列表
     */
    @GetMapping("/list")
    public AjaxResult list(DtbBookTemplateImage dtbBookTemplateImage) {
        List<DtbBookTemplateImage> list = dtbBookTemplateImageService.selectDtbBookTemplateImageList(dtbBookTemplateImage);
        return success(list);
    }

    /**
     * 查询教材模板图片列表
     */
    @GetMapping("/listForAuthor")
    public AjaxResult listForAuthor(DtbBookTemplateImage dtbBookTemplateImage) {
        dtbBookTemplateImage.setUserId(SecurityUtils.getUserId());
        List<DtbBookTemplateImage> list = dtbBookTemplateImageService.listForAuthor(dtbBookTemplateImage);
        return success(list);
    }


    /**
     * 新增教材模板图片
     */
    @Log(title = "新增教材模板图片", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookTemplateImage dtbBookTemplateImage) {
        return toAjax(dtbBookTemplateImageService.insertDtbBookTemplateImage(dtbBookTemplateImage));
    }

    /**
     * 修改教材模板图片
     */
    @Log(title = "修改教材模板图片", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookTemplateImage dtbBookTemplateImage) {
        return toAjax(dtbBookTemplateImageService.updateDtbBookTemplateImage(dtbBookTemplateImage));
    }

    /**
     * 删除教材模板图片
     */
    @Log(title = "删除教材模板图片", businessType = BusinessType.DELETE)
    @DeleteMapping("/{imageIds}")
    public AjaxResult remove(@PathVariable Long[] imageIds) {
        return toAjax(dtbBookTemplateImageService.deleteDtbBookTemplateImageByImageIds(Arrays.asList(imageIds)));
    }
}

package cn.dutp.book.service;

import java.util.List;
import cn.dutp.book.domain.DtbUserQuestionFolder;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 题库目录Service接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IDtbUserQuestionFolderService extends IService<DtbUserQuestionFolder>
{
    /**
     * 查询题库目录
     *
     * @param folderId 题库目录主键
     * @return 题库目录
     */
    public DtbUserQuestionFolder selectDtbBookQuestionFolderByFolderId(Long folderId);

    /**
     * 查询题库目录列表
     *
     * @param dtbUserQuestionFolder 题库目录
     * @return 题库目录集合
     */
    public List<DtbUserQuestionFolder> selectDtbBookQuestionFolderList(DtbUserQuestionFolder dtbUserQuestionFolder);

    /**
     * 新增题库目录
     *
     * @param dtbUserQuestionFolder 题库目录
     * @return 结果
     */
    public boolean insertDtbBookQuestionFolder(DtbUserQuestionFolder dtbUserQuestionFolder);

    /**
     * 修改题库目录
     *
     * @param dtbUserQuestionFolder 题库目录
     * @return 结果
     */
    public boolean updateDtbBookQuestionFolder(DtbUserQuestionFolder dtbUserQuestionFolder);

    /**
     * 批量删除题库目录
     *
     * @param folderIds 需要删除的题库目录主键集合
     * @return 结果
     */
    public boolean deleteDtbBookQuestionFolderByFolderIds(List<Long> folderIds);


    List<DtbUserQuestionFolder> selectDtbUserQuestionFolderList(DtbUserQuestionFolder queryParam);
}

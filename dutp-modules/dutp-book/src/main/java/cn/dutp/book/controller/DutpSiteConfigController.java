package cn.dutp.book.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import cn.dutp.domain.DutpSiteConfig;
import cn.dutp.book.service.IDutpSiteConfigService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 复制权限Controller
 *
 * <AUTHOR>
 * @date 2024-12-10
 */
@RestController
@RequestMapping("/config")
public class DutpSiteConfigController extends BaseController
{
    @Autowired
    private IDutpSiteConfigService dutpSiteConfigService;

/**
 * 查询复制权限列表
 */
@GetMapping("/list")
    public TableDataInfo list(DutpSiteConfig dutpSiteConfig)
    {
        startPage();
        List<DutpSiteConfig> list = dutpSiteConfigService.selectDutpSiteConfigList(dutpSiteConfig);
        return getDataTable(list);
    }

    /**
     * 导出复制权限列表
     */
    @RequiresPermissions("book:config:export")
    @Log(title = "导出复制权限", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DutpSiteConfig dutpSiteConfig)
    {
        List<DutpSiteConfig> list = dutpSiteConfigService.selectDutpSiteConfigList(dutpSiteConfig);
        ExcelUtil<DutpSiteConfig> util = new ExcelUtil<DutpSiteConfig>(DutpSiteConfig.class);
        util.exportExcel(response, list, "复制权限数据");
    }

    /**
     * 获取复制权限详细信息
     */
    @RequiresPermissions("book:config:query")
    @GetMapping(value = "/{configId}")
    public AjaxResult getInfo(@PathVariable("configId") Long configId)
    {
        return success(dutpSiteConfigService.selectDutpSiteConfigByConfigId(configId));
    }

    /**
     * 新增复制权限
     */
    @RequiresPermissions("book:config:add")
    @Log(title = "添加复制权限", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DutpSiteConfig dutpSiteConfig)
    {
        return toAjax(dutpSiteConfigService.insertDutpSiteConfig(dutpSiteConfig));
    }

    /**
     * 修改复制权限
     */
    @RequiresPermissions("book:config:edit")
    @Log(title = "修改复制权限", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DutpSiteConfig dutpSiteConfig)
    {
        return toAjax(dutpSiteConfigService.updateDutpSiteConfig(dutpSiteConfig));
    }

    /**
     * 删除复制权限
     */
    @RequiresPermissions("book:config:remove")
    @Log(title = "删除复制权限", businessType = BusinessType.DELETE)
    @DeleteMapping("/{configIds}")
    public AjaxResult remove(@PathVariable Long[] configIds)
    {
        return toAjax(dutpSiteConfigService.deleteDutpSiteConfigByConfigIds(Arrays.asList(configIds)));
    }
}

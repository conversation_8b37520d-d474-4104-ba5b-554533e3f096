package cn.dutp.book.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.book.domain.DtbBookTableTemplate;
import cn.dutp.book.service.IDtbBookTableTemplateService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 数字教材表格模板Controller
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@RestController
@RequestMapping("/tableTemplate")
public class DtbBookTableTemplateController extends BaseController {
    @Autowired
    private IDtbBookTableTemplateService dtbBookTableTemplateService;

    /**
     * 查询数字教材表格模板列表
     */
    @GetMapping("/list")
    public AjaxResult list(DtbBookTableTemplate dtbBookTableTemplate) {
        List<DtbBookTableTemplate> list = dtbBookTableTemplateService.selectDtbBookTableTemplateList(dtbBookTableTemplate);
        return success(list);
    }
}

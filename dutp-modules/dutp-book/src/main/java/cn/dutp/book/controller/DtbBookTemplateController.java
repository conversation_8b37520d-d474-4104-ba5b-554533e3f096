package cn.dutp.book.controller;

import cn.dutp.book.domain.DtbBookTemplate;
import cn.dutp.book.service.IDtbBookTemplateService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * DUTP-DTB-029教材模板Controller
 *
 * <AUTHOR>
 * @date 2025-02-17
 */
@RestController
@RequestMapping("/template")
public class DtbBookTemplateController extends BaseController {
    @Autowired
    private IDtbBookTemplateService dtbBookTemplateService;

    /**
     * 查询DUTP-DTB-029教材模板列表
     */
    @GetMapping("/list")
    public AjaxResult list(DtbBookTemplate dtbBookTemplate) {
        List<DtbBookTemplate> list = dtbBookTemplateService.selectDtbBookTemplateList(dtbBookTemplate);
        return success(list);
    }

    /**
     * 查询DUTP-DTB-029教材模板列表
     */
    @GetMapping("/listForAdmin")
    public TableDataInfo listForAdmin(DtbBookTemplate dtbBookTemplate) {
        startPage();
        List<DtbBookTemplate> list = dtbBookTemplateService.selectDtbBookTemplateList(dtbBookTemplate);
        return getDataTable(list);
    }

    /**
     * 导出DUTP-DTB-029教材模板列表
     */
    @RequiresPermissions("book:template:export")
    @Log(title = "导出DUTP-DTB-029教材模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbBookTemplate dtbBookTemplate) {
        List<DtbBookTemplate> list = dtbBookTemplateService.selectDtbBookTemplateList(dtbBookTemplate);
        ExcelUtil<DtbBookTemplate> util = new ExcelUtil<DtbBookTemplate>(DtbBookTemplate.class);
        util.exportExcel(response, list, "DUTP-DTB-029教材模板数据");
    }

    /**
     * 获取DUTP-DTB-029教材模板详细信息
     */
//    @RequiresPermissions("book:template:query")
    @GetMapping(value = "/{templateId}")
    public AjaxResult getInfo(@PathVariable("templateId") Long templateId) {
        return success(dtbBookTemplateService.selectDtbBookTemplateByTemplateId(templateId));
    }

    /**
     * 新增DUTP-DTB-029教材模板
     */
    @RequiresPermissions("book:template:add")
    @Log(title = "新增DUTP-DTB-029教材模板", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookTemplate dtbBookTemplate) {
        return toAjax(dtbBookTemplateService.insertDtbBookTemplate(dtbBookTemplate));
    }

    /**
     * 修改DUTP-DTB-029教材模板
     */
    @RequiresPermissions("book:template:edit")
    @Log(title = "修改DUTP-DTB-029教材模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookTemplate dtbBookTemplate) {
        return toAjax(dtbBookTemplateService.updateDtbBookTemplate(dtbBookTemplate));
    }

    /**
     * 修改DUTP-DTB-029教材模板
     */
    @RequiresPermissions("book:template:isDefault")
    @Log(title = "修改教材模板默认", businessType = BusinessType.UPDATE)
    @PutMapping("/editDefault")
    public AjaxResult editDefault(@RequestBody DtbBookTemplate dtbBookTemplate) {
        return toAjax(dtbBookTemplateService.editDefault(dtbBookTemplate));
    }

    /**
     * 删除DUTP-DTB-029教材模板
     */
    @RequiresPermissions("book:template:remove")
    @Log(title = "删除DUTP-DTB-029教材模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/{templateId}")
    public AjaxResult remove(@PathVariable Long templateId) {
        return toAjax(dtbBookTemplateService.deleteDtbBookTemplateByTemplateId(templateId));
    }


    /**
     * 通过章节id获取DUTP-DTB-029教材模板详细信息
     */

    @GetMapping(value = "/getInfoByChapterId/{chapterId}")
    public AjaxResult getInfoByChapterId(@PathVariable("chapterId") Long chapterId) {
        return success(dtbBookTemplateService.selectDtbBookTemplateByChapterId(chapterId));
    }

    /**
     * 通过教材Id获取模板列表
     */

    @GetMapping(value = "/templateListByBookId/{bookId}")
    public AjaxResult templateListByBookId(@PathVariable("bookId") Long bookId) {
        return success(dtbBookTemplateService.templateListByBookId(bookId));
    }


    /**
     * 修改教材模板
     */
    @Log(title = "修改教材模板", businessType = BusinessType.UPDATE)
    @PutMapping("/updateBookTemplate")
    public AjaxResult updateBookTemplate(@RequestBody DtbBookTemplate dtbBookTemplate) {
        return toAjax(dtbBookTemplateService.updateBookTemplate(dtbBookTemplate));
    }

}

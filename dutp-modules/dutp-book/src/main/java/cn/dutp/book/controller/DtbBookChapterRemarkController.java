package cn.dutp.book.controller;

import cn.dutp.book.domain.DtbBookChapterRemark;
import cn.dutp.book.service.IDtbBookChapterRemarkService;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.hutool.core.util.ObjectUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 章节标注Controller
 *
 * <AUTHOR>
 * @date 2025-02-06
 */
@RestController
@RequestMapping("/chapterRemark")
public class DtbBookChapterRemarkController extends BaseController {
    @Autowired
    private IDtbBookChapterRemarkService dtbBookChapterRemarkService;

    /**
     * 查询章节标注列表
     */
    @GetMapping("/list")
    public AjaxResult list(DtbBookChapterRemark dtbBookChapterRemark) {
        List<DtbBookChapterRemark> list = dtbBookChapterRemarkService.selectDtbBookChapterRemarkList(dtbBookChapterRemark);
        return success(list);
    }


    /**
     * 获取章节标注详细信息
     */
    @GetMapping(value = "/{remarkId}")
    public AjaxResult getInfo(@PathVariable("remarkId") Long remarkId) {
        return success(dtbBookChapterRemarkService.selectDtbBookChapterRemarkByRemarkId(remarkId));
    }

    @Log(title = "新增章节标注", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookChapterRemark dtbBookChapterRemark) {
        return toAjax(dtbBookChapterRemarkService.insertDtbBookChapterRemark(dtbBookChapterRemark));
    }

    @Log(title = "修改章节标注", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookChapterRemark dtbBookChapterRemark) {
        return toAjax(dtbBookChapterRemarkService.updateDtbBookChapterRemark(dtbBookChapterRemark));
    }

    @Log(title = "处理章节标注", businessType = BusinessType.UPDATE)
    @PutMapping("/handleState")
    public AjaxResult handleState(@RequestBody DtbBookChapterRemark dtbBookChapterRemark) {
        if (ObjectUtil.isEmpty(dtbBookChapterRemark.getRemarkId())) {
            return error("参数不能为空");
        }
        dtbBookChapterRemark.setState(2);
        dtbBookChapterRemark.setHandleUserId(SecurityUtils.getUserId());
        return toAjax(dtbBookChapterRemarkService.updateDtbBookChapterRemark(dtbBookChapterRemark));
    }

    /**
     * 删除章节标注
     */
    @Log(title = "删除章节标注", businessType = BusinessType.DELETE)
    @DeleteMapping("/{remarkIds}")
    public AjaxResult remove(@PathVariable Long[] remarkIds) {
        return toAjax(dtbBookChapterRemarkService.deleteDtbBookChapterRemarkByRemarkIds(Arrays.asList(remarkIds)));
    }
}

package cn.dutp.book.service.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Arrays;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.book.mapper.DtbArVrResourceRecordMapper;
import cn.dutp.book.domain.DtbArVrResourceRecord;
import cn.dutp.book.domain.DtbArVrResource;
import cn.dutp.book.domain.DtbBookResource;
import cn.dutp.book.domain.DtbUserResource;
import cn.dutp.book.domain.vo.DtbArVrResourceRecordVO;
import cn.dutp.book.service.IDtbArVrResourceRecordService;
import cn.dutp.book.service.IDtbArVrResourceService;
import cn.dutp.book.service.IDtbBookResourceService;
import cn.dutp.book.service.IDtbUserResourceService;
import cn.dutp.common.security.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;

/**
 * AR/VR资源指派记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Service
public class DtbArVrResourceRecordServiceImpl extends ServiceImpl<DtbArVrResourceRecordMapper, DtbArVrResourceRecord> implements IDtbArVrResourceRecordService {
    private static final Logger log = LoggerFactory.getLogger(DtbArVrResourceRecordServiceImpl.class);

    @Autowired
    private DtbArVrResourceRecordMapper dtbArVrResourceRecordMapper;

    @Autowired
    private IDtbBookResourceService dtbBookResourceService;

    @Autowired
    private IDtbArVrResourceService dtbArVrResourceService;

    @Autowired
    private IDtbUserResourceService dtbUserResourceService;

    /**
     * 查询AR/VR资源指派记录
     *
     * @param id AR/VR资源指派记录主键
     * @return AR/VR资源指派记录
     */
    @Override
    public DtbArVrResourceRecord selectDtbArVrResourceRecordById(Long id) {
        return this.getById(id);
    }

    /**
     * 查询AR/VR资源指派记录列表
     *
     * @param dtbArVrResourceRecord AR/VR资源指派记录
     * @return AR/VR资源指派记录
     */
    @Override
    public List<DtbArVrResourceRecord> selectDtbArVrResourceRecordList(DtbArVrResourceRecord dtbArVrResourceRecord) {
        LambdaQueryWrapper<DtbArVrResourceRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotEmpty(dtbArVrResourceRecord.getResourceId())) {
            lambdaQueryWrapper.eq(DtbArVrResourceRecord::getResourceId
                    , dtbArVrResourceRecord.getResourceId());
        }
        if (ObjectUtil.isNotEmpty(dtbArVrResourceRecord.getBookId())) {
            lambdaQueryWrapper.eq(DtbArVrResourceRecord::getBookId
                    , dtbArVrResourceRecord.getBookId());
        }
        if (ObjectUtil.isNotEmpty(dtbArVrResourceRecord.getFolderId())) {
            lambdaQueryWrapper.eq(DtbArVrResourceRecord::getFolderId
                    , dtbArVrResourceRecord.getFolderId());
        }
        if (ObjectUtil.isNotEmpty(dtbArVrResourceRecord.getIsRecall())) {
            lambdaQueryWrapper.eq(DtbArVrResourceRecord::getIsRecall
                    , dtbArVrResourceRecord.getIsRecall());
        }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 查询AR/VR资源指派记录VO列表
     *
     * @param dtbArVrResourceRecord AR/VR资源指派记录
     * @return AR/VR资源指派记录VO集合
     */
    @Override
    public List<DtbArVrResourceRecordVO> selectDtbArVrResourceRecordVOList(DtbArVrResourceRecord dtbArVrResourceRecord) {
        return dtbArVrResourceRecordMapper.selectDtbArVrResourceRecordVOList(dtbArVrResourceRecord);
    }

    /**
     * 新增AR/VR资源指派记录
     *
     * @param dtbArVrResourceRecord AR/VR资源指派记录
     * @return 结果
     */
    @Override
    public boolean insertDtbArVrResourceRecord(DtbArVrResourceRecord dtbArVrResourceRecord) {
        return this.save(dtbArVrResourceRecord);
    }

    /**
     * 修改AR/VR资源指派记录
     *
     * @param dtbArVrResourceRecord AR/VR资源指派记录
     * @return 结果
     */
    @Override
    public boolean updateDtbArVrResourceRecord(DtbArVrResourceRecord dtbArVrResourceRecord) {
        return this.updateById(dtbArVrResourceRecord);
    }

    /**
     * 批量删除AR/VR资源指派记录
     *
     * @param ids 需要删除的AR/VR资源指派记录主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbArVrResourceRecordByIds(List<Long> ids) {
        return this.removeByIds(ids);
    }

    /**
     * 撤回AR/VR资源指派记录
     *
     * @param dtbArVrResourceRecord AR/VR资源指派记录
     * @return 结果
     */
    @Override
    public boolean recallResourceRecord(DtbArVrResourceRecord dtbArVrResourceRecord) {
        // 将指派记录标记为已撤回
        dtbArVrResourceRecord.setIsRecall(1);

        // 删除关联的教材资源
        Long resourceId = dtbArVrResourceRecord.getResourceId();
        Long bookId = dtbArVrResourceRecord.getBookId();
        Long folderId = dtbArVrResourceRecord.getFolderId();
        Long bookResourceId = dtbArVrResourceRecord.getBookResourceId();

        if (bookResourceId != null) {
            // 如果有教材资源ID，直接删除
            dtbBookResourceService.deleteDtbBookResourceByBookResourceIds(Arrays.asList(bookResourceId));
        } else if (resourceId != null && bookId != null) {
            // 向后兼容：查找与当前AR/VR资源相关的教材资源
            DtbBookResource query = new DtbBookResource();
            query.setResourceId(resourceId);
            query.setBookId(bookId);
            if (folderId != null) {
                query.setFolderId(folderId);
            }
            List<DtbBookResource> resources = dtbBookResourceService.selectDtbBookResourceList(query);

            if (resources != null && !resources.isEmpty()) {
                List<Long> bookResourceIds = new ArrayList<>();
                for (DtbBookResource resource : resources) {
                    bookResourceIds.add(resource.getBookResourceId());
                }
                dtbBookResourceService.deleteDtbBookResourceByBookResourceIds(bookResourceIds);
            }
        }

        // 更新记录状态为已撤回
        return this.deleteDtbArVrResourceRecordByIds(Arrays.asList(dtbArVrResourceRecord.getId()));
    }

    /**
     * 指派AR/VR资源到教材
     * 该方法将AR/VR资源指派到教材，并同时将资源添加到教材资源中
     *
     * @param dtbArVrResourceRecord AR/VR资源指派记录
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignArVrResourceToBook(DtbArVrResourceRecord dtbArVrResourceRecord) {
        boolean result = false;
        try {
            // 获取AR/VR资源信息
            DtbArVrResource arVrResource = dtbArVrResourceService.selectDtbArVrResourceByResourceId(dtbArVrResourceRecord.getResourceId());
            if (arVrResource != null) {
                // 1. 先创建用户资源
                DtbUserResource userResource = new DtbUserResource();
                // 从AR/VR资源拷贝相关信息到用户资源
                userResource.setFileName(arVrResource.getName());
                userResource.setFileUrl(arVrResource.getResourceUrl());
                userResource.setFileType("5");
                userResource.setFolderId(-1L);
                // 设置当前登录用户ID
                userResource.setUserId(SecurityUtils.getUserId());

                // 保存用户资源
                boolean userResourceResult = dtbUserResourceService.insertDtbUserResource(userResource);

                if (userResourceResult) {
                    // 2. 再创建教材资源，关联到新创建的用户资源
                    DtbBookResource bookResource = new DtbBookResource();
                    bookResource.setBookId(dtbArVrResourceRecord.getBookId());
                    bookResource.setFolderId(dtbArVrResourceRecord.getFolderId());
                    // 设置关联的用户资源ID
                    bookResource.setResourceId(userResource.getResourceId());

                    // 将AR/VR资源添加到教材资源中
                    boolean insertDtbBookResource = dtbBookResourceService.insertDtbBookResource(bookResource);

                    if (insertDtbBookResource) {
                        // 保存教材资源ID
                        dtbArVrResourceRecord.setBookResourceId(bookResource.getBookResourceId());
        
                        result = this.insertDtbArVrResourceRecord(dtbArVrResourceRecord);
                    }

                } else {
                    log.error("创建用户资源失败");
                }
            }
        } catch (Exception e) {
            log.error("添加教材资源失败", e);
            throw new RuntimeException("添加教材资源失败");
        }


        return result;
    }
}

package cn.dutp.book.service.impl;

import cn.dutp.api.common.constant.DutpConstant;
import cn.dutp.book.domain.*;
import cn.dutp.book.domain.vo.ResourceVO;
import cn.dutp.book.esmapper.EsDtbBooksMapper;
import cn.dutp.book.mapper.*;
import cn.dutp.book.service.*;
import cn.dutp.common.core.constant.SecurityConstants;
import cn.dutp.common.core.domain.R;
import cn.dutp.common.core.domain.UploadFileDto;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.utils.*;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.mongo.service.MongoService;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.*;
import cn.dutp.message.api.RemoteUserMessageService;
import cn.dutp.message.api.domain.DutpUserMessage;
import cn.dutp.system.api.RemoteUserService;
import cn.dutp.system.api.domain.SysUser;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.shaded.com.google.gson.reflect.TypeToken;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static cn.dutp.book.service.impl.DtbBookChapterContentServiceImpl.BOOK_CHAPTER_CONTENT;
import static cn.dutp.common.core.utils.zipUtil.generateZip;

/**
 * DUTP-DTB_002数字教材Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-14
 */
@Slf4j
@Service
public class DtbBookServiceImpl extends ServiceImpl<DtbBookMapper, DtbBook> implements IDtbBookService {
    @Autowired
    private DtbBookBookMapper dtbBookMapper;

    @Autowired
    private DtbBookChapterMapper dtbBookChapterMapper;

    @Autowired
    private DtbBookTypeMapper bookTypeMapper;

    @Autowired
    private DtbBookCommonMapper bookCommonMapper;

    @Autowired
    private DtbBookPublishProcessMapper bookPublishProcessMapper;

    @Autowired
    private DtbBookVersionMapper bookVersionMapper;

    @Autowired
    private DtbBookRecallAmendMapper recallAmendMapper;

    @Autowired
    private EsDtbBooksMapper esDtbBooksMapper;

    @Autowired
    private DtbBookAttributeMapper bookAttributeMapper;

    @Autowired
    private DtbBookGroupMapper bookGroupMapper;

    @Autowired
    private DtbBookPublishProcessAuditUserMapper publishProcessAuditUserMapper;

    @Autowired
    private RemoteUserMessageService remoteUserMessageService;

    @Autowired
    private DutpTaskMapper dutpTaskMapper;

    @Autowired
    private DtbBookResourceFolderMapper bookResourceFolderMapper;

    @Autowired
    private IDtbBookResourceFolderService dtbBookResourceFolderService;

    @Autowired
    private IDtbBookGroupService dtbBookGroupService;

    @Autowired
    private IDtbBookChapterService chapterService;

    @Autowired
    private IDtbBookFileService bookFileService;

    @Autowired
    private DtbBookChapterDataMapper chapterDataMapper;

    @Autowired
    private IDtbBookChapterDataService chapterDataService;

    @Autowired
    private DtbBookResourceMapper bookResourceMapper;

    @Autowired
    private IDtbBookResourceService dtbBookResourceService;

    @Autowired
    private DtbBookChapterResourceMapper bookChapterResourceMapper;

    @Autowired
    private IDtbBookChapterResourceService dtbBookChapterResourceService;

    @Autowired
    private DtbBookChapterEditorMapper chapterEditorMapper;

    @Autowired
    private MongoService mongoService;

    @Autowired
    private DtbBookQuestionFolderMapper bookQuestionFolderMapper;

    @Autowired
    private IDtbBookQuestionFolderService dtbBookQuestionFolderService;

    @Autowired
    private DtbBookQuestionMapper bookQuestionMapper;

    @Autowired
    private IDtbBookQuestionService dtbBookQuestionService;

    @Autowired
    private DtbBookChapterCatalogMapper chapterCatalogMapper;

    @Autowired
    private IDtbBookChapterCatalogService chapterCatalogService;

    @Autowired
    private DocxToJsonConverter docxToJsonConverter;

    @Autowired
    private DtbBookChapterAuditLogMapper chapterAuditLogMapper;

    @Autowired
    private DtbBookChapterRemarkMapper chapterRemarkMapper;

    @Autowired
    private DtbUserBookReadMapper dtbUserBookReadMapper;

    @Autowired
    private DtbUserBookNoteMapper dtbUserBookNoteMapper;

    @Autowired
    private DtbBookQuestionAnswerMapper dtbBookQuestionAnswerMapper;

    @Autowired
    private DtbPurchaseCodeMapper dtbPurchaseCodeMapper;

    @Autowired
    private IDtbUserBookService dtbUserBookService;

    @Autowired
    private AliyunOssStsUtil aliyunOssStsUtil;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private PdfConver pdfToJsonConverter;


    // 导出教材的位置
    @Value("${file.export.path}")
    private String fileExportPath;

    ExecutorService customExecutor = Executors.newFixedThreadPool(10);

    /**
     * 通用验证书籍状态，必须上架和出版状态。
     *
     * @param bookId
     * @return
     */
    public boolean checkBookStatus(Long bookId) {
        LambdaQueryWrapper<DtbBook> bookLambdaQueryWrapper = new LambdaQueryWrapper<>();
        bookLambdaQueryWrapper.eq(DtbBook::getBookId, bookId).eq(DtbBook::getPublishStatus, 2).eq(DtbBook::getShelfState, 1);
        DtbBook dtbBook = this.getOne(bookLambdaQueryWrapper);
        if (ObjectUtil.isNull(dtbBook)) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 查询DUTP-DTB_002数字教材
     *
     * @param bookId DUTP-DTB_002数字教材主键
     * @return DUTP-DTB_002数字教材
     */
    @Override
    public DtbBook selectDtbBookByBookId(Long bookId) {
        DtbBook book = dtbBookMapper.queryBookByBookId(bookId);
        if (ObjectUtil.isNotEmpty(book)) {
            // 判断定稿提交是否驳回
            if (book.getStepId() == 2) {
                DtbBookPublishProcess bookPublishProcess = bookPublishProcessMapper.queryBookLastProcess(bookId);
                book.setAuditState(bookPublishProcess.getState());
            } else if (book.getStepId() > 2) {
                book.setAuditState(0);
            }
            if (book.getMasterFlag() == 2) {
                // 主教材
                // 查询副教材列表
                List<DtbBook> dtbBookList = dtbBookMapper.queryBookListByBookId(bookId);
                book.setDeputyBookList(dtbBookList);
                if (ObjectUtil.isNotEmpty(dtbBookList)) {
                    book.setDeputyBookName(dtbBookList.stream().map(DtbBook::getBookName).collect(Collectors.joining(",")));
                }
            } else if (book.getMasterFlag() == 3) {
                // 副教材
                // 查询主教材
                DtbBook masterBook = this.getOne(new LambdaQueryWrapper<DtbBook>()
                        .select(DtbBook::getBookId, DtbBook::getBookNo, DtbBook::getBookName)
                        .eq(DtbBook::getBookId, book.getMasterBookId()));
                if (ObjectUtil.isNotEmpty(masterBook)) {
                    book.setMasterBookNo(masterBook.getBookNo());
                    book.setMasterBookName(masterBook.getBookName());
                }
            }
            // 中图分类
            if (ObjectUtil.isNotEmpty(book.getBookType())) {
                DtbBookType bookType = bookTypeMapper.getBookTypeById(book.getBookType());
                book.setBookTypeName(bookType.getTypeName());
                // TODO 暂时展示一级
                // Long parentId = bookType.getParentId();
                // while (parentId.longValue() != 0l) {
                //     bookType = bookTypeMapper.getBookTypeById(parentId);
                //     parentId = bookType.getParentId();
                // }
            }
            if (ObjectUtil.isNotEmpty(book.getBookId())) {
                // 学校
                List<DutpBookSchoolVo> dutpSchoolVoList = bookCommonMapper.querySchoolByBookId(book.getBookId());
                book.setSchoolList(dutpSchoolVoList);
                if (ObjectUtil.isNotEmpty(dutpSchoolVoList)) {
                    book.setSchoolName(dutpSchoolVoList.stream().map(DutpBookSchoolVo::getSchoolName).collect(Collectors.joining(",")));
                }
                // 专区分类
                List<DutpAreaVo> dutpBookAreaVoList = bookCommonMapper.queryAreaByBookId(book.getBookId());
                book.setAreaList(dutpBookAreaVoList);
            }

            // 教育学科分类
            Long lastSubjectId = getLastSubjectId(book);
            if (ObjectUtil.isNotEmpty(lastSubjectId)) {
                DutpSubjectVo dutpSubjectVo = bookCommonMapper.querySubjectBySubjectId(lastSubjectId);
                book.setSubject(dutpSubjectVo);
            }
            // 计算完成度
            completionCalculation(book);
        }
        return book;
    }

    /**
     * 计算完成度
     *
     * @param book
     */
    private void completionCalculation(DtbBook book) {
        List<DtbBookChapter> dtbBookChapterList = dtbBookChapterMapper.queryBookChapterListByBookId(book.getBookId());
        if (ObjectUtil.isNotEmpty(dtbBookChapterList)) {
            BigDecimal average = dtbBookChapterList.stream()
                    .map(DtbBookChapter::getCompleteRate)
                    .filter(ObjectUtil::isNotEmpty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .divide(BigDecimal.valueOf(dtbBookChapterList.size()), 0, BigDecimal.ROUND_HALF_UP);
            book.setCompleteRate(average);
        } else {
            book.setCompleteRate(BigDecimal.ZERO);
        }
    }

    /**
     * 查询DUTP-DTB_002数字教材列表
     *
     * @param dtbBook DUTP-DTB_002数字教材
     * @return DUTP-DTB_002数字教材
     */
    @Override
    public List<DtbBook> selectDtbBookList(DtbBook dtbBook) {
        List<DtbBook> dtbBookList = dtbBookMapper.selectListByDtbBook(dtbBook);
        for (DtbBook book : dtbBookList) {

            // 计算完成度
            completionCalculation(book);

            // 判断定稿提交是否驳回
            if (book.getStepId() == 2) {
                DtbBookPublishProcess bookPublishProcess = bookPublishProcessMapper.queryBookLastProcess(book.getBookId());
                book.setAuditState(bookPublishProcess.getState());
            } else if (book.getStepId() > 2) {
                book.setAuditState(0);
            }

            // 检测是否修改版本
            if (book.getCurrentVersionId().longValue() != book.getLastVersionId().longValue()) {
                DtbBook amendBook = new DtbBook();
                BeanUtil.copyProperties(book, amendBook);
                amendBook.setVersionNo(amendBook.getAmendVersionNo());
                amendBook.setCreateTime(amendBook.getAmendCreateTime());
                book.setLastVersionId(book.getCurrentVersionId());
                List<DtbBook> children = new ArrayList<>(1);
                children.add(amendBook);
                book.setChildren(children);
            }
        }
        return dtbBookList;
    }

    public List<DtbBook> commonDtbBookList(DtbBook dtbBook) {
        LambdaQueryWrapper<DtbBook> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotEmpty(dtbBook.getBookName())) {
            lambdaQueryWrapper.like(DtbBook::getBookName
                    , dtbBook.getBookName());
        }

        if (ObjectUtil.isNotEmpty(dtbBook.getIsbn())) {
            lambdaQueryWrapper.eq(DtbBook::getIsbn
                    , dtbBook.getIsbn());
        }
        if (ObjectUtil.isNotEmpty(dtbBook.getBookNo())) {
            lambdaQueryWrapper.eq(DtbBook::getBookNo
                    , dtbBook.getBookNo());
        }
        if (ObjectUtil.isNotEmpty(dtbBook.getPublishOrganization())) {
            lambdaQueryWrapper.eq(DtbBook::getPublishOrganization
                    , dtbBook.getPublishOrganization());
        }
        if (ObjectUtil.isNotEmpty(dtbBook.getPublishStatus())) {
            lambdaQueryWrapper.eq(DtbBook::getPublishStatus
                    , dtbBook.getPublishStatus());
        }
        if (ObjectUtil.isNotEmpty(dtbBook.getSchoolId())) {
            lambdaQueryWrapper.eq(DtbBook::getSchoolId
                    , dtbBook.getSchoolId());
        }
        if (ObjectUtil.isNotEmpty(dtbBook.getBookType())) {
            lambdaQueryWrapper.eq(DtbBook::getBookType
                    , dtbBook.getBookType());
        }
        if (ObjectUtil.isNotEmpty(dtbBook.getShelfStates())) {
            lambdaQueryWrapper.in(DtbBook::getShelfState, dtbBook.getShelfStates());
        }
        if (ObjectUtil.isNotEmpty(dtbBook.getTopSubjectId())) {
            lambdaQueryWrapper.eq(DtbBook::getTopSubjectId, dtbBook.getTopSubjectId());
        }
        if (ObjectUtil.isNotEmpty(dtbBook.getMasterFlags())) {
            lambdaQueryWrapper.in(DtbBook::getMasterFlag, dtbBook.getMasterFlags());
        }
        if (ObjectUtil.isNotEmpty(dtbBook.getHouseId())) {
            lambdaQueryWrapper.in(DtbBook::getHouseId, dtbBook.getHouseId());
        }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增DUTP-DTB_002数字教材
     *
     * @param dtbBook DUTP-DTB_002数字教材
     * @return 结果
     */
    @Transactional
    @Override
    public Long insertDtbBook(DtbBook dtbBook) {
        // isbn issn 测重
        checkIsbnOrIssnRepeat(dtbBook);

        // 生成教材编号
        dtbBook.setBookNo(String.valueOf(System.currentTimeMillis()));

        // 默认 1.0.0 版本
        DtbBookVersion bookVersion = insertInitVersion();
        Long versionId = bookVersion.getVersionId();
        dtbBook.setCurrentVersionId(versionId);
        dtbBook.setLastVersionId(versionId);
        dtbBook.setCurrentStepId(1l);
        boolean success = this.save(dtbBook);
        if (success) {
            Long bookId = dtbBook.getBookId();

            // 新增文件夹
            generateResourceFolder(bookId);

            DtbBookQuestionFolder bookQuestionFolder = new DtbBookQuestionFolder();
            bookQuestionFolder.setFolderName(getFolderName(8));
            bookQuestionFolder.setBookId(bookId);
            bookQuestionFolder.setParentId(0L);
            bookQuestionFolder.setDefaultType(1);
            bookQuestionFolderMapper.insert(bookQuestionFolder);

            // 更新版本ID
            bookVersionMapper.updateBookId(bookId, versionId);

            // 更新副教材
            updateDeputyBook(dtbBook);

            // 插入学校
            List<DutpBookSchoolVo> schoolList = dtbBook.getSchoolList();
            if (ObjectUtil.isNotEmpty(schoolList)) {
                bookCommonMapper.batchAddSchool(schoolList, bookId);
            }

            // 更新专区分类
            List<DutpAreaVo> areaList = dtbBook.getAreaList();
            if (ObjectUtil.isNotEmpty(areaList)) {
                bookCommonMapper.batchAddArea(areaList, bookId);
            }
            if (ObjectUtil.isNotEmpty(dtbBook.getShelfState()) && dtbBook.getShelfState() == 4) {
                // 新增
                EsDtbBook esDtbBook = generateEsDtbBook(dtbBook);
                esDtbBooksMapper.insert(esDtbBook);
            }


        }
        return dtbBook.getBookId();
    }

    /**
     * 获取默认文件夹名称
     *
     * @param i
     * @return
     */
    private String getFolderName(int i) {
        // 文件类型1=图片，2=音频，3=视频，4=虚拟仿真，5=AR/VR，6=3D模型，7习题，8课件
        if (i == 1) {
            return "图片文件夹";
        }
        if (i == 2) {
            return "音频文件夹";
        }
        if (i == 3) {
            return "视频文件夹";
        }
        if (i == 4) {
            return "虚拟仿真文件夹";
        }
        if (i == 5) {
            return "AR/VR文件夹";
        }
        if (i == 6) {
            return "3D模型文件夹";
        }
        if (i == 7) {
            return "题库文件夹";
        }
        if (i == 8) {
            return "教学资源文件夹";
        }
        return "默认文件夹";
    }

    /**
     * 初始化版本
     *
     * @return
     */
    @NotNull
    private DtbBookVersion insertInitVersion() {
        DtbBookVersion bookVersion = new DtbBookVersion();
        bookVersion.setVersionNo("1.0.0");
        bookVersion.setVersionIntroduce("第一个版本");
        bookVersionMapper.insert(bookVersion);
        return bookVersion;
    }

    /**
     * 更新副教材
     *
     * @param dtbBook
     */
    private void updateDeputyBook(DtbBook dtbBook) {
        if (dtbBook.getMasterFlag().intValue() == 2) {
            List<DtbBook> deputyBookList = dtbBook.getDeputyBookList();
            if (ObjectUtil.isEmpty(deputyBookList)) return;
            List<Long> deputyBookIdList = deputyBookList.stream().map(DtbBook::getBookId).collect(Collectors.toList());
            dtbBookMapper.updateDeputyBook(dtbBook, deputyBookIdList);
            for (Long bookId : deputyBookIdList) {
                bookCommonMapper.delAllSchoolByBookId(bookId);
                // 插入学校
                List<DutpBookSchoolVo> schoolList = dtbBook.getSchoolList();
                if (ObjectUtil.isNotEmpty(schoolList)) {
                    bookCommonMapper.batchAddSchool(schoolList, bookId);
                }

                bookCommonMapper.delAllAreaByBookId(bookId);
                // 更新专区分类
                List<DutpAreaVo> areaList = dtbBook.getAreaList();
                if (ObjectUtil.isNotEmpty(areaList)) {
                    bookCommonMapper.batchAddArea(areaList, bookId);
                }
            }
        }
    }

    /**
     * 修改DUTP-DTB_002数字教材
     *
     * @param dtbBook DUTP-DTB_002数字教材
     * @return 结果
     */
    @Transactional
    @Override
    public boolean updateDtbBook(DtbBook dtbBook) {
        // isbn issn 测重
        checkIsbnOrIssnRepeat(dtbBook);
        DtbBook book = dtbBookMapper.selectOne(new LambdaQueryWrapper<DtbBook>()
                .select(DtbBook::getShelfState)
                .eq(DtbBook::getBookId, dtbBook.getBookId()));
        // TODO 公开和校本教材切换校验
        int row = dtbBookMapper.updateByBookId(dtbBook);
        Boolean success = row > 0 ? true : false;
        if (success) {
            Long bookId = dtbBook.getBookId();
            // 更新副教材
            // 删除之前的副教材
            dtbBookMapper.delAllDeputyBookByBookId(bookId);
            updateDeputyBook(dtbBook);

            // 插入学校
            bookCommonMapper.delAllSchoolByBookId(bookId);
            List<DutpBookSchoolVo> schoolList = dtbBook.getSchoolList();
            if (ObjectUtil.isNotEmpty(schoolList)) {
                bookCommonMapper.batchAddSchool(schoolList, bookId);
            }

            // 更新专区分类
            bookCommonMapper.delAllAreaByBookId(bookId);
            List<DutpAreaVo> areaList = dtbBook.getAreaList();
            if (ObjectUtil.isNotEmpty(areaList)) {
                bookCommonMapper.batchAddArea(areaList, bookId);
            }

            // 更新es
            if (book.getShelfState().intValue() == 4 && dtbBook.getShelfState() == 4) {
                // 更新
                EsDtbBook esDtbBook = generateEsDtbBook(dtbBook);
                esDtbBooksMapper.updateById(esDtbBook);
            } else if (book.getShelfState().intValue() == 4 && dtbBook.getShelfState() != 4) {
                // 删除
                esDtbBooksMapper.deleteById(bookId);
            } else if (book.getShelfState().intValue() != 4 && dtbBook.getShelfState() == 4) {
                // 新增
                EsDtbBook esDtbBook = generateEsDtbBook(dtbBook);
                esDtbBooksMapper.insert(esDtbBook);
            }

        }
        return success;
    }

    /**
     * 生成es搜索对象
     *
     * @param dtbBook
     * @return
     */
    @NotNull
    private EsDtbBook generateEsDtbBook(DtbBook dtbBook) {
        EsDtbBook esDtbBook = new EsDtbBook();
        esDtbBook.setBookId(dtbBook.getBookId());
        esDtbBook.setBookName(dtbBook.getBookName());
        esDtbBook.setAuthorLabel(dtbBook.getAuthorLabel());
        esDtbBook.setAuthorValue(dtbBook.getAuthorValue());
        esDtbBook.setCover(dtbBook.getCover());
        esDtbBook.setIsbn(dtbBook.getIsbn());
        esDtbBook.setBookNo(dtbBook.getBookNo());
        esDtbBook.setPublishDate(DateUtil.formatDate(dtbBook.getPublishDate()));
        esDtbBook.setBookType(dtbBook.getBookType());
        esDtbBook.setPriceCounter(dtbBook.getPriceCounter());
        esDtbBook.setPriceSale(dtbBook.getPriceSale());
        esDtbBook.setShelfTime(DateUtil.formatDate(dtbBook.getShelfTime()));
        esDtbBook.setReadQuantity(dtbBook.getReadQuantity());
        esDtbBook.setMasterFlag(dtbBook.getMasterFlag());
        esDtbBook.setTopSubjectId(dtbBook.getTopSubjectId());
        esDtbBook.setSecondSubjectId(dtbBook.getSecondSubjectId());
        esDtbBook.setThirdSubjectId(dtbBook.getThirdSubjectId());
        esDtbBook.setForthSubjectId(dtbBook.getForthSubjectId());

        return esDtbBook;
    }

    /**
     * isbn issn 测重
     *
     * @param dtbBook
     */
    private void checkIsbnOrIssnRepeat(DtbBook dtbBook) {
        if (dtbBook.getMasterFlag() != 3) {
            String isbn = dtbBook.getIsbn();
            String issn = dtbBook.getIssn();
            Long bookId = dtbBook.getBookId();
            if (ObjectUtil.isNotEmpty(isbn) && ObjectUtil.isNotEmpty(issn)) {
                throw new ServiceException("issn 和 isbn 两者只能选填一项");
            }
            if (ObjectUtil.isNotEmpty(issn)) {
                int count = dtbBookMapper.checkRepeatByIssn(issn, bookId);
                if (count > 0) {
                    throw new ServiceException("issn 已存在");
                }
            }
            if (ObjectUtil.isNotEmpty(isbn)) {
                int count = dtbBookMapper.checkRepeatByIsbn(isbn, bookId);
                if (count > 0) {
                    throw new ServiceException("isbn 已存在");
                }
            }
        }
    }

    /**
     * 删除数字教材
     *
     * @param bookId 需要删除的DUTP-DTB_002数字教材主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookByBookId(Long bookId) {
        // 校验教材状态
        DtbBook book = this.getOne(new LambdaQueryWrapper<DtbBook>()
                .select(DtbBook::getBookId, DtbBook::getCurrentStepId, DtbBook::getPublishStatus)
                .eq(DtbBook::getBookId, bookId));
        if (ObjectUtil.isEmpty(book)) {
            throw new ServiceException("教材不存在");
        }
        if (book.getCurrentStepId() != 1l) {
            throw new ServiceException("教材当前节点不是制作中，不能删除");
        }
        if (book.getPublishStatus() != 1) {
            throw new ServiceException("教材当前节点不是未出版，不能删除");
        }
        // 删除资源包
        bookFileService.remove(new LambdaQueryWrapper<DtbBookFile>()
                .eq(DtbBookFile::getBookId, bookId));
        // 删除团队管理
        dtbBookGroupService.remove(new LambdaQueryWrapper<DtbBookGroup>()
                .eq(DtbBookGroup::getBookId, bookId));
        // 删除简介
        bookAttributeMapper.delete(new LambdaQueryWrapper<DtbBookAttribute>()
                .eq(DtbBookAttribute::getBookId, bookId));
        // 删除章节目录
        chapterCatalogMapper.delete(new LambdaQueryWrapper<DtbBookChapterCatalog>()
                .eq(DtbBookChapterCatalog::getBookId, bookId));
        // 删除审核记录
        chapterAuditLogMapper.delete(new LambdaQueryWrapper<DtbBookChapterAuditLog>()
                .eq(DtbBookChapterAuditLog::getBookId, bookId));
        // 删除章节统计数据
        chapterDataMapper.delete(new LambdaQueryWrapper<DtbBookChapterData>()
                .eq(DtbBookChapterData::getBookId, bookId));
        // 删除资源
        bookResourceMapper.delete(new LambdaQueryWrapper<DtbBookResource>()
                .eq(DtbBookResource::getBookId, bookId));
        bookResourceFolderMapper.delete(new LambdaQueryWrapper<DtbBookResourceFolder>()
                .eq(DtbBookResourceFolder::getBookId, bookId));

        // TODO 删除互动
        // 删除章节对应编辑关系
        chapterEditorMapper.delete(new LambdaQueryWrapper<DtbBookChapterEditor>()
                .eq(DtbBookChapterEditor::getBookId, bookId));
        // 删除章节对应标注
        chapterRemarkMapper.delete(new LambdaQueryWrapper<DtbBookChapterRemark>()
                .eq(DtbBookChapterRemark::getBookId, bookId));
        // 删除章节内容
        List<DtbBookChapter> chapterList = dtbBookChapterMapper.selectList(new LambdaQueryWrapper<DtbBookChapter>()
                .select(DtbBookChapter::getChapterId)
                .eq(DtbBookChapter::getBookId, bookId));
        for (DtbBookChapter chapter : chapterList) {
            mongoService.deleteOne(BOOK_CHAPTER_CONTENT, new Query(Criteria.where("chapterId").is(chapter.getChapterId())));
        }
        // 删除副教材关联关系
        dtbBookMapper.delAllDeputyBookByBookId(bookId);
        // 删除章节
        dtbBookChapterMapper.delete(new LambdaQueryWrapper<DtbBookChapter>()
                .eq(DtbBookChapter::getBookId, bookId));
        // 删除教材
        return this.removeById(bookId);
    }

    @Override
    public List<DtbBookVo> selectCmsDtbBookList(DtbBook dtbBook) {
        List<DtbBookVo> result = new ArrayList<>();
        List<DtbBook> dtbBooks = this.commonDtbBookList(dtbBook);
        dtbBooks.forEach(item -> {
                    DtbBookVo dtbBookVo = new DtbBookVo();
                    BeanUtil.copyProperties(item, dtbBookVo);
                    result.add(dtbBookVo);
                }
        );
        return result;
    }

    /**
     * 学生教师端多维交叉查询
     */
    @Override
    public List<DtbBook> miSearchEducation(DtbBook dtbBook) {
        LambdaQueryWrapper<DtbBook> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotEmpty(dtbBook.getBookName())) {
            lambdaQueryWrapper.like(DtbBook::getBookName
                    , dtbBook.getBookName());
        }

        if (ObjectUtil.isNotEmpty(dtbBook.getAuthorValue())) {
            lambdaQueryWrapper.like(DtbBook::getAuthorValue
                    , dtbBook.getAuthorValue());
        }
        if (ObjectUtil.isNotEmpty(dtbBook.getTopSubjectId())) {
            lambdaQueryWrapper.eq(DtbBook::getTopSubjectId
                    , dtbBook.getTopSubjectId());
        }
        if (ObjectUtil.isNotEmpty(dtbBook.getThirdSubjectId())) {
            lambdaQueryWrapper.eq(DtbBook::getThirdSubjectId
                    , dtbBook.getThirdSubjectId());
        }
        if (ObjectUtil.isNotEmpty(dtbBook.getBookType())) {
            lambdaQueryWrapper.eq(DtbBook::getBookType
                    , dtbBook.getBookType());
        }
        if (ObjectUtil.isNotNull(dtbBook.getIsbnType())
                && dtbBook.getIsbnType() == 1
                && ObjectUtil.isNotNull(dtbBook.getIsbnOrIssn())) {
            lambdaQueryWrapper.eq(DtbBook::getIsbn
                    , dtbBook.getIsbnOrIssn());
        } else if (ObjectUtil.isNotNull(dtbBook.getIsbnType())
                && dtbBook.getIsbnType() == 2
                && ObjectUtil.isNotNull(dtbBook.getIsbnOrIssn())) {
            lambdaQueryWrapper.eq(DtbBook::getIssn
                    , dtbBook.getIsbnOrIssn());
        }
        if (ObjectUtil.isNotNull(dtbBook.getBookStartPrice())) {
            if (!dtbBook.getBookStartPrice().equals(new BigDecimal("0")) && !dtbBook.getBookEndprice().equals(new BigDecimal("0"))) {
                lambdaQueryWrapper.between(DtbBook::getPriceCounter, dtbBook.getBookStartPrice(), dtbBook.getBookEndprice());
            }
        }
        if (ObjectUtil.isNotNull(dtbBook.getPushYearRange())) {
            if (dtbBook.getPushYearRange() != null && dtbBook.getPushYearRange().size() == 2) {
                Date startDate = dtbBook.getPushYearRange().get(0);
                Date endDate = dtbBook.getPushYearRange().get(1);
                lambdaQueryWrapper.between(DtbBook::getPublishDate, startDate, endDate);
            }
        }
        lambdaQueryWrapper.ne(DtbBook::getMasterFlag, DutpConstant.NUM_THREE);
        lambdaQueryWrapper.in(DtbBook::getShelfState, DutpConstant.NUM_ONE, DutpConstant.NUM_FOUR);
        lambdaQueryWrapper.in(DtbBook::getBookOrganize, DutpConstant.NUM_ONE);

        // 打印 SQL 语句及其参数值
        System.out.println("Generated SQL: " + lambdaQueryWrapper.getSqlSegment());
        return this.list(lambdaQueryWrapper);
    }

    @Override
    @Transactional
    public int amendDtbBook(DtbBook dtbBook) {
        Long bookId = dtbBook.getBookId();
        // 新增版本
        DtbBookVersion bookVersion = new DtbBookVersion();
        bookVersion.setBookId(bookId);
        bookVersion.setVersionNo(dtbBook.getVersionNo());
        bookVersion.setVersionIntroduce("修正版本");
        bookVersionMapper.insert(bookVersion);

        Long versionId = bookVersion.getVersionId();
        Long userId = SecurityUtils.getUserId();
        // 新增修正历史
        DtbBookRecallAmend dtbBookRecallAmend = new DtbBookRecallAmend();
        dtbBookRecallAmend.setBookId(bookId);
        dtbBookRecallAmend.setRecallType(1);
        dtbBookRecallAmend.setUserId(userId);
        dtbBookRecallAmend.setReason(dtbBook.getReason());
        dtbBookRecallAmend.setVersionId(versionId);
        recallAmendMapper.insert(dtbBookRecallAmend);

        // 校验教材状态
        // 查询当前教材处于的最后一个流程节点
        DtbBookPublishProcess bookPublishProcess = bookPublishProcessMapper.queryBookLastProcess(bookId);

        // 更新状态
        DtbBookPublishProcess publishProcess = new DtbBookPublishProcess();
        publishProcess.setBookId(bookId);
        publishProcess.setStepId(14l);
        publishProcess.setState(1);
        // 这里存修正后的版本id
        publishProcess.setVersionId(versionId);
        publishProcess.setPromoterUserId(userId);
        publishProcess.setPrevProcessId(bookPublishProcess.getProcessId());
        int row = bookPublishProcessMapper.insert(publishProcess);

        // 更新节点到发布环节
        // 更新状态
        DtbBook book = new DtbBook();
        book.setBookId(bookId);
        book.setCurrentStepId(14l);
        book.setLastVersionId(versionId);

        // 策划编辑
        Long planningEditor = bookGroupMapper.queryGroupUserByType(5, bookId);
        DtbBookPublishProcessAuditUser auditUser = new DtbBookPublishProcessAuditUser();
        auditUser.setUserId(planningEditor);
        auditUser.setState(1);
        auditUser.setProcessId(publishProcess.getProcessId());
        publishProcessAuditUserMapper.insert(auditUser);

        // 查询章节
        DtbBookChapter dtbBookChapter = new DtbBookChapter();
        dtbBookChapter.setBookId(bookId);
        List<DtbBookChapter> chapterList = dtbBookChapterMapper.queryCopyBookChapterMapper(dtbBookChapter);
        if (ObjectUtil.isNotEmpty(chapterList)) {
            chapterList.stream().forEach(o -> {
                o.setBookId(bookId);
                o.setVersionId(versionId);
                o.setCompleteRate(new BigDecimal("100"));
            });
            chapterService.saveBatch(chapterList);

            // 复制目录
            for (DtbBookChapter bookChapter : chapterList) {
                List<DtbBookChapterCatalog> chapterCatalogList = chapterCatalogMapper.queryBookChapterCatalogTree(bookId, bookChapter.getSort());
                if (ObjectUtil.isNotEmpty(chapterCatalogList)) {
                    chapterCatalogList.stream().forEach(o -> {
                        o.setBookId(bookId);
                        o.setOldCatalogId(o.getCatalogId());
                        o.setVersionId(versionId);
                        o.setChapterId(bookChapter.getChapterId());
                        o.setCatalogId(null);
                    });
                    chapterCatalogList = TreeUtil.makeTree(chapterCatalogList, x -> x.getParentId() == 0l, (x, y) -> x.getOldCatalogId().equals(y.getParentId()), DtbBookChapterCatalog::setChildren);
                    saveChapterCatalogTreeNode(chapterCatalogList, 0l);
                }
            }
            for (DtbBookChapter bookChapter : chapterList) {
                Long oldChapterId = dtbBookChapterMapper.queryChapterId(bookId, bookChapter.getSort());
                // 查询章节内容
                Query query = new Query(Criteria.where("chapterId").is(oldChapterId));
                DtbBookChapterContent chapterContent = mongoService.findOne(BOOK_CHAPTER_CONTENT, query, DtbBookChapterContent.class);
                if (ObjectUtil.isNotEmpty(chapterContent)) {
                    Long chapterId = bookChapter.getChapterId();
                    Update update = new Update();
                    update.set("chapterId", chapterId);
                    update.set("content", chapterContent.getContent());
                    mongoService.updateOne(BOOK_CHAPTER_CONTENT, new Query(Criteria.where("chapterId").is(chapterId)), update, DtbBookChapterContent.class);
                }
            }

            // 添加文件夹
            List<DtbBookQuestionFolder> folderList = bookQuestionFolderMapper.selectList(new LambdaQueryWrapper<DtbBookQuestionFolder>()
                    .select(DtbBookQuestionFolder::getFolderId, DtbBookQuestionFolder::getFolderName, DtbBookQuestionFolder::getParentId, DtbBookQuestionFolder::getDefaultType)
                    .eq(DtbBookQuestionFolder::getBookId, bookId));
            if (ObjectUtil.isNotEmpty(folderList)) {
                // 保存全书题目文件
                for (DtbBookChapter bookChapter : chapterList) {
                    for (DtbBookQuestionFolder folder : folderList) {
                        List<DtbBookQuestion> bookQuestionList = bookQuestionMapper.queryBookQuestion(bookId, bookChapter.getSort(), folder.getFolderId());
                        bookQuestionList.stream().forEach(o -> {
                            o.setBookId(bookId);
                            o.setChapterId(bookChapter.getChapterId());
                            o.setFolderId(folder.getFolderId());
                        });
                        dtbBookQuestionService.saveBatch(bookQuestionList);
                    }
                }
            } else {
                // 添加文件夹
                DtbBookQuestionFolder bookQuestionFolder = new DtbBookQuestionFolder();
                bookQuestionFolder.setFolderName(getFolderName(8));
                bookQuestionFolder.setBookId(bookId);
                bookQuestionFolder.setParentId(0L);
                bookQuestionFolder.setDefaultType(1);
                bookQuestionFolderMapper.insert(bookQuestionFolder);
            }
        }

        // 发送消息
        // 编辑角色和系统管理员
        List<Long> adminIdList = bookCommonMapper.queryAdminUser();
        List<DtbBookGroup> groupList = bookGroupMapper.selectList(new LambdaQueryWrapper<DtbBookGroup>()
                .select(DtbBookGroup::getUserId)
                .eq(DtbBookGroup::getBookId, bookId)
                .in(DtbBookGroup::getRoleType, 5, 6));
        List<Long> editorIdList = null;
        if (groupList != null) {
            editorIdList = groupList.stream().map(DtbBookGroup::getUserId).collect(Collectors.toList());
        }
        List<Long> userIdList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(adminIdList)) {
            userIdList.addAll(adminIdList);
        }

        if (ObjectUtil.isNotEmpty(editorIdList)) {
            userIdList.addAll(editorIdList);
        }
        DtbBook bookInfo = dtbBookMapper.selectOne(new LambdaQueryWrapper<DtbBook>()
                .select(DtbBook::getBookName, DtbBook::getBookNo)
                .eq(DtbBook::getBookId, bookId));
        userIdList = userIdList.stream().distinct().collect(Collectors.toList());
        for (Long toUserId : userIdList) {
            DutpUserMessage dutpUserMessage = new DutpUserMessage();
            dutpUserMessage.setContent("您好，教材已修正，修正原因：" + dtbBook.getReason() + "。【教材名称：" + bookInfo.getBookName() + "；教材编号：" + bookInfo.getBookNo() + "】");
            dutpUserMessage.setTitle("教材修正提醒");
            dutpUserMessage.setFromUserId(userId);
            dutpUserMessage.setToUserId(toUserId);
            dutpUserMessage.setBusinessId(bookPublishProcess.getProcessId());
            dutpUserMessage.setMessageType(1);
            dutpUserMessage.setFromUserType(1);
            dutpUserMessage.setToUserType(1);
            remoteUserMessageService.addMessage(dutpUserMessage);
        }
        Long contact = bookGroupMapper.queryGroupUserByType(1, bookId);
        DutpUserMessage dutpUserMessage = new DutpUserMessage();
        dutpUserMessage.setContent("您好，教材已修正，已回退到发布节点，请尽快处理。【教材名称：" + bookInfo.getBookName() + ";教材编号：" + bookInfo.getBookNo() + "】");
        dutpUserMessage.setTitle("教材审核提醒");
        dutpUserMessage.setFromUserId(userId);
        dutpUserMessage.setToUserId(contact);
        dutpUserMessage.setBusinessId(bookPublishProcess.getProcessId());
        dutpUserMessage.setMessageType(1);
        dutpUserMessage.setFromUserType(1);
        dutpUserMessage.setToUserType(1);
        remoteUserMessageService.addMessage(dutpUserMessage);
        return dtbBookMapper.updateById(book);
    }

    @Override
    public DtbBook queryProfile(DtbBook dtbBook) {
        Long bookId = dtbBook.getBookId();
        LambdaQueryWrapper<DtbBook> dtbBookLambdaQueryWrapper = new LambdaQueryWrapper<>();
        dtbBookLambdaQueryWrapper.eq(DtbBook::getBookId, bookId);
        dtbBookLambdaQueryWrapper.select(DtbBook::getBookId, DtbBook::getDeptId, DtbBook::getBookType, DtbBook::getAuthorLabel,
                DtbBook::getAuthorValue, DtbBook::getTopSubjectId, DtbBook::getSecondSubjectId,
                DtbBook::getThirdSubjectId, DtbBook::getForthSubjectId);
        DtbBook book = this.getOne(dtbBookLambdaQueryWrapper);

        // 简介
        DtbBookAttribute bookAttribute = bookAttributeMapper.selectOne(new LambdaQueryWrapper<DtbBookAttribute>()
                .select(DtbBookAttribute::getBookId, DtbBookAttribute::getAttributeId,
                        DtbBookAttribute::getCopyright, DtbBookAttribute::getDeclaration,
                        DtbBookAttribute::getRecommend, DtbBookAttribute::getIntroduce)
                .eq(DtbBookAttribute::getBookId, bookId));
        if (ObjectUtil.isNotEmpty(bookAttribute)) {
            book.setCopyright(bookAttribute.getCopyright());
            book.setDeclaration(bookAttribute.getDeclaration());
            book.setRecommend(bookAttribute.getRecommend());
            book.setIntroduce(bookAttribute.getIntroduce());
        }

        // 部门
        if (ObjectUtil.isNotEmpty(book.getDeptId())) {
            String deptName = bookCommonMapper.queryDeptName(book.getDeptId());
            book.setDeptName(deptName);
        }

        // 中图分类
        Long bookType = book.getBookType();
        if (ObjectUtil.isNotEmpty(bookType)) {
            Long parentId = bookType;
            List<String> bookTypeStrList = new ArrayList<>(5);
            while (true) {
                if (parentId.longValue() == 0l) {
                    break;
                }
                DtbBookType dtbBookType = bookTypeMapper.getBookTypeById(parentId);
                parentId = dtbBookType.getParentId();
                bookTypeStrList.add(dtbBookType.getTypeName());
            }
            Collections.reverse(bookTypeStrList);
            book.setBookTypeName(bookTypeStrList.stream().collect(Collectors.joining(">")));
        }
        // 专区分类
        if (ObjectUtil.isNotEmpty(book.getBookId())) {
            List<DutpAreaVo> dutpBookAreaVoList = bookCommonMapper.queryAreaByBookId(book.getBookId());
            if (ObjectUtil.isNotEmpty(dutpBookAreaVoList)) {
                book.setAreaName(dutpBookAreaVoList.stream().map(DutpAreaVo::getAreaName).collect(Collectors.joining(",")));
            }

        }

        // 教育学科分类
        Long lastSubjectId = getLastSubjectId(book);
        if (ObjectUtil.isNotEmpty(lastSubjectId)) {
            Long parentId = lastSubjectId;
            List<String> subjectStrList = new ArrayList<>(5);
            while (true) {
                if (parentId.longValue() == 0l) {
                    break;
                }
                DutpSubjectVo dutpSubjectVo = bookCommonMapper.querySubjectBySubjectId(parentId);
                if (ObjectUtil.isNotEmpty(dutpSubjectVo)) {
                    parentId = dutpSubjectVo.getParentId();
                    subjectStrList.add(dutpSubjectVo.getSubjectName());
                } else {
                    parentId = 0l;
                }
            }
            Collections.reverse(subjectStrList);
            book.setSubjectName(subjectStrList.stream().collect(Collectors.joining(">")));
        }
        return book;
    }

    @Override
    public List<DtbBook> selectDtbBookListWithGroup(DtbBook dtbBook) {

        Long userId = SecurityUtils.getUserId();
        long superAdminId = 2l;
        if (userId==superAdminId){
           userId=null;
        }

        return dtbBookMapper.selectDtbBookListWithGroup(dtbBook, userId);
    }


    @Override
    public DtbBook queryCaptionStyle(DtbBook dtbBook) {
        return this.getOne(new LambdaQueryWrapper<DtbBook>()
                .select(DtbBook::getBookId, DtbBook::getTableNumberType, DtbBook::getImageNumberType)
                .eq(DtbBook::getBookId, dtbBook.getBookId()));
    }

    @Override
    public DtbBook searchOne(DtbBook dtbBook) {
        LambdaQueryWrapper<DtbBook> dtbBookLambdaQueryWrapper = new LambdaQueryWrapper<>();
        dtbBookLambdaQueryWrapper.eq(DtbBook::getBookNo, dtbBook.getBookNo());
        dtbBookLambdaQueryWrapper.eq(DtbBook::getBookOrganize, dtbBook.getBookOrganize());
        // dtbBookLambdaQueryWrapper.eq(DtbBook::getPublishStatus, 1);

        if (dtbBook.getMasterFlag().intValue() == 2) {
            // 主教材
            dtbBookLambdaQueryWrapper.eq(DtbBook::getMasterFlag, 3);
            // dtbBookLambdaQueryWrapper.isNull(DtbBook::getMasterBookId);
            dtbBookLambdaQueryWrapper.select(DtbBook::getBookId, DtbBook::getBookName,
                    DtbBook::getBookNo, DtbBook::getPublishStatus, DtbBook::getMasterBookId);
        } else if (dtbBook.getMasterFlag().intValue() == 3) {
            // 副教材
            dtbBookLambdaQueryWrapper.eq(DtbBook::getMasterFlag, 2);
            dtbBookLambdaQueryWrapper.select(DtbBook::getBookId, DtbBook::getBookName,
                    DtbBook::getBookNo, DtbBook::getIsbn, DtbBook::getIssn, DtbBook::getBookType,
                    DtbBook::getTopSubjectId, DtbBook::getSecondSubjectId, DtbBook::getThirdSubjectId,
                    DtbBook::getForthSubjectId, DtbBook::getPublishStatus);
        }
        DtbBook book = this.getOne(dtbBookLambdaQueryWrapper);
        if (ObjectUtil.isEmpty(book)) {
            throw new ServiceException("教材不存在");
        }
        if (book.getPublishStatus() == 2) {
            throw new ServiceException("教材已出版");
        }
        if (dtbBook.getMasterFlag() == 2 && book.getMasterBookId() != null) {
            DtbBook masterBook = this.getOne(new LambdaQueryWrapper<DtbBook>()
                    .select(DtbBook::getBookName)
                    .eq(DtbBook::getBookId, book.getMasterBookId()));
            throw new ServiceException("《" + book.getBookName() + "》副教材已关联《" + masterBook.getBookName() + "》主教材");
        }
        if (dtbBook.getMasterFlag().intValue() == 3) {
            // 副教材
            // 中图分类
            if (ObjectUtil.isNotEmpty(book.getBookType())) {
                DtbBookType bookType = bookTypeMapper.getBookTypeById(book.getBookType());
                book.setBookTypeName(bookType.getTypeName());
                // TODO 暂时展示一级
                // Long parentId = bookType.getParentId();
                // while (parentId.longValue() != 0l) {
                //     bookType = bookTypeMapper.getBookTypeById(parentId);
                //     parentId = bookType.getParentId();
                // }
            }

            if (ObjectUtil.isNotEmpty(book.getBookId())) {
                // 学校
                List<DutpBookSchoolVo> dutpSchoolVoList = bookCommonMapper.querySchoolByBookId(book.getBookId());
                book.setSchoolList(dutpSchoolVoList);
                // 专区分类
                List<DutpAreaVo> dutpBookAreaVoList = bookCommonMapper.queryAreaByBookId(book.getBookId());
                book.setAreaList(dutpBookAreaVoList);
            }

            // 教育学科分类
            Long lastSubjectId = getLastSubjectId(book);
            if (ObjectUtil.isNotEmpty(lastSubjectId)) {
                DutpSubjectVo dutpSubjectVo = bookCommonMapper.querySubjectBySubjectId(lastSubjectId);
                book.setSubject(dutpSubjectVo);
            }
        }
        return book;
    }

    @Override
    public int copyDtbBook(DtbBook dtbBook) {

        // 新起版本
        // 默认 1.0.0 版本
        DtbBookVersion bookVersion = insertInitVersion();
        Long oldBookId = dtbBook.getBookId();
        DtbBook oldBook = this.getById(oldBookId);
        DtbBook book = new DtbBook();
        book.setBookName(oldBook.getBookName() + "-副本");
        Long versionId = bookVersion.getVersionId();
        book.setLastVersionId(versionId);
        book.setCurrentVersionId(versionId);
        book.setShelfState(2);
        book.setPublishStatus(1);
        book.setCurrentStepId(1l);
        book.setBookNo(String.valueOf(System.currentTimeMillis()));
        book.setAuthorLabel(oldBook.getAuthorLabel());
        book.setAuthorValue(oldBook.getAuthorValue());
        book.setCover(oldBook.getCover());

        book.setPublishDate(oldBook.getPublishDate());
        book.setHouseId(oldBook.getHouseId());

        book.setMasterFlag(oldBook.getMasterFlag());
        // book.setMasterBookId(oldBook.getMasterBookId());
        // book.setSoldQuantity(oldBook.getSoldQuantity());
        // book.setReadQuantity(oldBook.getReadQuantity());
        book.setPriceCounter(oldBook.getPriceCounter());
        book.setPriceSale(oldBook.getPriceSale());
        book.setBookOrganize(oldBook.getBookOrganize());
        book.setTopicNo(oldBook.getTopicNo());
        book.setLanguageId(oldBook.getLanguageId());
        book.setEdition(oldBook.getEdition());
        book.setTableNumberType(oldBook.getTableNumberType());
        book.setImageNumberType(oldBook.getImageNumberType());
        book.setDeptId(oldBook.getDeptId());
        if (oldBook.getMasterFlag() != 3) {
            // 副教材不复制与主教材相关联的字段
            book.setTopSubjectId(oldBook.getTopSubjectId());
            book.setSecondSubjectId(oldBook.getSecondSubjectId());
            book.setThirdSubjectId(oldBook.getThirdSubjectId());
            book.setForthSubjectId(oldBook.getForthSubjectId());
            book.setBookType(oldBook.getBookType());
        }

        Boolean success = this.save(book);
        Long bookId = book.getBookId();

        // 更新版本ID
        bookVersionMapper.updateBookId(bookId, versionId);

        // 添加复制任务
        Long userId = SecurityUtils.getUserId();
        DutpTask dutpTask = new DutpTask();
        dutpTask.setTaskType(3);
        dutpTask.setTaskContent("教材复制");
        dutpTask.setDataId(bookId);
        dutpTask.setUserId(userId);
        dutpTask.setTaskRate(0);
        dutpTask.setTaskState(1);
        dutpTask.setStartTime(new Date());
        dutpTaskMapper.insert(dutpTask);

        // 全部复制
        final String[] errorMsg = new String[11];
        // 教材简介
        CompletableFuture<Void> copyBookInfo = CompletableFuture.runAsync(() -> {
            DtbBookAttribute oldBookAttribute = bookAttributeMapper.selectOne(new LambdaQueryWrapper<DtbBookAttribute>()
                    .select(DtbBookAttribute::getIntroduce, DtbBookAttribute::getCopyright, DtbBookAttribute::getDeclaration, DtbBookAttribute::getRecommend)
                    .eq(DtbBookAttribute::getBookId, oldBookId));
            if (ObjectUtil.isNotEmpty(oldBookAttribute)) {
                oldBookAttribute.setBookId(bookId);
                int save = bookAttributeMapper.insert(oldBookAttribute);
                if (save <= 0) {
                    throw new ServiceException();
                }
            }

        }, customExecutor).exceptionally(ex -> {
            log.error("复制教材简介失败，bookId: {}, 异常：{}", bookId, ex);
            errorMsg[0] = "复制教材简介失败";
            return null;
        });
        // 学校
        CompletableFuture<Void> copySchoolBookInfo = null;
        // 专区分类
        CompletableFuture<Void> copyAreaBookInfo = null;
        if (oldBook.getMasterFlag() != 3) {
            copySchoolBookInfo = CompletableFuture.runAsync(() -> {
                List<DutpBookSchoolVo> schoolList = bookCommonMapper.querySchoolIdListByBookId(oldBookId);
                if (ObjectUtil.isNotEmpty(schoolList)) {
                    int save = bookCommonMapper.batchAddSchool(schoolList, bookId);
                    if (save <= 0) {
                        throw new ServiceException();
                    }
                }

            }, customExecutor).exceptionally(ex -> {
                log.error("复制学校失败，bookId: {}, 异常：{}", bookId, ex);
                errorMsg[9] = "复制学校失败";
                return null;
            });
            copyAreaBookInfo = CompletableFuture.runAsync(() -> {
                List<DutpAreaVo> areaList = bookCommonMapper.queryAreaIdListByBookId(oldBookId);
                if (ObjectUtil.isNotEmpty(areaList)) {
                    int save = bookCommonMapper.batchAddArea(areaList, bookId);
                    if (save <= 0) {
                        throw new ServiceException();
                    }
                }
            }, customExecutor).exceptionally(ex -> {
                log.error("复制专区分类失败，bookId: {}, 异常：{}", bookId, ex);
                errorMsg[10] = "复制专区分类失败";
                return null;
            });
        }

        // 团队人员
        CompletableFuture<Void> copyBookGroup = CompletableFuture.runAsync(() -> {
            List<DtbBookGroup> oldDtbBookGroupList = bookGroupMapper.selectList(new LambdaQueryWrapper<DtbBookGroup>()
                    .select(DtbBookGroup::getRoleType, DtbBookGroup::getUserId)
                    .eq(DtbBookGroup::getBookId, oldBookId));
            if (ObjectUtil.isNotEmpty(oldDtbBookGroupList)) {
                oldDtbBookGroupList.stream().forEach(o -> o.setBookId(bookId));
                boolean save = dtbBookGroupService.saveBatch(oldDtbBookGroupList);
                if (!save) {
                    throw new ServiceException();
                }
            }

        }, customExecutor).exceptionally(ex -> {
            log.error("复制团队人员失败，bookId: {}, 异常：{}", bookId, ex);
            errorMsg[1] = "复制团队人员失败";
            return null;
        });

        // 资源包
        CompletableFuture<Void> copyBookResourcePackage = CompletableFuture.runAsync(() -> {
            List<DtbBookFile> oldBookFileList = bookFileService.list(new LambdaQueryWrapper<DtbBookFile>()
                    .select(DtbBookFile::getFileName, DtbBookFile::getFileUrl, DtbBookFile::getFileSize,
                            DtbBookFile::getDownload, DtbBookFile::getFileType)
                    .eq(DtbBookFile::getBookId, oldBookId));
            if (ObjectUtil.isNotEmpty(oldBookFileList)) {
                oldBookFileList.stream().forEach(o -> o.setBookId(bookId));
                boolean save = bookFileService.saveBatch(oldBookFileList);
                if (!save) {
                    throw new ServiceException();
                }
            }

        }, customExecutor).exceptionally(ex -> {
            log.error("复制教材资源包失败，bookId: {}, 异常：{}", bookId, ex);
            errorMsg[2] = "复制教材资源包失败";
            return null;
        });

        // 章节目录
        CompletableFuture<Void> copyBookChapter = CompletableFuture.runAsync(() -> {
            // 查询章节
            DtbBookChapter dtbBookChapter = new DtbBookChapter();
            dtbBookChapter.setBookId(oldBookId);
            List<DtbBookChapter> chapterList = dtbBookChapterMapper.queryCopyBookChapterMapper(dtbBookChapter);
            if (ObjectUtil.isNotEmpty(chapterList)) {
                chapterList.stream().forEach(o -> {
                    o.setBookId(bookId);
                    o.setVersionId(versionId);
                });
                boolean save = chapterService.saveBatch(chapterList);
                if (!save) {
                    throw new ServiceException();
                }
            }
            // 复制目录
            try {
                for (DtbBookChapter bookChapter : chapterList) {
                    List<DtbBookChapterCatalog> chapterCatalogList = chapterCatalogMapper.queryBookChapterCatalogTree(oldBookId, bookChapter.getSort());
                    if (ObjectUtil.isNotEmpty(chapterCatalogList)) {
                        chapterCatalogList.stream().forEach(o -> {
                            o.setBookId(bookId);
                            o.setChapterId(bookChapter.getChapterId());
                            o.setOldCatalogId(o.getCatalogId());
                            o.setVersionId(versionId);
                            o.setCatalogId(null);
                        });
                        chapterCatalogList = TreeUtil.makeTree(chapterCatalogList, x -> x.getParentId() == 0l, (x, y) -> x.getOldCatalogId().equals(y.getParentId()), DtbBookChapterCatalog::setChildren);
                        saveChapterCatalogTreeNode(chapterCatalogList, 0l);
                    }
                }
            } catch (Exception e) {
                log.error("目录复制失败，bookId: {}, 异常：{}", bookId, e);
            }

        }, customExecutor).exceptionally(ex -> {
            log.error("复制教材章节目录失败，bookId: {}, 异常：{}", bookId, ex);
            errorMsg[3] = "复制教材章节目录失败";
            return null;
        });


        CompletableFuture<Void> bookCopyFirstFutures = null;
        if (oldBook.getMasterFlag() == 3) {
            bookCopyFirstFutures = CompletableFuture.allOf(copyBookGroup,
                    copyBookInfo, copyBookResourcePackage, copyBookChapter);
        } else {
            bookCopyFirstFutures = CompletableFuture.allOf(copyBookGroup,
                    copyBookInfo, copyAreaBookInfo, copySchoolBookInfo, copyBookResourcePackage, copyBookChapter);
        }

        bookCopyFirstFutures.thenRun(() -> {
            // 章节复制失败，后续复制都将无法进行
            if (ObjectUtil.isNotEmpty(errorMsg[3])) {
                updateTaskAndSentMessage(book, userId, dutpTask, errorMsg);
                return;
            }
            // 查询章节
            DtbBookChapter dtbBookChapter = new DtbBookChapter();
            dtbBookChapter.setBookId(bookId);
            List<DtbBookChapter> chapterList = dtbBookChapterMapper.dtbBookChapterMapper(dtbBookChapter);


            // 编辑器内容资源
            CompletableFuture<Void> copyBookChapterContent = CompletableFuture.runAsync(() -> {
                if (ObjectUtil.isNotEmpty(chapterList)) {
                    for (DtbBookChapter bookChapter : chapterList) {
                        Long oldChapterId = dtbBookChapterMapper.queryChapterId(oldBookId, bookChapter.getSort());
                        // 查询章节内容
                        Query query = new Query(Criteria.where("chapterId").is(oldChapterId));
                        DtbBookChapterContent chapterContent = mongoService.findOne(BOOK_CHAPTER_CONTENT, query, DtbBookChapterContent.class);
                        if (ObjectUtil.isNotEmpty(chapterContent)) {
                            Long chapterId = bookChapter.getChapterId();
                            Update update = new Update();
                            update.set("chapterId", chapterId);
                            update.set("content", chapterContent.getContent());
                            mongoService.updateOne(BOOK_CHAPTER_CONTENT, new Query(Criteria.where("chapterId").is(chapterId)), update, DtbBookChapterContent.class);
                        }
                    }
                }

            }, customExecutor).exceptionally(ex -> {
                log.error("复制编辑器内容资源失败，bookId: {}, 异常：", bookId, ex);
                errorMsg[4] = "复制编辑器内容资源失败";
                return null;
            });

            // 资源库-教材资源
            CompletableFuture<Void> copyBookResource = CompletableFuture.runAsync(() -> {
                Boolean save = generateResourceFolder(bookId);
                if (!save) {
                    throw new ServiceException();
                }
            }, customExecutor).exceptionally(ex -> {
                log.error("复制资源库-教材资源失败，bookId: {}, 异常：", bookId, ex);
                errorMsg[5] = "复制资源库-教材资源失败";
                return null;
            });

            // 数据总览
            CompletableFuture<Void> copyBookDataOverview = CompletableFuture.runAsync(() -> {
                List<DtbBookChapterData> dataList = new ArrayList<>();
                if (ObjectUtil.isNotEmpty(chapterList)) {
                    for (DtbBookChapter bookChapter : chapterList) {
                        DtbBookChapterData bookChapterData = chapterDataMapper.queryChapterDataByChapterSortAndBookId(oldBookId, bookChapter.getSort());
                        if (ObjectUtil.isNotEmpty(bookChapterData)) {
                            bookChapterData.setDataId(null);
                            bookChapterData.setBookId(bookId);
                            bookChapterData.setChapterId(bookChapter.getChapterId());
                            bookChapterData.setCreateBy(null);
                            bookChapterData.setCreateTime(null);
                            bookChapterData.setUpdateBy(null);
                            bookChapterData.setUpdateTime(null);
                            bookChapterData.setStudyTotalTime(null);
                            bookChapterData.setStudyVideoTime(null);
                            bookChapterData.setStudyNoteQuantity(null);
                            bookChapterData.setStudyHighlightQuantity(null);
                            bookChapterData.setStudyDiscussQuantity(null);
                            bookChapterData.setStudyUserQuantity(null);
                            bookChapterData.setStudyQuestionQuantity(null);
                            bookChapterData.setStudyQuestionRate(BigDecimal.ZERO);

                            dataList.add(bookChapterData);
                        }
                    }
                    if (ObjectUtil.isNotEmpty(dataList)) {
                        Boolean save = chapterDataService.saveBatch(dataList);
                        if (!save) {
                            throw new ServiceException();
                        }
                    }

                }

            }, customExecutor).exceptionally(ex -> {
                log.error("复制数据总览失败，bookId: {}, 异常：", bookId, ex);
                errorMsg[6] = "复制数据总览失败";
                return null;
            });

            // 章节编写者和查看者
            CompletableFuture<Void> copyBookEditorAndViewer = CompletableFuture.runAsync(() -> {
                if (ObjectUtil.isNotEmpty(chapterList)) {
                    for (DtbBookChapter bookChapter : chapterList) {
                        List<DtbBookChapterEditor> chapterEditorList = chapterEditorMapper.queryBookChapterEditor(oldBookId, bookChapter.getSort());
                        if (ObjectUtil.isNotEmpty(chapterEditorList)) {
                            // 编辑者
                            List<Long> editorIds = new ArrayList<>();
                            // 查看者
                            List<Long> viewerIds = new ArrayList<>();
                            for (DtbBookChapterEditor dtbBookChapterEditor : chapterEditorList) {
                                if (dtbBookChapterEditor.getRoleType() == 1) {
                                    editorIds.add(dtbBookChapterEditor.getUserId());
                                } else if (dtbBookChapterEditor.getRoleType() == 2) {
                                    viewerIds.add(dtbBookChapterEditor.getUserId());
                                }
                            }
                            if (ObjectUtil.isNotEmpty(editorIds)) {
                                chapterEditorMapper.batchSave(editorIds, bookId, bookChapter.getChapterId(), 1);
                                // 新增编辑发送消息
                                sentMessageForEditor(editorIds, bookChapter.getChapterId(), userId);
                            }
                            if (ObjectUtil.isNotEmpty(viewerIds)) {
                                chapterEditorMapper.batchSave(viewerIds, bookId, bookChapter.getChapterId(), 2);
                            }
                        }

                    }
                }

            }, customExecutor).exceptionally(ex -> {
                log.error("复制章节编写者和查看者失败，bookId: {}, 异常：", bookId, ex);
                errorMsg[7] = "复制章节编写者和查看者失败";
                return null;
            });

            // 题库
            CompletableFuture<Void> copyBookQuestion = CompletableFuture.runAsync(() -> {
                // 添加文件夹
                List<DtbBookQuestionFolder> folderList = bookQuestionFolderMapper.selectList(new LambdaQueryWrapper<DtbBookQuestionFolder>()
                        .select(DtbBookQuestionFolder::getFolderId, DtbBookQuestionFolder::getFolderName, DtbBookQuestionFolder::getParentId, DtbBookQuestionFolder::getDefaultType)
                        .eq(DtbBookQuestionFolder::getBookId, oldBookId));
                if (ObjectUtil.isNotEmpty(folderList)) {
                    folderList.stream().forEach(o -> {
                        o.setBookId(bookId);
                        o.setOldFolderId(o.getFolderId());
                        o.setFolderId(null);
                    });
                    folderList = TreeUtil.makeTree(folderList, x -> x.getParentId() == 0l, (x, y) -> x.getOldFolderId().equals(y.getParentId()), DtbBookQuestionFolder::setChildren);
                    saveQuestionTreeNode(folderList, 0l);
                    folderList = TreeUtil.flat(folderList, DtbBookQuestionFolder::getChildren, x -> x.setChildren(null));
                }

                Boolean save = true;

                if (ObjectUtil.isNotEmpty(chapterList) && ObjectUtil.isNotEmpty(folderList)) {
                    // 保存全书题目文件
                    for (DtbBookChapter bookChapter : chapterList) {
                        for (DtbBookQuestionFolder folder : folderList) {
                            List<DtbBookQuestion> bookQuestionList = bookQuestionMapper.queryBookQuestion(oldBookId, bookChapter.getSort(), folder.getOldFolderId());
                            bookQuestionList.stream().forEach(o -> {
                                o.setBookId(bookId);
                                o.setChapterId(bookChapter.getChapterId());
                                o.setFolderId(folder.getFolderId());
                            });
                            save = save & dtbBookQuestionService.saveBatch(bookQuestionList);
                        }
                    }
                    if (!save) {
                        throw new ServiceException();
                    }
                } else {
                    // 添加文件夹
                    DtbBookQuestionFolder bookQuestionFolder = new DtbBookQuestionFolder();
                    bookQuestionFolder.setFolderName(getFolderName(8));
                    bookQuestionFolder.setBookId(bookId);
                    bookQuestionFolder.setParentId(0L);
                    bookQuestionFolder.setDefaultType(1);
                    int save1 = bookQuestionFolderMapper.insert(bookQuestionFolder);
                    if (save1 <= 0) {
                        throw new ServiceException();
                    }
                }

            }, customExecutor).exceptionally(ex -> {
                log.error("复制题库失败，bookId: {}, 异常：", bookId, ex);
                errorMsg[8] = "复制题库失败";
                return null;
            });

            CompletableFuture<Void> bookCopySecondFutures = CompletableFuture.allOf(copyBookQuestion, copyBookEditorAndViewer, copyBookChapterContent, copyBookResource, copyBookDataOverview);
            bookCopySecondFutures.thenRun(() -> {
                log.info("所有复制任务完成：bookId: {}", bookId);
                // 更新任务结果
                updateTaskAndSentMessage(book, userId, dutpTask, errorMsg);
            });

        });
        return success ? 1 : 0;
    }

    /**
     * 保存树形结果
     *
     * @param dataList
     * @param parentId
     */
    private void saveQuestionTreeNode(List<DtbBookQuestionFolder> dataList, Long parentId) {
        for (DtbBookQuestionFolder folder : dataList) {
            folder.setParentId(parentId);
        }
        dtbBookQuestionFolderService.saveBatch(dataList);
        for (DtbBookQuestionFolder folder : dataList) {
            saveQuestionTreeNode(folder.getChildren(), folder.getFolderId());
        }
    }

    /**
     * 发送消息新增编辑
     *
     * @param sentMessageEditors
     * @param chapterId
     */
    private void sentMessageForEditor(List<Long> sentMessageEditors, Long chapterId, Long fromUserId) {
        if (ObjectUtil.isEmpty(sentMessageEditors)) {
            return;
        }
        DtbBookChapter chapter = dtbBookChapterMapper.queryBookByChapterId(chapterId);
        for (Long userId : sentMessageEditors) {
            DutpUserMessage dutpUserMessage = new DutpUserMessage();
            dutpUserMessage.setContent("您好，您已有本章编写权限。【章节名称：" + chapter.getChapterName() + "; 教材名称：" +
                    chapter.getBookName() + ";教材编号：" + chapter.getBookNo() + "】。");
            dutpUserMessage.setTitle("教材编写任务分配提醒");
            dutpUserMessage.setFromUserId(fromUserId);
            dutpUserMessage.setToUserId(userId);
            dutpUserMessage.setMessageType(1);
            dutpUserMessage.setFromUserType(1);
            dutpUserMessage.setToUserType(1);
            remoteUserMessageService.addMessage(dutpUserMessage);
        }
    }

    /**
     * 新增资源文件夹
     *
     * @param bookId
     * @return
     */
    @NotNull
    private Boolean generateResourceFolder(Long bookId) {
        // 新增文件夹
        List<DtbBookResourceFolder> folderList = new ArrayList<>();
        for (int i = 1; i <= 8; i++) {
            DtbBookResourceFolder dtbBookResourceFolder = new DtbBookResourceFolder();
            dtbBookResourceFolder.setDefaultType(String.valueOf(i));
            dtbBookResourceFolder.setFolderName(getFolderName(i));
            dtbBookResourceFolder.setParentId(0L);
            dtbBookResourceFolder.setBookId(bookId);
            folderList.add(dtbBookResourceFolder);
        }
        Boolean save = dtbBookResourceFolderService.saveBatch(folderList);
        return save;
    }

    /**
     * 保存树形结果
     *
     * @param dataList
     * @param parentId
     */
    private void saveChapterCatalogTreeNode(List<DtbBookChapterCatalog> dataList, Long parentId) {
        for (DtbBookChapterCatalog chapterCatalog : dataList) {
            chapterCatalog.setParentId(parentId);
        }
        chapterCatalogService.saveBatch(dataList);
        for (DtbBookChapterCatalog chapterCatalog : dataList) {
            saveChapterCatalogTreeNode(chapterCatalog.getChildren(), chapterCatalog.getCatalogId());
        }
    }

    /**
     * 更新任务结果和发送消息
     *
     * @param book
     * @param userId
     * @param dutpTask
     * @param errorMsg
     */
    private void updateTaskAndSentMessage(DtbBook book, Long userId, DutpTask dutpTask, String[] errorMsg) {
        // 更新任务结果
        Long taskId = dutpTask.getTaskId();
        DutpTask task = new DutpTask();
        task.setTaskId(taskId);
        task.setTaskRate(100);
        task.setEndTime(new Date());
        task.setTaskState(2);
        dutpTaskMapper.updateById(task);

        // 发送消息
        DutpUserMessage dutpUserMessage = new DutpUserMessage();
        StringJoiner joiner = new StringJoiner(",");
        for (String s : errorMsg) {
            if (ObjectUtil.isNotEmpty(s)) {
                joiner.add(s);
            }
        }
        dutpUserMessage.setContent("您好，教材复制任务已完成，复制的新教材名称为“" + book.getBookName() + "”教材编号：“" + book.getBookNo() + "“,请到" +
                "【教材管理-教材列表】内查看；" + joiner);
        dutpUserMessage.setTitle("任务提醒");
        dutpUserMessage.setToUserId(userId);
        dutpUserMessage.setMessageType(1);
        dutpUserMessage.setFromUserType(1);
        dutpUserMessage.setToUserType(1);
        remoteUserMessageService.addMessage(dutpUserMessage);
    }

    /**
     * 获取最后一级教育学科分类ID
     *
     * @param book
     * @return
     */
    private Long getLastSubjectId(DtbBook book) {
        if (ObjectUtil.isNotEmpty(book.getForthSubjectId())) {
            return book.getForthSubjectId();
        }
        if (ObjectUtil.isNotEmpty(book.getThirdSubjectId())) {
            return book.getThirdSubjectId();
        }
        if (ObjectUtil.isNotEmpty(book.getSecondSubjectId())) {
            return book.getSecondSubjectId();
        }
        if (ObjectUtil.isNotEmpty(book.getTopSubjectId())) {
            return book.getTopSubjectId();
        }
        return null;
    }

    /**
     * 根据bookId 查询出所有副教材 （发型管理)
     */
    @Override
    public List<DtbBook> listBookNature(List<Long> bookIds) {
        LambdaQueryWrapper<DtbBook> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.in(DtbBook::getMasterBookId, bookIds);
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 召回数字教材(发行管理)
     *
     * @param dtbBook
     * @return
     */
    @Override
    @Transactional
    public boolean recallDtbBook(DtbBook dtbBook) {
        Long bookId = dtbBook.getBookId();
        Long userId = SecurityUtils.getUserId();

        // 新增召回历史
        DtbBookRecallAmend dtbBookRecallAmend = new DtbBookRecallAmend();
        dtbBookRecallAmend.setBookId(bookId);
        dtbBookRecallAmend.setRecallType(2);
        dtbBookRecallAmend.setVersionId(dtbBook.getCurrentVersionId());
        dtbBookRecallAmend.setUserId(SecurityUtils.getUserId());
        dtbBookRecallAmend.setReason(dtbBook.getReason());
        recallAmendMapper.insert(dtbBookRecallAmend);

        // 更新状态为召回
        DtbBook book = dtbBookMapper.selectById(bookId);
        book.setShelfState(3);
        int row = dtbBookMapper.updateById(book);
        Boolean success = row > 0 ? true : false;

        // 关联的购书码更新为已作废
        DtbBookPurchaseCode dtbBookPurchaseCode = new DtbBookPurchaseCode();
        LambdaUpdateWrapper<DtbBookPurchaseCode> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(DtbBookPurchaseCode::getState,5);
        updateWrapper.eq(DtbBookPurchaseCode::getBookId,bookId);
        updateWrapper.eq(DtbBookPurchaseCode::getState,1);
        dtbPurchaseCodeMapper.update(dtbBookPurchaseCode,updateWrapper);

        // 查询副教材 同时修改召回状态  2025/5/8 新增需求 关联副教材只召回已发版的
        List<DtbBook> masterBookList = dtbBookMapper.selectList(new LambdaQueryWrapper<DtbBook>()
                .eq(DtbBook::getPublishStatus, 1)
                .eq(DtbBook::getMasterBookId, bookId)
                .eq(DtbBook::getDelFlag, 0));

        // 提取 bookId 列表
        List<Long> masterBookIds = masterBookList.stream()
                .map(DtbBook::getBookId)
                .collect(Collectors.toList());

        // 批量更新 shelfState
        if (!masterBookIds.isEmpty()) {
            DtbBook masterBook = new DtbBook();
            dtbBookMapper.update(masterBook, new LambdaUpdateWrapper<DtbBook>()
                    .in(DtbBook::getBookId, masterBookIds)
                    .set(DtbBook::getShelfState, 3));
        }

        // 更新ES
        if (success) {
            // 删除
            esDtbBooksMapper.deleteById(bookId);
        }

        // 发送消息
        if (success) {
            DtbBook finalBook = book;
            // 拼接副教材名称
            String masterBookName = "";
            for (DtbBook masterBook : masterBookList) {
                masterBookName += "《" + masterBook.getBookName() + "》、";
            }

            // 发送给教材编辑角色
            List<DtbBookGroup> dtbBookGroupList = dtbBookGroupService.list(new LambdaQueryWrapper<DtbBookGroup>().eq(DtbBookGroup::getBookId, finalBook.getBookId()));
            if (!CollectionUtils.isEmpty(dtbBookGroupList)) {
                String finalMasterBookName = StringUtils.isEmpty(masterBookName) ? "" : "及" + masterBookName.substring(0, masterBookName.length() - 1);
                dtbBookGroupList.stream()
                        .map(DtbBookGroup::getUserId) // 提取 userId
                        .distinct() // 去重
                        .forEach(toUserId -> {
                            DutpUserMessage dutpUserMessage = new DutpUserMessage();
                            dutpUserMessage.setContent("您好，《" + finalBook.getBookName() + "》" + finalMasterBookName + "已被召回，召回原因：" + dtbBook.getReason() + "。");
                            dutpUserMessage.setTitle("教材召回提醒");
                            dutpUserMessage.setFromUserId(userId);
                            dutpUserMessage.setToUserId(toUserId);
                            dutpUserMessage.setMessageType(1);
                            dutpUserMessage.setFromUserType(1);
                            dutpUserMessage.setToUserType(1);
                            remoteUserMessageService.addMessage(dutpUserMessage);
                        });
            }

            // 发送给系统管理员
            R<List<SysUser>> adminIdList = remoteUserService.listUserByRoleId(2L, SecurityConstants.FROM_SOURCE);
            if (R.FAIL == adminIdList.getCode()) {
                throw new ServiceException(adminIdList.getMsg());
            }
            if (!CollectionUtils.isEmpty(adminIdList.getData())) {
                String finalMasterBookName = StringUtils.isEmpty(masterBookName) ? "" : "及" + masterBookName.substring(0, masterBookName.length() - 1);
                adminIdList.getData().forEach(admin -> {
                    DutpUserMessage dutpUserMessage = new DutpUserMessage();
                    dutpUserMessage.setContent("您好，《" + finalBook.getBookName() + "》" + finalMasterBookName + "已被召回，召回原因：" + dtbBook.getReason() + "。");
                    dutpUserMessage.setTitle("教材召回提醒");
                    dutpUserMessage.setFromUserId(userId);
                    dutpUserMessage.setToUserId(admin.getUserId());
                    dutpUserMessage.setMessageType(1);
                    dutpUserMessage.setFromUserType(1);
                    dutpUserMessage.setToUserType(1);
                    remoteUserMessageService.addMessage(dutpUserMessage);
                });
            }

            // 查询购买书籍的前台用户
            List<DtbUserBook> dtbUserBookList = dtbUserBookService.list(new LambdaQueryWrapper<DtbUserBook>().eq(DtbUserBook::getBookId, bookId).gt(DtbUserBook::getExpireDate, new Date()));
            dtbUserBookList.forEach(user -> {
                DutpUserMessage dutpUserMessage = new DutpUserMessage();
                dutpUserMessage.setContent("亲爱的用户，《" + finalBook.getBookName() + "》已被召回，无法阅读，请您尽快提交退款申请进行退款操作。");
                dutpUserMessage.setTitle("教材召回提醒");
                dutpUserMessage.setFromUserId(userId);
                dutpUserMessage.setToUserId(user.getUserId());
                dutpUserMessage.setMessageType(3);
                dutpUserMessage.setFromUserType(1);
                dutpUserMessage.setToUserType(2);
                remoteUserMessageService.addMessage(dutpUserMessage);
            });
        }
        return success;
    }

    /**
     * 上下架数字教材(发行管理)
     *
     * @param dtbBook
     * @return
     */
    @Override
    public boolean shelfDtbBook(DtbBook dtbBook) {
        Long bookId = dtbBook.getBookId();
        Integer shelfState = dtbBook.getShelfState();

        // 更新上下架状态
        dtbBook.setShelfState(shelfState);
        dtbBook.setBookId(bookId);
        if (shelfState == 1) {
            dtbBook.setShelfTime(new Date());
        } else if (shelfState == 2) {
            dtbBook.setUnshelfTime(new Date());
        }
        int row = dtbBookMapper.updateById(dtbBook);
        // 查询子教材，处理相应逻辑
        QueryWrapper<DtbBook> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DtbBook::getMasterBookId, bookId).eq(DtbBook::getMasterFlag, 3)
                .eq(DtbBook::getMasterBookId, dtbBook.getBookId()).eq(DtbBook::getPublishStatus, 2);
        List<DtbBook> dtbBooks = dtbBookMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(dtbBooks)) {
            UpdateWrapper<DtbBook> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(DtbBook::getMasterBookId, bookId)
                    .set(DtbBook::getShelfState, shelfState)
                    .set(DtbBook::getUpdateTime, new Date())
                    .set(DtbBook::getUpdateBy, SecurityUtils.getUsername());
            if (shelfState == 1) {
                updateWrapper.lambda().set(DtbBook::getShelfTime, new Date());
            } else if (shelfState == 2) {
                updateWrapper.lambda().set(DtbBook::getUnshelfTime, new Date());
            }
            dtbBookMapper.update(null, updateWrapper);
        }
        Boolean success = row > 0 ? true : false;

        // 更新ES
        if (success) {
            DtbBook book = new DtbBook();
            book = dtbBookMapper.selectById(bookId);
            // 更新es
            if (book.getShelfState() == 2) {
                // 下架删除
                esDtbBooksMapper.deleteById(bookId);

            } else if (book.getShelfState() == 1) {
                // 上架新增
                EsDtbBook esDtbBook = generateEsDtbBook(book);
                esDtbBooksMapper.insert(esDtbBook);
            }
        }
        return success;
    }

    @Override
    public int finalizedSubmit(DtbBook dtbBook) {
        Long bookId = dtbBook.getBookId();
        if (ObjectUtil.isEmpty(bookId)) {
            throw new ServiceException("教材不存在");
        }

        // 校验操作人
        Long userId = SecurityUtils.getUserId();
        Long contact = bookGroupMapper.queryGroupUserByType(1, bookId);
        if (ObjectUtil.isEmpty(contact)) {
            throw new ServiceException("书稿联系人不存在");
        }
        if (!contact.equals(userId)) {
            log.error("违法操作，用户违法定稿提交：{}", userId);
            throw new ServiceException("你不是书稿联系人，不能进行此操作");
        }

        // 校验章节审核状态
        List<DtbBookChapter> dtbBookChapterList = dtbBookChapterMapper.listForFinalizedSubmit(dtbBook);
        dtbBookChapterList = dtbBookChapterList.stream().filter(o -> o.getChapterStatus() != 2).collect(Collectors.toList());
        if (dtbBookChapterList.size() > 0) {
            log.error("定稿提交失败，还存在章节不是已通过状态，当前操作人：{}", userId);
            throw new ServiceException("还存在章节不是已通过状态，不允许提交");
        }

        // 校验教材状态
        // 查询当前教材处于的最后一个流程节点
        DtbBookPublishProcess bookPublishProcess = bookPublishProcessMapper.queryBookLastProcess(bookId);
        if (ObjectUtil.isNotEmpty(bookPublishProcess.getProcessId())) {
            if (bookPublishProcess.getStepId() != 2) {
                throw new ServiceException("当前教材不是来稿确认环节");
            }
            if (bookPublishProcess.getState() == 1) {
                throw new ServiceException("策划编辑正在审核，未审核完成不能提交");
            }
        }

        // 更新状态
        DtbBookPublishProcess publishProcess = new DtbBookPublishProcess();
        publishProcess.setBookId(bookId);
        publishProcess.setStepId(2l);
        publishProcess.setState(1);
        publishProcess.setVersionId(bookPublishProcess.getVersionId());
        publishProcess.setPromoterUserId(userId);
        publishProcess.setPrevProcessId(bookPublishProcess.getProcessId());
        int row = bookPublishProcessMapper.insert(publishProcess);

        if (row > 0) {
            // 更新教材当前节点
            DtbBook book = new DtbBook();
            book.setBookId(bookId);
            book.setCurrentStepId(2l);
            dtbBookMapper.updateById(book);

            // 更新审核人
            // 策划编辑
            Long planningEditor = bookGroupMapper.queryGroupUserByType(5, bookId);
            DtbBookPublishProcessAuditUser auditUser = new DtbBookPublishProcessAuditUser();
            auditUser.setUserId(planningEditor);
            auditUser.setState(1);
            auditUser.setProcessId(publishProcess.getProcessId());
            publishProcessAuditUserMapper.insert(auditUser);

            DtbBook bookInfo = dtbBookMapper.selectOne(new LambdaQueryWrapper<DtbBook>()
                    .select(DtbBook::getBookName, DtbBook::getBookNo)
                    .eq(DtbBook::getBookId, bookId));

            // 发送消息
            String nickName = bookCommonMapper.queryNickNameByUserId(userId);
            DutpUserMessage dutpUserMessage = new DutpUserMessage();
            dutpUserMessage.setContent("您好，书稿联系人“" + nickName + "”已提交申请，请尽快处理。【教材名称：" +
                    bookInfo.getBookName() + ";教材编号：" + bookInfo.getBookNo() + "】");
            dutpUserMessage.setTitle("教材审核提醒");
            dutpUserMessage.setFromUserId(userId);
            dutpUserMessage.setToUserId(planningEditor);
            dutpUserMessage.setMessageType(1);
            dutpUserMessage.setFromUserType(1);
            dutpUserMessage.setToUserType(1);
            remoteUserMessageService.addMessage(dutpUserMessage);

            // 导出教材
            bookExportTask(userId, Arrays.asList(bookId));
        }
        return row;
    }

    @Override
    public List<DtbBook> listOfAuthorAndEditor(DtbBook dtbBook) {
        Long userId = SecurityUtils.getUserId();
        List<DtbBook> dtbBookList = dtbBookMapper.listOfAuthorAndEditor(dtbBook);
        for (DtbBook book : dtbBookList) {

            // 计算完成度
            completionCalculation(book);

            // 判断定稿提交是否驳回
            if (book.getStepId() == 2) {
                DtbBookPublishProcess bookPublishProcess = bookPublishProcessMapper.queryBookLastProcess(book.getBookId());
                book.setAuditState(bookPublishProcess.getState());
            } else if (book.getStepId() > 2) {
                book.setAuditState(0);
            }

            book.setIsShowAmendBtn(true);
            // 检测是否修改版本
            if (book.getCurrentVersionId().longValue() != book.getLastVersionId().longValue()) {
                DtbBook amendBook = new DtbBook();
                BeanUtil.copyProperties(book, amendBook);
                amendBook.setVersionNo(amendBook.getAmendVersionNo());
                amendBook.setCreateTime(amendBook.getAmendCreateTime());
                book.setLastVersionId(book.getCurrentVersionId());
                List<DtbBook> children = new ArrayList<>(1);
                children.add(amendBook);
                book.setChildren(children);
            } else {
                Integer isEditor = bookGroupMapper.checkIsEditor(userId, book.getBookId());
                if (isEditor == 0) {
                    book.setIsShowAmendBtn(false);
                }
            }
        }
        return dtbBookList;
    }

    @Override
    public boolean editCaptionStyle(DtbBook dtbBook) {
        // 检测今日是否存在相同任务
        DutpTask dutpTask = dutpTaskMapper.selectTask(6, dtbBook.getBookId(), new Date());
        if (ObjectUtil.isNotEmpty(dutpTask)) {
            throw new ServiceException("当天已存在更新题注任务");
        }
        boolean success = this.updateById(dtbBook);
        if (success) {
            // 添加任务
            Long userId = SecurityUtils.getUserId();
            dutpTask = new DutpTask();
            dutpTask.setTaskType(6);
            dutpTask.setTaskContent("更新题注");
            dutpTask.setDataId(dtbBook.getBookId());
            dutpTask.setUserId(userId);
            dutpTask.setTaskRate(0);
            dutpTask.setTaskState(0);
            dutpTaskMapper.insert(dutpTask);
        }
        return success;
    }

    @Override
    public AjaxResult getBooks(Long bookId) {
        List<HashMap<String, Object>> result = new ArrayList<>();
        DtbBook dtbBook = dtbBookMapper.selectById(bookId);
        // 有主副教材
        if (dtbBook.getMasterFlag().intValue() == 1) {
            HashMap<String, Object> bookMap = new HashMap<>();
            bookMap.put("bookId", String.valueOf(dtbBook.getBookId()));
            bookMap.put("bookName", dtbBook.getBookName());
            bookMap.put("masterFlag", dtbBook.getMasterFlag());
            result.add(bookMap);
            // 主教材
        } else if (dtbBook.getMasterFlag().intValue() == 2) {
            HashMap<String, Object> bookMap = new HashMap<>();
            bookMap.put("bookId", String.valueOf(dtbBook.getBookId()));
            bookMap.put("bookName", dtbBook.getBookName());
            bookMap.put("masterFlag", dtbBook.getMasterFlag());
            result.add(bookMap);
            LambdaQueryWrapper<DtbBook> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(DtbBook::getMasterBookId, bookId);
            lambdaQueryWrapper.eq(DtbBook::getPublishStatus, 2);
            List<DtbBook> children = dtbBookMapper.selectList(lambdaQueryWrapper);
            for (DtbBook child : children) {
                HashMap<String, Object> childBookMap = new HashMap<>();
                childBookMap.put("bookId", String.valueOf(child.getBookId()));
                childBookMap.put("bookName", child.getBookName());
                childBookMap.put("masterFlag", child.getMasterFlag());
                result.add(childBookMap);
            }
            // 副教材
        } else {
            HashMap<String, Object> bookMap = new HashMap<>();
            bookMap.put("bookId", String.valueOf(dtbBook.getBookId()));
            bookMap.put("bookName", dtbBook.getBookName());
            bookMap.put("masterFlag", dtbBook.getMasterFlag());
            result.add(bookMap);

            // 查询关联的副教材
            DtbBook fatherDtbBook = dtbBookMapper.selectById(dtbBook.getMasterBookId());
            bookMap = new HashMap<>();
            bookMap.put("bookId", String.valueOf(fatherDtbBook.getBookId()));
            bookMap.put("bookName", fatherDtbBook.getBookName());
            bookMap.put("masterFlag", fatherDtbBook.getMasterFlag());

            result.add(bookMap);

            // 查询同级的其他副教材
            LambdaQueryWrapper<DtbBook> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(DtbBook::getMasterBookId, dtbBook.getMasterBookId());
            lambdaQueryWrapper.eq(DtbBook::getPublishStatus, 2);
            lambdaQueryWrapper.notIn(DtbBook::getBookId, dtbBook.getBookId());
            lambdaQueryWrapper.eq(DtbBook::getDelFlag, 0);
            List<DtbBook> otherMasterBook = dtbBookMapper.selectList(lambdaQueryWrapper);
            if (otherMasterBook != null) {
                for (DtbBook otherMasterBookItem : otherMasterBook) {
                    bookMap = new HashMap<>();
                    bookMap.put("bookId", String.valueOf(otherMasterBookItem.getBookId()));
                    bookMap.put("bookName", otherMasterBookItem.getBookName());
                    bookMap.put("masterFlag", otherMasterBookItem.getMasterFlag());
                    result.add(bookMap);
                }
            }
        }
        return AjaxResult.success(result);
    }

    @Override
    public int importBook(MultipartFile file, Long bookId) {
        if (ObjectUtil.isEmpty(file) || ObjectUtil.isEmpty(bookId)) {
            throw new ServiceException("word文件或者bookId为空");
        }

        String fileName = file.getOriginalFilename();
        if (!fileName.endsWith(".docx")) {
            throw new ServiceException("文件格式不正确，请上传.docx文件");
        }

        InputStream wordFileInputStream = null;
        try {
            wordFileInputStream = file.getInputStream();
        } catch (IOException e) {
            log.info("读取文件流异常，", e);
            throw new ServiceException("读取文件错误");
        }

        DtbBook book = dtbBookMapper.selectOne(new LambdaQueryWrapper<DtbBook>()
                .select(DtbBook::getBookId, DtbBook::getBookName, DtbBook::getBookNo, DtbBook::getCurrentVersionId)
                .eq(DtbBook::getBookId, bookId));

        if (ObjectUtil.isEmpty(book)) {
            throw new ServiceException("教材不存在");
        }

        Long userId = SecurityUtils.getUserId();
        // 生成任务
        DutpTask dutpTask = new DutpTask();
        dutpTask.setTaskType(2);
        dutpTask.setTaskContent("导入教材内容");
        dutpTask.setDataId(bookId);
        dutpTask.setUserId(userId);
        dutpTask.setTaskRate(0);
        dutpTask.setStartTime(new Date());
        dutpTask.setTaskState(1);
        int success = dutpTaskMapper.insert(dutpTask);

        Long templateId = dtbBookChapterMapper.queryTemplateIdByBookId(bookId);

        // 新开线程执行任务
        InputStream finalWordFileInputStream = wordFileInputStream;
        CompletableFuture.runAsync(() -> {
            List<Map<String, Object>> docList = null;
            try {
                docList = docxToJsonConverter.analyzeWord(finalWordFileInputStream);
            } catch (ServiceException e) {
                throw e;
            } catch (Exception e) {
                log.error("导入教材章节失败，bookId: {}, 异常：{}", bookId, e.getMessage());
            }
            if (ObjectUtil.isEmpty(docList)) {
                throw new ServiceException("解析失败");
            }
            Integer sort = dtbBookChapterMapper.queryMaxSort(bookId);
            if (ObjectUtil.isEmpty(sort)) {
                sort = 1;
            }
            for (Map<String, Object> doc : docList) {

                List<Object> pageList = (List<Object>) doc.get("content");
                if (ObjectUtil.isEmpty(pageList)) {
                    throw new ServiceException("导入教材失败");
                }
                Map<String, Object> page = (Map<String, Object>) pageList.get(0);
                List<Object> contentList = (List<Object>) page.get("content");

                String chapterName = "空章节";
                if (ObjectUtil.isNotEmpty(contentList)) {
                    Map<String, Object> headingNode = (Map<String, Object>) contentList.get(0);
                    String headingType = (String) headingNode.get("type");
                    if ("heading".equals(headingType)) {
                        List<Object> textNodeList = (List<Object>) headingNode.get("content");
                        if (ObjectUtil.isNotEmpty(textNodeList)) {
                            chapterName = "";
                            for (Object node : textNodeList) {
                                Map<String, Object> textNode = (Map<String, Object>) node;
                                String text = (String) textNode.get("text");
                                if (ObjectUtil.isNotEmpty(text)) {
                                    chapterName += text;
                                }
                            }
                        }
                    }
                }
                // 新增章节
                DtbBookChapter chapter = new DtbBookChapter();
                chapter.setChapterName(chapterName);
                chapter.setBookId(bookId);
                chapter.setSort(sort);
                sort++;
                chapter.setVersionId(book.getCurrentVersionId());
                //  模板ID
                chapter.setTemplateId(templateId);
                chapter.setChapterTotalPage(1);
                dtbBookChapterMapper.insert(chapter);

                // 生成小标题
                generateChapterLog(doc, chapter);

                Long chapterId = chapter.getChapterId();
                // 保存内容
                Update update = new Update();
                update.set("chapterId", chapterId);
                update.set("content", JSONUtil.toJsonStr(doc));
                mongoService.updateOne(BOOK_CHAPTER_CONTENT, new Query(Criteria.where("chapterId").is(chapterId)), update, DtbBookChapterContent.class);

                // 扫描json资源
                List<ResourceVO> resourceVOList = new ArrayList<>();
                analysisNode(resourceVOList, pageList, userId, chapterId);

                // 保存资源到数据库
                dtbBookResourceService.batchUploadResource(resourceVOList, userId, chapterId);
            }

            dutpTask.setTaskState(2);
            updateTaskAndSentMessage("您好，教材导入任务已完成。【教材名称：" + book.getBookName() + "；教材编号：" + book.getBookNo() + "】。", userId, dutpTask);

        }, customExecutor).exceptionally(ex -> {
            log.error("导入教材章节失败，bookId: {}, 异常：{}", bookId, ex);
            // 结束任务
            dutpTask.setTaskState(3);
            updateTaskAndSentMessage("您好，教材导入任务已完成。【教材名称：" + book.getBookName() + "；教材编号：" + book.getBookNo() + "】。", userId, dutpTask);
            return null;
        });

        return success;
    }


    @Override
    public int importChapterPdf(MultipartFile file, Long bookId) {
        if (ObjectUtil.isEmpty(file) || ObjectUtil.isEmpty(bookId)) {
            throw new ServiceException("PDF文件或者chapterId为空");
        }

        String fileName = file.getOriginalFilename();
        if (!fileName.endsWith(".pdf")) {
            throw new ServiceException("文件格式不正确，请上传.pdf文件");
        }

        DtbBook book = dtbBookMapper.selectOne(new LambdaQueryWrapper<DtbBook>()
                .select(DtbBook::getBookId, DtbBook::getBookName, DtbBook::getBookNo, DtbBook::getCurrentVersionId)
                .eq(DtbBook::getBookId, bookId));

        if (ObjectUtil.isEmpty(book)) {
            throw new ServiceException("教材不存在");
        }


        Long userId = SecurityUtils.getUserId();
        // 生成任务
        DutpTask dutpTask = new DutpTask();
        dutpTask.setTaskType(2);
        dutpTask.setTaskContent("导入教材内容");
        dutpTask.setDataId(bookId);
        dutpTask.setUserId(userId);
        dutpTask.setTaskRate(0);
        dutpTask.setStartTime(new Date());
        dutpTask.setTaskState(1);
        int success = dutpTaskMapper.insert(dutpTask);

        // 1. 先将 MultipartFile 转换为 File
        File tempFile = null;
        try {
            tempFile = File.createTempFile("upload_", ".pdf");
            file.transferTo(tempFile);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("导入教材失败，chapterId: {}, 异常：", bookId, e);
        }

        Long templateId = dtbBookChapterMapper.queryTemplateIdByBookId(bookId);

        // 新开线程执行任务
        File finalTempFile = tempFile;
        CompletableFuture.runAsync(() -> {
            List<JsonObject> pdfList = new ArrayList<>();
            String jsonResponse = "";
            try {
                // 2. 调用 PdfToJsonHttpClient 上传 PDF
                pdfList = pdfToJsonConverter.convertPdf(finalTempFile, "html", true);
            } catch (ServiceException e) {
                throw e;
            } catch (Exception e) {
                log.error("导入教材失败，chapterId: {}, 异常：", bookId, e);
            }
            if (ObjectUtil.isEmpty(pdfList)) {
                throw new ServiceException("解析失败");
            }
            Integer sort = dtbBookChapterMapper.queryMaxSort(bookId);
            if (ObjectUtil.isEmpty(sort)) {
                sort = 1;
            }
            Gson gson = new Gson();
            for (JsonObject pdf : pdfList) {
                try {
                    // 结构校验
                    if (!pdf.has("content")) {
                        log.warn("跳过无content字段的PDF段落");
                        continue;
                    }

                    JsonElement contentElement = pdf.get("content");
                    if (!contentElement.isJsonArray()) {
                        throw new ServiceException("content字段类型异常");
                    }

                    // 安全转换
                    JsonArray contentArray = contentElement.getAsJsonArray();
                    List<Object> pageList = gson.fromJson(contentArray,
                            new TypeToken<List<Object>>() {
                            }.getType());

                    // 内容校验
                    if (CollectionUtils.isEmpty(pageList)) {
                        continue;
                    }

                    // 处理第一页内容
                    Map<String, Object> page = (Map<String, Object>) pageList.get(0);
                    List<Object> contentList = (List<Object>) page.get("content");

                    // 生成章节名称逻辑
                    String chapterName = "空章节";
                    if (ObjectUtil.isNotEmpty(contentList)) {
                        Map<String, Object> headingNode = (Map<String, Object>) contentList.get(0);
                        String headingType = (String) headingNode.get("type");
                        if ("heading".equals(headingType)) {
                            List<Object> textNodeList = (List<Object>) headingNode.get("content");
                            if (ObjectUtil.isNotEmpty(textNodeList)) {
                                chapterName = "";
                                for (Object node : textNodeList) {
                                    Map<String, Object> textNode = (Map<String, Object>) node;
                                    String text = (String) textNode.get("text");
                                    if (ObjectUtil.isNotEmpty(text)) {
                                        chapterName += text;
                                    }
                                }
                            }
                        }
                    }

                    // 创建章节实体
                    DtbBookChapter chapter = new DtbBookChapter();
                    chapter.setChapterName(chapterName);
                    chapter.setBookId(bookId);
                    chapter.setSort(sort);
                    sort++;
                    chapter.setVersionId(book.getCurrentVersionId());
                    chapter.setTemplateId(templateId);
                    chapter.setChapterTotalPage(1);
                    dtbBookChapterMapper.insert(chapter);

                    // 生成日志（参数改为pdf）
                    generateChapterLog(pdf, chapter);

                    // 保存章节内容到MongoDB
                    Long chapterId = chapter.getChapterId();
                    Update update = new Update();
                    update.set("chapterId", chapterId);
                    update.set("content", pdf.toString()); // 使用pdf内容
                    mongoService.updateOne(
                            BOOK_CHAPTER_CONTENT,
                            new Query(Criteria.where("chapterId").is(chapterId)),
                            update,
                            DtbBookChapterContent.class
                    );

                    // 资源扫描和保存
                    List<ResourceVO> resourceVOList = new ArrayList<>();
                    analysisNode(resourceVOList, pageList, userId, chapterId);
                    dtbBookResourceService.batchUploadResource(resourceVOList, userId, chapterId);
                } catch (Exception e) {
                    log.error("JSON解析失败", e);
                    throw new ServiceException("PDF解析异常，请检查文件内容格式");
                }
            }

            dutpTask.setTaskState(2);
            updateTaskAndSentMessage("您好，教材导入任务已完成。【教材名称：" + book.getBookName() + "；教材编号：" + book.getBookNo() + "】。", userId, dutpTask);


        }, customExecutor).exceptionally(ex -> {
            log.error("导入教材失败，chapterId: {}, 异常：{}", bookId, ex);
            // 结束任务
            dutpTask.setTaskState(3);
            updateTaskAndSentMessage("您好，教材导入任务已完成。【教材名称：" + book.getBookName() + "；教材编号：" + book.getBookNo() + "】。", userId, dutpTask);
            return null;
        });

        return success;
    }

    @Override
    public void exportBook(DtbBook dtbBook) {
        List<Long> bookIdList = dtbBook.getBookIdList();
        if (ObjectUtil.isEmpty(bookIdList)) {
            throw new ServiceException("导出教材不能为空");
        }

        // 鉴权
        Long userId = SecurityUtils.getUserId();
        String roleKey = bookCommonMapper.queryRole(userId);
        List<Long> exportBookIdList = new ArrayList<>();
        for (Long bookId : bookIdList) {
            DtbBook book = dtbBookMapper.selectOne(new LambdaQueryWrapper<DtbBook>()
                    .select(DtbBook::getBookId, DtbBook::getPublishStatus, DtbBook::getLastVersionId, DtbBook::getCurrentVersionId)
                    .eq(DtbBook::getBookId, bookId));
            // 修正版本不允许导出
            if (book.getCurrentVersionId().equals(book.getLastVersionId())) {
                Integer count = bookGroupMapper.selectCount(new LambdaQueryWrapper<DtbBookGroup>()
                        .eq(DtbBookGroup::getBookId, bookId)
                        .eq(DtbBookGroup::getUserId, userId)
                        .in(DtbBookGroup::getRoleType, 5, 6));
                if (ObjectUtil.isEmpty(count)) {
                    count = 0;
                }

                if (count == 0) {
                    // 不是编辑角色
                    // 作者编辑端鉴权
                    if ("writer".equals(roleKey) || "editor".equals(roleKey)) {
                        if (book.getPublishStatus() == 2) {
                            // 已经通过一次三审三校了
                            continue;
                        }

                        // 书稿联系人和主编
                        Integer isEditor = bookGroupMapper.selectCount(new LambdaQueryWrapper<DtbBookGroup>()
                                .eq(DtbBookGroup::getBookId, bookId)
                                .eq(DtbBookGroup::getUserId, userId)
                                .in(DtbBookGroup::getRoleType, 1, 2));
                        if (ObjectUtil.isEmpty(isEditor)) {
                            isEditor = 0;
                        }

                        if (isEditor == 0) {
                            continue;
                        }
                    }

                }
                exportBookIdList.add(bookId);
            }
        }

        if (ObjectUtil.isEmpty(exportBookIdList)) {
            throw new ServiceException("无权限，不允许导出");
        }

        exportBookIdList = exportBookIdList.stream().distinct().collect(Collectors.toList());

        bookExportTask(userId, exportBookIdList);
    }

    /**
     * 生成导出教材任务
     *
     * @param userId
     * @param exportBookIdList
     */
    private void bookExportTask(Long userId, List<Long> exportBookIdList) {
        String nowDay = DateUtil.format(new Date(), "yyyy-MM-dd");
        for (Long bookId : exportBookIdList) {
            // 生成任务
            DutpTask dutpTask = new DutpTask();
            dutpTask.setTaskType(5);
            dutpTask.setTaskContent("教材导出");
            dutpTask.setDataId(bookId);
            dutpTask.setUserId(userId);
            dutpTask.setTaskRate(0);
            dutpTask.setStartTime(new Date());
            dutpTask.setTaskState(1);
            dutpTaskMapper.insert(dutpTask);
            // 开启异步线程
            // List<Long> finalExportBookIdList = exportBookIdList;
            CompletableFuture.runAsync(() -> {
                String content = "您好，教材导出任务已完成，请到【教材管理-任务中心】内下载。";
                String path = fileExportPath + File.separator + dutpTask.getTaskId() + "_exportBook" + File.separator;
                // for (Long bookId : finalExportBookIdList) {
                DtbBook book = dtbBookMapper.selectOne(new LambdaQueryWrapper<DtbBook>()
                        .select(DtbBook::getBookId, DtbBook::getBookName, DtbBook::getBookNo)
                        .eq(DtbBook::getBookId, bookId));
                DtbBookChapter bookChapter = new DtbBookChapter();
                bookChapter.setBookId(bookId);
                List<DtbBookChapter> dtbBookChapterList = dtbBookChapterMapper.dtbBookChapterMapper(bookChapter);
                for (DtbBookChapter chapter : dtbBookChapterList) {
                    Long chapterId = chapter.getChapterId();
                    // 查询章节内容
                    Query query = new Query(Criteria.where("chapterId").is(chapterId));
                    DtbBookChapterContent chapterContent = mongoService.findOne(BOOK_CHAPTER_CONTENT, query, DtbBookChapterContent.class);
                    String contentJson = "";
                    if (ObjectUtil.isNotEmpty(chapterContent)) {
                        contentJson = chapterContent.getContent();
                    }
                    // 生成word
                    try {
                        JsonToDocxConverter.analyzeJson(contentJson, path + book.getBookName() + File.separator + chapter.getChapterName() + ".docx");
                    } catch (Exception e) {
                        log.info("导出章节失败：{}, 原因：{}", chapterId, e);
                    }
                    // TODO 生成pdf
                }
                content += "【教材名称：" + book.getBookName() + "；教材编号：" + book.getBookNo() + "】";

                // }
                // 打包zip
                File file = generateZip(path, fileExportPath + File.separator + dutpTask.getTaskId() + File.separator + "教材导出_" + nowDay + ".zip");
                UploadFileDto uploadFileDto = aliyunOssStsUtil.uploadFile(file);
                log.info("上传完后的文件url: {}", uploadFileDto);
                // 结束任务
                dutpTask.setUrl(uploadFileDto.getFileUrl() + "!&&&!" + "教材导出_" + nowDay + ".zip");
                dutpTask.setTaskState(2);
                updateTaskAndSentMessage(content, userId, dutpTask);
            }, customExecutor).exceptionally(ex -> {
                log.error("导出教材章节失败: 异常：{}", ex);
                // 结束任务
                dutpTask.setTaskState(3);
                updateTaskAndSentMessage("您好，教材导出任务已完成，请到【教材管理-任务中心】内下载。", userId, dutpTask);
                return null;
            });
        }
    }

    @Override
    public void toggleBatchShelf(DtbBook dtbBook) {
        UpdateWrapper<DtbBook> updateWrapper = new UpdateWrapper<>();
        if (!CollectionUtils.isEmpty(dtbBook.getIds())) {
            updateWrapper.lambda().in(DtbBook::getBookId, dtbBook.getIds())
                    .set(DtbBook::getShelfState, dtbBook.getShelfState())
                    .set(DtbBook::getShelfTime, new Date())
                    .set(DtbBook::getCreateTime, new Date())
                    .set(DtbBook::getCreateBy, SecurityUtils.getUsername());

            if (dtbBook.getShelfState() == 1) {
                updateWrapper.lambda().set(DtbBook::getShelfTime, new Date());
            } else if (dtbBook.getShelfState() == 2) {
                updateWrapper.lambda().set(DtbBook::getUnshelfTime, new Date());
            }
            dtbBookMapper.update(null, updateWrapper);
            updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().in(DtbBook::getMasterBookId, dtbBook.getIds())
                    .eq(DtbBook::getPublishStatus, 2)
                    .set(DtbBook::getShelfState, dtbBook.getShelfState())
                    .set(DtbBook::getShelfTime, new Date())
                    .set(DtbBook::getCreateTime, new Date())
                    .set(DtbBook::getCreateBy, SecurityUtils.getUsername());
            if (dtbBook.getShelfState() == 1) {
                updateWrapper.lambda().set(DtbBook::getShelfTime, new Date());
            } else if (dtbBook.getShelfState() == 2) {
                updateWrapper.lambda().set(DtbBook::getUnshelfTime, new Date());
            }
            dtbBookMapper.update(null, updateWrapper);
            dtbBook.getIds().forEach(dtbBookId -> {
                DtbBook book = new DtbBook();
                book = dtbBookMapper.selectById(dtbBookId);
                // 更新es
                if (book.getShelfState() == 2) {
                    // 下架删除
                    esDtbBooksMapper.deleteById(dtbBookId);
                } else if (book.getShelfState() == 1) {
                    // 上架新增
                    EsDtbBook esDtbBook = generateEsDtbBook(book);
                    esDtbBooksMapper.insert(esDtbBook);
                }

            });
        }
    }

    @Override
    public List<DtbBook> getListByBookIds(List<Long> bookIds) {
        QueryWrapper<DtbBook> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(DtbBook::getBookId, bookIds);
        return dtbBookMapper.selectList(queryWrapper);
    }

    /**
     * 分析节点
     *
     * @param resourceList
     * @param nodeList
     */
    private void analysisNode(List<ResourceVO> resourceList, List<Object> nodeList, Long userId, Long chapterId) {
        for (Object parentNode : nodeList) {
            Map<String, Object> node = (Map<String, Object>) parentNode;
            String type = (String) node.get("type");

            if (type.equals("text")) {
                // 文字

            } else if (type.equals("imageLayout") || type.equals("imageInLine") || type.equals("imageIcon") || type.equals("imageGallery")) {
                // 图片
                if (type.equals("imageGallery")) {
                    // 画廊
                    List<Object> imgList = (List<Object>) node.get("imgList");
                    if (ObjectUtil.isNotEmpty(imgList)) {
                        for (Object img : imgList) {
                            Map<String, Object> imgNode = (Map<String, Object>) img;
                            generateResource(resourceList, "1", userId, chapterId, imgNode);
                        }
                    }
                } else {
                    // 非画廊
                    Map<String, Object> attrs = (Map<String, Object>) node.get("attrs");
                    generateResource(resourceList, "1", userId, chapterId, attrs);
                }
            } else if (type.equals("bubbleInline")) {
                // 气泡

            } else if (type.equals("links")) {
                // 链接

            } else if (type.equals("image")) {
                // TODO 先默认这个为公式
                // 公式

            } else if (type.equals("resourceCover")) {
                // 资源封面组件
                Map<String, Object> attrs = (Map<String, Object>) node.get("attrs");
                Integer rcType = (Integer) attrs.get("rcType");
                if (rcType == 0) {
                    // 3D

                } else if (rcType == 1 || rcType == 2) {
                    // AR/VR

                } else if (rcType == 3) {
                    // 仿真

                } else if (rcType == 4) {
                    // 游戏

                } else if (rcType == 5) {
                    // 教学资源

                } else if (rcType == 6) {
                    // 扩展阅读

                } else if (rcType == 7) {
                    // 实训
                    generateResource(resourceList, "9", userId, chapterId, attrs);
                }
            } else if (type.equals("questions")) {
                // 试题

            }
            // TODO 二期 脚注

            else if (type.equals("audio")) {
                // 音频
                Map<String, Object> attrs = (Map<String, Object>) node.get("attrs");
                generateResource(resourceList, "2", userId, chapterId, attrs);
            } else if (type.equals("video")) {
                // 视频
                Map<String, Object> attrs = (Map<String, Object>) node.get("attrs");
                generateResource(resourceList, "3", userId, chapterId, attrs);

            } else if (type.equals("codeBlock")) {
                // 代码块

            }
            // TODO 二期 互动

            List<Object> childrenNodeList = (List<Object>) node.get("content");
            if (ObjectUtil.isNotEmpty(childrenNodeList)) {
                analysisNode(resourceList, childrenNodeList, userId, chapterId);
            }
        }
    }

    /**
     * 生成资源
     *
     * @param resourceList
     * @param userId
     * @param chapterId
     * @param imgNode
     */
    private void generateResource(List<ResourceVO> resourceList, String fileType, Long userId, Long chapterId, Map<String, Object> imgNode) {
        String src = (String) imgNode.get("src");
        String name = (String) imgNode.get("name");
        // 添加类型安全转换
        Number sizeNumber = (Number) imgNode.get("size");
        Long fileSize = (sizeNumber != null) ? sizeNumber.longValue() : 0L;
        ResourceVO resourceVO = new ResourceVO();
        resourceVO.setFileName(name);
        resourceVO.setFileUrl(src);
        resourceVO.setFileSize(fileSize);
        resourceVO.setFileType(fileType);
        resourceVO.setUserId(userId);
        resourceVO.setChapterId(chapterId);
        resourceList.add(resourceVO);
    }

    /**
     * 生成小节
     *
     * @param doc
     * @param chapter
     */
    private void generateChapterLog(Map<String, Object> doc, DtbBookChapter chapter) {
        Long bookId = chapter.getBookId();
        Long versionId = chapter.getVersionId();
        Long chapterId = chapter.getChapterId();
        List<String> hasDomIdList = new ArrayList<>();
        List<Map<String, Object>> levelList = new ArrayList<>(6);
        List<Long> catalogIdList = new ArrayList<>(6);

        // 删除没有存在的id
        LambdaQueryWrapper<DtbBookChapterCatalog> lambdaDeleteQueryWrapper = new LambdaQueryWrapper<DtbBookChapterCatalog>()
                .eq(DtbBookChapterCatalog::getChapterId, chapterId);
        // if (ObjectUtil.isNotEmpty(hasDomIdList)) {
        //     lambdaDeleteQueryWrapper.notIn(DtbBookChapterCatalog::getDomId, hasDomIdList);
        // }
        chapterCatalogMapper.delete(lambdaDeleteQueryWrapper);

        // 有效长度
        int effectiveLength = 0;
        int pageNumber = 0;
        List<Object> pageList = (List<Object>) doc.get("content");

        for (Object page : pageList) {
            Map<String, Object> pageObj = (Map<String, Object>) page;
            List<Object> contentList = (List<Object>) pageObj.get("content");
            if (ObjectUtil.isEmpty(contentList)) {
                continue;
            }
            pageNumber++;
            for (Object content : contentList) {
                Map<String, Object> contentObj = (Map<String, Object>) content;
                String type = (String) contentObj.get("type");
                if ("heading".equals(type)) {
                    Map<String, Object> attrs = (Map<String, Object>) contentObj.get("attrs");
                    Integer level = (Integer) attrs.get("level");
                    String domId = (String) attrs.get("id");
                    // 要操作的index
                    int index = -1;
                    Long parentId = 0L;
                    if (effectiveLength == 0) {
                        levelList.add(attrs);
                        index = 0;
                        parentId = 0l;
                        effectiveLength = 1;
                    } else {

                        for (int i = 0; i < effectiveLength; i++) {
                            Map<String, Object> hLabelObjectAttrs = levelList.get(i);
                            Integer iterLevel = (Integer) hLabelObjectAttrs.get("level");
                            // 当前level 比循环中的level小于等于 就替换当前值
                            if (iterLevel >= level) {
                                index = i;
                                effectiveLength = i + 1;
                                if (i == 0) {
                                    parentId = 0l;
                                } else {
                                    parentId = catalogIdList.get(i - 1);
                                    ;
                                }

                                levelList.set(i, attrs);
                                break;
                            }
                        }

                        if (index == -1) {
                            if (effectiveLength == levelList.size()) {
                                // 末尾追加
                                index = levelList.size();
                                levelList.add(index, attrs);
                            } else {
                                // 中间替换
                                index = effectiveLength;
                                levelList.set(index, attrs);
                            }
                            effectiveLength++;
                            // 父Id为数组最后一个的catalogId
                            parentId = catalogIdList.get(index - 1);
                        }
                    }
                    String text = (String) ((Map<String, Object>) ((List<Object>) contentObj.get("content")).get(0)).get("text");
                    // 更新数据库
                    // Long catalogId = chapterCatalogMapper.checkIsExist(domId);
                    DtbBookChapterCatalog dtbBookChapterCatalog = new DtbBookChapterCatalog();
                    dtbBookChapterCatalog.setTitle(text);
                    dtbBookChapterCatalog.setParentId(parentId);
                    dtbBookChapterCatalog.setPageNumber(pageNumber);
                    // if (ObjectUtil.isNotEmpty(catalogId)) {
                    //     dtbBookChapterCatalog.setCatalogId(catalogId);
                    // chapterCatalogMapper.updateById(dtbBookChapterCatalog);
                    // } else {
                    dtbBookChapterCatalog.setChapterId(chapterId);
                    dtbBookChapterCatalog.setBookId(bookId);
                    dtbBookChapterCatalog.setVersionId(versionId);
                    dtbBookChapterCatalog.setDomId(domId);
                    chapterCatalogMapper.insert(dtbBookChapterCatalog);
                    // }
                    if (effectiveLength <= catalogIdList.size()) {
                        catalogIdList.set(index, dtbBookChapterCatalog.getCatalogId());
                    } else {
                        catalogIdList.add(index, dtbBookChapterCatalog.getCatalogId());
                    }

                    hasDomIdList.add(domId);
                    log.info("level: {}, Text: {}, ParentId: {}, domID: {}", level, text, parentId, domId);
                }
            }
        }
        // 更新总页数
        DtbBookChapter bookChapter = new DtbBookChapter();
        bookChapter.setChapterId(chapterId);
        bookChapter.setChapterTotalPage(pageList.size());
        dtbBookChapterMapper.updateById(bookChapter);
    }


    /**
     * 生成小节 (JsonObject版本)
     *
     * @param pdf
     * @param chapter
     */
    private void generateChapterLog(JsonObject pdf, DtbBookChapter chapter) {
        Long bookId = chapter.getBookId();
        Long versionId = chapter.getVersionId();
        Long chapterId = chapter.getChapterId();
        List<String> hasDomIdList = new ArrayList<>();
        List<JsonObject> levelList = new ArrayList<>(6);
        List<Long> catalogIdList = new ArrayList<>(6);

        // 删除没有存在的id
        LambdaQueryWrapper<DtbBookChapterCatalog> lambdaDeleteQueryWrapper = new LambdaQueryWrapper<DtbBookChapterCatalog>()
                .eq(DtbBookChapterCatalog::getChapterId, chapterId);
        // if (ObjectUtil.isNotEmpty(hasDomIdList)) {
        //     lambdaDeleteQueryWrapper.notIn(DtbBookChapterCatalog::getDomId, hasDomIdList);
        // }
        chapterCatalogMapper.delete(lambdaDeleteQueryWrapper);

        // 有效长度
        int effectiveLength = 0;
        int pageNumber = 0;
        JsonArray pageList = pdf.getAsJsonArray("content");

        for (int i = 0; i < pageList.size(); i++) {
            JsonObject pageObj = pageList.get(i).getAsJsonObject();
            JsonArray contentList = pageObj.getAsJsonArray("content");
            if (contentList == null || contentList.size() == 0) {
                continue;
            }
            pageNumber++;
            for (int j = 0; j < contentList.size(); j++) {
                JsonObject contentObj = contentList.get(j).getAsJsonObject();
                String type = contentObj.get("type").getAsString();
                if ("heading".equals(type)) {
                    JsonObject attrs = contentObj.getAsJsonObject("attrs");
                    Integer level = attrs.get("level").getAsInt();
                    String domId = attrs.get("id").getAsString();
                    // 要操作的index
                    int index = -1;
                    Long parentId = 0L;
                    if (effectiveLength == 0) {
                        levelList.add(attrs);
                        index = 0;
                        parentId = 0L;
                        effectiveLength = 1;
                    } else {
                        for (int k = 0; k < effectiveLength; k++) {
                            JsonObject hLabelObjectAttrs = levelList.get(k);
                            Integer iterLevel = hLabelObjectAttrs.get("level").getAsInt();
                            // 当前level 比循环中的level小于等于 就替换当前值
                            if (iterLevel >= level) {
                                index = k;
                                effectiveLength = k + 1;
                                if (k == 0) {
                                    parentId = 0L;
                                } else {
                                    parentId = catalogIdList.get(k - 1);
                                }

                                levelList.set(k, attrs);
                                break;
                            }
                        }

                        if (index == -1) {
                            if (effectiveLength == levelList.size()) {
                                // 末尾追加
                                index = levelList.size();
                                levelList.add(index, attrs);
                            } else {
                                // 中间替换
                                index = effectiveLength;
                                levelList.set(index, attrs);
                            }
                            effectiveLength++;
                            // 父Id为数组最后一个的catalogId
                            parentId = catalogIdList.get(index - 1);
                        }
                    }

                    // 获取文本内容
                    String text = contentObj.getAsJsonArray("content").get(0).getAsJsonObject().get("text").getAsString();

                    // 更新数据库
                    // Long catalogId = chapterCatalogMapper.checkIsExist(domId);
                    DtbBookChapterCatalog dtbBookChapterCatalog = new DtbBookChapterCatalog();
                    dtbBookChapterCatalog.setTitle(text);
                    dtbBookChapterCatalog.setParentId(parentId);
                    dtbBookChapterCatalog.setPageNumber(pageNumber);
                    // if (ObjectUtil.isNotEmpty(catalogId)) {
                    //     dtbBookChapterCatalog.setCatalogId(catalogId);
                    // chapterCatalogMapper.updateById(dtbBookChapterCatalog);
                    // } else {
                    dtbBookChapterCatalog.setChapterId(chapterId);
                    dtbBookChapterCatalog.setBookId(bookId);
                    dtbBookChapterCatalog.setVersionId(versionId);
                    dtbBookChapterCatalog.setDomId(domId);
                    chapterCatalogMapper.insert(dtbBookChapterCatalog);
                    // }
                    if (effectiveLength <= catalogIdList.size()) {
                        catalogIdList.set(index, dtbBookChapterCatalog.getCatalogId());
                    } else {
                        catalogIdList.add(index, dtbBookChapterCatalog.getCatalogId());
                    }

                    hasDomIdList.add(domId);
                    log.info("level: {}, Text: {}, ParentId: {}, domID: {}", level, text, parentId, domId);
                }
            }
        }
        // 更新总页数
        DtbBookChapter bookChapter = new DtbBookChapter();
        bookChapter.setChapterId(chapterId);
        bookChapter.setChapterTotalPage(pageList.size());
        dtbBookChapterMapper.updateById(bookChapter);
    }


    /**
     * 更新任务结果和发送消息
     *
     * @param content
     * @param userId
     * @param dutpTask
     */
    private void updateTaskAndSentMessage(String content, Long userId, DutpTask dutpTask) {
        // 更新任务结果
        Long taskId = dutpTask.getTaskId();
        DutpTask task = new DutpTask();
        task.setTaskId(taskId);
        task.setTaskRate(100);
        task.setUrl(dutpTask.getUrl());
        task.setEndTime(new Date());
        task.setTaskState(dutpTask.getTaskState());
        dutpTaskMapper.updateById(task);

        // 发送消息
        DutpUserMessage dutpUserMessage = new DutpUserMessage();
        dutpUserMessage.setContent(content);
        dutpUserMessage.setTitle("任务提醒");
        dutpUserMessage.setToUserId(userId);
        dutpUserMessage.setMessageType(1);
        dutpUserMessage.setFromUserType(1);
        dutpUserMessage.setToUserType(1);
        remoteUserMessageService.addMessage(dutpUserMessage);
    }

    ;

    /**
     * 学习统计查询
     */
    @Override
    public LearningStatisticsVo getLearningStatistics() {
        LearningStatisticsVo learningStatisticsVo = new LearningStatisticsVo();
        Long userId = SecurityUtils.getUserId();
        // 用户阅读记录Mapper接口
        List<DtbUserBookRead> list = dtbUserBookReadMapper.selectList(new QueryWrapper<DtbUserBookRead>().eq("user_id", userId));
        // 计算总学习时长（秒）
        int totalSeconds = list.stream()
                .mapToInt(DtbUserBookRead::getReadTime)
                .sum();
        // 将秒转换为小时
        BigDecimal learningDurationInHours = BigDecimal.valueOf(totalSeconds).divide(BigDecimal.valueOf(3600), 2, BigDecimal.ROUND_HALF_UP);
        // 设置学习时长（小时）
        learningStatisticsVo.setLearningDuration(learningDurationInHours);

        // 使用Set去重
        Set<Long> bookIdSet = list.stream()
                .map(DtbUserBookRead::getBookId)
                .collect(Collectors.toSet());
        learningStatisticsVo.setBookCount(bookIdSet.size());
        // 笔记/标注Mapper接口
        learningStatisticsVo.setNotesCount(dtbUserBookNoteMapper.selectCount(new QueryWrapper<DtbUserBookNote>().eq("user_id", userId)));
        // 问题答案Mapper接口
        learningStatisticsVo.setExercisesCount(dtbBookQuestionAnswerMapper.selectCount(new QueryWrapper<DtbBookQuestionAnswer>().eq("user_id", userId)));
        return learningStatisticsVo;
    }

    @Override
    public List<DtbBook> getAllBySchoolId(DtbBook dtbBook) {
        dtbBook.setSchoolId(SecurityUtils.getLoginUser().getSchoolId());
        return dtbBookMapper.getAllBySchoolId(dtbBook);
    }
}

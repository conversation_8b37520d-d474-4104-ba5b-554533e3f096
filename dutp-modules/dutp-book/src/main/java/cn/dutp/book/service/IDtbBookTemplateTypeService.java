package cn.dutp.book.service;

import java.util.List;
import cn.dutp.book.domain.DtbBookTemplateType;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 教材模板分类Service接口
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface IDtbBookTemplateTypeService extends IService<DtbBookTemplateType>
{
    /**
     * 查询教材模板分类
     *
     * @param typeId 教材模板分类主键
     * @return 教材模板分类
     */
    public DtbBookTemplateType selectDtbBookTemplateTypeByTypeId(Long typeId);

    /**
     * 查询教材模板分类列表
     *
     * @param dtbBookTemplateType 教材模板分类
     * @return 教材模板分类集合
     */
    public List<DtbBookTemplateType> selectDtbBookTemplateTypeList(DtbBookTemplateType dtbBookTemplateType);

    /**
     * 新增教材模板分类
     *
     * @param dtbBookTemplateType 教材模板分类
     * @return 结果
     */
    public boolean insertDtbBookTemplateType(DtbBookTemplateType dtbBookTemplateType);

    /**
     * 修改教材模板分类
     *
     * @param dtbBookTemplateType 教材模板分类
     * @return 结果
     */
    public boolean updateDtbBookTemplateType(DtbBookTemplateType dtbBookTemplateType);

    /**
     * 批量删除教材模板分类
     *
     * @param typeIds 需要删除的教材模板分类主键集合
     * @return 结果
     */
    public boolean deleteDtbBookTemplateTypeByTypeIds(List<Long> typeIds);

}

package cn.dutp.book.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import cn.dutp.api.common.constant.DutpConstant;
import cn.dutp.book.domain.DtbBookChapter;
import cn.dutp.book.domain.DtbUserBook;
import cn.dutp.book.domain.DtbUserBookConfig;
import cn.dutp.book.domain.vo.DtbUserBookConfigVo;
import cn.dutp.book.domain.vo.DtbUserBookVo;
import cn.dutp.book.mapper.*;
import cn.dutp.book.service.IDtbBookService;
import cn.dutp.common.core.constant.HttpStatus;
import cn.dutp.common.core.utils.DateUtils;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.redis.service.RedisService;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBook;
import cn.dutp.domain.DutpSiteConfig;
import cn.dutp.system.api.domain.DutpUser;
import cn.dutp.system.api.model.LoginUser;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.seata.core.context.RootContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.book.service.IDtbUserBookService;
import org.springframework.transaction.annotation.Transactional;

/**
 * DUTP-DTB_014学生/教师书架Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-04
 */
@Service
@Transactional
public class DtbUserBookServiceImpl extends ServiceImpl<DtbUserBookMapper, DtbUserBook> implements IDtbUserBookService
{
    @Autowired
    private DtbUserBookMapper dtbUserBookMapper;
    @Autowired
    private DtbBookChapterMapper dtbBookChapterMapper;

    @Autowired
    private DtbBookMapper dtbBookMapper;

    @Autowired
    private DtbUserBookConfigMapper dtbUserBookConfigMapper;

    @Autowired
    private DutpSiteConfigMapper dutpSiteConfigMapper;

    @Autowired
    private RedisService redisService;

    /**
     * 查询DUTP-DTB_014学生/教师书架
     *
     * @param userBookId DUTP-DTB_014学生/教师书架主键
     * @return DUTP-DTB_014学生/教师书架
     */
    @Override
    public DtbUserBook selectDtbUserBookByUserBookId(Long userBookId)
    {
        return this.getById(userBookId);
    }

    /**
     * 查询DUTP-DTB_014学生/教师书架列表
     *
     * @param dtbUserBook DUTP-DTB_014学生/教师书架
     * @return DUTP-DTB_014学生/教师书架
     */
    @Override
    public List<DtbUserBook> selectDtbUserBookList(DtbUserBook dtbUserBook)
    {
        LambdaQueryWrapper<DtbUserBook> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(ObjectUtil.isNotEmpty(dtbUserBook.getUserId())) {
            lambdaQueryWrapper.eq(DtbUserBook::getUserId
                    ,dtbUserBook.getUserId());
        }
        if(ObjectUtil.isNotEmpty(dtbUserBook.getBookId())) {
            lambdaQueryWrapper.eq(DtbUserBook::getBookId
                    ,dtbUserBook.getBookId());
        }
        if(ObjectUtil.isNotEmpty(dtbUserBook.getAddWay())) {
            lambdaQueryWrapper.eq(DtbUserBook::getAddWay
                    ,dtbUserBook.getAddWay());
        }
        if(ObjectUtil.isNotEmpty(dtbUserBook.getReadRate())) {
            lambdaQueryWrapper.eq(DtbUserBook::getReadRate
                    ,dtbUserBook.getReadRate());
        }

        if(ObjectUtil.isNotEmpty(dtbUserBook.getExpireDate())) {
            lambdaQueryWrapper.eq(DtbUserBook::getExpireDate
                    ,dtbUserBook.getExpireDate());
        }
        if(ObjectUtil.isNotEmpty(dtbUserBook.getSort())) {
            lambdaQueryWrapper.eq(DtbUserBook::getSort
                    ,dtbUserBook.getSort());
        }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 学生教师端我的教材查询
     * @return 查询结果
     */
    @Override
    public List<DtbUserBookVo> getInfoEducation(DtbUserBook dtbUserBook)
    {
        // 获取用户Id
        dtbUserBook.setUserId(SecurityUtils.getUserId());
        return baseMapper.getInfoEducation(dtbUserBook);
    }


    /**
     * 新增DUTP-DTB_014学生/教师书架
     *
     * @param dtbUserBook DUTP-DTB_014学生/教师书架
     * @return 结果
     */
    @Override
    public boolean insertDtbUserBook(DtbUserBook dtbUserBook)
    {
        return this.save(dtbUserBook);
    }

    /**
     * 修改DUTP-DTB_014学生/教师书架
     *
     * @param dtbUserBook DUTP-DTB_014学生/教师书架
     * @return 结果
     */
    @Override
    public boolean updateDtbUserBook(DtbUserBook dtbUserBook)
    {
        return this.updateById(dtbUserBook);
    }

    /**
     * 学生教师端批量修改学生/教师书架
     *
     * @param dtbUserBookList 学生教师端批量修改学生/教师书架
     * @return 结果
     */
    @Override
    public boolean editEducation(List<DtbUserBook> dtbUserBookList)
    {
        // 执行批量更新
        return super.updateBatchById(dtbUserBookList);
    }
    /**
     * 批量删除DUTP-DTB_014学生/教师书架
     *
     * @param userBookIds 需要删除的DUTP-DTB_014学生/教师书架主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbUserBookByUserBookIds(List<Long> userBookIds)
    {
        return this.removeByIds(userBookIds);
    }

    /**
     * 兑换购书码状态变更
     *
     * @param dtbUserBook 包含用户ID和书籍ID的DtbUserBook对象
     * @return 如果操作成功则返回true，否则返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean editStatus(DtbUserBook dtbUserBook) {
        LambdaQueryWrapper<DtbUserBook> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DtbUserBook::getBookId, dtbUserBook.getBookId());
        lambdaQueryWrapper.eq(DtbUserBook::getUserId, dtbUserBook.getUserId());
        System.out.println("editStatus XID:"+ RootContext.getXID());
        DtbUserBook res = this.getOne(lambdaQueryWrapper);

        if (ObjectUtil.isNotNull(res)) {
            return this.update(dtbUserBook,lambdaQueryWrapper);
        } else {
            return this.save(dtbUserBook);
        }
    }

    @Override
    public AjaxResult getUserBookId(Long bookId) {
        Long userId = SecurityUtils.getUserId();
        LambdaQueryWrapper<DtbUserBook> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DtbUserBook::getBookId, bookId)
                .eq(DtbUserBook::getUserId, userId)
                .gt(DtbUserBook::getExpireDate,new Date());
        DtbUserBook dtbUserBook = dtbUserBookMapper.selectOne(lambdaQueryWrapper);
        if (ObjectUtil.isNull(dtbUserBook)) {
            return AjaxResult.error(HttpStatus.NO_BUY,"用户没有购买教材或已过期");
        } else {
            HashMap<String,String> result = new HashMap<>();
            result.put("userBookId",dtbUserBook.getUserBookId().toString());
            return AjaxResult.success(result);
        }
    }

    @Override
    public AjaxResult userStartRead(Long bookId, Long chapterId,int isMobile) {
        Long userId = SecurityUtils.getUserId();
        DtbUserBookConfigVo vo = new DtbUserBookConfigVo();

        // 验证书籍状态
        LambdaQueryWrapper<DtbBook> bookLambdaQueryWrapper = new LambdaQueryWrapper<>();
        bookLambdaQueryWrapper.eq(DtbBook::getBookId,bookId).eq(DtbBook::getPublishStatus,2);
        DtbBook dtbBook = dtbBookMapper.selectOne(bookLambdaQueryWrapper);
        if (ObjectUtil.isNull(dtbBook)) {
            return AjaxResult.error(HttpStatus.NO_BOOK, "教材不存在或已下架");
        }
        if (ObjectUtil.isNotEmpty(userId) && userId.longValue()!=0) {
            LambdaQueryWrapper<DtbUserBookConfig> configLambdaQueryWrapper = new LambdaQueryWrapper<>();
            configLambdaQueryWrapper.eq(DtbUserBookConfig::getBookId,bookId)
                                    .eq(DtbUserBookConfig::getUserId,SecurityUtils.getUserId())
                                    .eq(DtbUserBookConfig::getIsMobile,isMobile );
            DtbUserBookConfig dtbUserBookConfig = dtbUserBookConfigMapper.selectOne(configLambdaQueryWrapper);
            // 如果没有配置信息插入
            if (ObjectUtil.isNull(dtbUserBookConfig)) {
                dtbUserBookConfig = new DtbUserBookConfig();
                dtbUserBookConfig.setBookId(bookId);
                dtbUserBookConfig.setUserId(userId);
                dtbUserBookConfig.setIsMobile(isMobile);
                if(isMobile == 1){
                    dtbUserBookConfig.setFontSize(30);
                }
                // 点击章节进入阅读
                if (chapterId!=null) {
                    dtbUserBookConfig.setLastChapterId(chapterId);
                } else {
                    Long versionId = getUserVersionId(dtbBook, userId);
                    DtbBookChapter chapter = getFirstChapter(bookId, versionId);
                    if (ObjectUtil.isNotNull(chapter)) {
                        dtbUserBookConfig.setLastChapterId(chapter.getChapterId());
                    } else {
                        return AjaxResult.error(HttpStatus.NO_CHAPTER, "没有可阅读的章节");
                    }
                }
                dtbUserBookConfig.setLastPageNumber(1);
                dtbUserBookConfig.setLastSeeDate(new Date());
                dtbUserBookConfigMapper.insert(dtbUserBookConfig);
            }
            BeanUtil.copyProperties(dtbUserBookConfig,vo);

            // 新增处理另一端的配置数据（PC or Mobile）
            int otherDeviceType = 1 - isMobile;

            LambdaQueryWrapper<DtbUserBookConfig> otherConfigWrapper = new LambdaQueryWrapper<>();
            otherConfigWrapper.eq(DtbUserBookConfig::getBookId, bookId)
                    .eq(DtbUserBookConfig::getUserId, userId)
                    .eq(DtbUserBookConfig::getIsMobile, otherDeviceType);

            DtbUserBookConfig otherConfig = dtbUserBookConfigMapper.selectOne(otherConfigWrapper);

            if (ObjectUtil.isNull(otherConfig)) {
                otherConfig = new DtbUserBookConfig();
                otherConfig.setBookId(bookId);
                otherConfig.setUserId(userId);
                otherConfig.setIsMobile(otherDeviceType);
                otherConfig.setFontSize(otherDeviceType == 1 ? 30 : 14); // 设置默认字号
                if (chapterId != null) {
                    otherConfig.setLastChapterId(chapterId);
                } else {
                    Long versionId = getUserVersionId(dtbBook, userId);
                    DtbBookChapter chapter = getFirstChapter(bookId, versionId);
                    if (ObjectUtil.isNotNull(chapter)) {
                        otherConfig.setLastChapterId(chapter.getChapterId());
                    } else {
                        return AjaxResult.error(HttpStatus.NO_CHAPTER, "没有可阅读的章节");
                    }
                }
                otherConfig.setLastPageNumber(1);
                otherConfig.setLastSeeDate(new Date());
                dtbUserBookConfigMapper.insert(otherConfig);
            }
        } else {
            vo.setBookId(bookId);
            if (chapterId!=null) {
                vo.setLastChapterId(chapterId);
            } else {
                DtbBookChapter chapter = getFirstChapter(bookId, dtbBook.getCurrentVersionId());
                vo.setLastChapterId(chapter.getChapterId());
            }
        }
        vo.setBookName(dtbBook.getBookName());
        DtbBook bookUpdateParam = new DtbBook();
        bookUpdateParam.setBookId(bookId);
        bookUpdateParam.setReadQuantity(dtbBook.getReadQuantity().longValue() + 1);
        dtbBookMapper.updateById(bookUpdateParam);
        return AjaxResult.success(vo);
    }

    public Long getUserVersionId(DtbBook dtbBook, Long userId) {
        LambdaQueryWrapper<DtbUserBook> dtbUserBookLambdaQueryWrapper = new LambdaQueryWrapper<>();
        dtbUserBookLambdaQueryWrapper.eq(DtbUserBook::getBookId,dtbBook.getBookId())
                .eq(DtbUserBook::getUserId,userId).gt(DtbUserBook::getExpireDate,new Date());
        DtbUserBook dtbUserBook = dtbUserBookMapper.selectOne(dtbUserBookLambdaQueryWrapper);
        if (ObjectUtil.isNull(dtbUserBook)) {
            return dtbBook.getCurrentVersionId();
        } else {
            return dtbUserBook.getVersionId();
        }
    }

    @Override
    public AjaxResult checkBookCopyText(String copyText) {
        DutpSiteConfig dutpSiteConfig = dutpSiteConfigMapper.selectById(1);
        Integer wordQuantity = copyText.length();
        Long userId = SecurityUtils.getUserId();
        String copyKey = DutpConstant.REDIS_COPY_TIME_KEY + userId +  "_" + DateUtils.getDate();
        Integer copyTime = redisService.getCacheObject(copyKey);
        if (copyTime == null) {
            copyTime = 0;
        }
        System.out.println(copyKey);
        // 用户类型0普通读者1学生2教师
        Integer userType = SecurityUtils.getLoginUser().getHomeUserType();

        if (userType.intValue() == 0) {
            if (wordQuantity> dutpSiteConfig.getReaderCopyWordLimit()) {
                return AjaxResult.error(HttpStatus.FORBIDDEN, "每次复制文字最多" + dutpSiteConfig.getReaderCopyWordLimit() + "字");
            }
            if (copyTime!=null && copyTime.intValue()>=dutpSiteConfig.getReaderCopyCountLimit()) {
                return AjaxResult.error(HttpStatus.FORBIDDEN, "每天最多复制" + dutpSiteConfig.getReaderCopyCountLimit() + "次");
            } else {
                redisService.setCacheObject(copyKey, copyTime + 1,1l, TimeUnit.DAYS);
            }
        } else if (userType.intValue() == 1) {
            if (wordQuantity> dutpSiteConfig.getStudentCopyWordLimit()) {
                return AjaxResult.error(HttpStatus.FORBIDDEN, "每次复制文字最多" + dutpSiteConfig.getStudentCopyWordLimit() + "字");
            }
            if (copyTime!=null && copyTime.intValue()>=dutpSiteConfig.getStudentCopyCountLimit()) {
                return AjaxResult.error(HttpStatus.FORBIDDEN, "每天最多复制" + dutpSiteConfig.getStudentCopyCountLimit() + "次");
            } else {
                redisService.setCacheObject(copyKey, copyTime + 1,1l, TimeUnit.DAYS);
            }
        } else {
            if (wordQuantity> dutpSiteConfig.getTeacherCopyWordLimit()) {
                return AjaxResult.error(HttpStatus.FORBIDDEN, "每次复制文字最多" + dutpSiteConfig.getTeacherCopyWordLimit() + "字");
            }
            if (copyTime!=null && copyTime.intValue()>=dutpSiteConfig.getTeacherCopyCountLimit()) {
                return AjaxResult.error(HttpStatus.FORBIDDEN, "每天最多复制" + dutpSiteConfig.getTeacherCopyCountLimit() + "次");
            } else {
                redisService.setCacheObject(copyKey, copyTime + 1, 1l, TimeUnit.DAYS);
            }
        }
        return AjaxResult.success();
    }

    private DtbBookChapter getFirstChapter(Long bookId, Long versionId) {
        LambdaQueryWrapper<DtbBookChapter> chapterLambdaQueryWrapper = new LambdaQueryWrapper<>();
        chapterLambdaQueryWrapper.eq(DtbBookChapter::getBookId,bookId)
                .eq(DtbBookChapter::getVersionId,versionId)
                .orderByAsc(DtbBookChapter::getSort).last(" limit 1");
        DtbBookChapter chapter = dtbBookChapterMapper.selectOne(chapterLambdaQueryWrapper);
        return chapter;
    }

}

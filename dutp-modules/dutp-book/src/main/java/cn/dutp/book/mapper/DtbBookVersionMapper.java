package cn.dutp.book.mapper;


import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;
import cn.dutp.book.domain.DtbBookVersion;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * 电子教材版本Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Repository
public interface DtbBookVersionMapper extends BaseMapper<DtbBookVersion>
{

    @Update("update dtb_book_version set book_id = #{bookId} where version_id = #{versionId}")
    int updateBookId(@Param("bookId") Long bookId, @Param("versionId") Long versionId);

    List<DtbBookVersion> versionList(DtbBookVersion dtbBookVersion);
}

package cn.dutp.book.service.impl;

import java.util.List;

import cn.dutp.book.domain.DtbArVrResourceFolder;
import cn.dutp.book.mapper.DtbArVrResourceFolderMapper;
import cn.dutp.book.service.IDtbArVrResourceFolderService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * AR/VR资源库文件夹Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class DtbArVrResourceFolderServiceImpl extends ServiceImpl<DtbArVrResourceFolderMapper, DtbArVrResourceFolder> implements IDtbArVrResourceFolderService
{
    @Autowired
    private DtbArVrResourceFolderMapper dtbArVrResourceFolderMapper;

    /**
     * 查询AR/VR资源库文件夹
     *
     * @param folderId AR/VR资源库文件夹主键
     * @return AR/VR资源库文件夹
     */
    @Override
    public DtbArVrResourceFolder selectDtbArVrResourceFolderByFolderId(Long folderId)
    {
        return this.getById(folderId);
    }

    /**
     * 查询AR/VR资源库文件夹列表
     *
     * @param dtbArVrResourceFolder AR/VR资源库文件夹
     * @return AR/VR资源库文件夹
     */
    @Override
    public List<DtbArVrResourceFolder> selectDtbArVrResourceFolderList(DtbArVrResourceFolder dtbArVrResourceFolder)
    {
        LambdaQueryWrapper<DtbArVrResourceFolder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotEmpty(dtbArVrResourceFolder.getFolderName())) {
            lambdaQueryWrapper.like(DtbArVrResourceFolder::getFolderName, dtbArVrResourceFolder.getFolderName());
        }
        if (ObjectUtil.isNotEmpty(dtbArVrResourceFolder.getParentId())) {
            lambdaQueryWrapper.eq(DtbArVrResourceFolder::getParentId, dtbArVrResourceFolder.getParentId());
        }
        lambdaQueryWrapper.eq(DtbArVrResourceFolder::getDelFlag, "0");
        lambdaQueryWrapper.orderByAsc(DtbArVrResourceFolder::getCreateTime);
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增AR/VR资源库文件夹
     *
     * @param dtbArVrResourceFolder AR/VR资源库文件夹
     * @return 结果
     */
    @Override
    public boolean insertDtbArVrResourceFolder(DtbArVrResourceFolder dtbArVrResourceFolder)
    {
        return this.save(dtbArVrResourceFolder);
    }

    /**
     * 修改AR/VR资源库文件夹
     *
     * @param dtbArVrResourceFolder AR/VR资源库文件夹
     * @return 结果
     */
    @Override
    public boolean updateDtbArVrResourceFolder(DtbArVrResourceFolder dtbArVrResourceFolder)
    {
        return this.updateById(dtbArVrResourceFolder);
    }

    /**
     * 批量删除AR/VR资源库文件夹
     *
     * @param folderIds 需要删除的AR/VR资源库文件夹主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbArVrResourceFolderByFolderIds(List<Long> folderIds)
    {
        return this.removeByIds(folderIds);
    }

    /**
     * 删除AR/VR资源库文件夹信息
     *
     * @param folderId AR/VR资源库文件夹主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbArVrResourceFolderByFolderId(Long folderId)
    {
        return this.removeById(folderId);
    }
} 
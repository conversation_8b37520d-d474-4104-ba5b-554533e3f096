package cn.dutp.book.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.book.domain.DtbUserQuestionFolder;
import cn.dutp.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.book.service.IDtbUserQuestionFolderService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 题库目录Controller
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@RestController
@RequestMapping("/userQuestionFolder")
public class DtbUserQuestionFolderController extends BaseController
{
    @Autowired
    private IDtbUserQuestionFolderService dtbBookQuestionFolderService;

    /**
     * 查询题库目录列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbUserQuestionFolder dtbUserQuestionFolder)
    {

        // 接口应该是用户隔离的
        Long userId = SecurityUtils.getUserId();
        dtbUserQuestionFolder.setUserId(userId);


        startPage();
        List<DtbUserQuestionFolder> list = dtbBookQuestionFolderService.selectDtbBookQuestionFolderList(dtbUserQuestionFolder);
        return getDataTable(list);
    }

    /**
     * 导出题库目录列表
     */
    @RequiresPermissions("book:folder:export")
    @Log(title = "导出题库目录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbUserQuestionFolder dtbUserQuestionFolder)
    {
        List<DtbUserQuestionFolder> list = dtbBookQuestionFolderService.selectDtbBookQuestionFolderList(dtbUserQuestionFolder);
        ExcelUtil<DtbUserQuestionFolder> util = new ExcelUtil<DtbUserQuestionFolder>(DtbUserQuestionFolder.class);
        util.exportExcel(response, list, "题库目录数据");
    }

    /**
     * 获取题库目录详细信息
     */
    @RequiresPermissions("book:bookQuestion:query")
    @GetMapping(value = "/{folderId}")
    public AjaxResult getInfo(@PathVariable("folderId") Long folderId)
    {
        return success(dtbBookQuestionFolderService.selectDtbBookQuestionFolderByFolderId(folderId));
    }

    /**
     * 新增题库目录
     */
    @RequiresPermissions("book:bookQuestion:query")
    @Log(title = "新增题库目录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbUserQuestionFolder dtbUserQuestionFolder)
    {

        // 接口应该是用户隔离的
        Long userId = SecurityUtils.getUserId();
        dtbUserQuestionFolder.setUserId(userId);

        return toAjax(dtbBookQuestionFolderService.insertDtbBookQuestionFolder(dtbUserQuestionFolder));
    }

    /**
     * 修改题库目录
     */
    @RequiresPermissions("book:bookQuestion:query")
    @Log(title = "修改题库目录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbUserQuestionFolder dtbUserQuestionFolder)
    {
        return toAjax(dtbBookQuestionFolderService.updateDtbBookQuestionFolder(dtbUserQuestionFolder));
    }

    /**
     * 删除题库目录
     */
    @RequiresPermissions("book:bookQuestion:query")
    @Log(title = "删除题库目录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{folderIds}")
    public AjaxResult remove(@PathVariable Long[] folderIds)
    {
        return toAjax(dtbBookQuestionFolderService.deleteDtbBookQuestionFolderByFolderIds(Arrays.asList(folderIds)));
    }

}

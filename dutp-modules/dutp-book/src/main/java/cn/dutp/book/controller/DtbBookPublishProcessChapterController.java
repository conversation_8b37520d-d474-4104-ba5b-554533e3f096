package cn.dutp.book.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.book.domain.DtbBookPublishProcessChapter;
import cn.dutp.book.service.IDtbBookPublishProcessChapterService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 流程章节关系表Controller
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@RestController
@RequestMapping("/book/processChapter")
public class DtbBookPublishProcessChapterController extends BaseController
{
    @Autowired
    private IDtbBookPublishProcessChapterService dtbBookPublishProcessChapterService;

    /**
     * 查询流程章节关系表列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbBookPublishProcessChapter dtbBookPublishProcessChapter)
    {
        startPage();
        List<DtbBookPublishProcessChapter> list = dtbBookPublishProcessChapterService.selectDtbBookPublishProcessChapterList(dtbBookPublishProcessChapter);
        return getDataTable(list);
    }

    /**
     * 导出流程章节关系表列表
     */
    @RequiresPermissions("book:processChapter:export")
    @Log(title = "导出流程章节关系表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbBookPublishProcessChapter dtbBookPublishProcessChapter)
    {
        List<DtbBookPublishProcessChapter> list = dtbBookPublishProcessChapterService.selectDtbBookPublishProcessChapterList(dtbBookPublishProcessChapter);
        ExcelUtil<DtbBookPublishProcessChapter> util = new ExcelUtil<DtbBookPublishProcessChapter>(DtbBookPublishProcessChapter.class);
        util.exportExcel(response, list, "流程章节关系表数据");
    }

    /**
     * 获取流程章节关系表详细信息
     */
    @RequiresPermissions("book:processChapter:query")
    @GetMapping(value = "/{processChapterId}")
    public AjaxResult getInfo(@PathVariable("processChapterId") Long processChapterId)
    {
        return success(dtbBookPublishProcessChapterService.selectDtbBookPublishProcessChapterByProcessChapterId(processChapterId));
    }

    /**
     * 新增流程章节关系表
     */
    @RequiresPermissions("book:processChapter:add")
    @Log(title = "新增流程章节关系表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookPublishProcessChapter dtbBookPublishProcessChapter)
    {
        return toAjax(dtbBookPublishProcessChapterService.insertDtbBookPublishProcessChapter(dtbBookPublishProcessChapter));
    }

    /**
     * 修改流程章节关系表
     */
    @RequiresPermissions("book:processChapter:edit")
    @Log(title = "修改流程章节关系表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookPublishProcessChapter dtbBookPublishProcessChapter)
    {
        return toAjax(dtbBookPublishProcessChapterService.updateDtbBookPublishProcessChapter(dtbBookPublishProcessChapter));
    }

    /**
     * 删除流程章节关系表
     */
    @RequiresPermissions("book:processChapter:remove")
    @Log(title = "删除流程章节关系表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{processChapterIds}")
    public AjaxResult remove(@PathVariable Long[] processChapterIds)
    {
        return toAjax(dtbBookPublishProcessChapterService.deleteDtbBookPublishProcessChapterByProcessChapterIds(Arrays.asList(processChapterIds)));
    }
}

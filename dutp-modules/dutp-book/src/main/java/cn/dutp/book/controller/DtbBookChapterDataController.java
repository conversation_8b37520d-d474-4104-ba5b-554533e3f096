package cn.dutp.book.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.Logical;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.book.domain.DtbBookChapterData;
import cn.dutp.book.domain.vo.DtbBookApplicationUserDataVo;
import cn.dutp.book.service.IDtbBookChapterDataService;
import cn.dutp.common.core.utils.poi.ExcelUtil;


/**
 * 章节数据统计Controller
 *
 * <AUTHOR>
 * @date 2025-01-10
 */
@RestController
@RequestMapping("/chapterData")
public class DtbBookChapterDataController extends BaseController {
    @Autowired
    private IDtbBookChapterDataService dtbBookChapterDataService;

    /**
     * 查询应用数据列表
     */
    @GetMapping("/list")
    public AjaxResult list(DtbBookChapterData dtbBookChapterData) {
        List<DtbBookChapterData> list = dtbBookChapterDataService.selectDtbBookChapterDataList(dtbBookChapterData);
        return success(list);
    }

    /**
     * 查询数据总览列表
     */
    @GetMapping("/dataOverview")
    public AjaxResult dataOverview(DtbBookChapterData dtbBookChapterData) {
        DtbBookChapterData bookChapterData = dtbBookChapterDataService.dataOverview(dtbBookChapterData);
        return success(bookChapterData);
    }

    /**
     * 导出应用数据
     */
    @RequiresPermissions(value = {"book:book:exportAppData"}, logical = Logical.OR)
    @Log(title = "导出应用数据", businessType = BusinessType.EXPORT)
    @PostMapping("/exportAppData")
    public void exportAppData(HttpServletResponse response, DtbBookChapterData dtbBookChapterData) {
        dtbBookChapterDataService.exportAppData(response, dtbBookChapterData);
    }

    /**
     * 导出数据总览
     */
    @RequiresPermissions(value = {"book:book:exportDataOverview"}, logical = Logical.OR)
    @Log(title = "导出数据总览", businessType = BusinessType.EXPORT)
    @PostMapping("/exportDataOverview")
    public void exportDataOverview(HttpServletResponse response, DtbBookChapterData dtbBookChapterData) {
        dtbBookChapterDataService.exportDataOverview(response, dtbBookChapterData);
    }

    /**
     * 内部调用 - 导出应用数据
     */
    @PostMapping("/inner/exportAppData")
    public void innerExportAppData(HttpServletResponse response, @RequestBody DtbBookChapterData dtbBookChapterData) {
        dtbBookChapterDataService.exportAppData(response, dtbBookChapterData);
    }

    /**
     * 内部调用 - 导出数据总览
     */
    @PostMapping("/inner/exportDataOverview")
    public void innerExportDataOverview(HttpServletResponse response, @RequestBody DtbBookChapterData dtbBookChapterData) {
        dtbBookChapterDataService.exportDataOverview(response, dtbBookChapterData);
    }

    /**
     * 新增章节数据统计
     */
    @RequiresPermissions("book:chapterData:add")
    @Log(title = "新增章节数据统计", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookChapterData dtbBookChapterData) {
        return toAjax(dtbBookChapterDataService.insertDtbBookChapterData(dtbBookChapterData));
    }

    /**
     * 修改章节数据统计
     */
    @RequiresPermissions("book:chapterData:edit")
    @Log(title = "修改章节数据统计", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookChapterData dtbBookChapterData) {
        return toAjax(dtbBookChapterDataService.updateDtbBookChapterData(dtbBookChapterData));
    }

    /**
     * 内部调用 - 查询教材应用用户数据
     */
    @PostMapping("/inner/selectBookApplicationUserData")
    public AjaxResult innerSelectBookApplicationUserData(@RequestBody DtbBookApplicationUserDataVo dtbBookApplicationUserData) {
        DtbBookApplicationUserDataVo data = dtbBookChapterDataService.selectBookApplicationUserData(dtbBookApplicationUserData);
        return success(data);
    }

}

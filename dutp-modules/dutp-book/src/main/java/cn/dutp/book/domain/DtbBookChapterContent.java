package cn.dutp.book.domain;


import cn.dutp.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;

/**
 * 数字教材章节内容对象
 *
 * <AUTHOR>
 * @date 2024-11-30
 */
@Data
public class DtbBookChapterContent implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 章节ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    /**
     * 章节内容
     */
    private String content;

    /**
     * 开始的页码
     */
    private Integer startPageNumber;

}

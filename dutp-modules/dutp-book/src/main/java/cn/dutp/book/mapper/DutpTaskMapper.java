package cn.dutp.book.mapper;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import cn.dutp.book.domain.DutpTask;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 教材管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@Repository
public interface DutpTaskMapper extends BaseMapper<DutpTask> {

    List<DutpTask> selectTaskList(@Param("userId") Long userId,@Param("param") DutpTask dutpTask);

    @Select("SELECT task_id FROM dutp_task WHERE task_type = #{taskType} AND data_id = #{bookId} AND DATE_FORMAT(create_time, '%Y-%m-%d') = DATE_FORMAT(#{date},'%Y-%m-%d') LIMIT 1")
    DutpTask selectTask(@Param("taskType")Integer taskType, @Param("bookId")Long bookId, @Param("date")Date date);
}

package cn.dutp.book.mapper;

import cn.dutp.domain.DtbBookGroup;
import cn.dutp.system.api.domain.SysUser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 数字教材作者编辑团队Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@Repository
public interface DtbBookGroupMapper extends BaseMapper<DtbBookGroup> {

    @Update("update dtb_book_group set del_flag = '2' where book_id = #{bookId}")
    int delAllByBookId(Long bookId);

    List<SysUser> groupUserList(Long bookId);

    @Select("select user_id from dtb_book_group where book_id = #{bookId} and role_type = #{type} and del_flag = '0'")
    Long queryGroupUserByType(@Param("type") Integer type, @Param("bookId") Long bookId);

    @Select("select count(*) from dtb_book_group where role_type in (5, 6) and book_id = #{bookId} and user_id = #{userId} and del_flag = '0'")
    Integer checkIsEditor(@Param("userId") Long userId, @Param("bookId") Long bookId);
}

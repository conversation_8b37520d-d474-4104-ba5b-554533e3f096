package cn.dutp.book.mapper;

import cn.dutp.book.domain.DtbUserBookLine;
import cn.dutp.book.domain.vo.DtbUserBookLineVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * DUTP-DTB_020划线Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Repository
public interface DtbUserBookLineMapper extends BaseMapper<DtbUserBookLine> {
    List<DtbUserBookLineVO> selectReaderUserBookLineList(DtbUserBookLine bookLine);

    List<DtbUserBookLine> exportBookLine(DtbUserBookLine dtbUserBookLine);
}

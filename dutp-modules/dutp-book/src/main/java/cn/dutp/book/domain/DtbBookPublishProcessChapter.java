package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 流程章节关系表对象 dtb_book_publish_process_chapter
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@TableName("dtb_book_publish_process_chapter")
public class DtbBookPublishProcessChapter extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** id */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long processChapterId;

    /** 审批流程id */
        @Excel(name = "审批流程id")
    private Long fromProcessId;

    /** 下一流程id */
    @Excel(name = "下一流程id")
    private Long toProcessId;

    /** 章节id */
        @Excel(name = "章节id")
    private Long chapterId;

}

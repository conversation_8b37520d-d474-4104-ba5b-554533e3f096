package cn.dutp.book.service;

import cn.dutp.book.domain.DutpAiUserConfig;
import cn.dutp.book.mapper.DutpAiUserConfigMapper;
import cn.dutp.common.core.domain.UploadFileDto;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.utils.AliyunOssStsUtil;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DutpAiHistory;
import cn.dutp.common.ai.common.ai.utils.baidu.TextToVideoUtil;
import cn.dutp.domain.DutpAiPrompt;
import cn.dutp.book.domain.DutpAiWhiteBlack;
import cn.dutp.book.domain.vo.XunfeiResultVo;
import cn.dutp.common.ai.common.ai.domain.ChatAiRequest;
import cn.dutp.common.ai.common.ai.utils.baidu.BaiduCommonUtil;
import cn.dutp.common.ai.common.ai.utils.baidu.GsonUtils;
import cn.dutp.common.ai.common.ai.utils.xunfei.ImageComplianceUtil;
import cn.dutp.common.ai.common.ai.utils.xunfei.*;
import cn.dutp.common.core.utils.StringUtils;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.alibaba.nacos.shaded.com.google.gson.JsonObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonParser;
import org.apache.commons.lang.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;


@Service
public class AiCommonServiceImpl {

    private final static Logger log = LoggerFactory.getLogger(AiCommonServiceImpl.class);

    @Autowired
    IDutpAiWhiteBlackService dutpAiWhiteBlackService;

    @Autowired
    IDutpAiHistoryService dutpAiHistoryService;

    @Autowired
    IDutpAiPromptService dutpAiPromptService;

    @Autowired
    AliyunOssStsUtil aliyunOssStsUtil;

    @Autowired
    DutpAiUserConfigMapper dutpAiUserConfigMapper;

    /*1续写2缩写3扩写4润色5生成试题6生成总结7生成学习目标8生成脑图9生成大纲*/
    public String getByPrompt(ChatAiRequest chatAiRequest, String chatCompletionsUrl, String apiPassword, DutpAiHistory history) {
        try {
            Integer count = 0;
            if (chatAiRequest.getAbility() == 26) {
                QueryWrapper<DutpAiUserConfig> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(DutpAiUserConfig::getUserId, SecurityUtils.getUserId())
                        .eq(DutpAiUserConfig::getUserType,chatAiRequest.getUserType())
                        .eq(DutpAiUserConfig::getDelFlag, 0);
                DutpAiUserConfig dutpAiUserConfig = dutpAiUserConfigMapper.selectOne(queryWrapper);
                if (ObjectUtil.isEmpty(dutpAiUserConfig) || dutpAiUserConfig.getAiExperimentCount() <= 0) {

                    return "次数已用完，请购买次数";
                }
                count = dutpAiUserConfig.getAiExperimentCount();
            }
            DutpAiPrompt dutp = dutpAiPromptService.selectDutpAiPromptByAbility(chatAiRequest.getAbility());
            ChatAiRequest dutpAiPrompt = new ChatAiRequest();
            dutpAiPrompt.setAbility(chatAiRequest.getAbility());
            if (chatAiRequest.getAbility() == 25) {
                dutpAiPrompt.setPrompt(String.format(dutp.getPrompt(),chatAiRequest.getDevelopmentLanguage(),chatAiRequest.getDevelopmentLanguage(),chatAiRequest.getDevelopmentLanguage(),chatAiRequest.getDevelopmentLanguage()));
                dutpAiPrompt.setQuestion(StringEscapeUtils.unescapeJava(chatAiRequest.getQuestion()));
            } else {
                dutpAiPrompt.setPrompt(dutp.getPrompt());
                dutpAiPrompt.setQuestion(chatAiRequest.getQuestion());
            }
            String requestParam = ChatAiUtil.buildHttpBody(dutpAiPrompt);
            log.info("入参：\n" + requestParam);
            Map<String, String> header = ChatAiUtil.buildHttpHeader(apiPassword);
            Map<String, Object> resultMap = HttpUtil.doPost2(chatCompletionsUrl, header, requestParam);

            ObjectMapper objectMapper = new ObjectMapper();
            String jsonString = objectMapper.writeValueAsString(resultMap);
            history.setAnswer(jsonString);
            dutpAiHistoryService.insertDutpAiHistory(history);
            log.info("实际返回结果：\n" + jsonString);
            // ai问答，精灵，翻译(26,23)限制使用次数
            if (chatAiRequest.getAbility() == 26) {
                minusCount(objectMapper,resultMap.get("body").toString(),count);
            }
            return editJson(jsonString);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void minusCount(ObjectMapper objectMapper,String jsonString,Integer aiExperimentCount) throws JsonProcessingException {
        // 判断请求结果扣除次数
        JsonNode rootNode = objectMapper.readTree(jsonString);
        int code = rootNode.path("code").asInt();
        if (code == 0) {
            updateCount(aiExperimentCount);
        }
    }

    private void updateCount(Integer aiExperimentCount){
        UpdateWrapper<DutpAiUserConfig> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(DutpAiUserConfig::getUserId, SecurityUtils.getUserId())
                .eq(DutpAiUserConfig::getDelFlag, 0)
                .set(DutpAiUserConfig::getAiExperimentCount,aiExperimentCount-1)
                .set(DutpAiUserConfig::getUpdateTime,new Date())
                .set(DutpAiUserConfig::getUpdateBy,SecurityUtils.getLoginUser().getUsername());
        dutpAiUserConfigMapper.update(null,updateWrapper);
    }

    /*文字纠错(上传黑名单 (违禁词替换) 和白名单 (免审词设置))*/
    public String uploadCorrectionWhiteBlackList(ChatAiRequest chatAiRequest, String appId, String uid, String resId, String regUrl, DutpAiHistory history) {
        DutpAiWhiteBlack black = new DutpAiWhiteBlack();
        black.setWhiteContent(chatAiRequest.getWhiteList());
        black.setBlackContent(chatAiRequest.getBlackList());
        black.setType(1);
        String res = null;
        String sid = null;
        try {
            res = UploadWhiteBlackListUtil.reg(appId, uid, resId, regUrl, chatAiRequest.getWhiteList(), chatAiRequest.getBlackList());
            log.info("文字纠错(上传黑名单 (违禁词替换) 和白名单 (免审词设置))返回结果：\n" + res);
            history.setAnswer(res);
            dutpAiHistoryService.insertDutpAiHistory(history);
            JSONObject jsonObject = new JSONObject(res);
            sid = jsonObject.get("sid").toString();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        black.setUid(sid);
        DutpAiWhiteBlack entity = dutpAiWhiteBlackService.selectDutpAiWhiteBlackByUid(sid);
        if (ObjectUtil.isNotEmpty(entity)) {
            dutpAiWhiteBlackService.updateDutpAiWhiteBlack(black);
        } else {
            dutpAiWhiteBlackService.insertDutpAiWhiteBlack(black);
        }
        return sid;
    }

    /*文字纠错*/
    public String webTextCorrection(ChatAiRequest chatAiRequest, String appid, String apiKey, String apiSecret, String textCorrectionHostUrl, DutpAiHistory history) {
        try {
            String authUrl = XunfeiCommonUtil.getAuthUrl(textCorrectionHostUrl, apiKey, apiSecret);
            String json = "";
            if (chatAiRequest.getCheckBlackWhite()) {
                DutpAiWhiteBlack black = dutpAiWhiteBlackService.selectDutpAiWhiteBlackByType(1);
                if (ObjectUtil.isNotEmpty(black)) {
                    json = WebTextCorrectionUtil.getRequestJson(appid, chatAiRequest.getQuestion(), chatAiRequest.getCheckBlackWhite(), black.getUid(), black.getUid());
                } else {
                    json = WebTextCorrectionUtil.getRequestJson(appid, chatAiRequest.getQuestion(), chatAiRequest.getCheckBlackWhite(), null, null);
                }
            } else {
                json = WebTextCorrectionUtil.getRequestJson(appid, chatAiRequest.getQuestion(), chatAiRequest.getCheckBlackWhite(), null, null);
            }

            String backResult = WebTextCorrectionUtil.doPostJson(authUrl, json);
            log.info("文字纠错返回结果：\n" + backResult);
            history.setAnswer(backResult);
            dutpAiHistoryService.insertDutpAiHistory(history);
            return backResult;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /*翻译*/
    public String translate(ChatAiRequest chatAiRequest, String appid, String apiKey, String apiSecret, String webitsUrl, DutpAiHistory history) {
        try {
            QueryWrapper<DutpAiUserConfig> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(DutpAiUserConfig::getUserId, SecurityUtils.getUserId())
                    .eq(DutpAiUserConfig::getUserType,chatAiRequest.getUserType())
                    .eq(DutpAiUserConfig::getDelFlag, 0);
            DutpAiUserConfig dutpAiUserConfig = dutpAiUserConfigMapper.selectOne(queryWrapper);
            if (ObjectUtil.isEmpty(dutpAiUserConfig) || dutpAiUserConfig.getAiExperimentCount() <= 0) {
                return "次数已用完，请购买次数";
            }
            String body = TranslateUtil.buildHttpBody(appid, chatAiRequest.getFromLanguage(), chatAiRequest.getToLanguage(), chatAiRequest.getQuestion());
            Map<String, String> header = TranslateUtil.buildHttpHeader(body, webitsUrl, apiSecret, apiKey);
            Map<String, Object> resultMap = HttpUtil.doPost2(webitsUrl, header, body);
            if (resultMap != null) {
                String resultStr = resultMap.get("body").toString();
                log.info("【OTS WebAPI 接口调用结果】\n" + resultStr);
                //以下仅用于调试
                Gson json = new Gson();
                XunfeiResultVo resultData = json.fromJson(resultStr, XunfeiResultVo.class);
                int code = resultData.getCode();
                if (resultData.getCode() != 0) {
                    log.info("请前往https://www.xfyun.cn/document/error-code?code=" + code + "查询解决办法");
                }
                history.setAnswer(resultStr);
                log.info("翻译返回结果：\n" + resultStr);
                dutpAiHistoryService.insertDutpAiHistory(history);
                ObjectMapper objectMapper = new ObjectMapper();
                // 根据请求结果扣除次数
                if (resultData.getCode() == 0) {
                    updateCount(dutpAiUserConfig.getAiExperimentCount());
                }
                return resultStr;
            } else {
                log.info("调用失败！请根据错误信息检查代码，接口文档：https://www.xfyun.cn/doc/nlp/niutrans/API.html");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    /*文本合规*/
    public String textCompliance(ChatAiRequest chatAiRequest, String apiKey, String appid, String apiSecret, String textComplianceUrl, DutpAiHistory history) {
        try {
            String json = "";
            if (chatAiRequest.getCheckBlackWhite()) {
                DutpAiWhiteBlack black = dutpAiWhiteBlackService.selectDutpAiWhiteBlackByType(2);
                if (ObjectUtil.isNotEmpty(black)) {
                    json = TextComplianceUtil.getJsonString(chatAiRequest.getQuestion(), chatAiRequest.getCheckBlackWhite(), black.getUid(), black.getUid());
                } else {
                    json = TextComplianceUtil.getJsonString(chatAiRequest.getQuestion(), chatAiRequest.getCheckBlackWhite(), null, null);
                }
            } else {
                json = TextComplianceUtil.getJsonString(chatAiRequest.getQuestion(), chatAiRequest.getCheckBlackWhite(), null, null);
            }
            // 获取鉴权
            Map<String, String> urlParams = CommonUtil.getAuth(appid, apiKey, apiSecret);
            // 发起请求
            String returnResult = CommonUtil.doPostJson(textComplianceUrl, urlParams, json);
            log.info("文本合规返回结果：\n" + returnResult);
            history.setAnswer(returnResult);
            dutpAiHistoryService.insertDutpAiHistory(history);
            String result = checkReturnResult(returnResult,"000000");
            return result;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /*图片合规*/
    public String imageCompliance(ChatAiRequest chatAiRequest, String appid, String apiKey, String apiSecret, String imageComplianceUrl, DutpAiHistory history) {
        try {
            // 1、业务参数
            String json = ImageComplianceUtil.getJsonString(chatAiRequest.getQuestion());
            // 2、获取鉴权
            Map<String, String> urlParams = CommonUtil.getAuth(appid, apiKey, apiSecret);
            ///3、发起请求
            String returnResult = CommonUtil.doPostJson(imageComplianceUrl, urlParams, json);
            history.setAnswer(returnResult);
            dutpAiHistoryService.insertDutpAiHistory(history);
            log.info("图片合规返回结果：\n" + returnResult);
            String result = checkReturnResult(returnResult,"000000");
            return result;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private String checkReturnResult(String returnResult,String resultCode) {
        JSONObject jsonObject = new JSONObject(returnResult);
        String code = jsonObject.get("sid").toString();
        if (!code.equals(resultCode)) {
            jsonObject.put("msg",jsonObject.get("desc").toString());
            return jsonObject.toString();
        } else {
            return returnResult;
        }
    }

    /*音频合规*/
    public String audioCompliance(ChatAiRequest chatAiRequest, String audioComplianceUrl, String appid, String apiKey, String apiSecret, String videoComplianceQueryUrl, DutpAiHistory history) {
        try {
            /*chatAiRequest.getQuestion()待校验的音频公网地址*/
            // 1、业务参数
            String json = AudioComplianceUtil.getJsonString(audioComplianceUrl, chatAiRequest.getQuestion());
            // 2、获取鉴权
            Map<String, String> urlParams = CommonUtil.getAuth(appid, apiKey, apiSecret);
            // 3、发起请求
            String returnResult = CommonUtil.doPostJson(audioComplianceUrl, urlParams, json);
            String request_id = (String) com.alibaba.fastjson.JSONObject.parseObject(com.alibaba.fastjson.JSONObject.parseObject(returnResult).get("data").toString()).get("request_id");
            //4、拿到request_id后主动查询合规结果
            while (StringUtils.isNotBlank(request_id)) {
                String queryJson = "{\n" +
                        "  \"request_id\": \"" + request_id + "\"\n" +
                        "}";
                urlParams = CommonUtil.getAuth(appid, apiKey, apiSecret);
                returnResult = CommonUtil.doPostJson(videoComplianceQueryUrl, urlParams, queryJson);
                history.setAnswer(returnResult);
                log.info("音频合规返回结果：\n" + returnResult);
                dutpAiHistoryService.insertDutpAiHistory(history);
                Integer finishIndex = (Integer) com.alibaba.fastjson.JSONObject.parseObject(com.alibaba.fastjson.JSONObject.parseObject(returnResult).get("data").toString()).get("audit_status");
                if (finishIndex == 0) {
                    log.info("音频合规待审核...");
                }
                if (finishIndex == 1) {
                    log.info("音频合规审核中...");
                }
                if (finishIndex == 2) {
                    log.info("音频合规审核完成：");
                    log.info(returnResult);
                    break;
                }
                if (finishIndex == 4) {
                    log.info("音频合规审核异常：");
                    log.info(returnResult);
                }
                Thread.sleep(3000);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    /*视频合规*/
    public String videoCompliance(ChatAiRequest chatAiRequest, String appid, String apiKey, String apiSecret, String videoComplianceUrl, String videoComplianceQueryUrl, DutpAiHistory history) {
        try {
            // 1、业务参数,chatAiRequest.getQuestion()视频公网地址
            String json = VideoComplianceUtil.getJsonString(chatAiRequest.getQuestion());
            // 2、获取鉴权
            Map<String, String> urlParams = CommonUtil.getAuth(appid, apiKey, apiSecret);
            ///3、发起请求
            String returnResult = CommonUtil.doPostJson(videoComplianceUrl, urlParams, json);
            // log.info("请到回调地址或存储库查看结果，调用信息如下：\n" + returnResult);
            String request_id = (String) com.alibaba.fastjson.JSONObject.parseObject(com.alibaba.fastjson.JSONObject.parseObject(returnResult).get("data").toString()).get("request_id");
            //4、拿到request_id后主动查询合规结果
            while (true) {
                String queryJson = "{\n" +
                        "  \"request_id\": \"" + request_id + "\"\n" +
                        "}";
                urlParams = CommonUtil.getAuth(appid, apiKey, apiSecret);
                returnResult = CommonUtil.doPostJson(videoComplianceQueryUrl, urlParams, queryJson);
                log.info("视频合规返回结果：\n" + returnResult);
                history.setAnswer(returnResult);
                dutpAiHistoryService.insertDutpAiHistory(history);
                Integer finishIndex = (Integer) com.alibaba.fastjson.JSONObject.parseObject(com.alibaba.fastjson.JSONObject.parseObject(returnResult).get("data").toString()).get("audit_status");
                if (finishIndex == 0) {
                    log.info("视频合规待审核...");
                }
                if (finishIndex == 1) {
                    log.info("视频合规审核中...");
                }
                if (finishIndex == 2) {
                    log.info("视频合规审核完成：");
                    log.info(returnResult);
                    break;
                }
                if (finishIndex == 4) {
                    log.info("视频合规审核异常：");
                    log.info(returnResult);
                }
                Thread.sleep(5000);
            }
            // 结果示例{"code":"000000","desc":"请求成功","data":{"request_id":"T20241115115004019c6592bb50b4000","audit_status":2,"result_list":[{"name":"xxxxb4d.mp4","suggest":"pass","detail":{"images":[],"audios":[]}}]},"sid":"d02dca5c2e664bd1bc8d6b27cf6aeb6c"}
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    /*文生图*/
    public String textToPicture(ChatAiRequest chatAiRequest, String textToPictureUrl, String apiKey, String apiSecret, String appid, DutpAiHistory history) {
        try {
            String authUrl = TextToPictureUtil.getAuthUrl(textToPictureUrl, apiKey, apiSecret);
            String json = TextToPictureUtil.createRequest(appid, chatAiRequest.getQuestion());
            String res = CommonUtil.doPostJson(authUrl, null, json);
            history.setAnswer(res);
            dutpAiHistoryService.insertDutpAiHistory(history);
            log.info("文生图返回结果：\n" + res);
            return res;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /*文生语音*/
    public String textToVoice(ChatAiRequest chatAiRequest, String textToVoiceUrl,String apiKey,String apiSecret,String appId, DutpAiHistory history) throws Exception {
        String future = TextToVoiceUtil.getPayload(chatAiRequest,appId,apiKey,apiSecret,textToVoiceUrl);
        history.setAnswer(future);
        dutpAiHistoryService.insertDutpAiHistory(history);
        log.info("文生语音返回结果：\n" + future);
        //上传oss
        String url = uploadVideo(future);
        return url;
    }

    private String uploadVideo(String jsonString) {
        // 解码Base64字符串为字节数组
        byte[] decodedBytes = Base64.getDecoder().decode(jsonString);
        // 创建临时文件
        File tempFile = null;
        try {
            // 创建临时文件并写入字节数据
            tempFile = File.createTempFile("uploadVideo", ".mp3"); // 可以根据文件类型调整扩展名
            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                fos.write(decodedBytes);
            }

            // 使用aliyunOssStsUtil上传文件
            UploadFileDto uploadFileDto = aliyunOssStsUtil.uploadFile(tempFile);
            log.info("文生语音上传完后的文件url: {}", uploadFileDto);

            // 返回文件的URL
            return uploadFileDto.getFileUrl();
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        } finally {
            // 删除临时文件
            if (tempFile != null && tempFile.exists()) {
                boolean deleted = tempFile.delete();
                if (deleted) {
                    log.info("临时文件已被删除: {}", tempFile.getAbsolutePath());
                } else {
                    log.warn("临时文件删除失败: {}", tempFile.getAbsolutePath());
                }
            }
        }
    }

    /*百度图片增强（图片修复）*/
    public String imageEnhancement(ChatAiRequest chatAiRequest, String clientId, String clientSecret, String baiduTokenUrl, String baiduInpaintUrl, DutpAiHistory history) {
        /*chatAiRequest.getQuestion()待修复图片的公网地址*/
        try {
            String accessToken = BaiduCommonUtil.getAccessToken(clientId, clientSecret, baiduTokenUrl);
            Map<String, Object> map = new HashMap<>();
            map.put("url", chatAiRequest.getQuestion());
            List<Object> rectangle = new ArrayList<>();
            Map<String, Object> rectangleMap = new HashMap<>();
            // 框选范围
            rectangleMap.put("top", chatAiRequest.getTop());
            rectangleMap.put("left", chatAiRequest.getLeft());
            rectangleMap.put("width", chatAiRequest.getWidth());
            rectangleMap.put("height", chatAiRequest.getHeight());
            rectangle.add(rectangleMap);
            map.put("rectangle", rectangle);
            String param = GsonUtils.toJson(map);
            String result = cn.dutp.common.ai.common.ai.utils.baidu.HttpUtil.post(baiduInpaintUrl, accessToken, "application/json", param);
            Gson gson = new Gson();
            JsonObject jsonObject = gson.fromJson(result, JsonObject.class);
            String base64 = jsonObject.get("image").getAsString();
            history.setAnswer(result);
            dutpAiHistoryService.insertDutpAiHistory(history);
            log.info("图像修复返回结果：\n" + base64);
            return base64;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /*百度图片增强（清晰度增强）*/
    public String enhancedClarity(ChatAiRequest chatAiRequest, String clientId, String clientSecret, String baiduTokenUrl, String baifduImageEnhancement, DutpAiHistory history) {
        /*chatAiRequest.getQuestion()待修复图片的公网地址*/
        // todo 文件类型待确定
        // String image = "" 图片base64编码，url和image二选一
        try {
            String accessToken = cn.dutp.common.ai.common.ai.utils.baidu.BaiduCommonUtil.getAccessToken(clientId, clientSecret, baiduTokenUrl);
            // 本地文件路径
//        String filePath = "[本地文件路径]";
//        byte[] imgData = FileUtil.readFileByBytes(filePath);
//        String imgStr = Base64Util.encode(imgData);
//        String imgParam = URLEncoder.encode(imgStr, "UTF-8");
//
//        String param = "image=" + imgParam;
            String param = "url=" + chatAiRequest.getQuestion();
            String result = cn.dutp.common.ai.common.ai.utils.baidu.HttpUtil.post(baifduImageEnhancement, accessToken, param);
            Gson gson = new Gson();
            JsonObject jsonObject = gson.fromJson(result, JsonObject.class);
            String base64 = jsonObject.get("image").getAsString();
            history.setAnswer(result);
            dutpAiHistoryService.insertDutpAiHistory(history);
            log.info("图像增强返回结果：\n" + base64);
            return base64;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /*重写*/
    public String textRewriting(ChatAiRequest chatAiRequest,String textRewritingUrl,String appid, String apiKey, String apiSecret, DutpAiHistory history) throws Exception {
        String level = "<L6>";// todo 改写等级，L1~L6
        String resp = TextRewritingUtil.doRequest(textRewritingUrl,appid,apiKey,apiSecret,level,chatAiRequest.getQuestion());
        log.info("text字段Base64解码后=>" + resp);
        history.setAnswer(resp);
        dutpAiHistoryService.insertDutpAiHistory(history);
        return resp;
    }

    private String editJson(String json){
        // 解析原始 JSON 字符串
        JSONObject jsonObj = new JSONObject(json);

        // 提取 content 字段
        String body = jsonObj.get("body").toString();

        String content = new JSONObject(body).getJSONArray("choices")
                .getJSONObject(0)
                .getJSONObject("message")
                .get("content").toString();

        // 去掉多余的部分并修复 content
        String fixedContent = fixContent(content);

        String newBody = new JSONObject(body).getJSONArray("choices")
                .getJSONObject(0)
                .getJSONObject("message")
                .put("content",fixedContent).toString();
        JSONObject newResult = new JSONObject(newBody);

        // 更新修正后的 content
        JSONObject newRes = new JSONObject();
        newRes.put("body",newResult);
        return newRes.toString();
    }

    // 修正 content 中的非标准 JSON 格式
    private static String fixContent(String content) {
        // 判断 content 是否为标准的 JSON 格式
        if (isValidJson(content)) {
            return content;  // 如果是有效的 JSON，直接返回
        }

        // 如果 content 不是有效的 JSON，则去掉 json 和换行符，返回有效 JSON
        content = content.replaceAll("```json", "").replaceAll("```", "");

        // 最终去掉空白符、换行符等多余的字符后返回
        return content.trim();
    }

    // 判断是否为有效的 JSON 格式
    private static boolean isValidJson(String content) {
        try {
            new JSONObject(content);  // 尝试解析为 JSON 对象
            return true;  // 如果没有抛出异常，则为有效 JSON
        } catch (Exception e) {
            return false;  // 如果抛出异常，则不是有效 JSON
        }
    }

    /*文生视频*/
    public String textToVideo(ChatAiRequest chatAiRequest, String clientId, String clientSecret, String baiduTokenUrl,String textToVideoUrl, DutpAiHistory history) {
        try {
            String accessToken = BaiduCommonUtil.getAccessToken(clientId, clientSecret, baiduTokenUrl);
            String res = TextToVideoUtil.getResponse(textToVideoUrl,chatAiRequest.getQuestion(),accessToken);
            log.info("文生视频返回结果：\n" + res);
            history.setAnswer(res);
            dutpAiHistoryService.insertDutpAiHistory(history);
            return res;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    public DutpAiUserConfig getAiExperiment() {
        // ai问答，精灵，翻译(26,23)限制使用次数
        QueryWrapper<DutpAiUserConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DutpAiUserConfig::getUserId, SecurityUtils.getUserId())
                .eq(DutpAiUserConfig::getDelFlag, 0);
        if (SecurityUtils.getLoginUser().getUserType() == 1) {
            // 后台用户
            queryWrapper.lambda().eq(DutpAiUserConfig::getUserType, 1);
        } else {
            // 前台用户
            queryWrapper.lambda().eq(DutpAiUserConfig::getUserType, 0);
        }
        DutpAiUserConfig dutpAiUserConfig = dutpAiUserConfigMapper.selectOne(queryWrapper);
        return dutpAiUserConfig;
    }

    //返回的json结果拆解
    public class JsonParse {
        int code;
        String sid;
        Data data;
    }

    public class Data {
        int status;
        String audio;
    }
}

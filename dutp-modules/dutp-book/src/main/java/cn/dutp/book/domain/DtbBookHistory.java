package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 数字教材保存历史对象 dtb_book_history
 *
 * <AUTHOR>
 * @date 2024-11-30
 */
@Data
@TableName("dtb_book_history")
public class DtbBookHistory extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long historyId;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 章节ID
     */
    @Excel(name = "章节ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    /**
     * 章节保存的mongodb中的objectId
     */
    @Excel(name = "章节保存的mongodb中的objectId")
    private String chapterObjectId;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("historyId", getHistoryId())
                .append("bookId", getBookId())
                .append("chapterId", getChapterId())
                .append("chapterObjectId", getChapterObjectId())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

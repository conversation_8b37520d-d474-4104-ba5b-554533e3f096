package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.util.List;

/**
 * 试卷题型集合对象 dtb_test_paper_question_collection
 *
 * <AUTHOR>
 * @date 2025-02-08
 */

@Data
public class DtbTestPaperQuestionCollection extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableId
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long collectionId;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long paperId;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Integer sort;

    /**
     * 小题类型1单选 2多选 3填空 4排序 5连线 6简答 7判断 8编程
     */
    @Excel(name = "小题类型1单选 2多选 3填空 4排序 5连线 6简答 7判断 8编程")
    private Integer questionType;

    /**
     * 小题集合
     */
    @TableField(exist = false)
    private List<DtbTestPaperQuestion> questionList;


    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("collectionId", getCollectionId())
                .append("paperId", getPaperId())
                .append("sort", getSort())
                .append("questionType", getQuestionType())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

package cn.dutp.book.websocket;


import lombok.extern.slf4j.Slf4j;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class BookChapterContentEditorWsSessionManager {

    /**
     * 保存连接 session 的地方
     */
    private static ConcurrentHashMap<String, WebSocketSession> CHAPTER_POOL = new ConcurrentHashMap<>();


    /**
     * 添加章节编写人的session
     *
     * @param key
     */
    public static void addChapterEditor(String key, WebSocketSession session) {
        // 添加 session
        CHAPTER_POOL.put(key, session);
    }


    /**
     * 删除 章节编写人的session,会返回删除的 session
     *
     * @param key
     * @return
     */
    public static WebSocketSession removeChapterEditor(String key) {
        // 删除 session
        return CHAPTER_POOL.remove(key);
    }

    /**
     * 删除并同步关闭连接
     *
     * @param key
     */
    public static void removeAndClose(String key) {
        WebSocketSession session = removeChapterEditor(key);
        if (session != null) {
            try {
                // 关闭连接
                session.close();
            } catch (IOException e) {
                log.error("关闭连接错误,{}", e);
            }
        }
    }

    /**
     * 获得章节编写人的session
     *
     * @param key
     * @return
     */
    public static WebSocketSession getChapterEditor(String key) {
        // 获得 session
        return CHAPTER_POOL.get(key);
    }

    /**
     * 删除相同session_id
     * @param key
     */
    public static void removeBySessionId(String key) {
        CHAPTER_POOL.values().removeIf(value -> value.getAttributes().get("session_id").equals(key));
    }
}
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbBookCommonMapper">


    <select id="querySchoolByBookId" resultType="cn.dutp.domain.DutpBookSchoolVo">
        SELECT
            s.school_id,
            s.school_name
        FROM
            dutp_school s INNER JOIN dtb_book_school bs ON bs.school_id = s.school_id
        WHERE
            bs.book_id = #{bookId}
    </select>
    <select id="querySubjectBySubjectId" resultType="cn.dutp.domain.DutpSubjectVo">
        SELECT
            s.subject_id,
            s.subject_name,
            s.parent_id
        FROM
            dutp_subject s
        WHERE
            s.subject_id = #{subjectId}
    </select>
    <select id="queryAreaByBookId" resultType="cn.dutp.domain.DutpAreaVo">
        SELECT
            ba.area_id,
            ba.area_name
        FROM
            dutp_book_area ba
                INNER JOIN dutp_book_area_relation ar ON ba.area_id = ar.area_id
        WHERE
            ar.book_id = #{bookId}
    </select>
    <select id="querySchoolNameBySchoolId" resultType="java.lang.String">
        SELECT
            s.school_name
        FROM
            dutp_school s
        WHERE
            s.school_id = #{schoolId}
    </select>
    <select id="queryAdminUser" resultType="java.lang.Long">
        SELECT
            DISTINCT
            ur.user_id
        FROM
            sys_user u
                INNER JOIN sys_user_role ur ON ur.user_id = u.user_id
        WHERE
            u.del_flag = '0'
          AND u.STATUS = 0
          AND ur.role_id = 2
    </select>
    <select id="queryRole" resultType="java.lang.String">
        SELECT
            r.role_key
        FROM
            sys_role r
                INNER JOIN sys_user_role ur ON ur.role_id = r.role_id
        WHERE
            ur.user_id = #{userId}
    </select>
    <select id="queryEduRoleUser" resultType="java.lang.Long">
        SELECT DISTINCT
            ur.user_id
        FROM
            sys_role r
                INNER JOIN sys_user_role ur ON ur.role_id = r.role_id
                INNER JOIN sys_user u ON u.user_id = ur.user_id
        WHERE
            r.role_id = 5
          and u.school_id = #{schoolId}
    </select>

    <insert id="batchAddSchool">
        insert into dtb_book_school(book_id, school_id) values
        <foreach item="item" collection="schoolList" separator=",">
            (#{bookId}, #{item.schoolId})
        </foreach>
    </insert>

    <insert id="batchAddArea">
        insert into dutp_book_area_relation(book_id, area_id) values
        <foreach item="item" collection="areaList" separator=",">
            (#{bookId}, #{item.areaId})
        </foreach>
    </insert>
</mapper>
package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.List;

/**
 * 心理健康量与章节关系对象 mooc_psychology_health_chapter
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Data
@TableName("mooc_psychology_health_chapter")
public class MoocPsychologyHealthChapter extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long healthChapterId;

    /**
     * 量表id
     */
    @Excel(name = "量表id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long scaleId;

    /**
     * 章节id
     */
    @Excel(name = "章节id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    /**
     * 教材id
     */
    @Excel(name = "教材id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 页码
     */
    private Integer pageNumber;

    /**
     * 节点domid
     */
    private String domId;

    @TableField(exist = false)
    private List<Long> scaleIds;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("healthChapterId", getHealthChapterId())
                .append("scaleId", getScaleId())
                .append("chapterId", getChapterId())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("delFlag", getDelFlag())
                .append("pageNumber", getPageNumber())
                .append("domId", getDomId())
                .toString();
    }
}

package cn.dutp.book.service;

import cn.dutp.book.domain.DtbUserResource;
import cn.dutp.book.domain.vo.CheckResourceVO;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 用户资源库Service接口
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
public interface IDtbUserResourceService extends IService<DtbUserResource> {
    /**
     * 查询用户资源库
     *
     * @param resourceId 用户资源库主键
     * @return 用户资源库
     */
    public DtbUserResource selectDtbUserResourceByResourceId(Long resourceId);

    public List<DtbUserResource> selectDtbUserResourceListByRecycle(DtbUserResource dtbUserResource);

    /**
     * 查询用户资源库列表
     *
     * @param dtbUserResource 用户资源库
     * @return 用户资源库集合
     */
    public List<DtbUserResource> selectDtbUserResourceList(DtbUserResource dtbUserResource);

    /**
     * 新增用户资源库
     *
     * @param dtbUserResource 用户资源库
     * @return 结果
     */
    public boolean insertDtbUserResource(DtbUserResource dtbUserResource);

    /**
     * 修改用户资源库
     *
     * @param dtbUserResource 用户资源库
     * @return 结果
     */
    public boolean updateDtbUserResource(DtbUserResource dtbUserResource);

    /**
     * 批量删除用户资源库
     *
     * @param resourceIds 需要删除的用户资源库主键集合
     * @return 结果
     */
    public boolean deleteDtbUserResourceByResourceIds(List<Long> resourceIds);

    public int recycle(List<Long> resourceIds);

    public int restore(List<Long> resourceIds);

    public int permanentDeleteDtbUserResourceByResourceIds(List<Long> asList);

    CheckResourceVO checkResourceIsExists(MultipartFile file);
}

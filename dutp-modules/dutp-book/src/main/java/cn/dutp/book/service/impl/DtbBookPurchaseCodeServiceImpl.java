package cn.dutp.book.service.impl;

import cn.dutp.book.mapper.DtbPurchaseCodeMapper;
import cn.dutp.book.service.DtbBookCodeExportInfoService;
import cn.dutp.book.service.DtbBookCodeExportItemService;
import cn.dutp.book.service.IDtbBookPurchaseCodeService;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.*;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.swagger.v3.oas.models.examples.Example;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 发行管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Service
public class DtbBookPurchaseCodeServiceImpl extends ServiceImpl<DtbPurchaseCodeMapper, DtbBookPurchaseCode> implements IDtbBookPurchaseCodeService {
    @Autowired
    private DtbPurchaseCodeMapper dtbBookPurchaseCodeMapper;

    @Autowired
    private DtbBookCodeExportInfoService dtbBookCodeExportInfoService;

    @Autowired
    private DtbBookCodeExportItemService dtbBookCodeExportItemService;

    /**
     * 查询发行管理列表
     *
     * @param dtbBookPurchaseCode 发行管理
     * @return 发行管理
     */
    @Override
    public List<DtbBookPurchaseCode> selectDtbBookPurchaseCodeList(DtbBookPurchaseCode dtbBookPurchaseCode) {
        List<DtbBookPurchaseCode> list = this.baseMapper.selectBookCodeList(dtbBookPurchaseCode);
        // 查询副教材
        List<DtbBookPurchaseCode> masterFlagList = this.baseMapper.selectBookCodeMasterFlagList(dtbBookPurchaseCode);

        for (DtbBookPurchaseCode bookPurchaseCode : list) {
            List<DtbBookPurchaseCode> childrenList = new ArrayList<>();
            for (DtbBookPurchaseCode purchaseCode : masterFlagList) {
                if (bookPurchaseCode.getBookId().equals(purchaseCode.getMasterBookId())){
                    childrenList.add(purchaseCode);
                }
            }
            bookPurchaseCode.setChildren(childrenList);
        }
        return list;
    }

    /**
     * 批量生成购书码
     *
     * @param dtbBookPurchaseCodeList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchPurchaseCode(List<DtbBookPurchaseCode> dtbBookPurchaseCodeList) {
        // 待存储数据集合
        List<DtbBookPurchaseCode> saveList = new ArrayList<>();
        DtbBookPurchaseCode saveCode = new DtbBookPurchaseCode();

        // 遍历出集合中每条数据的codeNum是多少
        List<String> codeList = new ArrayList<>();
        for (DtbBookPurchaseCode dtbBookPurchaseCode : dtbBookPurchaseCodeList) {
            int codeNum = dtbBookPurchaseCode.getCodeNum();

            for (int i = 0; i < codeNum; i++) {
                // 获取购书码
                String code = getRandomCode();

                // 组装保存数据
                saveCode = new DtbBookPurchaseCode();

                // 类型为临时码时 计算到期日期
                if (dtbBookPurchaseCode.getCodeType() == 2) {
                    saveCode.setTimeLimit(dtbBookPurchaseCode.getTimeLimit());
                }
                saveCode.setCode(code);
                saveCode.setBookId(dtbBookPurchaseCode.getBookId());
                saveCode.setCodeFrom(SecurityUtils.getLoginUser().getUserid());
                saveCode.setState(1);
                saveCode.setCodeType(dtbBookPurchaseCode.getCodeType());
                saveCode.setDelFlag("0");

                saveList.add(saveCode);
            }
        }
        return this.saveBatch(saveList);
    }

    /**
     * 导出购书吗
     * 前端选择书籍后填写要导出的数量和期限
     * 根据以上条件查询是否存在相同期限及足够数量的购书码导出
     *
     * @param dtbBookPurchaseCodeList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<DtbBookPurchaseCodeExcel> exprortCodeValid(List<DtbBookPurchaseCode> dtbBookPurchaseCodeList) {

        Long userId = SecurityUtils.getLoginUser().getUserid();
        // 返回结果集
        List<DtbBookPurchaseCode> codeResList = new ArrayList<>();

        // 遍历取出参数中的bookId 然后bookid作为添加 加上期限的条件查询购书码数据
        // 构建查询条件
        LambdaQueryWrapper<DtbBookPurchaseCode> queryWrapper = new LambdaQueryWrapper<>();
        int i = 0;
        for (DtbBookPurchaseCode param : dtbBookPurchaseCodeList) {
            if (i == 0) {
                queryWrapper.and(wrapper -> wrapper.eq(DtbBookPurchaseCode::getBookId, param.getBookId())
                        .eq(DtbBookPurchaseCode::getTimeLimit, param.getTimeLimit()));
            } else {
                queryWrapper.or(wrapper -> wrapper.eq(DtbBookPurchaseCode::getBookId, param.getBookId())
                        .eq(DtbBookPurchaseCode::getTimeLimit, param.getTimeLimit()));
            }
            i++;
        }
        codeResList = this.list(queryWrapper);

        // 查询出来后验证购书码数量是否充足
        for (DtbBookPurchaseCode param : dtbBookPurchaseCodeList) {
            List<DtbBookPurchaseCode> resList = new ArrayList<>();
            // 获取参数中的codeNum
            int codeNum = param.getCodeNum();
            // 获取查询出来的数据
            resList = codeResList.stream().
                    filter(result -> result.getBookId().equals(param.getBookId()) && result.getTimeLimit().equals(param.getTimeLimit()))
                    .limit(codeNum)
                    .collect(Collectors.toList());
            // 如果results为空 说明没有查询到该教材及同期限的购书码数据
            if (resList.isEmpty()) {
                throw new ServiceException("您所选择的购书码数量不足");
            }
            // 如果results.size < codeNum 说明数量不足
            if (resList.size() < codeNum) {
                throw new ServiceException("您所选择的购书码数量不足");
            }
        }

        // 保存导出记录
        List<DtbBookCodeExportInfo> dtbBookCodeExportInfoList = new ArrayList<>();
        for (DtbBookPurchaseCode param : dtbBookPurchaseCodeList) {
            DtbBookCodeExportInfo dtbBookCodeExportInfo = new DtbBookCodeExportInfo();
            dtbBookCodeExportInfo.setBookId(param.getBookId());
            dtbBookCodeExportInfo.setExportUserId(userId);
            dtbBookCodeExportInfo.setExportDate(new Date());
            dtbBookCodeExportInfo.setCodeQuantity(param.getCodeNum());
            dtbBookCodeExportInfoList.add(dtbBookCodeExportInfo);
        }
        dtbBookCodeExportInfoService.saveBatch(dtbBookCodeExportInfoList);

        // 保存导出明细
        List<DtbBookCodeExportItem> dtbBookCodeExportItemList = new ArrayList<>();
        for (DtbBookCodeExportInfo dtbBookCodeExportInfo : dtbBookCodeExportInfoList) {
            for (DtbBookPurchaseCode dtbBookPurchaseCode : codeResList) {
                DtbBookCodeExportItem dtbBookCodeExportItem = new DtbBookCodeExportItem();
                if (dtbBookCodeExportInfo.getBookId().equals(dtbBookPurchaseCode.getBookId())){
                    dtbBookCodeExportItem.setExportDataId(dtbBookCodeExportInfo.getExportDataId());
                    dtbBookCodeExportItem.setCodeId(dtbBookPurchaseCode.getCodeId());
                    dtbBookCodeExportItem.setBookId(dtbBookPurchaseCode.getBookId());
                    dtbBookCodeExportItemList.add(dtbBookCodeExportItem);
                }
            }
        }
        dtbBookCodeExportItemService.saveBatch(dtbBookCodeExportItemList);

        // 修改状态为已占用
        for (DtbBookPurchaseCode dtbBookPurchaseCode : codeResList) {
            dtbBookPurchaseCode.setState(2);
            dtbBookPurchaseCode.setBindDate(new Date());
        }
        this.updateBatchById(codeResList);
        // 导出结果
        List<DtbBookPurchaseCodeExcel> resList = new ArrayList<>();
        for (DtbBookPurchaseCode param : dtbBookPurchaseCodeList) {
            // 获取参数中的codeNum
            int codeNum = param.getCodeNum();
            // 获取对应书籍和期限的购书码，限制数量
            List<DtbBookPurchaseCode> bookCodes = codeResList.stream()
                    .filter(result -> result.getBookId().equals(param.getBookId()) && result.getTimeLimit().equals(param.getTimeLimit()))
                    .limit(codeNum)
                    .collect(Collectors.toList());

            // 为每个购书码创建Excel导出对象
            bookCodes.forEach(item -> {
                DtbBookPurchaseCodeExcel dtbBookPurchaseCodeExcel = new DtbBookPurchaseCodeExcel();
                dtbBookPurchaseCodeExcel.setBookName(param.getBookName());
                dtbBookPurchaseCodeExcel.setIsbn(param.getIsbn());
                dtbBookPurchaseCodeExcel.setCode(item.getCode());
                dtbBookPurchaseCodeExcel.setTimeLimit(item.getTimeLimit());
                resList.add(dtbBookPurchaseCodeExcel);
            });
        }
        return resList;
    }


    /**
     * 查看购书码页面的购书码列表
     *
     * @param dtbBookPurchaseCode 发行管理
     * @return
     */
    @Override
    public List<DtbBookPurchaseCode> selectCodelList(DtbBookPurchaseCode dtbBookPurchaseCode) {
        List<DtbBookPurchaseCode> list = this.baseMapper.selectCodeList(dtbBookPurchaseCode);
        return list;
    }

    /**
     * 解绑购书码
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean codeUnbind(DtbBookPurchaseCode dtbBookPurchaseCode) {
        // 更新成未占用
        try {
            dtbBookPurchaseCode.setState(1);
            this.baseMapper.updateById(dtbBookPurchaseCode);
        } catch (Exception e) {
            return false;
        }

        return true;
    }


    /**
     * 批量解绑购书码
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean codeBacthUnbind(List<DtbBookPurchaseCode> dtbBookPurchaseCodeList) {
        try {
            for (DtbBookPurchaseCode dtbBookPurchaseCode : dtbBookPurchaseCodeList) {
                // 更新成未占用
                dtbBookPurchaseCode.setState(1);
            }
            this.updateBatchById(dtbBookPurchaseCodeList);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    /**
     * 生成购书码 规则16位纯数字
     *
     * @param length
     * @return
     */
    public static String getRandomCode() {
        Random random = new Random();
        long id = random.nextLong();
        id = Math.abs(id % 10000000000000000L);
        return String.format("%016d", id);
    }

    /**
     * 计算X天后的日期
     *
     * @param date 当前日期
     * @param days 天数
     * @return
     */
    public static Date addDaysToDate(Date date, int days) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, days);
        // 设置时间为当天的最后一秒
        return cal.getTime();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTimeLimit(List<DtbBookPurchaseCode> dtbBookPurchaseCodeList) {
        return this.updateBatchById(dtbBookPurchaseCodeList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBacthPurchaseCode(List<DtbBookPurchaseCode> dtbBookPurchaseCodeList) {
        List<Long> ids = dtbBookPurchaseCodeList.stream().map(DtbBookPurchaseCode::getCodeId).collect(Collectors.toList());
        return this.removeByIds(ids);
    }
}

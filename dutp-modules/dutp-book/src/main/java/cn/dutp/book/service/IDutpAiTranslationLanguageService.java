package cn.dutp.book.service;

import cn.dutp.book.domain.DutpAiTranslationLanguage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
/**
 * 翻译语种Service接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
public interface IDutpAiTranslationLanguageService extends IService<DutpAiTranslationLanguage>
{

    /**
     * 查询翻译语种列表
     *
     * @param dutpAiTranslationLanguage 翻译语种
     * @return 翻译语种集合
     */
    List<DutpAiTranslationLanguage> selectDutpAiTranslationLanguageList(DutpAiTranslationLanguage dutpAiTranslationLanguage);

}

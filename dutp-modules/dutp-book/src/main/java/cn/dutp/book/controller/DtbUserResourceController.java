package cn.dutp.book.controller;

import cn.dutp.book.domain.DtbUserResource;
import cn.dutp.book.service.IDtbUserResourceService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 用户资源库Controller
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
@RestController
@RequestMapping("/userResource")
public class DtbUserResourceController extends BaseController {
    @Autowired
    private IDtbUserResourceService dtbUserResourceService;

    /**
     * 查询用户资源库列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbUserResource dtbUserResource) {
        dtbUserResource.setUserId(SecurityUtils.getUserId());
        startPage();
        List<DtbUserResource> list = dtbUserResourceService.selectDtbUserResourceList(dtbUserResource);
        return getDataTable(list);
    }


    /**
     * 查询回收站列表
     */
    @GetMapping("/recycle/list")
    public TableDataInfo recycleList(DtbUserResource dtbUserResource) {
        dtbUserResource.setUserId(SecurityUtils.getUserId());
        dtbUserResource.setDelFlag("1"); // 查询已删除的记录
        startPage();
        List<DtbUserResource> list = dtbUserResourceService.selectDtbUserResourceListByRecycle(dtbUserResource);
        return getDataTable(list);
    }

    /**
     * 导出用户资源库列表
     */
    @RequiresPermissions("book:userResource:export")
    @Log(title = "用户资源库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbUserResource dtbUserResource) {
        dtbUserResource.setUserId(SecurityUtils.getUserId());
        List<DtbUserResource> list = dtbUserResourceService.selectDtbUserResourceList(dtbUserResource);
        ExcelUtil<DtbUserResource> util = new ExcelUtil<DtbUserResource>(DtbUserResource.class);
        util.exportExcel(response, list, "用户资源库数据");
    }

    /**
     * 获取用户资源库详细信息
     */
    @RequiresPermissions("book:userResource:query")
    @GetMapping(value = "/{resourceId}")
    public AjaxResult getInfo(@PathVariable("resourceId") Long resourceId) {
        return success(dtbUserResourceService.selectDtbUserResourceByResourceId(resourceId));
    }

    /**
     * 新增用户资源库
     */
    @RequiresPermissions("book:userResource:add")
    @Log(title = "新增用户资源库", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbUserResource dtbUserResource) {
        dtbUserResource.setUserId(SecurityUtils.getUserId());
        return toAjax(dtbUserResourceService.insertDtbUserResource(dtbUserResource));
    }

    /**
     * 修改用户资源库
     */
    @RequiresPermissions("book:userResource:edit")
    @Log(title = "修改用户资源库", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbUserResource dtbUserResource) {
        dtbUserResource.setUserId(SecurityUtils.getUserId());
        return toAjax(dtbUserResourceService.updateDtbUserResource(dtbUserResource));
    }

    /**
     * 删除用户资源库
     */
    @RequiresPermissions("book:userResource:remove")
    @Log(title = "删除用户资源库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{resourceIds}")
    public AjaxResult remove(@PathVariable Long[] resourceIds) {
        return toAjax(dtbUserResourceService.deleteDtbUserResourceByResourceIds(Arrays.asList(resourceIds)));
    }

    /**
     * 放入回收站
     */
    @RequiresPermissions("book:userResource:edit")
    @Log(title = "放入回收站", businessType = BusinessType.UPDATE)
    @PutMapping("/recycle/{resourceIds}")
    public AjaxResult moveToRecycle(@PathVariable Long[] resourceIds) {
        return toAjax(dtbUserResourceService.recycle(Arrays.asList(resourceIds)));
    }

    /**
     * 从回收站恢复
     */
    @RequiresPermissions("book:userResource:edit")
    @Log(title = "从回收站恢复", businessType = BusinessType.UPDATE)
    @PutMapping("/restore/{resourceIds}")
    public AjaxResult restore(@PathVariable Long[] resourceIds) {
        return toAjax(dtbUserResourceService.restore(Arrays.asList(resourceIds)));
    }


    /**
     * 彻底删除用户资源
     */
    @RequiresPermissions("book:userResource:remove")
    @Log(title = "彻底删除用户资源", businessType = BusinessType.DELETE)
    @DeleteMapping("/permanent/{resourceIds}")
    public AjaxResult permanentDelete(@PathVariable Long[] resourceIds) {
        return toAjax(dtbUserResourceService.permanentDeleteDtbUserResourceByResourceIds(Arrays.asList(resourceIds)));
    }

    /**
     * 判断用户资源库是否存在当前文件
     */
    @Log(title = "判断用户资源库是否存在当前文件", businessType = BusinessType.INSERT)
    @PostMapping("/checkResourceIsExists")
    public AjaxResult checkResourceIsExists(@RequestParam("file") MultipartFile file) {
        return success(dtbUserResourceService.checkResourceIsExists(file));
    }
}

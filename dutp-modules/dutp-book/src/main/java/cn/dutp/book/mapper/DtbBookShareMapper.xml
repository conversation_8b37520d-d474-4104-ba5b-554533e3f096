<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbBookShareMapper">
    

    <select id="selectDtbBookShareList" resultType="cn.dutp.book.domain.DtbBookShare">
        select
            bs.share_id as shareId,
            bs.book_id as bookId,
            bs.user_id as userId,
            bs.validity_period as validityPeriod,
            bs.code,
            bs.share_link as shareLink,
            bs.status,
            bs.start_date,
            bs.end_date,
            bs.share_remark,
            bs.create_time as createTime,
            b.book_id,
            b.book_name,
            b.book_no,
            b.cover,
            b.isbn,
            b.issn,
            b.publish_status,
            b.shelf_state,
            b.master_flag,
            b.price_counter,
            b.price_sale,
            b.topic_no,
            b.publish_date,
            b.edition,
            b.current_step_id as step_id,
            b.current_version_id,
            b.last_version_id,
            count(sub.share_id) as commentNum
        from dtb_book_share bs
                 left join dtb_book b on bs.book_id = b.book_id
                 left join (select share_id,comment_id from dtb_book_share_comment where del_flag = '0') sub on sub.share_id = bs.share_id
        where bs.del_flag = '0'
        and user_id = #{param.userId}
        <if test="param.bookName != null  and param.bookName != ''"> and b.book_name like concat('%', #{param.bookName}, '%')</if>
        <if test="param.isbn != null  and param.isbn != ''"> and b.isbn like concat('%', #{param.isbn}, '%') </if>
        <if test="param.issn != null  and param.issn != ''"> and b.issn like concat('%', #{param.issn}, '%') </if>
        <if test="param.bookNo != null  and param.bookNo != ''"> and b.book_no = #{param.bookNo}</if>
        <if test="param.houseId != null "> and b.house_id = #{param.houseId}</if>
        <if test="param.status != null "> and  bs.step_id = #{param.status}</if>
        group by shareId
        order by bs.create_time desc
    </select>

</mapper>
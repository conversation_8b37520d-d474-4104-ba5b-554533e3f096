package cn.dutp.book.mapper;

import cn.dutp.domain.DtbBook;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * DUTP-DTB_002数字教材Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-14
 */
@Repository
public interface DtbBookBookMapper extends BaseMapper<DtbBook> {

    List<DtbBook> selectListByDtbBook(DtbBook dtbBook);

    int updateByBookId(DtbBook dtbBook);

    @Update("UPDATE dtb_book SET master_book_id = NULL,book_type = NULL,isbn = NULL,issn = NULL,top_subject_id = NULL,second_subject_id = NULL,third_subject_id = NULL,forth_subject_id = NULL WHERE master_book_id = #{bookId}")
    int delAllDeputyBookByBookId(Long bookId);

    int checkRepeatByIssn(@Param("issn") String issn, @Param("bookId") Long bookId);

    int checkRepeatByIsbn(@Param("isbn") String isbn, @Param("bookId") Long bookId);


    DtbBook queryBookByBookId(Long bookId);

    List<DtbBook> queryBookListByBookId(Long bookId);

    List<DtbBook> selectDtbBookListWithGroup(@Param("dtbBook") DtbBook dtbBook, @Param("userId") Long userId);

    List<DtbBook> listOfAuthorAndEditor(DtbBook dtbBook);

    DtbBook queryBookByChapterId(Long chapterId);

    int updateDeputyBook(@Param("book") DtbBook dtbBook, @Param("deputyBookIdList") List<Long> deputyBookIdList);

    @Select("SELECT count(*) FROM dtb_book WHERE publish_status = 2 AND del_flag = 0 AND book_id IN (SELECT book_id FROM dtb_book_test_paper WHERE paper_id = #{paperId})")
    int countPublishedBooksByPaperId(Long paperId);

    List<DtbBook> getAllBySchoolId(DtbBook dtbBook);
}

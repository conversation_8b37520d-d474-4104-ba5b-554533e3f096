package cn.dutp.book.service.impl;

import cn.dutp.book.domain.DtbUserBook;
import cn.dutp.book.domain.EduSchoolBook;
import cn.dutp.book.mapper.DtbBookBookMapper;
import cn.dutp.book.mapper.DtbBookCommonMapper;
import cn.dutp.book.mapper.DtbUserBookMapper;
import cn.dutp.book.mapper.EduSchoolBookMapper;
import cn.dutp.book.service.IEduSchoolBookService;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBook;
import cn.dutp.message.api.RemoteUserMessageService;
import cn.dutp.message.api.domain.DutpUserMessage;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 校本教材推送Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Slf4j
@Service
public class EduSchoolBookServiceImpl extends ServiceImpl<EduSchoolBookMapper, EduSchoolBook> implements IEduSchoolBookService {
    @Autowired
    private EduSchoolBookMapper schoolBookMapper;

    @Autowired
    private DtbBookBookMapper bookMapper;

    @Autowired
    private DtbBookCommonMapper bookCommonMapper;

    @Autowired
    private RemoteUserMessageService remoteUserMessageService;

    @Autowired
    private DtbUserBookMapper dtbUserBookMapper;

    /**
     * 查询校本教材推送列表
     *
     * @param eduSchoolBook 校本教材推送
     * @return 校本教材推送
     */
    @Override
    public List<EduSchoolBook> selectEduSchoolBookList(EduSchoolBook eduSchoolBook) {
        return schoolBookMapper.selectEduSchoolBookList(eduSchoolBook);
    }

    /**
     * 新增校本教材推送
     *
     * @param eduSchoolBook 校本教材推送
     * @return 结果
     */
    @Override
    public String insertEduSchoolBook(EduSchoolBook eduSchoolBook) {
        Long userId = SecurityUtils.getUserId();
        DtbBook book = bookMapper.selectOne(new LambdaQueryWrapper<DtbBook>()
                .select(DtbBook::getBookId, DtbBook::getBookOrganize, DtbBook::getMasterFlag,
                        DtbBook::getPublishStatus, DtbBook::getBookName)
                .eq(DtbBook::getBookId, eduSchoolBook.getBookId()));
        List<Long> schoolIdList = eduSchoolBook.getSchoolIdList();
        if (ObjectUtil.isEmpty(schoolIdList)) {
            log.error("推送教材学校ID为空, 当前操作人ID：{}", userId);
            throw new ServiceException("推送教材学校为空");
        }

        // 校验主副教材
        if (ObjectUtil.isEmpty(book)) {
            log.error("推送教材不存在, 当前操作人ID：{}", userId);
            throw new ServiceException("推送教材不存在");
        }
        if (book.getMasterFlag() == 3) {
            log.error("推送副教材, 当前操作人ID：{}", userId);
            throw new ServiceException("副教材不允许推送");
        }
        if (book.getBookOrganize() == 1) {
            log.error("推送教材类型错误, 当前操作人ID：{}", userId);
            throw new ServiceException("推送教材类型错误，公开教材不允许这样推送");
        }
        if (book.getPublishStatus() == 1) {
            log.error("推送教材状态错误, 当前操作人ID：{}", userId);
            throw new ServiceException("推送教材状态错误，已创建状态的校本教材不允许推送");
        }
        // 校验学校是否到截止日期
        List<EduSchoolBook> eduSchoolBookList = schoolBookMapper.selectList(new LambdaQueryWrapper<EduSchoolBook>()
                .select(EduSchoolBook::getSchoolId, EduSchoolBook::getExpireDate)
                .in(EduSchoolBook::getSchoolId, schoolIdList)
                .eq(EduSchoolBook::getBookId, eduSchoolBook.getBookId()));
        StringBuilder res = new StringBuilder();
        int f = 1;
        DateTime nowDate = DateUtil.date();
        List<Long> notPushSchoolIdList = new ArrayList<>();
        List<DtbBook> deputyBookList = null;
        if (book.getMasterFlag() == 2) {
            deputyBookList = bookMapper.selectList(new LambdaQueryWrapper<DtbBook>()
                    .select(DtbBook::getBookId, DtbBook::getBookName)
                    .eq(DtbBook::getPublishStatus, 2)
                    .eq(DtbBook::getMasterFlag, 3)
                    .eq(DtbBook::getMasterBookId, eduSchoolBook.getBookId()));
        }

        for (EduSchoolBook schoolBook : eduSchoolBookList) {
            if (nowDate.isBefore(schoolBook.getExpireDate())) {
                f = 2;
                notPushSchoolIdList.add(schoolBook.getSchoolId());
                String schoolName = bookCommonMapper.querySchoolNameBySchoolId(schoolBook.getSchoolId());
                res.append(schoolName + "还未到截止日期，不能推送；\n");
            }
        }
        // 设置过期时间
        Long dayLimit = eduSchoolBook.getDayLimit();
        DateTime expireDate = DateUtil.date();
        expireDate = expireDate.offset(DateField.DAY_OF_YEAR, dayLimit.intValue());
        schoolIdList = schoolIdList.stream().filter(schoolId -> !notPushSchoolIdList.contains(schoolId)).collect(Collectors.toList());
        List<EduSchoolBook> schoolBookList = new ArrayList<>();
        for (Long schoolId : schoolIdList) {
            EduSchoolBook schoolBook = new EduSchoolBook();
            schoolBook.setBookId(eduSchoolBook.getBookId());
            schoolBook.setSchoolId(schoolId);
            schoolBook.setDayLimit(dayLimit);
            schoolBook.setExpireDate(expireDate);
            schoolBookList.add(schoolBook);
            if (ObjectUtil.isNotEmpty(deputyBookList)) {
                for (DtbBook deputyBook : deputyBookList) {
                    EduSchoolBook schoolBook1 = new EduSchoolBook();
                    schoolBook1.setBookId(deputyBook.getBookId());
                    schoolBook1.setSchoolId(schoolId);
                    schoolBook1.setDayLimit(dayLimit);
                    schoolBook1.setExpireDate(expireDate);
                    schoolBookList.add(schoolBook1);
                }
            }
        }
        if (f == 2 && ObjectUtil.isEmpty(schoolIdList)) {
            return res.toString();
        }

        boolean success = this.saveBatch(schoolBookList);
        if (!success) {
            log.error("推送数据保存失败，{}", schoolBookList);
            throw new ServiceException("推送失败");
        }
        if (success) {
            // 发送推送消息
            for (Long schoolId : schoolIdList) {
                List<Long> eduUserIdList = bookCommonMapper.queryEduRoleUser(schoolId);
                if (ObjectUtil.isNotEmpty(eduUserIdList)) {
                    for (Long eduUserId : eduUserIdList) {
                        DutpUserMessage message = generatePushMessage(userId, eduUserId, book, expireDate, dayLimit);
                        remoteUserMessageService.addMessage(message);
                        if (ObjectUtil.isNotEmpty(deputyBookList)) {
                            for (DtbBook deputyBook : deputyBookList) {
                                DutpUserMessage message1 = generatePushMessage(userId, eduUserId, deputyBook, expireDate, dayLimit);
                                remoteUserMessageService.addMessage(message1);
                            }

                        }
                    }
                }
            }

        }
        if (f == 2) {
            return res.toString();
        }
        return "推送成功";
    }

    /**
     * 生成消息
     *
     * @param userId
     * @param eduUserId
     * @param book
     * @return
     */
    private static DutpUserMessage generatePushMessage(Long userId, Long eduUserId, DtbBook book, Date expireDate, Long dayLimit) {
        DutpUserMessage message = new DutpUserMessage();
        message.setContent("您好，为您推送《" + book.getBookName() + "》书籍，有效期 " + dayLimit + "天，在" + DateUtil.format(expireDate, "yyyy-MM-dd") + "失效，请到【教材管理-校本教材】内查看。");
        message.setTitle("教材推送提醒");
        message.setFromUserId(userId);
        message.setBookId(book.getBookId());
        message.setBusinessId(book.getBookId());
        message.setToUserId(eduUserId);
        message.setMessageType(2);
        message.setFromUserType(1);
        message.setToUserType(1);
        return message;
    }

    @Override
    @Transactional
    public Boolean deleteByBookIdAndSchoolId(Long bookId) {
        QueryWrapper<EduSchoolBook> queryWrapper = new QueryWrapper<>();
        // 清空书架
        List<DtbUserBook> userBookList = dtbUserBookMapper.selectByBookIdAndSchoolId(bookId,SecurityUtils.getLoginUser().getSchoolId());
        if (!CollectionUtils.isEmpty(userBookList)) {
            List<Long> userIdList = userBookList.stream().map(DtbUserBook::getUserBookId).collect(Collectors.toList());
            UpdateWrapper<DtbUserBook> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().in(DtbUserBook::getUserBookId, userIdList).set(DtbUserBook::getDelFlag, 1)
                    .set(DtbUserBook::getUpdateTime, new Date()).set(DtbUserBook::getUpdateBy, SecurityUtils.getUsername());
            dtbUserBookMapper.update(null, updateWrapper);
        }
        queryWrapper.lambda().eq(EduSchoolBook::getBookId, bookId)
                .eq(EduSchoolBook::getSchoolId, SecurityUtils.getLoginUser().getSchoolId());
        return this.remove(queryWrapper);
    }
}

package cn.dutp.book.controller;

import cn.dutp.domain.DutpAiHistory;
import cn.dutp.book.service.IDutpAiHistoryService;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * ai请求记录ServiceController
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@RestController
@Slf4j
@RequestMapping("/ai/history")
public class DutpAiHistoryController extends BaseController
{
    @Autowired
    private IDutpAiHistoryService aiHistoryService;

    /**
     * 查询ai请求记录列表
     * @param chapterId
     * @param ability
     * @return
     */
    @GetMapping("/queryHistoryByChapterId")
    public AjaxResult queryHistoryByChapterId(Long chapterId, Integer ability, String question){
        if(ObjectUtil.isEmpty(chapterId)){
            return error("章节id不能为空");
        }
        // Long userId = SecurityUtils.getUserId();
        // TODO 前台登录未实现，暂时写死
        Long userId = 255L;
        LambdaQueryWrapper<DutpAiHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DutpAiHistory::getChapterId, chapterId);
        if (ObjectUtil.isNotEmpty(question)){
            queryWrapper.like(DutpAiHistory::getQuestion, question);
        }
        queryWrapper.eq(DutpAiHistory::getPromptAbility, ability);
        queryWrapper.select(DutpAiHistory::getPromptHistoryId, DutpAiHistory::getQuestion, DutpAiHistory::getCreateTime);
        queryWrapper.orderByDesc(DutpAiHistory::getCreateTime);
        List<DutpAiHistory> dutpAiHistoryList = aiHistoryService.list(queryWrapper);
        return success(dutpAiHistoryList);
    }

    /**
     * 根据promptHistoryId查询ai请求记录
     * @param promptHistoryId
     * @return
     */
    @GetMapping("/queryHistoryByPromptHistoryId")
    public AjaxResult queryHistoryByPromptHistoryId(Long promptHistoryId){
        if(ObjectUtil.isEmpty(promptHistoryId)){
            return error("promptHistoryId不能为空");
        }
        LambdaQueryWrapper<DutpAiHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DutpAiHistory::getPromptHistoryId, promptHistoryId);
        queryWrapper.select(DutpAiHistory::getPromptHistoryId, DutpAiHistory::getQuestion,
                DutpAiHistory::getAnswer, DutpAiHistory::getCreateTime);
        DutpAiHistory dutpAiHistory = aiHistoryService.getOne(queryWrapper);
        return success(dutpAiHistory);
    }

    /**
     * 删除ai请求记录
     * @param promptHistoryId
     * @return
     */
    @Log(title = "删除ai请求记录", businessType = BusinessType.DELETE)
    @DeleteMapping("delete")
    public AjaxResult delete(Long promptHistoryId){
        if(ObjectUtil.isEmpty(promptHistoryId)){
            return error("promptHistoryId不能为空");
        }
        LambdaQueryWrapper<DutpAiHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DutpAiHistory::getPromptHistoryId, promptHistoryId);
        boolean remove = aiHistoryService.remove(queryWrapper);
        return toAjax(remove);
    }
}

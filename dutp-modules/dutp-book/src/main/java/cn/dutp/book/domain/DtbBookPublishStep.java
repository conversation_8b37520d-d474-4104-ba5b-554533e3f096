package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * DUTP-DTB-027数字教材初版步骤对象 dtb_book_publish_step
 *
 * <AUTHOR>
 * @date 2024-12-27
 */
@Data
@TableName("dtb_book_publish_step")
public class DtbBookPublishStep extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long stepId;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String stepName;

    /**
     * 0校本教材没有的步骤1校本教材有
     */
    @Excel(name = "0校本教材没有的步骤1校本教材有")
    private Integer schoolFlag;
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbBookRecallAmendMapper">

    <resultMap type="cn.dutp.book.domain.DtbBookRecallAmend" id="DtbBookRecallAmendResult">
        <result property="recallId" column="recall_id"/>
        <result property="bookId" column="book_id"/>
        <result property="reason" column="reason"/>
        <result property="nextVersion" column="next_version"/>
        <result property="userId" column="user_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="masterFlag" column="masterFlag"/>
        <result property="bookOrganize" column="bookOrganize"/>
        <result property="phonenumber" column="phonenumber"/>
        <result property="isbn" column="isbn"/>
        <result property="bookNo" column="bookNo"/>
        <result property="bookName" column="bookName"/>
    </resultMap>

    <resultMap type="cn.dutp.book.domain.DtbBookRecallAmend" id="dtbBookHistoryResult">
        <result property="recallId" column="recall_id"/>
        <result property="bookId" column="book_id"/>
        <result property="reason" column="reason"/>
        <result property="nextVersion" column="next_version"/>
        <result property="userId" column="user_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="masterFlag" column="masterFlag"/>
        <result property="bookOrganize" column="bookOrganize"/>
        <result property="phonenumber" column="phonenumber"/>
        <result property="isbn" column="isbn"/>
        <result property="issn" column="issn"/>
        <result property="bookNo" column="bookNo"/>
        <result property="bookName" column="bookName"/>
        <result property="nextVersion" column="nextVersion"/>
        <result property="versionNo" column="versionNo"/>
        <collection property="childList" ofType="cn.dutp.domain.DtbBook">
            <result property="bookName" column="book_name"/>
            <result property="bookNo" column="book_no"/>
        </collection>
    </resultMap>

    <sql id="selectDtbBookRecallAmendVo">
        select recall_id,
               book_id,
               reason,
               user_id,
               next_version,
               create_by,
               create_time,
               update_by,
               update_time,
               del_flag
        from dtb_book_recall_amend
    </sql>

    <select id="selectDtbBookRecallAmendList" parameterType="cn.dutp.book.domain.DtbBookRecallAmend"
            resultMap="dtbBookHistoryResult">
        select
            da.recall_id,
            da.book_id,
            da.version_id,
            dv.version_no as versionNo,
            da.reason,
            dk.book_name as bookName,
            dk.book_no as bookNo,
            dk.isbn,
            dk.issn,
            dk.master_flag as masterFlag,
            dk.book_organize as bookOrganize,
            da.create_by,
            da.create_time,
            du.phonenumber,
            dc.book_name,
            dc.book_no,
            dc.master_flag
        from
            dtb_book_recall_amend da
        left join
            dtb_book dk on dk.book_id = da.book_id
        left join
            sys_user du on du.user_id = da.user_id
        left join
            dtb_book dc on dc.master_book_id = dk.book_id and dc.master_flag = 3
        left join
            dtb_book_version dv on dv.version_id = da.version_id and dv.del_flag = 0
        <where>
            da.recall_type = #{recallType}
            <if test="isbn != null  and isbn != ''">and dk.isbn like concat('%', #{isbn}, '%')</if>
            <if test="issn != null  and issn != ''">and dk.issn like concat('%', #{issn}, '%')</if>
            <if test="bookNo != null  and bookNo != ''">and (dk.book_no like concat('%', #{bookNo}, '%') or dc.book_no like concat('%', #{bookNo}, '%'))</if>
            <if test="bookName != null  and bookName != ''">and (dk.book_name like concat('%', #{bookName}, '%') or dc.book_name like concat('%', #{bookName}, '%'))</if>
            <if test="bookOrganize != null ">and (dk.book_organize = #{bookOrganize} or dc.book_organize = #{bookOrganize})</if>
        </where>
        order by da.create_time desc
    </select>

    <select id="selectDtbBookAmendList" parameterType="cn.dutp.book.domain.DtbBookRecallAmend"
            resultMap="dtbBookHistoryResult">
        select
            da.recall_id,
            da.book_id,
            da.reason,
            dk.book_name as bookName,
            dk.book_no as bookNo,
            dk.isbn,
            dk.issn,
            dk.master_flag as masterFlag,
            dk.book_organize as bookOrganize,
            da.create_by,
            da.create_time,
            da.recall_type,
            du.phonenumber,
            dv.version_no as nextVersion
        from
            dtb_book_recall_amend da
        left join
            dtb_book dk on dk.book_id = da.book_id
        left join
            sys_user du on du.user_id = da.user_id
        left join
            dtb_book_version dv on dv.version_id = da.version_id and dv.del_flag = 0
        <where>
            da.recall_type = #{recallType}
            <if test="isbn != null  and isbn != ''">and dk.isbn like concat('%', #{isbn}, '%')</if>
            <if test="issn != null  and issn != ''">and dk.issn like concat('%', #{issn}, '%')</if>
            <if test="bookNo != null  and bookNo != ''">and dk.book_no like concat('%', #{bookNo}, '%')</if>
            <if test="bookName != null  and bookName != ''">and dk.book_name like concat('%', #{bookName}, '%')</if>
            <if test="bookOrganize != null ">and dk.book_organize = #{bookOrganize}</if>
        </where>
        order by da.create_time desc
    </select>

    <select id="selectDtbBookRecallAmendByRecallId" parameterType="Long" resultMap="DtbBookRecallAmendResult">
        <include refid="selectDtbBookRecallAmendVo"/>
        where recall_id = #{recallId}
    </select>

    <insert id="insertDtbBookRecallAmend" parameterType="cn.dutp.book.domain.DtbBookRecallAmend" useGeneratedKeys="true"
            keyProperty="recallId">
        insert into dtb_book_recall_amend
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bookId != null">book_id,</if>
            <if test="reason != null">reason,</if>
            <if test="nextVersion != null">next_version,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bookId != null">#{bookId},</if>
            <if test="reason != null">#{reason},</if>
            <if test="nextVersion != null">#{nextVersion},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
        </trim>
    </insert>

    <update id="updateDtbBookRecallAmend" parameterType="cn.dutp.book.domain.DtbBookRecallAmend">
        update dtb_book_recall_amend
        <trim prefix="SET" suffixOverrides=",">
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="nextVersion != null">next_version = #{nextVersion},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where recall_id = #{recallId}
    </update>

    <delete id="deleteDtbBookRecallAmendByRecallId" parameterType="Long">
        delete
        from dtb_book_recall_amend
        where recall_id = #{recallId}
    </delete>

    <delete id="deleteDtbBookRecallAmendByRecallIds" parameterType="String">
        delete from dtb_book_recall_amend where recall_id in
        <foreach item="recallId" collection="array" open="(" separator="," close=")">
            #{recallId}
        </foreach>
    </delete>
</mapper>
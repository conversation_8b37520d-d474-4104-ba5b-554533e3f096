package cn.dutp.book.controller;


import cn.dutp.book.domain.DtbBookChapterContent;
import cn.dutp.book.service.IDtbBookChapterContentService;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.security.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 章节内容Controller
 *
 * <AUTHOR>
 * @date 2024-11-30
 */
@RestController
@RequestMapping("/chapterContent")
public class DtbChapterContentController extends BaseController
{
    @Autowired
    private IDtbBookChapterContentService dtbBookChapterContentService;


    /**
     * 查询章节内容
     */
    @GetMapping(value = "/{chapterId}")
    public AjaxResult getChapterContentInfo(@PathVariable("chapterId") Long chapterId)
    {
        return success(dtbBookChapterContentService.getChapterContentInfo(chapterId));
    }

    /**
     * 更新教材章节内容
     */
    @PostMapping("/updateChapterContentInfo")
    public AjaxResult updateChapterContentInfo(@RequestBody DtbBookChapterContent dtbBookChapterContent)
    {
        return toAjax(dtbBookChapterContentService.updateChapterContentInfo(dtbBookChapterContent));
    }

}

package cn.dutp.book.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.book.domain.DtbBookVersion;
import cn.dutp.book.service.IDtbBookVersionService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 电子教材版本Controller
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@RestController
@RequestMapping("/version")
public class DtbBookVersionController extends BaseController {
    @Autowired
    private IDtbBookVersionService dtbBookVersionService;

    /**
     * 查询电子教材版本列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbBookVersion dtbBookVersion) {
        startPage();
        List<DtbBookVersion> list = dtbBookVersionService.selectDtbBookVersionList(dtbBookVersion);
        return getDataTable(list);
    }

    /**
     * 查询电子教材版本列表
     */
    @GetMapping("/versionList")
    public TableDataInfo versionList(DtbBookVersion dtbBookVersion) {
        startPage();
        List<DtbBookVersion> list = dtbBookVersionService.versionList(dtbBookVersion);
        return getDataTable(list);
    }

    /**
     * 查询电子教材版本列表
     */
    @GetMapping("/versionListNotPage")
    public AjaxResult versionListNotPage(DtbBookVersion dtbBookVersion) {
        List<DtbBookVersion> list = dtbBookVersionService.versionListNotPage(dtbBookVersion);
        return success(list);
    }
    /**
     * 导出电子教材版本列表
     */
    @RequiresPermissions("book:version:export")
    @Log(title = "电子教材版本", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbBookVersion dtbBookVersion) {
        List<DtbBookVersion> list = dtbBookVersionService.selectDtbBookVersionList(dtbBookVersion);
        ExcelUtil<DtbBookVersion> util = new ExcelUtil<DtbBookVersion>(DtbBookVersion.class);
        util.exportExcel(response, list, "电子教材版本数据");
    }

    /**
     * 获取电子教材版本详细信息
     */
    @RequiresPermissions("book:version:query")
    @GetMapping(value = "/{versionId}")
    public AjaxResult getInfo(@PathVariable("versionId") Long versionId) {
        return success(dtbBookVersionService.selectDtbBookVersionByVersionId(versionId));
    }

    /**
     * 学生教师端获取电子教材版本详细信息
     */
    @GetMapping(value = "/getInfoEducation/{versionId}")
    public AjaxResult getInfoEducation(@PathVariable("versionId") Long versionId) {
        return success(dtbBookVersionService.selectDtbBookVersionByVersionId(versionId));
    }

    /**
     * 新增电子教材版本
     */
    @RequiresPermissions("book:version:add")
    @Log(title = "电子教材版本", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookVersion dtbBookVersion) {
        return toAjax(dtbBookVersionService.insertDtbBookVersion(dtbBookVersion));
    }

    /**
     * 修改电子教材版本
     */
    @RequiresPermissions("book:version:edit")
    @Log(title = "电子教材版本", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookVersion dtbBookVersion) {
        return toAjax(dtbBookVersionService.updateDtbBookVersion(dtbBookVersion));
    }

    /**
     * 删除电子教材版本
     */
    @RequiresPermissions("book:version:remove")
    @Log(title = "电子教材版本", businessType = BusinessType.DELETE)
    @DeleteMapping("/{versionIds}")
    public AjaxResult remove(@PathVariable Long[] versionIds) {
        return toAjax(dtbBookVersionService.deleteDtbBookVersionByVersionIds(Arrays.asList(versionIds)));
    }
}

package cn.dutp.book.mapper;

import java.util.List;

import cn.dutp.book.domain.vo.DtbBookChapterCatalogVO;
import cn.dutp.book.domain.vo.DtbBookChapterTreeVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import cn.dutp.book.domain.DtbBookChapterCatalog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
/**
 * 教材章节目录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-30
 */
@Repository
public interface DtbBookChapterCatalogMapper extends BaseMapper<DtbBookChapterCatalog>
{

    @Select("SELECT catalog_id FROM dtb_book_chapter_catalog WHERE dom_id = #{domId}")
    Long checkIsExist(String domId);

    @Select("SELECT catalog_id, title as name, chapter_id, book_id, parent_id, dom_id as id, dom_id FROM dtb_book_chapter_catalog WHERE chapter_id = #{chapterId}")
    List<DtbBookChapterTreeVO> queryBookChapterCatalogTreeByChapterId(Long chapterId);

    @Select("SELECT catalog_id, title, chapter_id, dom_id, parent_id, page_number FROM dtb_book_chapter_catalog WHERE chapter_id = #{chapterId}")
    List<DtbBookChapterCatalogVO> getBookChapterCatalogByReader(Long chapterId);

    List<DtbBookChapterCatalog> queryBookChapterCatalogTree(@Param("bookId") Long bookId, @Param("sort") Integer sort);
}

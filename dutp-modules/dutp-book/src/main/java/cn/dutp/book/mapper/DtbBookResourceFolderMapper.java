package cn.dutp.book.mapper;

import java.util.List;

import cn.dutp.book.domain.vo.ResourceVO;
import org.springframework.stereotype.Repository;
import cn.dutp.book.domain.DtbBookResourceFolder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
/**
 * 教材资源文件夹Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
@Repository
public interface DtbBookResourceFolderMapper extends BaseMapper<DtbBookResourceFolder>
{

    List<ResourceVO> selectCombinedResources(ResourceVO dtbBookResourceFolder);
}

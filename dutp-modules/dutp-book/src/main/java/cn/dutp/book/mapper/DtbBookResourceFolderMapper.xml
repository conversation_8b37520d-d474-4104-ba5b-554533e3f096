<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbBookResourceFolderMapper">
    
    <resultMap type="DtbBookResourceFolder" id="DtbBookResourceFolderResult">
        <result property="folderId"    column="folder_id"    />
        <result property="folderName"    column="folder_name"    />
        <result property="parentId"    column="parent_id"    />
        <result property="bookId"    column="book_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="defaultType"    column="default_type"    />
    </resultMap>

    <sql id="selectDtbBookResourceFolderVo">
        select folder_id, folder_name, parent_id, book_id, create_by, create_time, update_by, update_time, del_flag from dtb_book_resource_folder
    </sql>

    <select id="selectDtbBookResourceFolderList" parameterType="DtbBookResourceFolder" resultMap="DtbBookResourceFolderResult">
        <include refid="selectDtbBookResourceFolderVo"/>
        <where>  
            <if test="folderName != null  and folderName != ''"> and folder_name like concat('%', #{folderName}, '%')</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="bookId != null "> and book_id = #{bookId}</if>
        </where>
    </select>
    
    <select id="selectDtbBookResourceFolderByFolderId" parameterType="Long" resultMap="DtbBookResourceFolderResult">
        <include refid="selectDtbBookResourceFolderVo"/>
        where folder_id = #{folderId}
    </select>

    <insert id="insertDtbBookResourceFolder" parameterType="DtbBookResourceFolder" useGeneratedKeys="true" keyProperty="folderId">
        insert into dtb_book_resource_folder
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="folderName != null">folder_name,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="bookId != null">book_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="folderName != null">#{folderName},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="bookId != null">#{bookId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateDtbBookResourceFolder" parameterType="DtbBookResourceFolder">
        update dtb_book_resource_folder
        <trim prefix="SET" suffixOverrides=",">
            <if test="folderName != null">folder_name = #{folderName},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where folder_id = #{folderId}
    </update>

    <delete id="deleteDtbBookResourceFolderByFolderId" parameterType="Long">
        delete from dtb_book_resource_folder where folder_id = #{folderId}
    </delete>

    <delete id="deleteDtbBookResourceFolderByFolderIds" parameterType="String">
        delete from dtb_book_resource_folder where folder_id in 
        <foreach item="folderId" collection="array" open="(" separator="," close=")">
            #{folderId}
        </foreach>
    </delete>



    <select id="selectCombinedResources" resultType="cn.dutp.book.domain.vo.ResourceVO">
        SELECT
        folder_id as resourceId,
        folder_name as name,
        folder_name as folderName,
        Null as fileName,
        'folder' as type,
        parent_id as parentId,
        NULL as fileUrl,
        NULL as fileSize,
        NULL as fileType,
        NULL as userId,
        create_by as createBy,
        default_type as defaultType,
        create_time as createTime
        FROM
        dtb_book_resource_folder
        WHERE
        parent_id = #{folderId}
        AND book_id = #{bookId}
        <if test="folderName != null and folderName != ''">
            AND folder_name like concat('%', #{folderName}, '%')
        </if>
        and del_flag = 0
        UNION ALL
        SELECT
        dbr.book_resource_id as resourceId,
        dbu.file_name as name,
        dbu.file_name as fileName,
        NULL as folderName,
        'file' as type,
        dbr.folder_id as parentId,
        dbu.file_url as fileUrl,
        dbu.file_size as fileSize,
        dbu.file_type as fileType,
        Null as defaultType,
        dbu.user_id as userId,
        dbr.create_by as createBy,
        dbr.create_time as createTime
        FROM
        dtb_book_resource dbr
        left join dtb_user_resource dbu on dbr.resource_id = dbu.resource_id
        WHERE
        dbr.folder_id = #{folderId}
        AND book_id = #{bookId}
        and dbu.del_flag = 0
        <if test="folderName != null and folderName != ''">
            AND dbu.file_name like concat('%', #{folderName}, '%')
        </if>
        <if test="fileType != null and fileType != ''">
            AND dbu.file_type = #{fileType}
        </if>
    </select>
</mapper>
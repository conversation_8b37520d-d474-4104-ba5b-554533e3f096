<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbBookTemplateImageMapper">

    <select id="listForAuthor" resultType="cn.dutp.book.domain.DtbBookTemplateImage">
        SELECT
            i.image_id,
            i.url,
            i.belong_to
        FROM
            dtb_book_template_image i
        WHERE
        <if test="type == 1">
            i.type = 1
            AND i.template_id = #{templateId}
        </if>
        <if test="type == 2">
            i.type = 2
            AND i.user_id = #{userId}
        </if>
    </select>
</mapper>
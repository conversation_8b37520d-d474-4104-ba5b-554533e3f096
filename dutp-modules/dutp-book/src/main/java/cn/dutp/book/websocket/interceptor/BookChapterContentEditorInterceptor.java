package cn.dutp.book.websocket.interceptor;


import cn.dutp.common.security.auth.AuthUtil;
import cn.dutp.system.api.model.LoginUser;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.http.server.ServletServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 章节编写拦截器 用于用户身份校验，权限校验
 */
@Slf4j
@Component
public class BookChapterContentEditorInterceptor implements HandshakeInterceptor {

    /**
     * 握手前
     */
    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response, WebSocketHandler wsHandler, Map<String, Object> attributes) throws Exception {

        String hostName = request.getRemoteAddress().getHostName();
        HttpServletRequest servletRequest = ((ServletServerHttpRequest) request).getServletRequest();

        // 利用子协议存储token
        String token = servletRequest.getHeader("Sec-WebSocket-Protocol");

        // log.info("握手开始,用户hostName：{}, token: {}", hostName, token);
        if ("system".equals(token)) {
            // log.info("系统用户, 握手成功, hostName: {}", hostName);
            // 获取身份
            String sessionId = "system";

            // 放入属性域
            attributes.put("session_id", sessionId);
            attributes.put("loginUser", 0);
            attributes.put("userId", 0);
            attributes.put("token", token);
            return true;
        }
        if (ObjectUtil.isEmpty(token)) {
            // log.error("违法链接, 用户未登录, hostName: {}", hostName);
            return false;
        }

        LoginUser loginUser = AuthUtil.getLoginUser(token);
        if (ObjectUtil.isEmpty(loginUser)) {
            // log.error("违法链接, 用户登录已失效, hostName: {}", hostName);
            return false;
        }

        Long userId = loginUser.getSysUser().getUserId();

        // 获取身份
        String sessionId = hostName + ":" + userId + "-" + (int) (Math.random() * 1000);

        // 放入属性域
        attributes.put("session_id", sessionId);
        attributes.put("loginUser", loginUser);
        attributes.put("userId", userId);
        attributes.put("token", token);
        return true;
    }

    /**
     * 握手后
     */
    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response, WebSocketHandler wsHandler, Exception exception) {
        HttpServletRequest httpRequest = ((ServletServerHttpRequest) request).getServletRequest();
        HttpServletResponse httpResponse = ((ServletServerHttpResponse) response).getServletResponse();
        // 响应需要设置子协议不然会报错
        if (ObjectUtil.isNotEmpty(httpRequest.getHeader("Sec-WebSocket-Protocol"))) {
            httpResponse.addHeader("Sec-WebSocket-Protocol", httpRequest.getHeader("Sec-WebSocket-Protocol"));
        }
    }

}
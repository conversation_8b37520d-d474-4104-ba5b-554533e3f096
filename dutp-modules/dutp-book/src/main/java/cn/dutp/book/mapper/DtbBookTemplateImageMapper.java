package cn.dutp.book.mapper;

import cn.dutp.book.domain.DtbBookTemplateImage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教材模板图片Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-25
 */
@Repository
public interface DtbBookTemplateImageMapper extends BaseMapper<DtbBookTemplateImage> {

    List<DtbBookTemplateImage> listForAuthor(DtbBookTemplateImage dtbBookTemplateImage);
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbUserQuestionFolderMapper">
    
    <resultMap type="DtbUserQuestionFolder" id="DtbUserQuestionFolderResult">
        <result property="folderId"    column="folder_id"    />
        <result property="folderName"    column="folder_name"    />
        <result property="userId"    column="user_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectDtbUserQuestionFolderVo">
        select folder_id, folder_name, user_id, parent_id, create_by, create_time, update_by, update_time, del_flag from dtb_user_question_folder
    </sql>

    <select id="selectDtbUserQuestionFolderList" parameterType="DtbUserQuestionFolder" resultMap="DtbUserQuestionFolderResult">
        <include refid="selectDtbUserQuestionFolderVo"/>
        <where>  
            <if test="folderName != null  and folderName != ''"> and folder_name like concat('%', #{folderName}, '%')</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
        </where>
    </select>
    
    <select id="selectDtbUserQuestionFolderByFolderId" parameterType="Long" resultMap="DtbUserQuestionFolderResult">
        <include refid="selectDtbUserQuestionFolderVo"/>
        where folder_id = #{folderId}
    </select>

    <insert id="insertDtbUserQuestionFolder" parameterType="DtbUserQuestionFolder" useGeneratedKeys="true" keyProperty="folderId">
        insert into dtb_user_question_folder
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="folderName != null">folder_name,</if>
            <if test="userId != null">user_id,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="folderName != null">#{folderName},</if>
            <if test="userId != null">#{userId},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateDtbUserQuestionFolder" parameterType="DtbUserQuestionFolder">
        update dtb_user_question_folder
        <trim prefix="SET" suffixOverrides=",">
            <if test="folderName != null">folder_name = #{folderName},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where folder_id = #{folderId}
    </update>

    <delete id="deleteDtbUserQuestionFolderByFolderId" parameterType="Long">
        delete from dtb_user_question_folder where folder_id = #{folderId}
    </delete>

    <delete id="deleteDtbUserQuestionFolderByFolderIds" parameterType="String">
        delete from dtb_user_question_folder where folder_id in 
        <foreach item="folderId" collection="array" open="(" separator="," close=")">
            #{folderId}
        </foreach>
    </delete>
</mapper>
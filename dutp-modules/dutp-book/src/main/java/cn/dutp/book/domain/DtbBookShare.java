package cn.dutp.book.domain;

    import java.math.BigDecimal;
    import java.util.Date;
    import java.util.List;

    import com.baomidou.mybatisplus.annotation.TableField;
    import com.fasterxml.jackson.annotation.JsonFormat;
import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 教材分享记录对象 dtb_book_share
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Data
@TableName("dtb_book_share")
public class DtbBookShare extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** 主键id */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long shareId;

    /** 教材id */
        @Excel(name = "教材id")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /** 章节id */
        @Excel(name = "创建人id")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

        @Excel(name = "有效期")
    private Integer validityPeriod;

    /** 有效期开始日期 */
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        @Excel(name = "有效期开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startDate;

    /** 有效期结束日期 */
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        @Excel(name = "有效期结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endDate;

    /** 阅读码 */
        @Excel(name = "阅读码")
    private String code;

    /** 分享链接 */
        @Excel(name = "分享链接")
    private String shareLink;

    /** 1未生效 2生效中 3已过期 */
        @Excel(name = "1未生效 2生效中 3已过期")
    private Integer status;

    /** 备注 */
    @Excel(name = "备注")
    private String shareRemark;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    @TableField(exist = false)
    private List<DtbBookShareChapter> chapterList;

    @TableField(exist = false)
    private Integer commentNum;
    @TableField(exist = false)
    private String bookName;
    @TableField(exist = false)
    @Excel(name = "ISBN序列号")
    private String isbn;
    @TableField(exist = false)
    private String issn;
    @TableField(exist = false)
    private String bookNo;
    @TableField(exist = false)
    private String cover;
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出版日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date publishDate;
    @TableField(exist = false)
    private Integer shelfState;
    @TableField(exist = false)
    private Integer masterFlag;
    @TableField(exist = false)
    private BigDecimal priceCounter;
    @TableField(exist = false)
    private BigDecimal priceSale;
    @TableField(exist = false)
    private String topicNo;
    @TableField(exist = false)
    private String edition;
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long currentVersionId;
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long stepId;
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long lastVersionId;
    @TableField(exist = false)
    private Integer publishStatus;
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long houseId;


}

package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 数字教材习题对象 dtb_book_question
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@TableName("dtb_book_question")
public class DtbBookQuestion extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** $column.columnComment */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookQuestionId;

    /** 小题类型1单选 2多选 3填空 4排序 5连线 6简答 7判断 8编程 */
        @Excel(name = "小题类型1单选 2多选 3填空 4排序 5连线 6简答 7判断 8编程")
    private Integer questionType;

    /** 章节ID */
        @Excel(name = "章节ID")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    /** 教材ID */
        @Excel(name = "教材ID")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /** 排序 */
        @Excel(name = "排序")
    private Integer sort;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 所属目录 */
        @Excel(name = "所属目录")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long folderId;

    /** 储存题目内容的id，userQuestion */
        @Excel(name = "储存题目内容的id，userQuestion")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userQuestionId;


   /** 储存题目内容*/   
   @TableField(exist = false)
    private DtbUserQuestion userQuestion;


@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("bookQuestionId", getBookQuestionId())
            .append("questionType", getQuestionType())
            .append("chapterId", getChapterId())
            .append("bookId", getBookId())
            .append("sort", getSort())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .append("folderId", getFolderId())
            .append("userQuestionId", getUserQuestionId())
        .toString();
        }
        }

package cn.dutp.book.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import cn.dutp.book.domain.DtbBookShare;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
/**
 * 教材分享记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Repository
public interface DtbBookShareMapper extends BaseMapper<DtbBookShare>
{
     public List<DtbBookShare> selectDtbBookShareList(@Param("param") DtbBookShare dtbBookShare);
}

package cn.dutp.book.service;

import cn.dutp.book.domain.DtbBookPublishProcessAuditUser;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
/**
 * 发布流程审核人Service接口
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
public interface IDtbBookPublishProcessAuditUserService extends IService<DtbBookPublishProcessAuditUser>
{
    /**
     * 查询发布流程审核人
     *
     * @param auditUserId 发布流程审核人主键
     * @return 发布流程审核人
     */
    public DtbBookPublishProcessAuditUser selectDtbBookPublishProcessAuditUserByAuditUserId(Long auditUserId);

    /**
     * 查询发布流程审核人列表
     *
     * @param dtbBookPublishProcessAuditUser 发布流程审核人
     * @return 发布流程审核人集合
     */
    public List<DtbBookPublishProcessAuditUser> selectDtbBookPublishProcessAuditUserList(DtbBookPublishProcessAuditUser dtbBookPublishProcessAuditUser);

    /**
     * 新增发布流程审核人
     *
     * @param dtbBookPublishProcessAuditUser 发布流程审核人
     * @return 结果
     */
    public boolean insertDtbBookPublishProcessAuditUser(DtbBookPublishProcessAuditUser dtbBookPublishProcessAuditUser);

    /**
     * 修改发布流程审核人
     *
     * @param dtbBookPublishProcessAuditUser 发布流程审核人
     * @return 结果
     */
    public boolean updateDtbBookPublishProcessAuditUser(DtbBookPublishProcessAuditUser dtbBookPublishProcessAuditUser);

    /**
     * 批量删除发布流程审核人
     *
     * @param auditUserIds 需要删除的发布流程审核人主键集合
     * @return 结果
     */
    public boolean deleteDtbBookPublishProcessAuditUserByAuditUserIds(List<Long> auditUserIds);

}

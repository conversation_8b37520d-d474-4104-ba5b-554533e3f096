package cn.dutp.book.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.book.mapper.DtbUserBookNoteAttachmentMapper;
import cn.dutp.book.domain.DtbUserBookNoteAttachment;
import cn.dutp.book.service.IDtbUserBookNoteAttachmentService;

/**
 * 笔记附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class DtbUserBookNoteAttachmentServiceImpl extends ServiceImpl<DtbUserBookNoteAttachmentMapper, DtbUserBookNoteAttachment> implements IDtbUserBookNoteAttachmentService
{
    @Autowired
    private DtbUserBookNoteAttachmentMapper dtbUserBookNoteAttachmentMapper;

    /**
     * 查询笔记附件
     *
     * @param attachmentId 笔记附件主键
     * @return 笔记附件
     */
    @Override
    public DtbUserBookNoteAttachment selectDtbUserBookNoteAttachmentByAttachmentId(Long attachmentId)
    {
        return this.getById(attachmentId);
    }

    /**
     * 查询笔记附件列表
     *
     * @param dtbUserBookNoteAttachment 笔记附件
     * @return 笔记附件
     */
    @Override
    public List<DtbUserBookNoteAttachment> selectDtbUserBookNoteAttachmentList(DtbUserBookNoteAttachment dtbUserBookNoteAttachment)
    {
        LambdaQueryWrapper<DtbUserBookNoteAttachment> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbUserBookNoteAttachment.getNoteId())) {
                lambdaQueryWrapper.eq(DtbUserBookNoteAttachment::getNoteId
                ,dtbUserBookNoteAttachment.getNoteId());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookNoteAttachment.getAttachmentType())) {
                lambdaQueryWrapper.eq(DtbUserBookNoteAttachment::getAttachmentType
                ,dtbUserBookNoteAttachment.getAttachmentType());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增笔记附件
     *
     * @param dtbUserBookNoteAttachment 笔记附件
     * @return 结果
     */
    @Override
    public boolean insertDtbUserBookNoteAttachment(DtbUserBookNoteAttachment dtbUserBookNoteAttachment)
    {
        return this.save(dtbUserBookNoteAttachment);
    }

    /**
     * 修改笔记附件
     *
     * @param dtbUserBookNoteAttachment 笔记附件
     * @return 结果
     */
    @Override
    public boolean updateDtbUserBookNoteAttachment(DtbUserBookNoteAttachment dtbUserBookNoteAttachment)
    {
        return this.updateById(dtbUserBookNoteAttachment);
    }

    /**
     * 批量删除笔记附件
     *
     * @param attachmentIds 需要删除的笔记附件主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbUserBookNoteAttachmentByAttachmentIds(List<Long> attachmentIds)
    {
        return this.removeByIds(attachmentIds);
    }

}

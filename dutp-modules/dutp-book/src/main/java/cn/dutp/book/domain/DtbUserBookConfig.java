package cn.dutp.book.domain;

    import java.math.BigDecimal;
    import java.util.Date;
    import com.fasterxml.jackson.annotation.JsonFormat;
import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 教材配置对象 dtb_user_book_config
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Data
@TableName("dtb_user_book_config")
public class DtbUserBookConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long configId;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    private Integer isMobile;

    private String theme;

    private String fontFamily;

    private Integer fontSize;

    private Integer columnQuantity;

    private BigDecimal lineHeight;

    private Integer readRate;

    private String readMode;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date lastSeeDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long lastChapterId;

    private Integer lastPageNumber;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    private String bgColor;

    private String pageFlippingMethod;

}

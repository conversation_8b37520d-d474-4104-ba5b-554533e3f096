package cn.dutp.book.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.book.domain.vo.ResourceVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.book.domain.DtbBookResource;
import cn.dutp.book.service.IDtbBookResourceService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 教材资源Controller
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
@RestController
@RequestMapping("/bookResource")
public class DtbBookResourceController extends BaseController
{
    @Autowired
    private IDtbBookResourceService dtbBookResourceService;

    /**
     * 查询教材资源列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbBookResource dtbBookResource)
    {
        startPage();
        List<DtbBookResource> list = dtbBookResourceService.selectDtbBookResourceList(dtbBookResource);
        return getDataTable(list);
    }

    /**
     * 导出教材资源列表
     */
    @RequiresPermissions("book:bookResource:export")
    @Log(title = "教材资源", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbBookResource dtbBookResource)
    {
        List<DtbBookResource> list = dtbBookResourceService.selectDtbBookResourceList(dtbBookResource);
        ExcelUtil<DtbBookResource> util = new ExcelUtil<DtbBookResource>(DtbBookResource.class);
        util.exportExcel(response, list, "教材资源数据");
    }

    /**
     * 获取教材资源详细信息
     */
    @RequiresPermissions("book:bookResource:query")
    @GetMapping(value = "/{bookResourceId}")
    public AjaxResult getInfo(@PathVariable("bookResourceId") Long bookResourceId)
    {
        return success(dtbBookResourceService.selectDtbBookResourceByBookResourceId(bookResourceId));
    }

    /**
     * 新增教材资源
     */
    @RequiresPermissions("book:bookResource:add")
    @Log(title = "新增教材资源", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookResource dtbBookResource)
    {
        return toAjax(dtbBookResourceService.insertDtbBookResource(dtbBookResource));
    }

    /**
     * 编辑器上传资源
     */
    @Log(title = "编辑器上传资源", businessType = BusinessType.INSERT)
    @PostMapping("/uploadResource")
    public AjaxResult uploadResource(@RequestBody ResourceVO resourceVO)
    {
        return toAjax(dtbBookResourceService.uploadResource(resourceVO));
    }


    /**
     * 修改教材资源
     */
    @RequiresPermissions("book:bookResource:edit")
    @Log(title = "修改教材资源", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookResource dtbBookResource)
    {
        return toAjax(dtbBookResourceService.updateDtbBookResource(dtbBookResource));
    }

    /**
     * 删除教材资源
     */
    @RequiresPermissions("book:bookResource:remove")
    @Log(title = "删除教材资源", businessType = BusinessType.DELETE)
    @DeleteMapping("/{bookResourceIds}")
    public AjaxResult remove(@PathVariable Long[] bookResourceIds)
    {
        return toAjax(dtbBookResourceService.deleteDtbBookResourceByBookResourceIds(Arrays.asList(bookResourceIds)));
    }
}

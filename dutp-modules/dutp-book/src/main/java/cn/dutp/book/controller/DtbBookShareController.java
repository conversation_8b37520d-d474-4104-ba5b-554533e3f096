package cn.dutp.book.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.book.domain.DtbBookShare;
import cn.dutp.book.service.IDtbBookShareService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 教材分享记录Controller
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@RestController
@RequestMapping("/bookShare")
public class DtbBookShareController extends BaseController
{
    @Autowired
    private IDtbBookShareService dtbBookShareService;

    /**
     * 查询教材分享记录列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbBookShare dtbBookShare)
    {
        startPage();
        List<DtbBookShare> list = dtbBookShareService.selectDtbBookShareList(dtbBookShare);
        return getDataTable(list);
    }

    /**
     * 导出教材分享记录列表
     */
    @RequiresPermissions("book:bookShare:export")
    @Log(title = "导出教材分享记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbBookShare dtbBookShare)
    {
        List<DtbBookShare> list = dtbBookShareService.selectDtbBookShareList(dtbBookShare);
        ExcelUtil<DtbBookShare> util = new ExcelUtil<DtbBookShare>(DtbBookShare.class);
        util.exportExcel(response, list, "教材分享记录数据");
    }

    /**
     * 获取教材分享记录详细信息
     */
    @GetMapping(value = "/{shareId}")
    public AjaxResult getInfo(@PathVariable("shareId") Long shareId)
    {
        return success(dtbBookShareService.selectDtbBookShareByShareId(shareId));
    }

    /**
     * 新增教材分享记录
     */
//    @RequiresPermissions("book:bookShare:add")
    @Log(title = "新增教材分享记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookShare dtbBookShare)
    {
        return toAjax(dtbBookShareService.insertDtbBookShare(dtbBookShare));
    }

    /**
     * 修改教材分享记录
     */
//    @RequiresPermissions("book:bookShare:edit")
    @Log(title = "修改教材分享记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookShare dtbBookShare)
    {
        return toAjax(dtbBookShareService.updateDtbBookShare(dtbBookShare));
    }

    /**
     * 删除教材分享记录
     */
    @RequiresPermissions("book:bookShare:remove")
    @Log(title = "删除教材分享记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{shareIds}")
    public AjaxResult remove(@PathVariable Long[] shareIds)
    {
        return toAjax(dtbBookShareService.deleteDtbBookShareByShareIds(Arrays.asList(shareIds)));
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbUserBookStudyDataMapper">
    
    <resultMap type="DtbUserBookStudyData" id="DtbUserBookStudyDataResult">
        <result property="dataId"    column="data_id"    />
        <result property="userId"    column="user_id"    />
        <result property="bookId"    column="book_id"    />
        <result property="chapterId"    column="chapter_id"    />
        <result property="studyTime"    column="study_time"    />
        <result property="videoTime"    column="video_time"    />
        <result property="noteQuantity"    column="note_quantity"    />
        <result property="lightQuantity"    column="light_quantity"    />
        <result property="discussQuantity"    column="discuss_quantity"    />
        <result property="userQuantity"    column="user_quantity"    />
        <result property="questionQuantity"    column="question_quantity"    />
        <result property="questionRate"    column="question_rate"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDtbUserBookStudyDataVo">
        select data_id, user_id, book_id, chapter_id, study_time, video_time, note_quantity, light_quantity, discuss_quantity, user_quantity, question_quantity, question_rate, create_by, create_time, update_by, update_time from dtb_user_book_study_data
    </sql>

    <select id="selectDtbUserBookStudyDataList" parameterType="DtbUserBookStudyData" resultMap="DtbUserBookStudyDataResult">
        <include refid="selectDtbUserBookStudyDataVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="bookId != null "> and book_id = #{bookId}</if>
            <if test="chapterId != null "> and chapter_id = #{chapterId}</if>
            <if test="studyTime != null "> and study_time = #{studyTime}</if>
            <if test="videoTime != null "> and video_time = #{videoTime}</if>
            <if test="noteQuantity != null "> and note_quantity = #{noteQuantity}</if>
            <if test="lightQuantity != null "> and light_quantity = #{lightQuantity}</if>
            <if test="discussQuantity != null "> and discuss_quantity = #{discussQuantity}</if>
            <if test="userQuantity != null "> and user_quantity = #{userQuantity}</if>
            <if test="questionQuantity != null "> and question_quantity = #{questionQuantity}</if>
            <if test="questionRate != null "> and question_rate = #{questionRate}</if>
        </where>
    </select>
    
    <select id="selectDtbUserBookStudyDataByDataId" parameterType="Long" resultMap="DtbUserBookStudyDataResult">
        <include refid="selectDtbUserBookStudyDataVo"/>
        where data_id = #{dataId}
    </select>

    <insert id="insertDtbUserBookStudyData" parameterType="DtbUserBookStudyData" useGeneratedKeys="true" keyProperty="dataId">
        insert into dtb_user_book_study_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="bookId != null">book_id,</if>
            <if test="chapterId != null">chapter_id,</if>
            <if test="studyTime != null">study_time,</if>
            <if test="videoTime != null">video_time,</if>
            <if test="noteQuantity != null">note_quantity,</if>
            <if test="lightQuantity != null">light_quantity,</if>
            <if test="discussQuantity != null">discuss_quantity,</if>
            <if test="userQuantity != null">user_quantity,</if>
            <if test="questionQuantity != null">question_quantity,</if>
            <if test="questionRate != null">question_rate,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="bookId != null">#{bookId},</if>
            <if test="chapterId != null">#{chapterId},</if>
            <if test="studyTime != null">#{studyTime},</if>
            <if test="videoTime != null">#{videoTime},</if>
            <if test="noteQuantity != null">#{noteQuantity},</if>
            <if test="lightQuantity != null">#{lightQuantity},</if>
            <if test="discussQuantity != null">#{discussQuantity},</if>
            <if test="userQuantity != null">#{userQuantity},</if>
            <if test="questionQuantity != null">#{questionQuantity},</if>
            <if test="questionRate != null">#{questionRate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDtbUserBookStudyData" parameterType="DtbUserBookStudyData">
        update dtb_user_book_study_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="chapterId != null">chapter_id = #{chapterId},</if>
            <if test="studyTime != null">study_time = #{studyTime},</if>
            <if test="videoTime != null">video_time = #{videoTime},</if>
            <if test="noteQuantity != null">note_quantity = #{noteQuantity},</if>
            <if test="lightQuantity != null">light_quantity = #{lightQuantity},</if>
            <if test="discussQuantity != null">discuss_quantity = #{discussQuantity},</if>
            <if test="userQuantity != null">user_quantity = #{userQuantity},</if>
            <if test="questionQuantity != null">question_quantity = #{questionQuantity},</if>
            <if test="questionRate != null">question_rate = #{questionRate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where data_id = #{dataId}
    </update>

    <delete id="deleteDtbUserBookStudyDataByDataId" parameterType="Long">
        delete from dtb_user_book_study_data where data_id = #{dataId}
    </delete>

    <delete id="deleteDtbUserBookStudyDataByDataIds" parameterType="String">
        delete from dtb_user_book_study_data where data_id in 
        <foreach item="dataId" collection="array" open="(" separator="," close=")">
            #{dataId}
        </foreach>
    </delete>
</mapper>
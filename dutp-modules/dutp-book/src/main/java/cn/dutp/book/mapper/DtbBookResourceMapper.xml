<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbBookResourceMapper">
    
    <sql id="selectDtbBookResourceVo">
        select book_resource_id, book_id, resource_id, owner_id, folder_id, in_use, create_by, create_time, update_by, update_time from dtb_book_resource
    </sql>

    <select id="getReaderBookResource" resultType="cn.dutp.book.domain.vo.DtbBookResourceVO">
        SELECT
        chapter_resource_id,
        book_id,
        chapter_id,
        file_name,
        file_url,
        file_type,
        file_size
        FROM
        dtb_book_chapter_resource
        WHERE book_id = #{bookId}
        <if test="chapterId !=null">
            AND chapter_id = #{chapterId}
        </if>
    </select>
    <select id="getReaderBookQuestion" resultType="cn.dutp.book.domain.vo.DtbUserQuestionVO">
        SELECT
            bq.book_question_id,
            q.question_id,
            q.question_type,
            q.question_content,
            q.right_answer,
            q.analysis,
            q.disorder,
            q.sort,
            code_content
        FROM
            dtb_user_question q
        INNER JOIN dtb_book_question bq ON q.question_id = bq.user_question_id
        WHERE
            bq.book_id = #{bookId}
    </select>
    <select id="queryBookResource" resultType="cn.dutp.book.domain.DtbBookResource">
        SELECT
        r.resource_id,
        r.owner_id,
        r.in_use
        FROM
        dtb_book_resource r
        WHERE
        r.book_id = #{bookId}
        AND r.folder_id = #{folderId}
    </select>
    <select id="getReaderBookResourceByChapter" resultType="cn.dutp.book.domain.DtbBookChapterResource">
        select
            bc.chapter_id,
            bc.chapter_name,
            bcr.book_id,
            bcr.file_name,
            bcr.file_url,
            bcr.file_size,
            bcr.file_type,
            bcr.dom_id,
            bcr.page_number
        from dtb_book_chapter_resource bcr
        left join dtb_book_chapter bc on bcr.chapter_id = bc.chapter_id
        where  bcr.book_id = #{param.bookId}
        <if test="param.isFree == 2">
            and bc.free = 2
        </if>
        <if test="param.versionId != null">
            AND bc.version_id = #{param.versionId}
        </if>
        <if test="param.chapterId != null and param.chapterId != ''">
            AND bcr.chapter_id = #{param.chapterId}
        </if>
        <if test="param.resourceType == 1">
            AND bcr.file_type = '9'
        </if>
        <if test="param.resourceType == 0">
            AND bcr.file_type != '9'
        </if>
        <if test="param.fileType != null and param.fileType != ''">
            AND bcr.file_type = #{param.fileType}
        </if>
    </select>
</mapper>
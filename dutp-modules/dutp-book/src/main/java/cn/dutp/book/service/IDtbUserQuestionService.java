package cn.dutp.book.service;

import java.util.List;

import cn.dutp.book.domain.DtbBookQuestionAnswer;
import cn.dutp.book.domain.DtbUserQuestion;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 数字教材习题Service接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IDtbUserQuestionService extends IService<DtbUserQuestion>
{
    /**
     * 查询数字教材习题
     *
     * @param questionId 数字教材习题主键
     * @return 数字教材习题
     */
    public DtbUserQuestion selectDtbUserQuestionByQuestionId(Long questionId);


    /**
     * 查询数字教材习题列表
     *
     * @param dtbUserQuestion 数字教材习题
     * @return 数字教材习题集合
     */
    public List<DtbUserQuestion> selectDtbUserQuestionList(DtbUserQuestion dtbUserQuestion);

    /**
     * 新增数字教材习题
     *
     * @param dtbUserQuestion 数字教材习题
     * @return 结果
     */
    public boolean insertDtbUserQuestion(DtbUserQuestion dtbUserQuestion);

    /**
     * 修改数字教材习题
     *
     * @param dtbUserQuestion 数字教材习题
     * @return 结果
     */
    public boolean updateDtbUserQuestion(DtbUserQuestion dtbUserQuestion);

    /**
     * 批量删除数字教材习题
     *
     * @param questionIds 需要删除的数字教材习题主键集合
     * @return 结果
     */
    public boolean deleteDtbUserQuestionByQuestionIds(List<Long> questionIds);

    /**
     * 查询数字教材习题列表（包含选项）
     * 
     * @param dtbUserQuestion 数字教材习题
     * @return 数字教材习题集合
     */
    List<DtbUserQuestion> selectDtbUserQuestionListWithOptions(DtbUserQuestion dtbUserQuestion);

    /**
     * 查询数字教材习题数量
     * 
     * @param dtbUserQuestion 数字教材习题
     * @return 数字教材习题数量
     */
    int countDtbUserQuestion(DtbUserQuestion dtbUserQuestion);

    /**
     * 导入数字教材习题
     *
     * @param dtbUserQuestionList 数字教材习题列表
     * @return 导入结果
     */
    String importQuestions(List<DtbUserQuestion> dtbUserQuestionList);

     /**
     * 将数字教材习题移入回收站
     */
    boolean moveToRecycleBin(List<Long> questionIds);

    /**
     * 从回收站恢复数字教材习题
     */
    boolean restoreFromRecycleBin(List<Long> questionIds);

    /**
     * 查询回收站列表
     */
    List<DtbUserQuestion> recycleBinList(DtbUserQuestion dtbUserQuestion);

    /**
     * 获取用户答案详细信息
     */
    DtbUserQuestion getUserAnswerInfo(DtbUserQuestion dtbUserQuestion);

    /**
     * 检查题目是否被试卷管理
     * 
     * @param questionIds 题目ID集合
     * @return 是否被试卷管理
     */
    boolean checkPaperReference(Long[] questionIds);
}

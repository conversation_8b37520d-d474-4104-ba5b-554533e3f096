package cn.dutp.book.service;

import java.util.List;
import cn.dutp.book.domain.DtbBookResource;
import cn.dutp.book.domain.form.ReaderCommonForm;
import cn.dutp.book.domain.vo.DtbBookResourceTreeVO;
import cn.dutp.book.domain.vo.DtbUserQuestionVO;
import cn.dutp.book.domain.vo.DtbBookResourceVO;
import cn.dutp.book.domain.vo.ResourceVO;
import cn.dutp.common.core.web.domain.AjaxResult;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 教材资源Service接口
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
public interface IDtbBookResourceService extends IService<DtbBookResource>
{
    /**
     * 查询教材资源
     *
     * @param bookResourceId 教材资源主键
     * @return 教材资源
     */
    public DtbBookResource selectDtbBookResourceByBookResourceId(Long bookResourceId);

    /**
     * 查询教材资源列表
     *
     * @param dtbBookResource 教材资源
     * @return 教材资源集合
     */
    public List<DtbBookResource> selectDtbBookResourceList(DtbBookResource dtbBookResource);

    /**
     * 新增教材资源
     *
     * @param dtbBookResource 教材资源
     * @return 结果
     */
    public boolean insertDtbBookResource(DtbBookResource dtbBookResource);

    /**
     * 修改教材资源
     *
     * @param dtbBookResource 教材资源
     * @return 结果
     */
    public boolean updateDtbBookResource(DtbBookResource dtbBookResource);

    /**
     * 批量删除教材资源
     *
     * @param bookResourceIds 需要删除的教材资源主键集合
     * @return 结果
     */
    public boolean deleteDtbBookResourceByBookResourceIds(List<Long> bookResourceIds);

    List<DtbBookResourceVO> getReaderBookResource(ReaderCommonForm readerCommonForm);

    AjaxResult getReaderBookResourceByChapter(ReaderCommonForm readerCommonForm);

    List<DtbUserQuestionVO> getReaderBookQuestion(ReaderCommonForm readerCommonForm);

    boolean uploadResource(ResourceVO resourceVO);

    boolean batchUploadResource(List<ResourceVO> resourceVOList, Long userId, Long chapterId);
}

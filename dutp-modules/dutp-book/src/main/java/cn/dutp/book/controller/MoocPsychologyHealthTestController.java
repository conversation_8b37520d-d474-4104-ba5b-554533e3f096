package cn.dutp.book.controller;

import cn.dutp.book.domain.MoocPsychologyHealthScale;
import cn.dutp.book.service.IMoocPsychologyHealthUserResultService;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 心理健康测试Controller
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@RestController
@RequestMapping("/moocPsychologyHealth")
public class MoocPsychologyHealthTestController extends BaseController {
    @Autowired
    private IMoocPsychologyHealthUserResultService moocPsychologyHealthUserResultService;

    /**
     * 查看量表测试结果列表
     */
    @GetMapping("/getPsychologyTestList")
    public TableDataInfo getPsychologyTestList(MoocPsychologyHealthScale scale) {
        return getDataTable(moocPsychologyHealthUserResultService.getPsychologyTestList(scale));
    }

    /**
     * 查看量表测试结果详情
     */
    @GetMapping("/getPsychologyTestDetail")
    public AjaxResult getPsychologyTestDetail(MoocPsychologyHealthScale scale) {
        return AjaxResult.success(moocPsychologyHealthUserResultService.getPsychologyTestDetail(scale));
    }

}

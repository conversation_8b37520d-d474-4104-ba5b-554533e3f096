package cn.dutp.book.service.impl;

import java.util.Date;
import java.util.List;

import cn.dutp.book.domain.DtbUserBook;
import cn.dutp.book.domain.DtbUserBookConfig;
import cn.dutp.book.mapper.DtbUserBookConfigMapper;
import cn.dutp.book.mapper.DtbUserBookMapper;
import cn.dutp.book.service.IDutpAiVoiceService;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.domain.BaseEntity;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBook;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.book.mapper.DtbUserBookReadMapper;
import cn.dutp.book.domain.DtbUserBookRead;
import cn.dutp.book.service.IDtbUserBookReadService;

/**
 * 用户阅读记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
public class DtbUserBookReadServiceImpl extends ServiceImpl<DtbUserBookReadMapper, DtbUserBookRead> implements IDtbUserBookReadService
{
    @Autowired
    private DtbUserBookReadMapper dtbUserBookReadMapper;
    @Autowired
    private DtbUserBookMapper dtbUserBookMapper;
    @Autowired
    private DtbUserBookConfigMapper dtbUserBookConfigMapper;

    @Autowired
    IDutpAiVoiceService dutpAiVoiceService;
    /**
     * 查询用户阅读记录
     *
     * @param readId 用户阅读记录主键
     * @return 用户阅读记录
     */
    @Override
    public DtbUserBookRead selectDtbUserBookReadByReadId(Long readId)
    {
        return this.getById(readId);
    }

    /**
     * 查询用户阅读记录列表
     *
     * @param dtbUserBookRead 用户阅读记录
     * @return 用户阅读记录
     */
    @Override
    public List<DtbUserBookRead> selectDtbUserBookReadList(DtbUserBookRead dtbUserBookRead)
    {
        LambdaQueryWrapper<DtbUserBookRead> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbUserBookRead.getUserId())) {
                lambdaQueryWrapper.eq(DtbUserBookRead::getUserId
                ,dtbUserBookRead.getUserId());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookRead.getBookId())) {
                lambdaQueryWrapper.eq(DtbUserBookRead::getBookId
                ,dtbUserBookRead.getBookId());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookRead.getChapterId())) {
                lambdaQueryWrapper.eq(DtbUserBookRead::getChapterId
                ,dtbUserBookRead.getChapterId());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookRead.getPageNumber())) {
                lambdaQueryWrapper.eq(DtbUserBookRead::getPageNumber
                ,dtbUserBookRead.getPageNumber());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增用户阅读记录
     *
     * @param dtbUserBookRead 用户阅读记录
     * @return 结果
     */
    @Override
    public AjaxResult insertDtbUserBookRead(DtbUserBookRead dtbUserBookRead)
    {
        Long userId = SecurityUtils.getUserId();
        if (ObjectUtil.isNotEmpty(userId)) {
            // 修改配置表
            LambdaQueryWrapper<DtbUserBookConfig> updateWrapper = new LambdaQueryWrapper<>();
            updateWrapper.eq(DtbUserBookConfig::getBookId, dtbUserBookRead.getBookId())
                    .eq(DtbUserBookConfig::getUserId, userId);
            DtbUserBookConfig updateEntity = new DtbUserBookConfig();
            updateEntity.setReadRate(dtbUserBookRead.getReadRate());
            updateEntity.setLastChapterId(dtbUserBookRead.getChapterId());
            updateEntity.setLastPageNumber(dtbUserBookRead.getPageNumber());
            updateEntity.setLastSeeDate(new Date());
            dtbUserBookConfigMapper.update(updateEntity, updateWrapper);

            // 保存阅读记录
            dtbUserBookRead.setUserId(SecurityUtils.getUserId());
            this.save(dtbUserBookRead);
            // 修改阅读最后时间
            LambdaQueryWrapper<DtbUserBook> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(DtbUserBook::getUserId,userId).eq(DtbUserBook::getBookId,dtbUserBookRead.getBookId());
            DtbUserBook dtbUserBook = dtbUserBookMapper.selectOne(lambdaQueryWrapper);
            if (ObjectUtil.isNotEmpty(dtbUserBook)) {
                DtbUserBook param = new DtbUserBook();
                param.setUserBookId(dtbUserBook.getUserBookId());
                param.setLastSeeDate(new Date());
                param.setReadRate(dtbUserBookRead.getReadRate());
                dtbUserBookMapper.updateById(param);
            }
        }
        return AjaxResult.success("保存成功",dtbUserBookRead.getReadId().toString());
    }

    /**
     * 修改用户阅读记录
     *
     * @param dtbUserBookRead 用户阅读记录
     * @return 结果
     */
    @Override
    public boolean updateDtbUserBookRead(DtbUserBookRead dtbUserBookRead)
    {
        return this.updateById(dtbUserBookRead);
    }

    /**
     * 批量删除用户阅读记录
     *
     * @param readIds 需要删除的用户阅读记录主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbUserBookReadByReadIds(List<Long> readIds)
    {
        return this.removeByIds(readIds);
    }

    @Override
    public AjaxResult updateUserReadTime(Long readId, Integer readTime) {
        dtbUserBookReadMapper.updateReadTime(readId, readTime);
        return AjaxResult.success();
    }

    @Override
    public AjaxResult getReadRole() {

        return null;
    }
}

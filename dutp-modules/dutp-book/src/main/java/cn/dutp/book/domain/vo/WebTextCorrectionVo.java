package cn.dutp.book.domain.vo;

import lombok.Data;

import java.util.List;

/**
 * 文本纠错返回实体
 * 元素内的错误类型与变量名称非驼峰名称相同，有备注的除外
 * 纠正文本为空表示当前无纠正结果
 */
@Data
public class WebTextCorrectionVo {

    /** 黑名单纠错 元素内分别表示错误位置、错误文本、纠正文本、更详细的错误类型
     * 类型中的blacklist代表黑名单错误
     **/
    private List<List<TextCommonResultVo>> blackList;

    /** 政治术语纠错 元素内分别表示错误位置、错误文本、纠正文本、更详细的错误类型 */
    private List<List<TextCommonResultVo>> pol;

    /** 别字纠错 元素内分别表示错误位置、错误文本、纠正文本、更详细的错误类型 */
    /** 返回值名称与关键字重叠，用别名代替 */
//    private List<List<TextCommonResultVo>> char;
    private List<List<TextCommonResultVo>> chars;

    /** 别词纠错 元素内分别表示错误位置、错误文本、纠正文本、更详细的错误类型 */
    private List<List<TextCommonResultVo>> word;

    /** 别词纠错 元素内分别表示错误位置、错误文本、纠正文本、更详细的错误类型 */
    private List<List<TextCommonResultVo>> redund;

    /** 语法纠错-缺失 元素内分别表示错误位置、错误文本、纠正文本、更详细的错误类型 */
    private List<List<TextCommonResultVo>> miss;

    /** 语法纠错-乱序 元素内分别表示错误位置、错误文本、纠正文本、更详细的错误类型 */
    private List<List<TextCommonResultVo>> order;

    /** 搭配纠错 元素内分别表示错误位置、错误文本、纠正文本、更详细的错误类型 */
    private List<List<TextCommonResultVo>> dapei;

    /**
     * 标点纠错 元素内分别表示错误位置、错误文本、纠正文本、更详细的错误类型
     * 类型包括：
     * 半角标点误用成对符号不匹配
     * 重复标点
     * 连续使用标点
     * 顿号使用不当
     * 省略号使用不当
     * 连接号使用不当
     * 标示发文年号不规范
     * 疑似省略号误用
     * 书名号内顿号使用不当
     * 疑似标点错误
     **/
    private List<List<TextCommonResultVo>> punc;

    /** 成语纠错 元素内分别表示错误位置、错误文本、纠正文本、更详细的错误类型 */
    private List<List<TextCommonResultVo>> idm;

    /**
     * 机构名纠错 元素内分别表示错误位置、错误文本、纠正文本、更详细的错误类型
     * 类型可能包括：
     * org_R：机构名字词冗余
     * org_M：机构名字词缺失
     * org_S：机构名字词错误
     * org_N：机构名称变更
     * org_P：机构名字词乱序
     **/
    private List<List<TextCommonResultVo>> org;

    /**
     * 领导人职称纠错 元素内分别表示错误位置、错误文本、纠正文本、更详细的错误类型
     * 类型包括：
     * lea代表领导人职称纠错
     **/
    private List<List<TextCommonResultVo>> leader;

    /**
     * 数字纠错 元素内分别表示错误位置、错误文本、纠正文本、更详细的错误类型
     * 类型可能包括：
     * time：时间纠错
     * date-m：日期纠错（月份）
     * date-d：日期纠错（日）
     **/
    private List<List<TextCommonResultVo>> number;

    /**
     * 地名纠错 元素内分别表示错误位置、错误文本、纠正文本、更详细的错误类型
     * 类型包括：
     * addr_R-地名字词冗余、addr_M-地名字词缺失、addr_S-地名字词错误
     **/
    private List<List<TextCommonResultVo>> addr;

    /** 全文人名纠错 元素内分别表示错误位置、错误文本、纠正文本、更详细的错误类型 */
    private List<List<TextCommonResultVo>> name;

    /** 句式杂糅&语义重复 元素内分别表示错误位置、错误文本、纠正文本、更详细的错误类型 */
    private List<List<TextCommonResultVo>> grammarPc;

}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.EduSchoolBookMapper">

    <resultMap type="EduSchoolBook" id="EduSchoolBookResult">
        <result property="schoolBookId" column="school_book_id"/>
        <result property="schoolId" column="school_id"/>
        <result property="bookId" column="book_id"/>
        <result property="dayLimit" column="day_limit"/>
        <result property="expireDate" column="expire_date"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectEduSchoolBookVo">
        select school_book_id,
               school_id,
               book_id,
               day_limit,
               expire_date,
               create_by,
               create_time,
               update_by,
               update_time
        from edu_school_book
    </sql>


    <select id="selectEduSchoolBookBySchoolBookId" parameterType="Long" resultMap="EduSchoolBookResult">
        <include refid="selectEduSchoolBookVo"/>
        where school_book_id = #{schoolBookId}
    </select>
    <select id="selectEduSchoolBookList" resultType="cn.dutp.book.domain.EduSchoolBook">
        SELECT
            s.school_code,
            s.school_name,
            s.school_id,
            sb.book_id,
            sb.day_limit,
            sb.expire_date,
            sb.create_time
        FROM
            edu_school_book sb
                INNER JOIN dutp_school s ON sb.school_id = s.school_id
        WHERE sb.book_id = #{bookId}
        <if test="schoolId != null">AND s.school_id = #{schoolId}</if>
        order by sb.create_time desc
    </select>


    <insert id="insertEduSchoolBook" parameterType="EduSchoolBook" useGeneratedKeys="true" keyProperty="schoolBookId">
        insert into edu_school_book
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="schoolId != null">school_id,</if>
            <if test="bookId != null">book_id,</if>
            <if test="dayLimit != null">day_limit,</if>
            <if test="expireDate != null">expire_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="schoolId != null">#{schoolId},</if>
            <if test="bookId != null">#{bookId},</if>
            <if test="dayLimit != null">#{dayLimit},</if>
            <if test="expireDate != null">#{expireDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateEduSchoolBook" parameterType="EduSchoolBook">
        update edu_school_book
        <trim prefix="SET" suffixOverrides=",">
            <if test="schoolId != null">school_id = #{schoolId},</if>
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="dayLimit != null">day_limit = #{dayLimit},</if>
            <if test="expireDate != null">expire_date = #{expireDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where school_book_id = #{schoolBookId}
    </update>

    <delete id="deleteEduSchoolBookBySchoolBookId" parameterType="Long">
        delete
        from edu_school_book
        where school_book_id = #{schoolBookId}
    </delete>

    <delete id="deleteEduSchoolBookBySchoolBookIds" parameterType="String">
        delete from edu_school_book where school_book_id in
        <foreach item="schoolBookId" collection="array" open="(" separator="," close=")">
            #{schoolBookId}
        </foreach>
    </delete>
</mapper>
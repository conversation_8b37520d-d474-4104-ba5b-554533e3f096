package cn.dutp.book.service;

import java.util.List;

import cn.dutp.book.domain.DtbUserBook;
import cn.dutp.book.domain.vo.DtbUserBookVo;
import cn.dutp.common.core.web.domain.AjaxResult;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * DUTP-DTB_014学生/教师书架Service接口
 *
 * <AUTHOR>
 * @date 2024-12-04
 */
public interface IDtbUserBookService extends IService<DtbUserBook>
{
    /**
     * 查询DUTP-DTB_014学生/教师书架
     *
     * @param userBookId DUTP-DTB_014学生/教师书架主键
     * @return DUTP-DTB_014学生/教师书架
     */
    public DtbUserBook selectDtbUserBookByUserBookId(Long userBookId);

    /**
     * 学生教师端我的教材查询
     *
     * @param dtbUserBook
     * @return 查询结果
     */
    public List<DtbUserBookVo> getInfoEducation(DtbUserBook dtbUserBook);

    /**
     * 查询DUTP-DTB_014学生/教师书架列表
     *
     * @param dtbUserBook DUTP-DTB_014学生/教师书架
     * @return DUTP-DTB_014学生/教师书架集合
     */
    public List<DtbUserBook> selectDtbUserBookList(DtbUserBook dtbUserBook);

    /**
     * 新增DUTP-DTB_014学生/教师书架
     *
     * @param dtbUserBook DUTP-DTB_014学生/教师书架
     * @return 结果
     */
    public boolean insertDtbUserBook(DtbUserBook dtbUserBook);

    /**
     * 修改DUTP-DTB_014学生/教师书架
     *
     * @param dtbUserBook DUTP-DTB_014学生/教师书架
     * @return 结果
     */
    public boolean updateDtbUserBook(DtbUserBook dtbUserBook);

    /**
     * 学生教师端批量修改学生/教师书架
     *
     * @param dtbUserBookList 学生教师端批量修改学生/教师书架
     * @return 结果
     */
    public boolean editEducation(List<DtbUserBook> dtbUserBookList);
    /**
     * 批量删除DUTP-DTB_014学生/教师书架
     *
     * @param userBookIds 需要删除的DUTP-DTB_014学生/教师书架主键集合
     * @return 结果
     */
    public boolean deleteDtbUserBookByUserBookIds(List<Long> userBookIds);

    /**
     * 兑换购书码状态变更
     *
     * @param dtbUserBook DUTP-DTB_014学生/教师书架
     * @return 结果
     */
    public boolean editStatus(DtbUserBook dtbUserBook);

    AjaxResult getUserBookId(Long bookId);

    AjaxResult userStartRead(Long bookId, Long chapterId,int isMobile);

    AjaxResult checkBookCopyText(String copyText);
}

package cn.dutp.book.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 心里健康量表测试结果实体
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Data
public class MoocPsychologyHealthScaleResultVo {

    /**
     * 测试结果ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long resultId;

    /**
     * 用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 教材ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 章节ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}

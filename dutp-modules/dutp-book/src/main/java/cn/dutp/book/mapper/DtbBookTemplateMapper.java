package cn.dutp.book.mapper;

import cn.dutp.book.domain.DtbBookTemplate;
import cn.dutp.book.domain.vo.DtbBookTemplateVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

/**
 * DUTP-DTB-029教材模板Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-17
 */
@Repository
public interface DtbBookTemplateMapper extends BaseMapper<DtbBookTemplate> {

    DtbBookTemplateVO selectDtbBookTemplateByChapterId(@Param("chapterId") Long chapterId);

    @Select("SELECT template_id FROM dtb_book_template WHERE is_default = 1 and del_flag = 0 and status = 1 LIMIT 1")
    Long queryDefaultTemplateId();

    @Update("UPDATE dtb_book_template SET is_default = 0 WHERE del_flag = 0")
    void cancelDefault();

    @Update("UPDATE dtb_book_template SET is_default = 1 WHERE template_id = #{templateId} and del_flag = 0")
    int setDefault(DtbBookTemplate dtbBookTemplate);
}

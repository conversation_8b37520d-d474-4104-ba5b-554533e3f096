package cn.dutp.book.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.book.domain.DtbUserResource;
import cn.dutp.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.book.domain.DtbUserResourceFolder;
import cn.dutp.book.domain.vo.ResourceVO;
import cn.dutp.book.service.IDtbUserResourceFolderService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 个人资源文件夹Controller
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
@RestController
@RequestMapping("/userResourceFolder")
public class DtbUserResourceFolderController extends BaseController
{
    @Autowired
    private IDtbUserResourceFolderService dtbUserResourceFolderService;

    /**
     * 查询个人资源文件夹列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbUserResourceFolder dtbUserResourceFolder)
    {
        startPage();
        //注入用户id
        dtbUserResourceFolder.setUserId(SecurityUtils.getUserId());
        List<DtbUserResourceFolder> list = dtbUserResourceFolderService.selectDtbUserResourceFolderList(dtbUserResourceFolder);
        return getDataTable(list);
    }


     /**
     * 查询个人资源列表（包含文件夹和文件）
     */
    @GetMapping("/resourceList")
    public TableDataInfo getResourceList(DtbUserResource dtbUserResourceFolder){

            startPage();
        Long userId = SecurityUtils.getUserId();
        dtbUserResourceFolder.setUserId(userId);
        List<ResourceVO> list = dtbUserResourceFolderService.selectCombinedResources(dtbUserResourceFolder);
        return getDataTable(list);
    }

    /**
     * 查询个人资源文件夹列表(不分页)
     */
    @GetMapping("/listAll") 
    public TableDataInfo listAll(DtbUserResourceFolder dtbUserResourceFolder)
    {
        dtbUserResourceFolder.setUserId(SecurityUtils.getUserId());
        List<DtbUserResourceFolder> list = dtbUserResourceFolderService.selectDtbUserResourceFolderList(dtbUserResourceFolder);
        return getDataTable(list);
    }


    /**
     * 导出个人资源文件夹列表
     */
    @RequiresPermissions("book:userResourceFolder:export")
    @Log(title = "个人资源文件夹", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbUserResourceFolder dtbUserResourceFolder)
    {
        dtbUserResourceFolder.setUserId(SecurityUtils.getUserId());
        List<DtbUserResourceFolder> list = dtbUserResourceFolderService.selectDtbUserResourceFolderList(dtbUserResourceFolder);
        ExcelUtil<DtbUserResourceFolder> util = new ExcelUtil<DtbUserResourceFolder>(DtbUserResourceFolder.class);
        util.exportExcel(response, list, "个人资源文件夹数据");
    }

    /**
     * 获取个人资源文件夹详细信息
     */
    @RequiresPermissions("book:userResourceFolder:query")
    @GetMapping(value = "/{userFolderId}")
    public AjaxResult getInfo(@PathVariable("userFolderId") Long userFolderId)
    {
        return success(dtbUserResourceFolderService.selectDtbUserResourceFolderByUserFolderId(userFolderId));
    }

    /**
     * 新增个人资源文件夹
     */
    @RequiresPermissions("book:userResourceFolder:add")
    @Log(title = "新增个人资源文件夹", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbUserResourceFolder dtbUserResourceFolder)
    {
        dtbUserResourceFolder.setUserId(SecurityUtils.getUserId());
        return toAjax(dtbUserResourceFolderService.insertDtbUserResourceFolder(dtbUserResourceFolder));
    }

    /**
     * 修改个人资源文件夹
     */
    @RequiresPermissions("book:userResourceFolder:edit")
    @Log(title = "修改个人资源文件夹", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbUserResourceFolder dtbUserResourceFolder)
    {
        dtbUserResourceFolder.setUserId(SecurityUtils.getUserId());
        return toAjax(dtbUserResourceFolderService.updateDtbUserResourceFolder(dtbUserResourceFolder));
    }

    /**
     * 删除个人资源文件夹
     */
    @RequiresPermissions("book:userResourceFolder:remove")
    @Log(title = "删除个人资源文件夹", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userFolderIds}")
    public AjaxResult remove(@PathVariable Long[] userFolderIds)
    {
        return toAjax(dtbUserResourceFolderService.deleteDtbUserResourceFolderByUserFolderIds(Arrays.asList(userFolderIds)));
    }
}

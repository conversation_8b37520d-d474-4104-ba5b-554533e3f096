package cn.dutp.test.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * 学校管理对象 dutp_school
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
@Data
@TableName("dutp_school")
public class DutpSchool extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 学校ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long schoolId;

    /** 学校名称 */
    @Excel(name = "学校名称")
    private String schoolName;

    /** 学校代码 */
            @Excel(name = "学校代码")
    private String schoolCode;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("schoolId", getSchoolId())
            .append("schoolName", getSchoolName())
            .append("schoolCode", getSchoolCode())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
        .toString();
        }
        }

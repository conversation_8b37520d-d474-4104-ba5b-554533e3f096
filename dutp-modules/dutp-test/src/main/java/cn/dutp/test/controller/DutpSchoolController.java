package cn.dutp.test.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.test.domain.DutpSchool;
import cn.dutp.test.service.IDutpSchoolService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 学校管理Controller
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
@RestController
@RequestMapping("/school")
public class DutpSchoolController extends BaseController
{
    @Autowired
    private IDutpSchoolService dutpSchoolService;

/**
 * 查询学校管理列表
 */
@RequiresPermissions("@ss.hasPermi('test:school:list')")
@GetMapping("/list")
    public TableDataInfo list(DutpSchool dutpSchool)
    {
        startPage();
        List<DutpSchool> list = dutpSchoolService.selectDutpSchoolList(dutpSchool);
        return getDataTable(list);
    }

    /**
     * 导出学校管理列表
     */
    @RequiresPermissions("@ss.hasPermi('test:school:export')")
    @Log(title = "学校管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DutpSchool dutpSchool)
    {
        List<DutpSchool> list = dutpSchoolService.selectDutpSchoolList(dutpSchool);
        ExcelUtil<DutpSchool> util = new ExcelUtil<DutpSchool>(DutpSchool.class);
        util.exportExcel(response, list, "学校管理数据");
    }

    /**
     * 获取学校管理详细信息
     */
    @RequiresPermissions("@ss.hasPermi('test:school:query')")
    @GetMapping(value = "/{schoolId}")
    public AjaxResult getInfo(@PathVariable("schoolId") Long schoolId)
    {
        return success(dutpSchoolService.selectDutpSchoolBySchoolId(schoolId));
    }

    /**
     * 新增学校管理
     */
    @RequiresPermissions("@ss.hasPermi('test:school:add')")
    @Log(title = "学校管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DutpSchool dutpSchool)
    {
        return toAjax(dutpSchoolService.insertDutpSchool(dutpSchool));
    }

    /**
     * 修改学校管理
     */
    @RequiresPermissions("@ss.hasPermi('test:school:edit')")
    @Log(title = "学校管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DutpSchool dutpSchool)
    {
        return toAjax(dutpSchoolService.updateDutpSchool(dutpSchool));
    }

    /**
     * 删除学校管理
     */
    @RequiresPermissions("@ss.hasPermi('test:school:remove')")
    @Log(title = "学校管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{schoolIds}")
    public AjaxResult remove(@PathVariable Long[] schoolIds)
    {
        return toAjax(dutpSchoolService.deleteDutpSchoolBySchoolIds(Arrays.asList(schoolIds)));
    }
}

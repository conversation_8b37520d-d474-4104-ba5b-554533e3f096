package cn.dutp.test;

import cn.dutp.common.security.annotation.EnableCustomConfig;
import cn.dutp.common.security.annotation.EnableDutpFeignClients;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 系统模块
 * 
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableDutpFeignClients
@SpringBootApplication
public class DutpTestApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(DutpTestApplication.class, args);
        System.out.println("测试模块启动成功");
    }
}

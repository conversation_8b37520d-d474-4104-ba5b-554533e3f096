package cn.dutp.test.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.test.mapper.DutpSchoolMapper;
import cn.dutp.test.domain.DutpSchool;
import cn.dutp.test.service.IDutpSchoolService;

/**
 * 学校管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
@Service
public class DutpSchoolServiceImpl extends ServiceImpl<DutpSchoolMapper, DutpSchool> implements IDutpSchoolService
{
    @Autowired
    private DutpSchoolMapper dutpSchoolMapper;

    /**
     * 查询学校管理
     *
     * @param schoolId 学校管理主键
     * @return 学校管理
     */
    @Override
    public DutpSchool selectDutpSchoolBySchoolId(Long schoolId)
    {
        return this.getById(schoolId);
    }

    /**
     * 查询学校管理列表
     *
     * @param dutpSchool 学校管理
     * @return 学校管理
     */
    @Override
    public List<DutpSchool> selectDutpSchoolList(DutpSchool dutpSchool)
    {
        LambdaQueryWrapper<DutpSchool> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dutpSchool.getSchoolName())) {
                lambdaQueryWrapper.like(DutpSchool::getSchoolName
                ,dutpSchool.getSchoolName());
            }
                if(ObjectUtil.isNotEmpty(dutpSchool.getSchoolCode())) {
                lambdaQueryWrapper.eq(DutpSchool::getSchoolCode
                ,dutpSchool.getSchoolCode());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增学校管理
     *
     * @param dutpSchool 学校管理
     * @return 结果
     */
    @Override
    public boolean insertDutpSchool(DutpSchool dutpSchool)
    {
        return this.save(dutpSchool);
    }

    /**
     * 修改学校管理
     *
     * @param dutpSchool 学校管理
     * @return 结果
     */
    @Override
    public boolean updateDutpSchool(DutpSchool dutpSchool)
    {
        return this.updateById(dutpSchool);
    }

    /**
     * 批量删除学校管理
     *
     * @param schoolIds 需要删除的学校管理主键
     * @return 结果
     */
    @Override
    public boolean deleteDutpSchoolBySchoolIds(List<Long> schoolIds)
    {
        return this.removeByIds(schoolIds);
    }

}

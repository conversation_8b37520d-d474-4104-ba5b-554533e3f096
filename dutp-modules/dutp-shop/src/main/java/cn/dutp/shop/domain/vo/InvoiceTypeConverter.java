package cn.dutp.shop.domain.vo;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

public class InvoiceTypeConverter implements Converter<Integer> {
    @Override
    public Class<?> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        if (value == null) {
            return new WriteCellData<>("");
        }
        switch (value) {
            case 1:
                return new WriteCellData<>("增值税普通发票");
            case 2:
                return new WriteCellData<>("增值税专用发票");
            default:
                return new WriteCellData<>("");
        }
    }
}

package cn.dutp.shop.service;

import cn.dutp.shop.domain.DtbBookStatement;
import cn.dutp.shop.domain.DtbUserInvoiceApply;
import cn.dutp.shop.domain.dto.ReconcileLookDto;
import cn.dutp.shop.domain.dto.ReconciliationDto;
import cn.dutp.shop.domain.dto.StatementItemDto;
import cn.dutp.shop.domain.vo.DtbBookOrderItemVo;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 结算单Service接口
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface IDtbBookStatementService extends IService<DtbBookStatement> {
    /**
     * 查询结算单
     *
     * @param statementId 结算单主键
     * @return 结算单
     */
    public DtbBookStatement selectDtbBookStatementByStatementId(Long statementId);

    /**
     * 查询结算单列表
     *
     * @param dtbBookStatement 结算单
     * @return 结算单集合
     */
    public List<DtbBookStatement> selectDtbBookStatementList(DtbBookStatement dtbBookStatement);

    /**
     * 新增结算单
     *
     * @param dtbBookStatement 结算单
     * @return 结果
     */
    public boolean insertDtbBookStatement(DtbBookStatement dtbBookStatement);

    /**
     * 修改结算单
     *
     * @param dtbBookStatement 结算单
     * @return 结果
     */
    public boolean updateDtbBookStatement(DtbBookStatement dtbBookStatement);

    /**
     * 批量删除结算单
     *
     * @param statementId 需要删除的结算单主键集合
     * @return 结果
     */
    public boolean deleteDtbBookStatementByStatementIds(Long statementId);

    boolean changeStatementStatus(Long statementId);

    List<ReconciliationDto> reconcileList(ReconciliationDto reconciliationDto);

    List<ReconcileLookDto> getReconcileListById(Long orderId);

    List<DtbBookOrderItemVo> selectOrderItemByOrderId(Long orderId);

    void exportStatement(HttpServletResponse response, DtbBookStatement dtbBookStatement);

    void exportReconcile(HttpServletResponse response, DtbBookStatement dtbBookStatement);

    List<StatementItemDto> selectStatementItem(Long statementId);

    List<StatementItemDto> selectOrderItem(Long orderId);

    /**
     * 重新开票
     * @param statementId
     * @return 结果
     */
    public boolean changeInvoice(Long statementId);
}

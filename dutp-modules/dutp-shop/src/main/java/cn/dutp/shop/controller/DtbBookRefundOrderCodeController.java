package cn.dutp.shop.controller;

import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.shop.domain.DtbBookRefundOrderCode;
import cn.dutp.shop.service.IDtbBookRefundOrderCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 退款的二维码Controller
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@RestController
@RequestMapping("/code")
public class DtbBookRefundOrderCodeController extends BaseController
{
    @Autowired
    private IDtbBookRefundOrderCodeService dtbBookRefundOrderCodeService;

    /**
     * 查询退款的二维码列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbBookRefundOrderCode dtbBookRefundOrderCode)
    {
        startPage();
        List<DtbBookRefundOrderCode> list = dtbBookRefundOrderCodeService.selectDtbBookRefundOrderCodeList(dtbBookRefundOrderCode);
        return getDataTable(list);
    }

    /**
     * 导出退款的二维码列表
     */
    @RequiresPermissions("system:code:export")
    @Log(title = "导出退款的二维码", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbBookRefundOrderCode dtbBookRefundOrderCode)
    {
        List<DtbBookRefundOrderCode> list = dtbBookRefundOrderCodeService.selectDtbBookRefundOrderCodeList(dtbBookRefundOrderCode);
        ExcelUtil<DtbBookRefundOrderCode> util = new ExcelUtil<DtbBookRefundOrderCode>(DtbBookRefundOrderCode.class);
        util.exportExcel(response, list, "退款的二维码数据");
    }

    /**
     * 获取退款的二维码详细信息
     */
    @RequiresPermissions("system:code:query")
    @GetMapping(value = "/{refundCodeId}")
    public AjaxResult getInfo(@PathVariable("refundCodeId") Long refundCodeId)
    {
        return success(dtbBookRefundOrderCodeService.selectDtbBookRefundOrderCodeByRefundCodeId(refundCodeId));
    }

    /**
     * 新增退款的二维码
     */
    @RequiresPermissions("system:code:add")
    @Log(title = "新增退款的二维码", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookRefundOrderCode dtbBookRefundOrderCode)
    {
        return toAjax(dtbBookRefundOrderCodeService.insertDtbBookRefundOrderCode(dtbBookRefundOrderCode));
    }

    /**
     * 修改退款的二维码
     */
    @RequiresPermissions("system:code:edit")
    @Log(title = "修改退款的二维码", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookRefundOrderCode dtbBookRefundOrderCode)
    {
        return toAjax(dtbBookRefundOrderCodeService.updateDtbBookRefundOrderCode(dtbBookRefundOrderCode));
    }

    /**
     * 删除退款的二维码
     */
    @RequiresPermissions("system:code:remove")
    @Log(title = "删除退款的二维码", businessType = BusinessType.DELETE)
    @DeleteMapping("/{refundCodeIds}")
    public AjaxResult remove(@PathVariable Long[] refundCodeIds)
    {
        return toAjax(dtbBookRefundOrderCodeService.deleteDtbBookRefundOrderCodeByRefundCodeIds(Arrays.asList(refundCodeIds)));
    }
}

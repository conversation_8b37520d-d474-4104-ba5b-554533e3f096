package cn.dutp.shop.service.impl;

import cn.dutp.domain.DtbBookCodeExportItem;
import cn.dutp.shop.mapper.IDtbBookCodeExportItemMapper;
import cn.dutp.shop.service.IDtbBookCodeExportItemService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【dtb_book_code_export_item(导出购书码明细)】的数据库操作Service实现
* @createDate 2025-02-21 10:35:37
*/
@Service
public class IDtbBookCodeExportItemServiceImpl extends ServiceImpl<IDtbBookCodeExportItemMapper, DtbBookCodeExportItem>
    implements IDtbBookCodeExportItemService {

    @Override
    public boolean addExportItem(DtbBookCodeExportItem exportItem) {
        return this.save(exportItem);
    }
}





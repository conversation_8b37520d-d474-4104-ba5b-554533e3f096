package cn.dutp.shop.controller;

import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.domain.DtbBookPurchaseCode;
import cn.dutp.shop.service.IDtbBookPurchaseCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 购书码发行管理Controller
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@RestController
@RequestMapping("/purchaseCode")
public class DtbBookPurchaseCodeController extends BaseController
{
    @Autowired
    private IDtbBookPurchaseCodeService dtbBookPurchaseCodeService;

    /**
     * 查询购书码发行管理列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbBookPurchaseCode dtbBookPurchaseCode)
    {
        startPage();
        List<DtbBookPurchaseCode> list = dtbBookPurchaseCodeService.selectDtbBookPurchaseCodeList(dtbBookPurchaseCode);
        return getDataTable(list);
    }

    /**
     * 学生教师端查询用户购书码发行管理列表
     */
    @GetMapping("/listEducation")
    public TableDataInfo listEducation(DtbBookPurchaseCode dtbBookPurchaseCode)
    {
        startPage();
        List<DtbBookPurchaseCode> list = dtbBookPurchaseCodeService.listEducation(dtbBookPurchaseCode);
        return getDataTable(list);
    }

    /**
     * 导出购书码发行管理列表
     */
    @RequiresPermissions("shop:purchaseCode:export")
    @Log(title = "购书码发行管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbBookPurchaseCode dtbBookPurchaseCode)
    {
        List<DtbBookPurchaseCode> list = dtbBookPurchaseCodeService.selectDtbBookPurchaseCodeList(dtbBookPurchaseCode);
        ExcelUtil<DtbBookPurchaseCode> util = new ExcelUtil<DtbBookPurchaseCode>(DtbBookPurchaseCode.class);
        util.exportExcel(response, list, "购书码发行管理数据");
    }

    /**
     * 教师学生端获取购书码发行管理详细信息
     */
    @GetMapping(value = "/getInfoEducation")
    public AjaxResult getInfoEducation(DtbBookPurchaseCode dtbBookPurchaseCode)
    {
        return success(dtbBookPurchaseCodeService.getInfoEducation(dtbBookPurchaseCode));
    }

    /**
     * 获取购书码发行管理详细信息
     */
    @RequiresPermissions("shop:purchaseCode:query")
    @GetMapping(value = "/{codeId}")
    public AjaxResult getInfo(@PathVariable("codeId") Long codeId)
    {
        return success(dtbBookPurchaseCodeService.selectDtbBookPurchaseCodeByCodeId(codeId));
    }

    /**
     * 新增购书码发行管理
     */
    @RequiresPermissions("shop:purchaseCode:add")
    @Log(title = "购书码发行管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookPurchaseCode dtbBookPurchaseCode)
    {
        return toAjax(dtbBookPurchaseCodeService.insertDtbBookPurchaseCode(dtbBookPurchaseCode));
    }

    /**
     * 修改购书码发行管理
     */
    @RequiresPermissions("shop:purchaseCode:edit")
    @Log(title = "购书码发行管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookPurchaseCode dtbBookPurchaseCode)
    {
        return toAjax(dtbBookPurchaseCodeService.updateDtbBookPurchaseCode(dtbBookPurchaseCode));
    }

    /**
     * 删除购书码发行管理
     */
    @RequiresPermissions("shop:purchaseCode:remove")
    @Log(title = "购书码发行管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{codeIds}")
    public AjaxResult remove(@PathVariable Long[] codeIds)
    {
        return toAjax(dtbBookPurchaseCodeService.deleteDtbBookPurchaseCodeByCodeIds(Arrays.asList(codeIds)));
    }

    /**
     * 教师学生端购书码兑换
     */
    @Log(title = "教师学生端购书码兑换", businessType = BusinessType.UPDATE)
    @PutMapping("/bookCodeExchange")
    public AjaxResult bookCodeExchange(@RequestBody DtbBookPurchaseCode dtbBookPurchaseCode) throws Exception {
        return toAjax(dtbBookPurchaseCodeService.bookCodeExchange(dtbBookPurchaseCode));
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.shop.mapper.DtbUserInvoiceApplyMapper">

    <resultMap type="DtbUserInvoiceApply" id="DtbUserInvoiceApplyResult">
        <result property="applyId"    column="apply_id"    />
        <result property="titleId"    column="title_id"    />
        <result property="orderId"    column="order_id"    />
        <result property="applyStatus"    column="apply_status"    />
        <result property="applyType"    column="apply_type"    />
        <result property="uploadTime"    column="upload_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDtbUserInvoiceApplyVo">
        select apply_id, title_id, order_id, apply_status, apply_type, upload_time, create_by, create_time, update_by, update_time from dtb_user_invoice_apply
    </sql>

    <select id="selectDtbUserInvoiceApplyList" parameterType="DtbUserInvoiceApply" resultMap="DtbUserInvoiceApplyResult">
        <include refid="selectDtbUserInvoiceApplyVo"/>
        <where>
            <if test="titleId != null "> and title_id = #{titleId}</if>
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="applyStatus != null "> and apply_status = #{applyStatus}</if>
            <if test="applyType != null "> and apply_type = #{applyType}</if>
            <if test="uploadTime != null "> and upload_time = #{uploadTime}</if>
        </where>
    </select>

    <select id="selectDtbUserInvoiceApplyByApplyId" parameterType="Long" resultMap="DtbUserInvoiceApplyResult">
        <include refid="selectDtbUserInvoiceApplyVo"/>
        where apply_id = #{applyId}
    </select>

    <insert id="insertDtbUserInvoiceApply" parameterType="DtbUserInvoiceApply" useGeneratedKeys="true" keyProperty="applyId">
        insert into dtb_user_invoice_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="titleId != null">title_id,</if>
            <if test="orderId != null">order_id,</if>
            <if test="applyStatus != null">apply_status,</if>
            <if test="applyType != null">apply_type,</if>
            <if test="uploadTime != null">upload_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="titleId != null">#{titleId},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="applyStatus != null">#{applyStatus},</if>
            <if test="applyType != null">#{applyType},</if>
            <if test="uploadTime != null">#{uploadTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateDtbUserInvoiceApply" parameterType="DtbUserInvoiceApply">
        update dtb_user_invoice_apply
        <trim prefix="SET" suffixOverrides=",">
            <if test="titleId != null">title_id = #{titleId},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="applyStatus != null">apply_status = #{applyStatus},</if>
            <if test="applyType != null">apply_type = #{applyType},</if>
            <if test="uploadTime != null">upload_time = #{uploadTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where apply_id = #{applyId}
    </update>

    <delete id="deleteDtbUserInvoiceApplyByApplyId" parameterType="Long">
        delete from dtb_user_invoice_apply where apply_id = #{applyId}
    </delete>

    <delete id="deleteDtbUserInvoiceApplyByApplyIds" parameterType="String">
        delete from dtb_user_invoice_apply where apply_id in
        <foreach item="applyId" collection="array" open="(" separator="," close=")">
            #{applyId}
        </foreach>
    </delete>
    <select id="getInfoEducation" resultType="DtbUserInvoiceApplyVo">
        SELECT
        duia.apply_id,
        duia.title_id,
        duia.order_id,
        duia.statement_id,
        duia.apply_status,
        duia.deal_user_id,
        duia.deal_time,
        duia.order_type,
        duia.apply_type,
        duia.invoice_amount,
        duia.change_count,
        duia.user_email,
        duia.upload_time,
        duia.invoice_remark,
        duit.invoice_type,
        duit.title_type,
        duit.title_name,
        duit.tax_no,
        duit.regist_address,
        duit.regist_tel,
        duit.account_bank,
        duit.account_no,
        duit.user_id,
        duia.create_time
        FROM
        dtb_user_invoice_apply AS duia
        LEFT JOIN
        dtb_user_invoice_title AS duit
        ON
        duia.title_id = duit.title_id
        <where>
            <if test="applyId != null "> and duia.apply_id = #{applyId}</if>
            <if test="titleId != null "> and duia.title_id = #{titleId}</if>
            <if test="orderId != null "> and duia.order_id = #{orderId}</if>
            <if test="applyStatus != null "> and duia.apply_status = #{applyStatus}</if>
            <if test="applyType != null "> and duia.apply_type = #{applyType}</if>
            <if test="uploadTime != null "> and duia.upload_time = #{uploadTime}</if>
        </where>
        <!-- 根据实际需求添加更多条件 -->
    </select>
</mapper>
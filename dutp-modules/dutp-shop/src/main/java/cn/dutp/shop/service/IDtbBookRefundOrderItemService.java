package cn.dutp.shop.service;

import java.util.List;

import cn.dutp.domain.DtbBookRefundOrderItem;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 退款明细Service接口
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
public interface IDtbBookRefundOrderItemService extends IService<DtbBookRefundOrderItem>
{
    /**
     * 查询退款明细
     *
     * @param refundItemId 退款明细主键
     * @return 退款明细
     */
    public DtbBookRefundOrderItem selectDtbBookRefundOrderItemByRefundItemId(Long refundItemId);

    /**
     * 查询退款明细列表
     *
     * @param dtbBookRefundOrderItem 退款明细
     * @return 退款明细集合
     */
    public List<DtbBookRefundOrderItem> selectDtbBookRefundOrderItemList(DtbBookRefundOrderItem dtbBookRefundOrderItem);

    /**
     * 新增退款明细
     *
     * @param dtbBookRefundOrderItem 退款明细
     * @return 结果
     */
    public boolean insertDtbBookRefundOrderItem(DtbBookRefundOrderItem dtbBookRefundOrderItem);

    /**
     * 修改退款明细
     *
     * @param dtbBookRefundOrderItem 退款明细
     * @return 结果
     */
    public boolean updateDtbBookRefundOrderItem(DtbBookRefundOrderItem dtbBookRefundOrderItem);

    /**
     * 批量删除退款明细
     *
     * @param refundItemIds 需要删除的退款明细主键集合
     * @return 结果
     */
    public boolean deleteDtbBookRefundOrderItemByRefundItemIds(List<Long> refundItemIds);

}

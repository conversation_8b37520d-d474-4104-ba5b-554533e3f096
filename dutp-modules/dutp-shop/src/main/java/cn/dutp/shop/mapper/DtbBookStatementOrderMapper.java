package cn.dutp.shop.mapper;

import cn.dutp.domain.DtbBookOrder;
import cn.dutp.shop.domain.DtbBookStatementOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 结算单的订单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Repository
public interface DtbBookStatementOrderMapper extends BaseMapper<DtbBookStatementOrder>
{

    @Select("\n" +
            "SELECT\n" +
            "    bso.statement_id, \n" +
            "\t\tbso.order_id, \n" +
            "    bs.statement_no,\n" +
            "\t\tbo.order_no,\n" +
            "    ROUND(SUM(boi.book_quantity * boi.price_sale * (boi.discount / 100)), 2) AS shouldPay,\n" +
            "    SUM(boi.book_quantity * boi.price_sale) AS total\n" +
            "FROM\n" +
            "    dtb_book_statement_order AS bso\n" +
            "    LEFT JOIN dtb_book_order AS bo ON bo.order_id = bso.order_id\n" +
            "    LEFT JOIN dtb_book_order_item AS boi ON boi.order_id = bo.order_id\n" +
            "    LEFT JOIN dtb_book_statement AS bs ON bs.statement_id = bso.statement_id\n" +
            "WHERE\n" +
            "    bs.statement_id = #{statementId}\n" +
            "GROUP BY\n" +
            "    bso.statement_id, bso.order_id,bo.order_no,bs.statement_no;")
    List<DtbBookStatementOrder> selectByStatementId(Long statementId);

    @Delete("DELETE FROM dtb_book_statement_order WHERE statement_id = #{statementId}")
    void deleteStatementOrderById(Long statementId);


}

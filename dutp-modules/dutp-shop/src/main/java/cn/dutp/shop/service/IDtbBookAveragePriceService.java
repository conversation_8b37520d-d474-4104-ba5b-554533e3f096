package cn.dutp.shop.service;

import java.util.List;

import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.shop.domain.DtbBookAveragePrice;
import cn.dutp.shop.domain.dto.DtbBookPriceOrderVo;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 核定计算Service接口
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
public interface IDtbBookAveragePriceService extends IService<DtbBookAveragePrice>
{
    /**
     * 查询核定计算
     *
     * @param priceId 核定计算主键
     * @return 核定计算
     */
    public DtbBookAveragePrice selectDtbBookAveragePriceByPriceId(Long priceId);

    /**
     * 查询核定计算列表
     *
     * @param dtbBookAveragePrice 核定计算
     * @return 核定计算集合
     */
    public List<DtbBookAveragePrice> selectDtbBookAveragePriceList(DtbBookAveragePrice dtbBookAveragePrice);

    /**
     * 新增核定计算
     *
     * @param dtbBookAveragePrice 核定计算
     * @return 结果
     */
    public boolean insertDtbBookAveragePrice(DtbBookAveragePrice dtbBookAveragePrice);

    /**
     * 修改核定计算
     *
     * @param dtbBookAveragePrice 核定计算
     * @return 结果
     */
    public boolean updateDtbBookAveragePrice(DtbBookAveragePrice dtbBookAveragePrice);

    /**
     * 批量删除核定计算
     *
     * @param priceIds 需要删除的核定计算主键集合
     * @return 结果
     */
    public boolean deleteDtbBookAveragePriceByPriceIds(List<Long> priceIds);

    AjaxResult calcDtbBookAveragePrice(DtbBookAveragePrice dtbBookAveragePrice);

    List<DtbBookPriceOrderVo> selectDtbBookAveragePriceOrderList(DtbBookAveragePrice dtbBookAveragePrice);

    DtbBookPriceOrderVo selectDtbBookAveragePriceOrderData(DtbBookAveragePrice dtbBookAveragePrice);
}

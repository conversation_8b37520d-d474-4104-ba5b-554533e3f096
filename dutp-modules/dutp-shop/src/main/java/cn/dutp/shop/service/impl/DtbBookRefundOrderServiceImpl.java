package cn.dutp.shop.service.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.dutp.api.common.constant.DutpConstant;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBookOrder;
import cn.dutp.domain.DtbBookPurchaseCode;
import cn.dutp.domain.DtbBookRefundOrder;
import cn.dutp.domain.DtbBookRefundOrderItem;
import cn.dutp.message.api.RemoteUserMessageService;
import cn.dutp.message.api.domain.DutpUserMessage;
import cn.dutp.shop.domain.DtbBookOrderCode;
import cn.dutp.shop.domain.DtbBookRefundOrderCode;
import cn.dutp.shop.domain.dto.BookRefundOrderVO;
import cn.dutp.shop.domain.dto.RefundOrderItemVO;
import cn.dutp.shop.mapper.DtbBookOrderMapper;
import cn.dutp.shop.mapper.DtbBookRefundOrderCodeMapper;
import cn.dutp.shop.mapper.DtbBookRefundOrderItemMapper;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.shop.mapper.DtbBookRefundOrderMapper;
import cn.dutp.shop.service.IDtbBookRefundOrderService;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import cn.dutp.shop.mapper.DtbBookOrderCodeMapper;
import cn.dutp.shop.mapper.DtbBookPurchaseCodeMapper;

/**
 * 售后退款订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@Service
public class DtbBookRefundOrderServiceImpl extends ServiceImpl<DtbBookRefundOrderMapper, DtbBookRefundOrder> implements IDtbBookRefundOrderService
{
    private static final Logger log = LoggerFactory.getLogger(DtbBookRefundOrderServiceImpl.class);
    private static final Integer REFUND_SUCCESS = 1;

    @Autowired
    private DtbBookRefundOrderMapper dtbBookRefundOrderMapper;
    @Autowired
    private DtbBookRefundOrderItemMapper dtbBookRefundOrderItemMapper;
    @Autowired
    private DtbBookRefundOrderCodeMapper dtbBookRefundOrderCodeMapper;
    @Autowired
    private DtbBookOrderMapper dtbBookOrderMapper;
    
    @Autowired
    private DtbBookOrderCodeMapper dtbBookOrderCodeMapper;
    @Autowired
    private DtbBookPurchaseCodeMapper dtbBookPurchaseCodeMapper;

    @Autowired
    private RemoteUserMessageService remoteUserMessageService;


    /**
     * 查询售后退款订单
     *
     * @param refundOrderId 售后退款订单主键
     * @return 售后退款订单
     */
    @Override
    public DtbBookRefundOrder selectDtbBookRefundOrderByRefundOrderId(Long refundOrderId)
    {
        return this.getById(refundOrderId);
    }

    /**
     * 查询售后退款订单列表
     *
     * @param dtbBookRefundOrder 售后退款订单
     * @return 售后退款订单
     */
    @Override
    public List<DtbBookRefundOrder> selectDtbBookRefundOrderList(DtbBookRefundOrder dtbBookRefundOrder)
    {
        LambdaQueryWrapper<DtbBookRefundOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        
        // 修改orderNo查询条件为exists子查询，保持分页功能
        if(ObjectUtil.isNotEmpty(dtbBookRefundOrder.getOrderNo())) {
            lambdaQueryWrapper.exists(
                "SELECT 1 FROM dtb_book_order o WHERE o.order_id = dtb_book_refund_order.order_id AND o.order_no = '" + dtbBookRefundOrder.getOrderNo() + "'"
            );
        }
        
        if(ObjectUtil.isNotEmpty(dtbBookRefundOrder.getOrderId())) {
            lambdaQueryWrapper.eq(DtbBookRefundOrder::getOrderId, dtbBookRefundOrder.getOrderId());
        }
        if(ObjectUtil.isNotEmpty(dtbBookRefundOrder.getRefundAmount())) {
            lambdaQueryWrapper.eq(DtbBookRefundOrder::getRefundAmount, dtbBookRefundOrder.getRefundAmount());
        }
        if(ObjectUtil.isNotEmpty(dtbBookRefundOrder.getRefundStatus())) {
            lambdaQueryWrapper.eq(DtbBookRefundOrder::getRefundStatus, dtbBookRefundOrder.getRefundStatus());
        }
        if(ObjectUtil.isNotEmpty(dtbBookRefundOrder.getAuditTime())) {
            lambdaQueryWrapper.eq(DtbBookRefundOrder::getAuditTime, dtbBookRefundOrder.getAuditTime());
        }
        if(ObjectUtil.isNotEmpty(dtbBookRefundOrder.getSchoolId())) {
            lambdaQueryWrapper.eq(DtbBookRefundOrder::getSchoolId, dtbBookRefundOrder.getSchoolId());
        }
        if(ObjectUtil.isNotEmpty(dtbBookRefundOrder.getRefundOrderNo())) {
            lambdaQueryWrapper.eq(DtbBookRefundOrder::getRefundOrderNo, dtbBookRefundOrder.getRefundOrderNo());
        }
        if(ObjectUtil.isNotEmpty(dtbBookRefundOrder.getAuditRemark())) {
            lambdaQueryWrapper.eq(DtbBookRefundOrder::getAuditRemark, dtbBookRefundOrder.getAuditRemark());
        }
        if(ObjectUtil.isNotEmpty(dtbBookRefundOrder.getAuditUserId())) {
            lambdaQueryWrapper.eq(DtbBookRefundOrder::getAuditUserId, dtbBookRefundOrder.getAuditUserId());
        }
        return this.list(lambdaQueryWrapper);
    }


    /**
     * 新增售后退款订单
     *
     * @param dtbBookRefundOrder 售后退款订单
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertDtbBookRefundOrder(DtbBookRefundOrder dtbBookRefundOrder)
    {
        boolean isResult = false;
        LambdaQueryWrapper<DtbBookRefundOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(ObjectUtil.isNotEmpty(dtbBookRefundOrder.getOrderId())) {
            lambdaQueryWrapper.eq(DtbBookRefundOrder::getOrderId
                    ,dtbBookRefundOrder.getOrderId());
        }
        if(baseMapper.selectCount(lambdaQueryWrapper) > 0){
            throw new RuntimeException("该订单退货申请已提交");
        };
        // 新增退货订单
        // 备注
        dtbBookRefundOrder.setRemark(DutpConstant.RECALL_TEXTBOOKS);
        // 退单号
        dtbBookRefundOrder.setRefundOrderNo(refundOrderNumberGenerator());
        // 退款金额
        dtbBookRefundOrder.setRefundAmount(dtbBookRefundOrder.getPayAmount());
        // 创建时间
        dtbBookRefundOrder.setCreateTime(new Date());
        isResult = baseMapper.insert(dtbBookRefundOrder) > 0;
        if(!isResult){
            throw new RuntimeException(DutpConstant.ADDITION_FAILED);
        }
        // 新增退货明细
        DtbBookRefundOrderItem dtbBookRefundOrderItem = new DtbBookRefundOrderItem();
        // 教材ID
        dtbBookRefundOrderItem.setBookId(dtbBookRefundOrder.getBookId());
        // 退单ID
        dtbBookRefundOrderItem.setRefundOrderId(dtbBookRefundOrder.getRefundOrderId());
        // 订单明细id
        dtbBookRefundOrderItem.setOrderItemId(dtbBookRefundOrder.getOrderItemId());
        // 退款数量
        dtbBookRefundOrderItem.setRefundQuantity(DutpConstant.LONG_ONE);
        // 退款金额(单价)
        dtbBookRefundOrderItem.setRefundAmount(dtbBookRefundOrder.getRefundAmount());
        isResult = dtbBookRefundOrderItemMapper.insert(dtbBookRefundOrderItem) > 0;
        if(!isResult){
            throw new RuntimeException(DutpConstant.ADDITION_FAILED);
        }
        // 新增退货明细
        DtbBookRefundOrderCode dtbBookRefundOrderCode = new DtbBookRefundOrderCode();
        // 退货明细ID
        dtbBookRefundOrderCode.setRefundItemId(dtbBookRefundOrderItem.getRefundItemId());
        // 退单ID
        dtbBookRefundOrderCode.setRefundOrderId(dtbBookRefundOrder.getRefundOrderId());
        // 购书码Id
        dtbBookRefundOrderCode.setCodeId(dtbBookRefundOrder.getCodeId());
        isResult = dtbBookRefundOrderCodeMapper.insert(dtbBookRefundOrderCode) > 0;
        if(!isResult){
            throw new RuntimeException(DutpConstant.ADDITION_FAILED);
        }
        return isResult;
    }

    /**
     * 修改售后退款订单
     *
     * @param dtbBookRefundOrder 售后退款订单
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDtbBookRefundOrder(DtbBookRefundOrder dtbBookRefundOrder)
    {

        if (Objects.equals(dtbBookRefundOrder.getRefundStatus(), REFUND_SUCCESS)){
            refund(dtbBookRefundOrder);
        }else {
            dtbBookRefundOrder.setUpdateTime(new Date());
            dtbBookRefundOrder.setAuditTime(new Date());
            this.updateById(dtbBookRefundOrder);
        }

        sendMsg(dtbBookRefundOrder);
        return true;
    }

    private void sendMsg(DtbBookRefundOrder dtbBookRefundOrder) {
        DutpUserMessage message = new DutpUserMessage();
        
        // 设置基本信息
        message.setMessageType(1);
        message.setFromUserType(1);
        message.setToUserType(1);
        message.setReadFlag(0);
        message.setBusinessId(dtbBookRefundOrder.getRefundOrderId());
        message.setBookId(dtbBookRefundOrder.getBookId());
        message.setTitle("售后处理提醒");
        

        if (Objects.equals(dtbBookRefundOrder.getApplyResource(), DutpConstant.REFUND_RESOURCE_ADMIN)){

        }else if (Objects.equals(dtbBookRefundOrder.getApplyResource(), DutpConstant.REFUND_RESOURCE_SCHOOL)){

        }else if (Objects.equals(dtbBookRefundOrder.getApplyResource(), DutpConstant.REFUND_RESOURCE_USER)){
            message.setToUserType(2);
        }


        // 设置发送方和接收方
        message.setFromUserId(SecurityUtils.getUserId());
        message.setToUserId(dtbBookRefundOrder.getApplyUserId());

        boolean isRefundSuccess = Objects.equals(dtbBookRefundOrder.getRefundStatus(), REFUND_SUCCESS);
        
        StringBuilder content = new StringBuilder();
        
        if (isRefundSuccess) {
            content.append("尊敬的用户，您的退款售后申请已处理完毕，退款金额 ")
                  .append(dtbBookRefundOrder.getRefundAmount())
                  .append(" 已原路返回至您的支付账户，预计到账时间为7个工作日内，请您注意查收。如有任何疑问，请随时联系我们。感谢您的支持与理解！");
        } else {
            content.append("尊敬的用户，很抱歉，您的退款售后申请未通过审核，驳回原因：")
                  .append(dtbBookRefundOrder.getAuditRemark() != null ? dtbBookRefundOrder.getAuditRemark() : "不符合退款条件")
                  .append("。如果您有异议，请在7天内联系我们的客服进行申诉，我们会重新为您审核。");
        }
        
        message.setContent(content.toString());
        
        // 发送消息
        remoteUserMessageService.addMessage(message);
    }

    /**
     * 批量删除售后退款订单
     *
     * @param refundOrderIds 需要删除的售后退款订单主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookRefundOrderByRefundOrderIds(List<Long> refundOrderIds)
    {
        return this.removeByIds(refundOrderIds);
    }
    public String refundOrderNumberGenerator() {
        Date now = new Date();
        String formattedDate = new SimpleDateFormat(DutpConstant.DATE_FORMAT).format(now);
        LambdaQueryWrapper<DtbBookRefundOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.likeRight(DtbBookRefundOrder::getRefundOrderNo, formattedDate);
        Integer sequenceNumber = baseMapper.selectCount(lambdaQueryWrapper);
        sequenceNumber++;
        return formattedDate + DutpConstant.BUSINESS_CODE + String.format("%06d", sequenceNumber);
    }

    @Override
    public List<BookRefundOrderVO> selectRefundOrderVOList(BookRefundOrderVO dtbBookRefundOrder) {
        List<BookRefundOrderVO> orderVOList = dtbBookRefundOrderMapper.selectRefundOrderVOList(dtbBookRefundOrder);
        for (BookRefundOrderVO orderVO : orderVOList) {
            processRefundOrderItems(orderVO);
        }
        return orderVOList;
    }

    @Override
    public BookRefundOrderVO selectRefundOrderVOByRefundOrderId(Long refundOrderId) {
        BookRefundOrderVO orderVO = dtbBookRefundOrderMapper.selectRefundOrderVOByRefundOrderId(refundOrderId);

        if (orderVO!=null&&orderVO.getOrderNo()!=null){
            LambdaQueryWrapper<DtbBookOrder> wrapper = new LambdaQueryWrapper<>();
            DtbBookOrder dtbBookOrder = dtbBookOrderMapper.selectOne(wrapper.eq(DtbBookOrder::getOrderNo, orderVO.getOrderNo()));
            if (dtbBookOrder!=null){
                orderVO.setOrderTime(dtbBookOrder.getCreateTime());
                orderVO.setOperator(dtbBookOrder.getCreateBy());
            }
        }


        if (orderVO != null && orderVO.getRefundOrderItems() != null) {
            processRefundOrderItems(orderVO);
        }
        return orderVO;
    }

    private void processRefundOrderItems(BookRefundOrderVO orderVO) {
        orderVO.setBookName(joinListValues(orderVO.getRefundOrderItems(), RefundOrderItemVO::getBookName));
        orderVO.setIsbn(joinListValues(orderVO.getRefundOrderItems(), RefundOrderItemVO::getIsbn));
        orderVO.setQuantity(joinListValues(orderVO.getRefundOrderItems(), item -> item.getRefundQuantity() != null ? item.getRefundQuantity().toString() : "-"));
    }
    
    private String joinListValues(List<RefundOrderItemVO> items, Function<RefundOrderItemVO, String> mapper) {
        return items.stream()
                   .map(mapper)
                   .map(value -> value != null ? value : "-")
                   .collect(Collectors.joining(", "));
    }

    /**
     * 处理退款
     *
     * @param dtbBookRefundOrder 退款订单信息
     * @return 退款处理结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean refund(DtbBookRefundOrder dtbBookRefundOrder) {
        DtbBookOrder dtbBookOrder = dtbBookOrderMapper.selectById(dtbBookRefundOrder.getOrderId());
        
        // 基础检查
        if (dtbBookOrder == null) {
            throw new ServiceException("原订单不存在");
        }
        if (dtbBookOrder.getRefundStatus() != null && dtbBookOrder.getRefundStatus() == 1) {
            throw new ServiceException("订单已退款，请勿重复操作");
        }
        
        try {
            // 1. 更新订单退款状态
            LambdaUpdateWrapper<DtbBookOrder> orderWrapper = new LambdaUpdateWrapper<>();
            orderWrapper.eq(DtbBookOrder::getOrderId, dtbBookOrder.getOrderId());
            int refundStatus = dtbBookRefundOrder.getRefundAmount().compareTo(dtbBookOrder.getPayAmount()) == 0 ? 3 : 2;
            orderWrapper.set(DtbBookOrder::getRefundStatus, refundStatus);
            dtbBookOrderMapper.update(null, orderWrapper);

            // 2. 查询需要退款的码列表
            LambdaQueryWrapper<DtbBookOrderCode> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DtbBookOrderCode::getOrderId, dtbBookOrder.getOrderId())
                       .eq(DtbBookOrderCode::getBookId, dtbBookRefundOrder.getBookId());
            List<DtbBookOrderCode> orderCodes = dtbBookOrderCodeMapper.selectList(queryWrapper);

            // 3. 批量处理码的状态更新和记录
            for (DtbBookOrderCode orderCode : orderCodes) {
                // 更新订单码状态
                LambdaUpdateWrapper<DtbBookOrderCode> orderCodeWrapper = new LambdaUpdateWrapper<>();
                orderCodeWrapper.eq(DtbBookOrderCode::getOrderCodeId, orderCode.getOrderCodeId())
                               .set(DtbBookOrderCode::getRefundState, 3);
                dtbBookOrderCodeMapper.update(null, orderCodeWrapper);

                // 更新购书码状态为作废
                LambdaUpdateWrapper<DtbBookPurchaseCode> purchaseCodeWrapper = new LambdaUpdateWrapper<>();
                purchaseCodeWrapper.eq(DtbBookPurchaseCode::getCodeId, orderCode.getCodeId())
                                  .set(DtbBookPurchaseCode::getState, 5);
                // 5表示作废
                dtbBookPurchaseCodeMapper.update(null, purchaseCodeWrapper);

                // 记录退款码信息
                DtbBookRefundOrderCode refundOrderCode = new DtbBookRefundOrderCode();
                refundOrderCode.setRefundOrderId(dtbBookRefundOrder.getRefundOrderId());
                refundOrderCode.setRefundItemId(dtbBookRefundOrder.getOrderItemId());
                // 这里存储的是购书码的code_id
                refundOrderCode.setCodeId(orderCode.getCodeId());
                refundOrderCode.setCreateTime(new Date());
                dtbBookRefundOrderCodeMapper.insert(refundOrderCode);
            }

            // 处理支付渠道退款
            String payMethod = dtbBookOrder.getPaymentMethod();
            if ("支付宝".equals(payMethod) && false) {
                log.warn("支付宝支付功能暂未开通");
                throw new ServiceException("支付宝支付功能暂未开通");
                
                // 注释掉原有的支付宝支付代码
                /*AlipayTradeRefundApplyResponse applyResponse = alipayService.refund(outTradeNo, null, refundReason, outRefundNo);
                if (!applyResponse.isSuccess()) {
                    throw new ServiceException("支付宝退款失败：" + applyResponse.getMsg());
                }*/
            } else if ("微信".equals(payMethod)&&false) {
                // 暂时禁用微信支付功能
                log.warn("微信支付功能暂未开通");
                throw new ServiceException("微信支付功能暂未开通");
            }

            // 更新退款订单状态
            dtbBookRefundOrder.setRefundStatus(dtbBookRefundOrder.getRefundStatus());
            dtbBookRefundOrder.setAuditTime(new Date());
            dtbBookRefundOrder.setAuditRemark(dtbBookRefundOrder.getAuditRemark());
            return this.updateById(dtbBookRefundOrder);
            
        } catch (Exception e) {
            log.error("退款处理失败", e);
            throw new ServiceException("退款处理失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchRefund(BookRefundOrderVO refundOrderVO) {
        List<Long> refundOrderIds = refundOrderVO.getRefundOrderIds();
        Integer refundStatus = refundOrderVO.getRefundStatus();
        String auditRemark = refundOrderVO.getAuditRemark();
        
        for (Long refundOrderId : refundOrderIds) {
            // 获取退款订单信息
            DtbBookRefundOrder refundOrder = this.getById(refundOrderId);
            if (refundOrder == null) {
                throw new ServiceException("退款订单不存在：" + refundOrderId);
            }
            
            // 设置审核信息
            refundOrder.setRefundStatus(refundStatus);
            refundOrder.setAuditRemark(auditRemark);
            refundOrder.setAuditTime(new Date());


            refund(refundOrder);
        }
        
        return true;
    }
}

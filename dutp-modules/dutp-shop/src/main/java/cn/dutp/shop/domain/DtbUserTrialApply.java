package cn.dutp.shop.domain;

import java.util.Date;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import org.springframework.cloud.commons.util.IdUtils;

/**
 * DUTP-DTB_025教师试用申请对象 dtb_user_trial_apply
 *
 * <AUTHOR>
 * @date 2024-11-07
 */
@Data
@TableName("dtb_user_trial_apply")
public class DtbUserTrialApply extends BaseEntity {

    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long applyId;

    /**
     *
     */
    @Excel(name = "")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     *
     */
    @Excel(name = "")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date trialTime;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date examineTime;

    /**
     * 激活时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "激活时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date activationTime;

    /**
     * 审核资料
     */
    @Excel(name = "审核资料")
    private String examineFileUrl;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remark;

    /**
     * 试用天数
     */
    @Excel(name = "试用天数")
    private Long trialDays;

    /**
     * 状态0待审核1通过2驳回
     */
    @Excel(name = "状态1通过2驳回")
    private Long status;

    /**
     * 驳回原因
     */
    @Excel(name = "驳回原因")
    private String reject;

    /**
     * 账户
     */
    @TableField(exist = false)
    private String userName;

    /**
     * 姓名
     */
    @TableField(exist = false)
    private String nickName;

    /**
     * 书名
     */
    @TableField(exist = false)
    private String bookName;

    /**
     * 学校名
     */
    @TableField(exist = false)
    private String schoolName;

    /**
     * isbn国际标准书号
     */
    @Excel(name = "国际标准书号")
    @TableField(exist = false)
    private String isbn;

    /**
     * 申请原因
     */
    @Excel(name = "申请原因")
    private String applicationReason;

    /**
     * 申请次数
     */
    @TableField(exist = false)
    private Integer trialCount;

    /**
     * 封面图片地址
     */
    @Excel(name = "封面图片地址")
    @TableField(exist = false)
    private String cover;
    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("applyId", getApplyId())
                .append("userId", getUserId())
                .append("bookId", getBookId())
                .append("trialTime", getTrialTime())
                .append("examineTime", getExamineTime())
                .append("examineFileUrl", getExamineFileUrl())
                .append("trialDays", getTrialDays())
                .append("status", getStatus())
                .append("reject", getReject())
                .append("userName", getUserName())
                .append("bookName", getBookName())
                .append("schoolName", getSchoolName())
                .append("isbn", getIsbn())
                .append("applicationReason", getApplicationReason())
                .append("cover", getCover())
                .append("trialCount", getTrialCount())
                .toString();
    }
}

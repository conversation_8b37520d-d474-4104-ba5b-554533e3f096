package cn.dutp.shop.domain.dto;

import cn.dutp.common.core.annotation.Excel;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class DtbUserInvoiceTitleItemExport {

    @ExcelIgnore
    private Long bookId;

    @ExcelIgnore
    private Long orderId;

    @ExcelIgnore
    private Long orderItemId;

    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称", order = 1)
    private String bookName;

    /**
     * 商品和服务税收编号
     */
    @ExcelProperty(value = "商品和服务税收编号", order = 2)
    private String bookTaxNo = "304990000000000";

    /**
     * 单价
     */
    @ExcelProperty(value = "单价", order = 3)
    private BigDecimal priceSale;

    /**
     * 数量
     */
    @ExcelProperty(value = "数量", order = 4)
    private Integer bookQuantity;

    /**
     * 金额
     */
    @ExcelProperty(value = "金额", order = 5)
    private String price;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注", order = 6)
    private String remark;
}

package cn.dutp.shop.service;

import cn.dutp.domain.DutpSaleAreaMember;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 大区成员Service接口
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
public interface IDutpSaleAreaMemberService extends IService<DutpSaleAreaMember>
{
    /**
     * 查询大区成员
     *
     * @param memberId 大区成员主键
     * @return 大区成员
     */
    public List<DutpSaleAreaMember> selectDutpSaleAreaMemberByMemberId(Long memberId);

    /**
     * 查询大区成员列表
     *
     * @param dutpSaleAreaMember 大区成员
     * @return 大区成员集合
     */
    public List<DutpSaleAreaMember> selectDutpSaleAreaMemberList(DutpSaleAreaMember dutpSaleAreaMember);

    /**
     * 新增大区成员
     *
     * @param dutpSaleAreaMember 大区成员
     * @return 结果
     */
    public boolean insertDutpSaleAreaMember(List<DutpSaleAreaMember> dutpSaleAreaMember);

    /**
     * 修改大区成员
     *
     * @param dutpSaleAreaMember 大区成员
     * @return 结果
     */
    public boolean updateDutpSaleAreaMember(DutpSaleAreaMember dutpSaleAreaMember);

    /**
     * 批量删除大区成员
     *
     * @param memberIds 需要删除的大区成员主键集合
     * @return 结果
     */
    public boolean deleteDutpSaleAreaMemberByMemberIds(List<Long> memberIds);

    /**
     * 根据用户id，获取大区id
     *
     * @param userId 用户id
     * @return 大区id
     */
    List<Long> selectAreaByUserId(Long userId);

    List<DutpSaleAreaMember> memberList(DutpSaleAreaMember dutpSaleAreaMember);
    List<DutpSaleAreaMember> getOperatorListNopage();

    List<DutpSaleAreaMember> selectByMemberList(List<Long> areaIdList);
}

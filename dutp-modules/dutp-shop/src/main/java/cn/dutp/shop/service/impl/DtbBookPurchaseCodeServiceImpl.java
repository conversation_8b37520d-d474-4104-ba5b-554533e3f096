package cn.dutp.shop.service.impl;

import cn.dutp.api.common.constant.DutpConstant;
import cn.dutp.api.common.constant.NotificationConstants;
import cn.dutp.book.RemoteDtbUserBookService;
import cn.dutp.book.domain.DtbUserBook;
import cn.dutp.common.core.constant.CacheConstants;
import cn.dutp.common.core.constant.Constants;
import cn.dutp.common.core.utils.StringUtils;
import cn.dutp.common.redis.service.RedisService;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.common.sms.utils.AliyunSmsUtil;
import cn.dutp.domain.DtbBook;
import cn.dutp.domain.DtbBookOrder;
import cn.dutp.domain.DtbBookPurchaseCode;
import cn.dutp.message.api.RemoteUserMessageService;
import cn.dutp.message.api.domain.DutpUserMessage;
import cn.dutp.shop.domain.DtbBookOrderCode;
import cn.dutp.shop.domain.DtbUserTrialApply;
import cn.dutp.shop.mapper.DtbBookOrderCodeMapper;
import cn.dutp.shop.mapper.DtbBookOrderMapper;
import cn.dutp.shop.mapper.DtbBookPurchaseCodeMapper;
import cn.dutp.shop.mapper.DtbUserTrialApplyMapper;
import cn.dutp.shop.service.IDtbBookPurchaseCodeService;
import cn.dutp.system.api.RemoteDtbBookCodeExchangeLogService;
import cn.dutp.system.api.domain.DtbBookCodeExchangeLog;
import cn.dutp.system.api.domain.DutpUser;
import cn.dutp.system.api.model.LoginUser;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.seata.core.context.RootContext;
import io.seata.spring.annotation.GlobalTransactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

/**
 * 购书码发行管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class DtbBookPurchaseCodeServiceImpl extends ServiceImpl<DtbBookPurchaseCodeMapper, DtbBookPurchaseCode> implements IDtbBookPurchaseCodeService
{
    @Autowired
    private DtbBookPurchaseCodeMapper dtbBookPurchaseCodeMapper;

    @Autowired
    private RemoteDtbUserBookService remoteDtbUserBookService;

    @Autowired
    private RemoteDtbBookCodeExchangeLogService remoteDtbBookCodeExchangeLogService;

    @Autowired
    private RemoteUserMessageService remoteUserMessageService;

    @Autowired
    private DtbUserTrialApplyMapper dtbUserTrialApplyMapper;

    @Autowired
    private DtbBookOrderCodeMapper dtbBookOrderCodeMapper;

    @Autowired
    private DtbBookOrderMapper dtbBookOrderMapper;

    @Autowired
    private RedisService redisService;
    /**
     * 教师学生端获取购书码发行管理详细信息
     *
     * @param dtbBookPurchaseCode 购书码发行管理
     * @return 购书码发行管理
     */
    @Override
    public DtbBookPurchaseCode getInfoEducation(DtbBookPurchaseCode dtbBookPurchaseCode)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        // 获取用户最新信息
        DutpUser userInfo = baseMapper.getUserInfo(loginUser.getUserid());
        DtbBookPurchaseCode code = baseMapper.getInfoEducation(dtbBookPurchaseCode);
        // 临时码直接兑换
        if(ObjectUtil.isNull(code)){
            return null;
        }
        else if(ObjectUtil.isNotNull(code) && DutpConstant.NUM_TWO.equals(code.getCodeType())){
            return code;
//            // 教师身份校验
//            if(DutpConstant.STR_TWO.equals(userInfo.getUserType())){
//                LambdaQueryWrapper<DtbUserTrialApply> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//                lambdaQueryWrapper.eq(DtbUserTrialApply::getBookId, code.getBookId());
//                lambdaQueryWrapper.eq(DtbUserTrialApply::getUserId, loginUser.getUserid());
//                lambdaQueryWrapper.orderByDesc(DtbUserTrialApply::getTrialTime);
//                List<DtbUserTrialApply> dtbUserTrialApplyList = dtbUserTrialApplyMapper.selectList(lambdaQueryWrapper);
//                if (ObjectUtil.isNotEmpty( dtbUserTrialApplyList) && DutpConstant.LONG_ONE.equals(dtbUserTrialApplyList.get(0).getStatus())) {
//                    return code;
//                }else{
//                    return null;
//                }
//            }
//            return code;
        } else {
            LambdaQueryWrapper<DtbBookOrderCode> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(DtbBookOrderCode::getCodeId, code.getCodeId());
            lambdaQueryWrapper.eq(DtbBookOrderCode::getState, DutpConstant.NUM_TWO);
            DtbBookOrderCode dtbBookOrderCode =  dtbBookOrderCodeMapper.selectOne(lambdaQueryWrapper);
            if(ObjectUtil.isNotEmpty(dtbBookOrderCode)) {
                LambdaQueryWrapper<DtbBookOrder> dtbBookOrderlqw = new LambdaQueryWrapper<>();
                dtbBookOrderlqw.eq(DtbBookOrder::getOrderId, dtbBookOrderCode.getOrderId());
                DtbBookOrder dtbBookOrder =  dtbBookOrderMapper.selectOne(dtbBookOrderlqw);
                // 样书订单不需要校验学校id
                if (DutpConstant.NUM_FOUR.equals(dtbBookOrder.getOrderType())){
                    return code;
                }
                // 除零售的其他订单类型需要校验学校id
                else if(!DutpConstant.NUM_ONE.equals(dtbBookOrder.getOrderType())) {
                    if (ObjectUtil.equals(dtbBookOrderCode.getSchoolId(), userInfo.getSchoolId())) {
                        return code;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 查询购书码发行管理
     *
     * @param codeId 购书码发行管理主键
     * @return 购书码发行管理
     */
    @Override
    public DtbBookPurchaseCode selectDtbBookPurchaseCodeByCodeId(Long codeId)
    {
        return this.getById(codeId);
    }

    /**
     * 查询购书码发行管理列表
     *
     * @param dtbBookPurchaseCode 购书码发行管理
     * @return 购书码发行管理
     */
    @Override
    public List<DtbBookPurchaseCode> selectDtbBookPurchaseCodeList(DtbBookPurchaseCode dtbBookPurchaseCode)
    {
        LambdaQueryWrapper<DtbBookPurchaseCode> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(ObjectUtil.isNotEmpty(dtbBookPurchaseCode.getBookId())) {
            lambdaQueryWrapper.eq(DtbBookPurchaseCode::getBookId
                    ,dtbBookPurchaseCode.getBookId());
        }
        if(ObjectUtil.isNotEmpty(dtbBookPurchaseCode.getPhone())) {
            lambdaQueryWrapper.eq(DtbBookPurchaseCode::getPhone
                    ,dtbBookPurchaseCode.getPhone());
        }
        if(ObjectUtil.isNotEmpty(dtbBookPurchaseCode.getCode())) {
            lambdaQueryWrapper.eq(DtbBookPurchaseCode::getCode
                    ,dtbBookPurchaseCode.getCode());
        }
        if(ObjectUtil.isNotEmpty(dtbBookPurchaseCode.getCodeFrom())) {
            lambdaQueryWrapper.eq(DtbBookPurchaseCode::getCodeFrom
                    ,dtbBookPurchaseCode.getCodeFrom());
        }
        if(ObjectUtil.isNotEmpty(dtbBookPurchaseCode.getState())) {
            lambdaQueryWrapper.eq(DtbBookPurchaseCode::getState
                    ,dtbBookPurchaseCode.getState());
        }
        if(ObjectUtil.isNotEmpty(dtbBookPurchaseCode.getExchangeDate())) {
            lambdaQueryWrapper.eq(DtbBookPurchaseCode::getExchangeDate
                    ,dtbBookPurchaseCode.getExchangeDate());
        }
        if(ObjectUtil.isNotEmpty(dtbBookPurchaseCode.getExpiryDate())) {
            lambdaQueryWrapper.eq(DtbBookPurchaseCode::getExpiryDate
                    ,dtbBookPurchaseCode.getExpiryDate());
        }
        if(ObjectUtil.isNotEmpty(dtbBookPurchaseCode.getBindDate())) {
            lambdaQueryWrapper.eq(DtbBookPurchaseCode::getBindDate
                    ,dtbBookPurchaseCode.getBindDate());
        }
        return this.list(lambdaQueryWrapper);
    }


    /**
     * 学生教师端查询用户购书码发行管理列表
     *
     * @param dtbBookPurchaseCode 购书码发行管理
     * @return 购书码发行管理集合
     */
    @Override
    public List<DtbBookPurchaseCode> listEducation(DtbBookPurchaseCode dtbBookPurchaseCode)
    {
        // 获取用户Id
        dtbBookPurchaseCode.setUserId(SecurityUtils.getUserId());
        return  baseMapper.listEducation(dtbBookPurchaseCode);
    }

    /**
     * 新增购书码发行管理
     *
     * @param dtbBookPurchaseCode 购书码发行管理
     * @return 结果
     */
    @Override
    public boolean insertDtbBookPurchaseCode(DtbBookPurchaseCode dtbBookPurchaseCode)
    {
        return this.save(dtbBookPurchaseCode);
    }

    /**
     * 修改购书码发行管理
     *
     * @param dtbBookPurchaseCode 购书码发行管理
     * @return 结果
     */
    @Override
    public boolean updateDtbBookPurchaseCode(DtbBookPurchaseCode dtbBookPurchaseCode)
    {
        return this.updateById(dtbBookPurchaseCode);
    }

    /**
     * 批量删除购书码发行管理
     *
     * @param codeIds 需要删除的购书码发行管理主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookPurchaseCodeByCodeIds(List<Long> codeIds)
    {
        return this.removeByIds(codeIds);
    }

    /**
     * 教师学生端购书码兑换
     *
     * @param dtbBookPurchaseCode 购书码发行管理
     * @return 结果
     */
    @Override
    @GlobalTransactional(name = "bookCodeExchange", rollbackFor = Exception.class)
    @Transactional(rollbackFor = Exception.class)
    public boolean bookCodeExchange(DtbBookPurchaseCode dtbBookPurchaseCode) throws Exception {
        Logger logger = LoggerFactory.getLogger(this.getClass());

        // 获取当前用户
        LoginUser loginUser = SecurityUtils.getLoginUser();

        DutpUser userInfo = baseMapper.getUserInfo(loginUser.getUserid());
        logger.info("获取当前最新用户信息：{}", userInfo);
        // 验证短信验证码
        Integer checkCode = AliyunSmsUtil.checkCode(dtbBookPurchaseCode.getPhone(), dtbBookPurchaseCode.getVerificationCode(), "SMS_314725755");
        logger.info("验证短信验证码结果：{}", checkCode);
        if (!DutpConstant.SUCCESS_CODE.equals(checkCode)) {
            throw new RuntimeException(DutpConstant.VERIFICATION_CODE_ERROR);
        }

        // 校验购书码是否可用
        DtbBookPurchaseCode dtbBookPurchaseCodeData = this.selectDtbBookPurchaseCodeByCodeId(dtbBookPurchaseCode.getCodeId());
        logger.info("查询购书码信息：{}", dtbBookPurchaseCodeData);
        if (ObjectUtil.isNull(dtbBookPurchaseCodeData) || DutpConstant.NUM_THREE.equals(dtbBookPurchaseCodeData.getState())) {
            throw new RuntimeException(DutpConstant.BOOK_PURCHASE_CODE);
        }
        // 更新教师试用申请激活状态
        if (DutpConstant.STR_TWO.equals(userInfo.getUserType())) {
            if(loginUser.getUserid().equals(dtbBookPurchaseCodeData.getUserId())){
                LambdaQueryWrapper<DtbUserTrialApply> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(DtbUserTrialApply::getBookId, dtbBookPurchaseCode.getBookId());
                lambdaQueryWrapper.eq(DtbUserTrialApply::getUserId, dtbBookPurchaseCode.getUserId());
                lambdaQueryWrapper.orderByDesc(DtbUserTrialApply::getTrialTime);
                List<DtbUserTrialApply> dtbUserTrialApplyList = dtbUserTrialApplyMapper.selectList(lambdaQueryWrapper);
                logger.info("查询教师试用申请结果：{}", dtbUserTrialApplyList);

                if (ObjectUtil.isNotEmpty(dtbUserTrialApplyList)) {
                    DtbUserTrialApply dtbUserTrialApply = dtbUserTrialApplyList.get(0);
                    dtbUserTrialApply.setActivationTime(DateUtil.date());
                    dtbUserTrialApply.setUpdateTime(DateUtil.date());
                    dtbUserTrialApply.setUpdateBy(dtbBookPurchaseCode.getUserId().toString());
                    logger.info("准备更新教师试用申请：{}", dtbUserTrialApply);

                    boolean isDtbUserTrialApply = dtbUserTrialApplyMapper.updateById(dtbUserTrialApply) > 0;
                    logger.info("更新教师试用申请结果：{}", isDtbUserTrialApply);

                    if (!isDtbUserTrialApply) {
                        throw new RuntimeException(DutpConstant.BOOK_CODE_REDEMPTION_FAILED);
                    }
                }
            }
        }
        // 设置兑换日期和用户ID
        SimpleDateFormat sdf = new SimpleDateFormat(DutpConstant.DATE_FORMAT_YYYY_MM_DD);
        Date maxDate = sdf.parse(DutpConstant.MAX_DATE);
        dtbBookPurchaseCodeData.setExchangeDate(new Date());
        dtbBookPurchaseCodeData.setUserId(loginUser.getUserid());
        dtbBookPurchaseCodeData.setState(DutpConstant.NUM_THREE);
        dtbBookPurchaseCodeData.setPhone(dtbBookPurchaseCode.getPhone());
        logger.info("设置兑换日期和用户ID：{}", dtbBookPurchaseCodeData);

        // 计算短期码过期日期
        Date expiryDate = null;
        if (DutpConstant.NUM_TWO.equals(dtbBookPurchaseCodeData.getCodeType())) {
            LocalDateTime exchangeDateTime = LocalDateTime.ofInstant(dtbBookPurchaseCodeData.getExchangeDate().toInstant(), ZoneId.systemDefault());
            long timeLimitDays = dtbBookPurchaseCodeData.getTimeLimit();
            Duration timeLimitDuration = Duration.ofDays(timeLimitDays);
            LocalDateTime expiryDateTime = exchangeDateTime.plus(timeLimitDuration);
            expiryDate = Date.from(expiryDateTime.atZone(ZoneId.systemDefault()).toInstant());
        }
        dtbBookPurchaseCodeData.setExpiryDate(DutpConstant.NUM_ONE.equals(dtbBookPurchaseCodeData.getCodeType()) ? maxDate : expiryDate);
        logger.info("计算过期日期：{}", dtbBookPurchaseCodeData.getExpiryDate());
        // 更新购书码状态
        boolean isUpdateSuccess = this.updateById(dtbBookPurchaseCodeData);

        // 订单下的购书码
        dtbBookOrderMapper.updateBookOrderCodeId(SecurityUtils.getUsername(), dtbBookPurchaseCodeData.getCodeId());

        logger.info("更新购书码状态结果：{}", isUpdateSuccess);

        // 查询教材信息
        DtbBook dtbBook = dtbBookPurchaseCodeMapper.selectDtbBookById(dtbBookPurchaseCode.getBookId());
        logger.info("查询教材信息结果：{}", dtbBook);

        // 添加用户购买的教材
        DtbUserBook dtbUserBook = new DtbUserBook();
        dtbUserBook.setBookId(dtbBookPurchaseCode.getBookId());
        dtbUserBook.setUserId(dtbBookPurchaseCode.getUserId());
        dtbUserBook.setCodeId(dtbBookPurchaseCode.getCodeId());
        dtbUserBook.setAddWay(DutpConstant.NUM_ONE.equals(dtbBookPurchaseCodeData.getCodeType()) ? DutpConstant.NUM_ONE : DutpConstant.NUM_THREE);
        dtbUserBook.setSort(0L);
        if (ObjectUtil.isNotNull(dtbBook)) {
            dtbUserBook.setVersionId(dtbBook.getCurrentVersionId());
            dtbUserBook.setBookTypeId(dtbBook.getBookType());
        }
        dtbUserBook.setExpireDate(DutpConstant.NUM_ONE.equals(dtbBookPurchaseCodeData.getCodeType()) ? maxDate : dtbBookPurchaseCodeData.getExpiryDate());
        logger.info("准备添加用户购买的教材：{}", dtbUserBook);

        boolean isEditStatusSuccess = remoteDtbUserBookService.editStatus(dtbUserBook).getCode() == 200;
        logger.info("添加用户购买的教材结果：{}", isEditStatusSuccess);

        // 添加购书码兑换记录
        DtbBookCodeExchangeLog dtbBookCodeExchangeLog = new DtbBookCodeExchangeLog();
        dtbBookCodeExchangeLog.setCodeId(dtbBookPurchaseCode.getCodeId());
        dtbBookCodeExchangeLog.setUserId(dtbBookPurchaseCode.getUserId());
        dtbBookCodeExchangeLog.setBookId(dtbBookPurchaseCode.getBookId());
        dtbBookCodeExchangeLog.setExchangeDate(DateUtil.date());
        logger.info("准备添加购书码兑换记录：{}", dtbBookCodeExchangeLog);

        boolean isAddEducationSuccess = remoteDtbBookCodeExchangeLogService.addEducation(dtbBookCodeExchangeLog).getCode() == 200;
        logger.info("添加购书码兑换记录结果：{}", isAddEducationSuccess);

        // 打印分布式事务XID
        System.out.println("bookCodeExchange XID:" + RootContext.getXID());
        logger.info("bookCodeExchange XID:{}", RootContext.getXID());

        // 发送消息通知用户
        DutpUserMessage dutpUserMessage = new DutpUserMessage();
        dutpUserMessage.setContent(DutpConstant.NUM_ONE.equals(dtbBookPurchaseCodeData.getCodeType()) ?
                String.format(NotificationConstants.BOOK_CODE_REDEEMED_LONG, dtbBook.getBookName())
                :String.format(NotificationConstants.BOOK_CODE_REDEEMED, dtbBook.getBookName(), DateUtil.format(dtbUserBook.getExpireDate(), DutpConstant.DATE_FORMAT_YYYY_MM_DD))
        );
        dutpUserMessage.setTitle(NotificationConstants.BOOK_CODE_REDEEMED_TITLE);
        dutpUserMessage.setToUserId(loginUser.getUserid());
        dutpUserMessage.setMessageType(DutpConstant.NUM_THREE);
        dutpUserMessage.setReadFlag(DutpConstant.NUM_ZERO);
        dutpUserMessage.setBusinessId(dtbBook.getBookId());
        dutpUserMessage.setToUserType(DutpConstant.NUM_TWO);
        logger.info("准备发送消息通知用户：{}", dutpUserMessage);
        boolean isSendMessageSuccess = remoteUserMessageService.addMessage(dutpUserMessage).getCode() == 200;
        // 检查所有操作是否成功
        if (!isUpdateSuccess || !isEditStatusSuccess || !isAddEducationSuccess || !isSendMessageSuccess) {
            logger.error("购书码兑换失败，部分操作未成功");
            throw new RuntimeException(DutpConstant.BOOK_CODE_REDEMPTION_FAILED);
        }
        String key = Constants.PREFIX_PHONE_CODE + dtbBookPurchaseCode.getPhone() + "_" + "SMS_314725755";
        redisService.deleteObject(key);
        return true;
    }

    @Override
    public List<DtbBookPurchaseCode> selectByOrderId(Long orderId) {
        List<DtbBookPurchaseCode> resList = dtbBookPurchaseCodeMapper.selectByOrderId(orderId);
        return resList;
    }

}

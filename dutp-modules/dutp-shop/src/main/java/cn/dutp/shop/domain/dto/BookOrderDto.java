package cn.dutp.shop.domain.dto;

import cn.dutp.common.core.serialize.LongListToStringSerializer;
import cn.dutp.common.core.web.domain.BaseEntity;
import cn.dutp.domain.DtbBookOrderItem;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 订单对象
 *
 * <AUTHOR>
 */
@Data
public class BookOrderDto extends BaseEntity {

    /**
     * 教材ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 教材名称/ISBN/ISSN
     */
    private String bookName;

    /**
     * 学校名称
     */
    private String schoolName;

    /**
     * 用户名称
     */
    private String nickName;

    /**
     * 订单状态
     */
    private Integer status;

    /**
     * 订单类型1直接购买2教务统一购买3管理端协助教务采购4样书订单5书商采购
     */
    private Integer orderType;

    /**
     * 大区地区名称
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long areaId;

    /**
     * 大区经办人
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long managerId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单状态，pending：待处理、processing：处理中、completed：已完成、cancelled：已取消等
     */
    private String orderStatus;

    /**
     * 样书订单状态，搜索用，1订单确认2订单驳回3已完成4已取消
     */
    private Integer orderSampleStatus;

    /**
     * 订单下书籍
     */
    private List<DtbBookOrderItem> addOrderList;

    /**
     * 获取列表类型1获取订单选定书籍列表，2添加商品书籍列表（所有有库存上线的书籍）
     */
    private Integer bookListType;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderId;

    /**
     * 学校id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolId;

    /**
     * 书商id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long merchantId;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;
    /**
     * 查询条件
     */
    private String selParm;


    /**
     * 经办人ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long operatorId;

    /**
     * 订单审核状态 '审核状态0待审核1通过2驳回',
     */
    private Integer auditStatus;

    /**
     * 订单审核类型，'1采购审核2修改审核3作废审核4样书一级审核5样书二级审核'
     */
    private Integer auditType;

    /**
     * 审核人ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long auditUserId;

    /**
     * 当前用户
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;
    /**
     * 上架状态1已上架2未上架3召回4即将上架
     */
    private Integer shelfState;
    /**
     * 审核备注
     */
    private String auditContent;

    private Integer pageNum;
    private Integer pageSize;

    /**
     * 0未退款1退款中2部分退款3全额退款
     */
    private Integer refundStatus;

    /**
     * 导出的订单
     */
    @JsonSerialize(using = LongListToStringSerializer.class)
    private List<Long> exportList;
}

package cn.dutp.shop.service.impl;

import cn.dutp.api.common.constant.DutpConstant;
import cn.dutp.api.common.enums.*;
import cn.dutp.book.domain.DtbUserBook;
import cn.dutp.common.core.constant.CacheConstants;
import cn.dutp.common.core.constant.SecurityConstants;
import cn.dutp.common.core.domain.R;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.text.Convert;
import cn.dutp.common.core.utils.StringUtils;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.redis.service.RedisService;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.*;
import cn.dutp.domain.vo.BookOrderVo;
import cn.dutp.message.api.RemoteUserMessageService;
import cn.dutp.message.api.domain.DutpUserMessage;
import cn.dutp.shop.domain.DtbBookMerchant;
import cn.dutp.shop.domain.DtbBookOrderCode;
import cn.dutp.shop.domain.dto.BookOrderDto;
import cn.dutp.shop.domain.dto.OrderReviewDto;
import cn.dutp.shop.domain.vo.DtbBookOrderItemEditApprovalVo;
import cn.dutp.shop.domain.vo.DtbBookOrderItemVo;
import cn.dutp.shop.domain.vo.DutpSchoolVo;
import cn.dutp.shop.domain.vo.SysUserVo;
import cn.dutp.shop.mapper.*;
import cn.dutp.shop.service.IDtbBookOrderItemService;
import cn.dutp.shop.service.IDtbBookOrderService;
import cn.dutp.system.api.RemoteUserService;
import cn.dutp.system.api.domain.SysUser;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * DUTP-DTB_012订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Service
public class DtbBookOrderServiceImpl extends ServiceImpl<DtbBookOrderMapper, DtbBookOrder> implements IDtbBookOrderService
{
    @Autowired
    private DtbBookOrderMapper dtbBookOrderMapper;

    @Autowired
    private IDtbBookOrderItemService dtbBookOrderItemService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private DtbBookPurchaseCodeMapper dtbBookPurchaseCodeMapper;

    @Autowired
    private DtbBookOrderCodeMapper dtbBookOrderCodeMapper;

    @Autowired
    private DtbBookOrderItemMapper dtbBookOrderItemMapper;

    @Autowired
    private DtbBookOrderAuditUserMapper dtbBookOrderAuditUserMapper;

    @Autowired
    private RemoteUserMessageService remoteUserMessageService;

    @Autowired
    private DtbBookMerchantMapper dtbBookMerchantMapper;

    @Autowired
    private ISysUserMapper sysUserMapper;

    @Autowired
    private IDutpSchoolMapper dutpSchoolMapper;

    private final static Logger log = LoggerFactory.getLogger(DtbBookOrderServiceImpl.class);

    @Autowired
    private DtbBookRefundOrderMapper dtbBookRefundOrderMapper;

    @Autowired
    private DtbBookRefundOrderItemServiceImpl dtbBookRefundOrderItemService;

    @Autowired
    private DtbShopBookMapper dtbBookMapper;


    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private DutpSaleAreaMapper dutpSaleAreaMapper;

    @Autowired
    private DtbUserBookMapper dtbUserBookMapper;
    /**
     * 查询DUTP-DTB_012订单
     *
     * @param orderId DUTP-DTB_012订单主键
     * @return DUTP-DTB_012订单
     */
    @Override
    public DtbBookOrder selectDtbBookOrderByOrderId(Long orderId)
    {
        return this.getById(orderId);
    }

    /**
     * 查询DUTP-DTB_012订单列表
     *
     * @param dtbBookOrder DUTP-DTB_012订单
     * @return DUTP-DTB_012订单
     */
    @Override
    public List<DtbBookOrder> selectDtbBookOrderList(DtbBookOrder dtbBookOrder)
    {
        LambdaQueryWrapper<DtbBookOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(ObjectUtil.isNotEmpty(dtbBookOrder.getOrderNo())) {
            lambdaQueryWrapper.eq(DtbBookOrder::getOrderNo
                    ,dtbBookOrder.getOrderNo());
        }
        if(ObjectUtil.isNotEmpty(dtbBookOrder.getOrderType())) {
            lambdaQueryWrapper.eq(DtbBookOrder::getOrderType
                    ,dtbBookOrder.getOrderType());
        }
        if(ObjectUtil.isNotEmpty(dtbBookOrder.getBookId())) {
            lambdaQueryWrapper.eq(DtbBookOrder::getBookId
                    ,dtbBookOrder.getBookId());
        }
        if(ObjectUtil.isNotEmpty(dtbBookOrder.getUserId())) {
            lambdaQueryWrapper.eq(DtbBookOrder::getUserId
                    ,dtbBookOrder.getUserId());
        }
        if(ObjectUtil.isNotEmpty(dtbBookOrder.getSchoolUserId())) {
            lambdaQueryWrapper.eq(DtbBookOrder::getSchoolUserId
                    ,dtbBookOrder.getSchoolUserId());
        }
        if(ObjectUtil.isNotEmpty(dtbBookOrder.getSchoolId())) {
            lambdaQueryWrapper.eq(DtbBookOrder::getSchoolId
                    ,dtbBookOrder.getSchoolId());
        }
        if(ObjectUtil.isNotEmpty(dtbBookOrder.getPaymentStatus())) {
            lambdaQueryWrapper.eq(DtbBookOrder::getPaymentStatus
                    ,dtbBookOrder.getPaymentStatus());
        }
        if(ObjectUtil.isNotEmpty(dtbBookOrder.getPaymentMethod())) {
            lambdaQueryWrapper.eq(DtbBookOrder::getPaymentMethod
                    ,dtbBookOrder.getPaymentMethod());
        }
        if(ObjectUtil.isNotEmpty(dtbBookOrder.getOrderStatus())) {
            lambdaQueryWrapper.eq(DtbBookOrder::getOrderStatus
                    ,dtbBookOrder.getOrderStatus());
        }
        if(ObjectUtil.isNotEmpty(dtbBookOrder.getInvoiceStatus())) {
            lambdaQueryWrapper.eq(DtbBookOrder::getInvoiceStatus
                    ,dtbBookOrder.getInvoiceStatus());
        }
        if(ObjectUtil.isNotEmpty(dtbBookOrder.getPayTime())) {
            lambdaQueryWrapper.eq(DtbBookOrder::getPayTime
                    ,dtbBookOrder.getPayTime());
        }
        if(ObjectUtil.isNotEmpty(dtbBookOrder.getPayAmount())) {
            lambdaQueryWrapper.eq(DtbBookOrder::getPayAmount
                    ,dtbBookOrder.getPayAmount());
        }
        if(ObjectUtil.isNotEmpty(dtbBookOrder.getPayCertImageUrl())) {
            lambdaQueryWrapper.eq(DtbBookOrder::getPayCertImageUrl
                    ,dtbBookOrder.getPayCertImageUrl());
        }
        if(ObjectUtil.isNotEmpty(dtbBookOrder.getOrderAuditStatus())) {
            lambdaQueryWrapper.eq(DtbBookOrder::getOrderAuditStatus
                    ,dtbBookOrder.getOrderAuditStatus());
        }
        if(ObjectUtil.isNotEmpty(dtbBookOrder.getPaymentAuditStatus())) {
            lambdaQueryWrapper.eq(DtbBookOrder::getPaymentAuditStatus
                    ,dtbBookOrder.getPaymentAuditStatus());
        }
        if(ObjectUtil.isNotEmpty(dtbBookOrder.getPrice())) {
            lambdaQueryWrapper.eq(DtbBookOrder::getPrice
                    ,dtbBookOrder.getPrice());
        }
        if(ObjectUtil.isNotEmpty(dtbBookOrder.getDiscount())) {
            lambdaQueryWrapper.eq(DtbBookOrder::getDiscount
                    ,dtbBookOrder.getDiscount());
        }
        if(ObjectUtil.isNotEmpty(dtbBookOrder.getDeleted())) {
            lambdaQueryWrapper.eq(DtbBookOrder::getDeleted
                    ,dtbBookOrder.getDeleted());
        }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增DUTP-DTB_012订单
     *
     * @param dtbBookOrder DUTP-DTB_012订单
     * @return 结果
     */
    @Override
    public boolean insertDtbBookOrder(DtbBookOrder dtbBookOrder)
    {
        dtbBookOrder.setOrderNo(orderNumberGenerator());
        redisService.setCacheObject("OrderNo:" + dtbBookOrder.getOrderNo(), dtbBookOrder, CacheConstants.ORDER_LOCK_TIME, TimeUnit.MINUTES);
        return this.save(dtbBookOrder);
    }

    /**
     * 修改DUTP-DTB_012订单
     *
     * @param dtbBookOrder DUTP-DTB_012订单
     * @return 结果
     */
    @Override
    public boolean updateDtbBookOrder(DtbBookOrder dtbBookOrder)
    {
        return this.updateById(dtbBookOrder);
    }

    /**
     * 批量删除DUTP-DTB_012订单
     *
     * @param orderIds 需要删除的DUTP-DTB_012订单主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookOrderByOrderIds(List<Long> orderIds)
    {
        return this.removeByIds(orderIds);
    }

    /**
     * 创建订单可选的和已选定书籍（上线的）
     */
    @Override
    public TableDataInfo getOrderBookList(BookOrderDto dto) {
        TableDataInfo info = new TableDataInfo();
        List<BookOrderVo> list = null;
        if (dto.getOrderType() == 3) {
            /*代采(学校采购)书籍和（学校折扣和教材折扣取最高值）对应折扣*/
            list = dtbBookOrderMapper.getOrderSchoolBookList(dto);
            //取学校折扣、教材折扣的最高值作为最低折扣的验证
            list.stream().forEach(e -> {
                if (e.getMinDiscount() == null && e.getDiscount() == null) {
                    e.setMinDiscount(BigDecimal.ZERO);
                } else if (e.getMinDiscount() == null) {
                    e.setMinDiscount(e.getDiscount());
                } else if (e.getDiscount() != null) {
                    if (e.getMinDiscount().compareTo(e.getMinDiscount()) == -1) {
                        e.setMinDiscount(e.getDiscount());
                    }
                }
            });
        }
        if (dto.getOrderType() == 5) {
            /*书商书籍和(书商折扣和教材折扣取最高值)对应折扣*/
            list = dtbBookOrderMapper.getOrderMerchantBookList(dto);
            //取书商折扣、教材折扣的最高值作为最低折扣的验证
            list.stream().forEach(e -> {
                if (e.getMinDiscount() == null && e.getDiscount() == null) {
                    e.setMinDiscount(BigDecimal.ZERO);
                } else if (e.getMinDiscount() == null) {
                    e.setMinDiscount(e.getDiscount());
                } else if (e.getDiscount() != null) {
                    if (e.getMinDiscount().compareTo(e.getMinDiscount()) == -1) {
                        e.setMinDiscount(e.getDiscount());
                    }
                }
            });
        }
        if (dto.getOrderType() == 4) {
            /*样书书籍无折扣数据*/
            list = dtbBookOrderMapper.getOrderSampleBookList(dto);
        }
        info.setTotal(new PageInfo(list).getTotal());
        if (!CollectionUtils.isEmpty(list)) {
            // 获取子教材
            List<Long> ids = list.stream().map(BookOrderVo::getBookId).collect(Collectors.toList());
            for (int i = 0; i < list.size(); i++) {
                list.get(i).setIsbn(StringUtils.isBlank(list.get(i).getIsbn())?list.get(i).getIssn():list.get(i).getIsbn());
                Integer no = i + 1;
                list.get(i).setRowNum(no);
            }
            QueryWrapper<DtbBook> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(DtbBook::getMasterBookId, ids).eq(DtbBook::getDelFlag, 0).eq(DtbBook::getMasterFlag, 3)
                    .eq(DtbBook::getPublishStatus, 2);
            List<DtbBook> childList = dtbBookMapper.selectList(queryWrapper);
            if (!CollectionUtils.isEmpty(childList)) {
                for (DtbBook vo : childList) {
                    BookOrderVo child = new BookOrderVo();
                    child.setBookId(vo.getBookId());
                    child.setBookName(vo.getBookName());
                    child.setIsbn(StringUtil.isBlank(vo.getIsbn()) ? vo.getIssn() : vo.getIsbn());
                    child.setCover(vo.getCover());
                    child.setMasterBookId(vo.getMasterBookId());
                    child.setPriceSale(BigDecimal.ZERO);
                    list.add(child);
                }
            }
            List<Tree<String>> result = buildTree(list);
            info.setRows(result);
            return info;
        }
        return null;
    }

    private List<Tree<String>> buildTree(List<BookOrderVo> list) {
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setChildrenKey("children");

        List<Tree<String>> treeNodes = TreeUtil.build(list,null,treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getBookId().toString());
                    if (treeNode.getMasterBookId() != null) {
                        tree.setParentId(treeNode.getMasterBookId().toString());
                    } else {
                        tree.setParentId(null);
                    }
                    tree.setName(treeNode.getBookName());
                    tree.setWeight(treeNode.getRowNum());

                    tree.putExtra("bookId", treeNode.getBookId().toString());
                    tree.putExtra("bookName", treeNode.getBookName());
                    tree.putExtra("cover", treeNode.getCover());
                    tree.putExtra("isbn", treeNode.getIsbn());
                    tree.putExtra("bookNo", treeNode.getBookNo());
                    tree.putExtra("masterFlag", treeNode.getMasterFlag());
                    tree.putExtra("priceCounter", treeNode.getPriceCounter());
                    tree.putExtra("priceSale", treeNode.getPriceSale());
                    tree.putExtra("bookOrganize", treeNode.getBookOrganize());
                    tree.putExtra("stockCount", treeNode.getStockCount());
                    tree.putExtra("discount", treeNode.getDiscount());
                    tree.putExtra("minDiscount", treeNode.getMinDiscount());
                    tree.putExtra("rowNum", treeNode.getRowNum());
                    tree.putExtra("deptId", treeNode.getDeptId());
                });
         return treeNodes;
    }

    @Override
    public TableDataInfo getOrderList(BookOrderDto dto) {
        if (dto.getOrderType() == 1) {
            // 零售
            List<BookOrderVo> list = dtbBookOrderMapper.getRetailOrderList(dto);
            PageInfo<BookOrderVo> pageInfo = new PageInfo<>(list);
            TableDataInfo info = new TableDataInfo();
            info.setTotal(pageInfo.getTotal());
            info.setRows(pageInfo.getList());
            return info;
        }else if (dto.getOrderType() == 4) {
            // 样书
            List<BookOrderVo> list = dtbBookOrderMapper.getSampleOrderList(dto);
            PageInfo<BookOrderVo> pageInfo = new PageInfo<>(list);
            TableDataInfo info = new TableDataInfo();
            info.setTotal(pageInfo.getTotal());
            info.setRows(pageInfo.getList());
            return info;
        }
        return null;
    }

    @Override
    public BookOrderVo getOrderDetail(BookOrderDto dto) {
        BookOrderVo res = new BookOrderVo();
        // 订单信息
        if (dto.getOrderType() == 1) {
            // 零售
            res = dtbBookOrderMapper.getRetailOrderDetail(dto);
        } else if (dto.getOrderType() == 2 || dto.getOrderType() == 3 || dto.getOrderType() == 4 || dto.getOrderType() == 5) {
            // 批量采购、样书
            res = dtbBookOrderMapper.getEducationalDetail(dto);
            if (OrderStatusEnum.PENDING.getCode().equals(res.getOrderStatus()) || OrderStatusEnum.CREATE_REFUSED.getCode().equals(res.getOrderStatus())) {
                //采购确认阶段，可以编辑，
                res.setIsEditChecked(1);
                res.setShowChecked(1);
            }

            if (OrderStatusEnum.EDITING.getCode().equals(res.getOrderStatus()) || OrderStatusEnum.EDIT_REFUSED.getCode().equals(res.getOrderStatus())) {
                //修改审核阶段，不展示不可编辑
                res.setIsEditChecked(2);
                res.setShowChecked(2);
            }

            if (OrderStatusEnum.CANCELING.getCode().equals(res.getOrderStatus()) || OrderStatusEnum.CANCEL_REFUSED.getCode().equals(res.getOrderStatus())) {
                //驳回阶段，展示
                res.setShowChecked(1);
                res.setIsEditChecked(1);
            }
        }
        // 商品信息
        List<DtbBookOrderItem> bookDetail = dtbBookOrderMapper.getItemByOrderId(dto.getOrderId());
        bookDetail.stream().forEach(e -> {
            if (e.getMasterFlag() == 3) {
                Optional<Long> countValue = bookDetail.stream()
                        .filter(obj -> obj.getBookId().equals(e.getMasterBookId()))
                        .map(DtbBookOrderItem::getBookQuantity)
                        .findFirst();
                e.setBookQuantity(countValue.orElse(DutpConstant.LONG_ZERO));
            }
        });
        res.setAllBookList(getTreeNode(bookDetail));

        // 售后记录
        if (dto.getOrderType() != 4) {
            List<DtbBookRefundOrderItem> refundOrderList = dtbBookOrderMapper.getRefundByOrderId(dto.getOrderId());
            res.setRefundOrder(refundOrderList);
        }
        QueryWrapper<DtbBookOrderAuditUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DtbBookOrderAuditUser::getOrderId,dto.getOrderId());
        if (dto.getOrderType() != 4 && dto.getOrderType() != 1) {
//            if (OrderStatusEnum.PENDING.getCode().equals(res.getOrderStatus()) || OrderStatusEnum.CREATE_REFUSED.getCode().equals(res.getOrderStatus())) {
//                queryWrapper.lambda().eq(DtbBookOrderAuditUser::getAuditType,1);
//            }
//            if (OrderStatusEnum.EDIT_REFUSED.getCode().equals(res.getOrderStatus()) || OrderStatusEnum.EDITING.getCode().equals(res.getOrderStatus())) {
//                queryWrapper.lambda().eq(DtbBookOrderAuditUser::getAuditType,2);
//            }
//            if (OrderStatusEnum.CANCELING.getCode().equals(res.getOrderStatus()) || OrderStatusEnum.CANCEL_REFUSED.getCode().equals(res.getOrderStatus())) {
//                queryWrapper.lambda().eq(DtbBookOrderAuditUser::getAuditType,3);
//            }
//          // 批量采购的最新的审批记录
            List<DtbBookOrderAuditUser> auditList = dtbBookOrderAuditUserMapper.getByOrderId(dto.getOrderId());
            DtbBookOrderAuditUser audit = new DtbBookOrderAuditUser();
            if (!CollectionUtils.isEmpty(auditList)) {
                if (OrderStatusEnum.PENDING.getCode().equals(res.getOrderStatus()) || OrderStatusEnum.CREATE_REFUSED.getCode().equals(res.getOrderStatus())) {
                    audit = auditList.stream().filter(obj -> obj.getAuditType() == 1).findFirst().orElse(null);
                }
                if (OrderStatusEnum.EDIT_REFUSED.getCode().equals(res.getOrderStatus()) || OrderStatusEnum.EDITING.getCode().equals(res.getOrderStatus())) {
                    audit = auditList.stream().filter(obj -> obj.getAuditType() == 2).findFirst().orElse(null);
                }
                if (OrderStatusEnum.CANCELING.getCode().equals(res.getOrderStatus()) || OrderStatusEnum.CANCEL_REFUSED.getCode().equals(res.getOrderStatus())) {
                    audit = auditList.stream().filter(obj -> obj.getAuditType() == 3).findFirst().orElse(null);
                }
                res.setAudit(audit);
            }
        } else if (dto.getOrderType() == 4){
            // 样书的审批记录
            List<DtbBookOrderAuditUser> auditList = dtbBookOrderAuditUserMapper.getByOrderId(dto.getOrderId());
            if (!CollectionUtils.isEmpty(auditList)) {
                res.setFirstAudit(auditList.stream().filter(obj -> obj.getAuditType() == 4).findFirst().orElse(null));
                res.setSecondAudit(auditList.stream().filter(obj -> obj.getAuditType() == 5).findFirst().orElse(null));
            }
        }
        return res;
    }
    /**
     * 学生教师端查询订单列表
     *
     * @param dto 查询参数
     * @return 结果
     */
    @Override
    public List<BookOrderVo> getOrderInfo(BookOrderDto dto) {
        dto.setUserId(SecurityUtils.getUserId());
        List<BookOrderVo> book = dtbBookOrderMapper.getOrderInfo(dto);
        if (!book.isEmpty()) {
            book.forEach(item -> {
                List<Long> bookIds = new ArrayList<>();
                bookIds.add(item.getBookId());
                List<DtbBookOrderItem> bookDetail = dtbBookOrderMapper.getChildItemByOrderId(bookIds);
                item.setDeputyBookList(bookDetail);
            });
        }
        return book;
    }
    /**
     * 学生教师端新增订单列表
     *
     * @param dtbBookOrder 对象
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addOrder(DtbBookOrder dtbBookOrder){
        boolean isResult = false;
        if (ObjectUtil.isNotEmpty(dtbBookOrder)) {
            //新增订单表
            // 订单状态，pending：待处理、processing：处理中、completed：已完成、cancelled：已取消等
            dtbBookOrder.setOrderStatus(OrderStatusEnum.PENDING.getCode());
            // 1零售2教务采购3代采订单4样书订单5书商采购
            dtbBookOrder.setOrderType(DutpConstant.NUM_ONE);
            // 购书者ID
            dtbBookOrder.setUserId(SecurityUtils.getUserId());
            // 付款日期
            dtbBookOrder.setPayTime(new Date());
            //商品总额
            dtbBookOrder.setPrice(dtbBookOrder.getPayAmount());
            isResult = dtbBookOrderMapper.insert(dtbBookOrder) > 0;

            if(!isResult){
                throw new RuntimeException(DutpConstant.ADDITION_FAILED);
            }

            //新增订单明细
            DtbBookOrderItem dtbBookOrderItem = new DtbBookOrderItem();
            // 订单ID
            dtbBookOrderItem.setOrderId(dtbBookOrder.getOrderId());
            // 书籍ID
            dtbBookOrderItem.setBookId(dtbBookOrder.getBookId());
            // 书的定价
            dtbBookOrderItem.setPriceCounter(dtbBookOrder.getPriceCounter());
            // 书的售价
            dtbBookOrderItem.setPriceSale(dtbBookOrder.getPriceSale());
            // 改完的单价
            dtbBookOrderItem.setPriceOrderItem(dtbBookOrder.getPriceSale());
            // 书籍数量
            dtbBookOrderItem.setBookQuantity(DutpConstant.LONG_ONE);

            isResult = dtbBookOrderItemMapper.insert(dtbBookOrderItem) > 0;

            if(!isResult){
                throw new RuntimeException(DutpConstant.ADDITION_FAILED);
            }

            //新增订单下的购书码
            LambdaQueryWrapper<DtbBookPurchaseCode> dtbBookPurchaseCodeWrapper = new LambdaQueryWrapper<>();
            dtbBookPurchaseCodeWrapper.eq(DtbBookPurchaseCode::getState, DutpConstant.NUM_ONE);
            dtbBookPurchaseCodeWrapper.eq(DtbBookPurchaseCode::getBookId, dtbBookOrder.getBookId());
            dtbBookPurchaseCodeWrapper.isNull(DtbBookPurchaseCode::getExchangeDate);
            DtbBookPurchaseCode dtbBookPurchaseCode = dtbBookPurchaseCodeMapper.selectOne(dtbBookPurchaseCodeWrapper);

            DtbBookOrderCode dtbBookOrderCode = new DtbBookOrderCode();
            // 订单ID
            dtbBookOrderCode.setOrderId(dtbBookOrder.getOrderId());
            // 订单明细ID
            dtbBookOrderCode.setOrderItemId(dtbBookOrderItem.getOrderItemId());
            // 订单明细ID
            dtbBookOrderCode.setCodeId(dtbBookPurchaseCode.getCodeId());
            // 教材ID
            dtbBookOrderCode.setBookId(dtbBookPurchaseCode.getBookId());

            isResult = dtbBookOrderCodeMapper.insert(dtbBookOrderCode) > 0;

            if(!isResult){
                throw new RuntimeException(DutpConstant.ADDITION_FAILED);
            }
        }
        return isResult;
    }
    /**
     * 学生教师端修改订单列表
     *
     * @param dtbBookOrder 对象
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOrder(DtbBookOrder dtbBookOrder){
        boolean isResult = false;
        if (ObjectUtil.isNotEmpty(dtbBookOrder)) {
            isResult = dtbBookOrderMapper.updateById(dtbBookOrder) > 0;
            if(!isResult){
                throw new RuntimeException(DutpConstant.UPDATE_FAILED);
            }
        }
        return isResult;
    }
    private List<Tree<String>> getTreeNode(List<DtbBookOrderItem> bookDetail) {
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setChildrenKey("children");
        List<Tree<String>> treeNodes = TreeUtil.build(bookDetail,"0",treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getBookId().toString());
                    tree.setParentId(treeNode.getMasterBookId() == null ? "0": treeNode.getMasterBookId().toString());
                    tree.setName(treeNode.getBookName());

                    tree.putExtra("bookName",treeNode.getBookName());
                    tree.putExtra("isbn",treeNode.getIsbn());
                    tree.putExtra("issn",treeNode.getIssn());
                    tree.putExtra("bookQuantity",treeNode.getBookQuantity());
                    tree.putExtra("priceOrderItem",treeNode.getPriceOrderItem());
                    tree.putExtra("discount",treeNode.getDiscount());
                    tree.putExtra("priceSale",treeNode.getPriceSale());
                    tree.putExtra("activationCodeCount",treeNode.getActivationCodeCount());
                    tree.putExtra("isRefund",treeNode.getIsRefund());
                    tree.putExtra("shelfState",treeNode.getShelfState());
                    tree.putExtra("bookId", treeNode.getBookId().toString());
                    tree.putExtra("itemStatus",treeNode.getItemStatus());
                    tree.putExtra("masterFlag",treeNode.getMasterFlag());
                    tree.putExtra("orderItemId",treeNode.getOrderItemId() == null ? "" :treeNode.getOrderItemId().toString());

                });
        return treeNodes;
    }

    /**
     * 学生教师端删除订单
     *
     * @param dtbBookOrder 订单
     * @return 结果
     */
    @Override
    public boolean cancelDtbBookOrder(DtbBookOrder dtbBookOrder)
    {
        LambdaQueryWrapper<DtbBookOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DtbBookOrder::getOrderNo, dtbBookOrder.getOrderNo());
        lambdaQueryWrapper.eq(DtbBookOrder::getOrderId, dtbBookOrder.getOrderId());
        DtbBookOrder res = dtbBookOrderMapper.selectOne(lambdaQueryWrapper);
        if(OrderStatusEnum.PENDING.getCode().equals(res.getOrderStatus())){
            dtbBookOrder.setOrderStatus(OrderStatusEnum.CANCELLED.getCode());
            return this.update(dtbBookOrder,lambdaQueryWrapper);
        }
        return false;
    }
    @Transactional
    public DtbBookOrder addShopOrder(DtbBookOrder dtbBookOrder) {
        dtbBookOrder.setPaymentStatus(OrderPaymentStatusEnum.N.getCode());
        dtbBookOrder.setOrderStatus(OrderStatusEnum.PENDING.getCode());
        String orderNo = orderNumberGenerator();
        dtbBookOrder.setOrderNo(orderNo);
        dtbBookOrder.setCreateUserId(SecurityUtils.getUserId());
        String userName = SecurityUtils.getUsername();
        // 获取原价总和
        if (!CollectionUtils.isEmpty(dtbBookOrder.getBookIdList())) {
            //售价*数量。计算出原价
            BigDecimal total = dtbBookOrder.getBookIdList().stream()
                    .map(product -> product.getPriceSale().multiply(BigDecimal.valueOf(product.getBookQuantity())))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            dtbBookOrder.setPrice(total);
            //改完的单价*数量。计算出订单金额
            if (dtbBookOrder.getOrderType() != 4) {
                BigDecimal payAmount = dtbBookOrder.getBookIdList().stream()
                        .map(product -> (product.getPriceSale().multiply(product.getDiscount().divide(new BigDecimal("100")))).multiply(BigDecimal.valueOf(product.getBookQuantity())))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                dtbBookOrder.setPayAmount(payAmount);
            } else {
                dtbBookOrder.setPayAmount(BigDecimal.ZERO);
            }

        }
        //保存订单表
        this.save(dtbBookOrder);
        //保存审批人
        DtbBookOrderAuditUser dtbBookOrderAuditUser = new DtbBookOrderAuditUser();
        dtbBookOrderAuditUser.setOrderId(dtbBookOrder.getOrderId());
        dtbBookOrderAuditUser.setUserId(dtbBookOrder.getCurrentAuditUserId());
        dtbBookOrderAuditUser.setCreateBy(userName);
        dtbBookOrderAuditUser.setCreateTime(new Date());
        if (dtbBookOrder.getOrderType() != 4) {
            dtbBookOrderAuditUser.setAuditType(1);
        } else {
            dtbBookOrderAuditUser.setAuditType(4);
        }
        dtbBookOrderAuditUser.setUpdateBy(userName);
        dtbBookOrderAuditUser.setUpdateTime(new Date());
        dtbBookOrderAuditUserMapper.insert(dtbBookOrderAuditUser);
        // 更新订单表的审批人
        dtbBookOrder.setCurrentAuditUserId(dtbBookOrderAuditUser.getAuditUserId());
        this.updateById(dtbBookOrder);
        //保存订单明细
        if (!CollectionUtils.isEmpty(dtbBookOrder.getBookIdList())) {
            dtbBookOrder.getBookIdList().stream().forEach(e -> {
                e.setOrderId(dtbBookOrder.getOrderId());
                e.setSchoolId(dtbBookOrder.getSchoolId());
                if (dtbBookOrder.getOrderType() != 4) {
                    e.setPriceOrderItem(e.getPriceSale().multiply(e.getDiscount().divide(new BigDecimal("100"))));
                } else {
                    e.setPriceOrderItem(BigDecimal.ZERO);
                }
                e.setItemStatus(OrderItemStatusEnum.NORMAL.getCode());
                e.setCreateBy(userName);
                e.setCreateTime(new Date());
                e.setUpdateBy(userName);
                e.setUpdateTime(new Date());
            });
            dtbBookOrderItemService.saveBatch(dtbBookOrder.getBookIdList());
        }
        R<List<SysUser>> adminIdList = remoteUserService.listUserByRoleId(2L, SecurityConstants.FROM_SOURCE);
        if (R.FAIL == adminIdList.getCode())
        {
            throw new ServiceException(adminIdList.getMsg());
        }
        List<SysUser> receiveList = adminIdList.getData();
        // 发送消息
        if (dtbBookOrder.getOrderType() == 4) {
            // 发送样书消息提醒
            String title = "样书订单审核提醒";
            String content = String.format("您好，%s的采购人“%s”发起了订单审核，请及时处理。【电商中心-订单审核】内查看。",dtbBookOrder.getSchoolName(), userName);
            // 发给审核人
            sendOrderMsg(title, content, dtbBookOrderAuditUser.getUserId());
            // 发消息给管理员
//            if (!CollectionUtils.isEmpty(receiveList)) {
//                receiveList.stream().forEach(e -> {
//                    sendOrderMsg(title, content, e.getUserId());
//                    System.out.println(content);
//                });
//            }
        } else {
            // 发送批量采购消息提醒
            String title = "批量采购订单审核提醒";
            String content = String.format("您好，采购人“%s”发起了订单审核，请及时处理。请到[【电商中心-订单审核】内查看。", userName);
            // 发给审核人
            sendOrderMsg(title, content, dtbBookOrderAuditUser.getUserId());
            // 发消息给管理员
//            if (!CollectionUtils.isEmpty(receiveList)) {
//                receiveList.stream().forEach(e -> {
//                    sendOrderMsg(title, content, e.getUserId());
//                });
//            }
        }
        return dtbBookOrder;
    }

    public void sendOrderMsg(String title,String content,Long toUserId) {
        boolean isResult = false;
        DutpUserMessage dutpUserMessage = new DutpUserMessage();
        dutpUserMessage.setContent(content);
        dutpUserMessage.setTitle(title);
        // 发送者
        dutpUserMessage.setFromUserId(SecurityUtils.getUserId());
        // 接收者
        dutpUserMessage.setToUserId(toUserId);
        // 1系统消息2教务消息3推送消息
        dutpUserMessage.setMessageType(1);
        // 用户类型1后台用户2前台用户
        dutpUserMessage.setFromUserType(1);
        // 用户类型1后台用户2前台用户
        dutpUserMessage.setToUserType(1);
        R<Boolean> booleanR = remoteUserMessageService.addMessage(dutpUserMessage);
        if (booleanR.getCode() == 500) {
            isResult = false;
            throw new ServiceException("Message模块未启动，无法发送消息，订单提交失败！");
        }
    }

    public String orderNumberGenerator() {
        Date now = new Date();
        String formattedDate = new SimpleDateFormat(DutpConstant.DATE_FORMAT).format(now);
        LambdaQueryWrapper<DtbBookOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.likeRight(DtbBookOrder::getOrderNo, formattedDate);
        Integer sequenceNumber = dtbBookOrderMapper.selectCount(lambdaQueryWrapper);
        ++sequenceNumber;
        return formattedDate + DutpConstant.BUSINESS_CODE + String.format("%06d", sequenceNumber);
    }

    public String orderRefundNumberGenerator() {
        Date now = new Date();
        String formattedDate = new SimpleDateFormat(DutpConstant.DATE_FORMAT).format(now);
        LambdaQueryWrapper<DtbBookRefundOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.likeRight(DtbBookRefundOrder::getRefundOrderNo, formattedDate);
        Integer sequenceNumber = dtbBookRefundOrderMapper.selectCount(lambdaQueryWrapper);
        ++sequenceNumber;
        return formattedDate + DutpConstant.BUSINESSTD_CODE + String.format("%06d", sequenceNumber);
    }

    /**
     * 订单审核列表
     *
     * @param dto 查询信息
     * @return 列表信息
     */
    @Override
    public List<BookOrderVo> getOrderReview(BookOrderDto dto) {
        // 订单审核列表
        List<BookOrderVo> list = dtbBookOrderMapper.getOrderReview(dto);
        if (list != null) {
            for (BookOrderVo bookOrderVo : list) {
                // 根据订单id，统计所有子订单的数量，计算出订单总的采购数量
                Integer quantity = dtbBookOrderMapper.getOrderQuantityByOrderId(bookOrderVo.getOrderId());
                bookOrderVo.setPurchaseQuantity(quantity);
            }
        }
        return list;
    }

	@Override
    @Transactional
    public Boolean updateShopOrderStatus(DtbBookOrder dtbBookOrder) {
        QueryWrapper<DtbBookOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DtbBookOrder::getOrderId,dtbBookOrder.getOrderId());
        DtbBookOrder order = dtbBookOrderMapper.selectOne(queryWrapper);
        if (ObjectUtil.isEmpty(order)) {
            throw new ServiceException("未找到该笔订单");
        } else {
            if (dtbBookOrder.getOrderStatus().equals(order.getOrderStatus())) {
                // 取消订单
                if (dtbBookOrder.getOrderStatus().equals(OrderStatusEnum.CANCELLED.getCode())) {
                    throw new ServiceException("该笔订单已取消，不可重复取消");
                }
                // 取消审批
                if (dtbBookOrder.getOrderStatus().equals(OrderStatusEnum.CANCELLED.getCode())) {
                    throw new ServiceException("该笔订单已取消审批，不可重复取消审批");
                }
            }
            dtbBookOrder.setUpdateBy(SecurityUtils.getUsername());
            dtbBookOrder.setUpdateTime(new Date());
            dtbBookOrderMapper.updateById(dtbBookOrder);
            if (dtbBookOrder.getState() == 5) {
                // 取消订单，更改审批人状态
                UpdateWrapper<DtbBookOrderAuditUser> updateAuditUserWrapper = new UpdateWrapper<>();
                updateAuditUserWrapper.lambda().eq(DtbBookOrderAuditUser::getOrderId,dtbBookOrder.getOrderId())
                        .eq(DtbBookOrderAuditUser::getAuditType,1)
                        .set(DtbBookOrderAuditUser::getAuditStatus,3)
                        .set(DtbBookOrderAuditUser::getUpdateTime,new Date())
                        .set(DtbBookOrderAuditUser::getUpdateBy,SecurityUtils.getUsername());
                dtbBookOrderAuditUserMapper.update(null,updateAuditUserWrapper);
            }
            if (dtbBookOrder.getState() == 6) {
                // 取消审批
                // 冻结的购书码释放
                List<DtbBookPurchaseCode> frozenCodeList = dtbBookPurchaseCodeMapper.selectByOrderId(dtbBookOrder.getOrderId());
                if(!CollectionUtils.isEmpty(frozenCodeList)) {
                    List<Long> idList = frozenCodeList.stream().filter(e -> e.getIsFrozen() == 1).collect(Collectors.toList()).stream().map(DtbBookPurchaseCode::getCodeId).collect(Collectors.toList());
                    // 当已经驳回的情况下，没有冻结的购书码，不用解冻
                    // 只有还没审批的情况下，才将冻结的购书码释放
                    if (!idList.isEmpty()) {
                        UpdateWrapper<DtbBookPurchaseCode> updateWrapper = new UpdateWrapper<>();
                        updateWrapper.lambda().in(DtbBookPurchaseCode::getCodeId, idList)
                                .set(DtbBookPurchaseCode::getIsFrozen, 0)
                                .set(DtbBookPurchaseCode::getUpdateBy, SecurityUtils.getUsername())
                                .set(DtbBookPurchaseCode::getUpdateTime, new Date());
                        dtbBookPurchaseCodeMapper.update(null, updateWrapper);
                    }
                }
                UpdateWrapper<DtbBookOrderAuditUser> updateAuditUserWrapper = new UpdateWrapper<>();
                updateAuditUserWrapper.lambda().eq(DtbBookOrderAuditUser::getOrderId,dtbBookOrder.getOrderId())
                        .set(DtbBookOrderAuditUser::getAuditStatus,3)
                        .set(DtbBookOrderAuditUser::getUpdateTime,new Date())
                        .set(DtbBookOrderAuditUser::getUpdateBy,SecurityUtils.getUsername());
                // 取消申请，更改对应的审批状态为取消
                if (order.getOrderStatus().equals(OrderStatusEnum.EDITING.getCode()) || order.getOrderStatus().equals(OrderStatusEnum.EDIT_REFUSED.getCode())) {
                    updateAuditUserWrapper.lambda().eq(DtbBookOrderAuditUser::getAuditType,2);

                }
                if (order.getOrderStatus().equals(OrderStatusEnum.CANCELING.getCode())  || order.getOrderStatus().equals(OrderStatusEnum.CANCEL_REFUSED.getCode())) {
                    updateAuditUserWrapper.lambda().eq(DtbBookOrderAuditUser::getAuditType,3);
                }
                dtbBookOrderAuditUserMapper.update(null,updateAuditUserWrapper);
                // 作废的取消审批的时候，如果有申请作废状态的子订单，改为正常
                // 根据订单id查询子订单列表
                List<DtbBookOrderItemVo> oldOrderItem = dtbBookOrderItemMapper.getOrderItemByOrderId(dtbBookOrder.getOrderId());
                if (!oldOrderItem.isEmpty()) {
                    for (DtbBookOrderItemVo dtbBookOrderItemVo : oldOrderItem) {
                        if ("canceling".equals(dtbBookOrderItemVo.getItemStatus())) {
                            // 如果有申请作废状态的子订单，改为正常
                            UpdateWrapper<DtbBookOrderItem> updateWrapper = new UpdateWrapper<>();
                            updateWrapper.lambda().set(DtbBookOrderItem::getItemStatus, "normal");
                            updateWrapper.lambda().eq(DtbBookOrderItem::getOrderItemId, dtbBookOrderItemVo.getOrderItemId());
                            dtbBookOrderItemMapper.update(null, updateWrapper);
                        }
                    }
                }
            }
        }
        return null;
    }

    public void updateOrderByState(DtbBookOrder dtbBookOrder,DtbBookOrder order,Integer state) {
        DtbBookOrder updateOrder = new DtbBookOrder();
        updateOrder.setOrderId(dtbBookOrder.getOrderId());
        if (dtbBookOrder.getState() == 2) {
            // 修改订单 对订单数据只更新状态，修改的数据待审核通过后更新，冻结书籍数量的购书码数量
            updateOrder.setOrderStatus(OrderStatusEnum.EDITING.getCode());
            DtbBookOrderAuditUser auditUser = updateEditOrder(dtbBookOrder,order,updateOrder,state);
            // 消息发送
            String title = "批量采购订单审核提醒";
            String content = String.format("您好，采购人“%s”发起了修改订单审核，请及时处理。【电商中心-订单审核】内查看。", SecurityUtils.getUsername());
            // 发给审核人
            sendOrderMsg(title, content, auditUser.getUserId());
            // 发消息给管理员
            R<List<SysUser>> adminIdList = remoteUserService.listUserByRoleId(2L, SecurityConstants.FROM_SOURCE);
            if (R.FAIL == adminIdList.getCode())
            {
                throw new ServiceException(adminIdList.getMsg());
            }
            List<SysUser> receiveList = adminIdList.getData();
            Long userId = auditUser.getUserId();
            if (!CollectionUtils.isEmpty(receiveList)) {
                receiveList.removeIf(e -> e.getUserId().equals(userId));
                receiveList.stream().forEach(e -> {
                    sendOrderMsg(title, content, e.getUserId());
                });
            }
        } else if (dtbBookOrder.getState() == 3) {
            // 原订单状态为作废审核/作废驳回时，对订单数据更新状态和订单详情中书籍的状态，冻结购书码
            updateOrder.setOrderStatus(OrderStatusEnum.CANCELING.getCode());
            updateOrder.setRemark(dtbBookOrder.getRemark());
            DtbBookOrderAuditUser auditUser = updateCancelOrder(dtbBookOrder,order,updateOrder,dtbBookOrder.getState());
            // 消息发送
            String title = "批量采购订单审核提醒";
            String content = String.format("您好，采购人“%s”发起了作废订单审核，请及时处理。【电商中心-订单审核】内查看。", SecurityUtils.getUsername());
            // 发给审核人
            sendOrderMsg(title, content, auditUser.getUserId());
            // 发消息给管理员
            R<List<SysUser>> adminIdList = remoteUserService.listUserByRoleId(2L, SecurityConstants.FROM_SOURCE);
            if (R.FAIL == adminIdList.getCode())
            {
                throw new ServiceException(adminIdList.getMsg());
            }
            List<SysUser> receiveList = adminIdList.getData();
            Long userId = auditUser.getUserId();
            if (!CollectionUtils.isEmpty(receiveList)) {
                receiveList.removeIf(e -> e.getUserId().equals(userId));
                receiveList.stream().forEach(e -> {
                    sendOrderMsg(title, content, e.getUserId());
                });
            }
        } else if (dtbBookOrder.getState() == 4) {
            DtbBookOrderAuditUser auditUser = new DtbBookOrderAuditUser();
            // 修改申请 对审核记录数据存在待审核状态的做更新处理，否则新增数据
            //原订单状态为采购确认/驳回时，数据做更新处理
            if (order.getOrderStatus().equals(OrderStatusEnum.PENDING.getCode()) || order.getOrderStatus().equals(OrderStatusEnum.CREATE_REFUSED.getCode())) {
                updateOrder.setOrderStatus(OrderStatusEnum.PENDING.getCode());
                auditUser = updatePendingOrder(dtbBookOrder,order,updateOrder,dtbBookOrder.getState());
            }
            //原订单状态为修改审核/修改驳回时，对订单数据只更新状态，修改的数据待审核通过后更新，冻结书籍数量的购书码数量
            if (order.getOrderStatus().equals(OrderStatusEnum.EDITING.getCode()) || order.getOrderStatus().equals(OrderStatusEnum.EDIT_REFUSED.getCode())) {
                updateOrder.setOrderStatus(OrderStatusEnum.EDITING.getCode());
                auditUser = updateEditOrder(dtbBookOrder,order,updateOrder,dtbBookOrder.getState());
            }
            //原订单状态为作废审核/作废驳回时，对订单数据只更新状态和订单详情中书籍的状态，
            if (order.getOrderStatus().equals(OrderStatusEnum.CANCELING.getCode()) || order.getOrderStatus().equals(OrderStatusEnum.CANCEL_REFUSED.getCode())) {
                updateOrder.setOrderStatus(OrderStatusEnum.CANCELING.getCode());
                auditUser = updateCancelOrder(dtbBookOrder,order,updateOrder,dtbBookOrder.getState());
            }
            // 消息发送
            String title = "批量采购订单审核提醒";
            String content = String.format("您好，采购人“%s”发起了修改订单审核，请及时处理。【电商中心-订单审核】内查看。", SecurityUtils.getUsername());
            // 发给审核人
            sendOrderMsg(title, content, auditUser.getUserId());
            // 发消息给管理员
            R<List<SysUser>> adminIdList = remoteUserService.listUserByRoleId(2L, SecurityConstants.FROM_SOURCE);
            if (R.FAIL == adminIdList.getCode())
            {
                throw new ServiceException(adminIdList.getMsg());
            }
            Long userId = auditUser.getUserId();
            List<SysUser> receiveList = adminIdList.getData();
            if (!CollectionUtils.isEmpty(receiveList)) {
                receiveList.removeIf(e -> e.getUserId().equals(userId));
                receiveList.stream().forEach(e -> {
                    sendOrderMsg(title, content, e.getUserId());
                });
            }
        }

    }

    private DtbBookOrder getDiff(DtbBookOrder dtbBookOrder,DtbBookOrder order,DtbBookOrder updateOrder){
        if (dtbBookOrder.getMerchantId() != null && !dtbBookOrder.getMerchantId().equals(order.getMerchantId())) {
            updateOrder.setMerchantId(dtbBookOrder.getMerchantId());
        }

        if (!dtbBookOrder.getSchoolId().equals(order.getSchoolId())) {
            updateOrder.setSchoolId(dtbBookOrder.getSchoolId());
        }

        if (!dtbBookOrder.getOperatorId().equals(order.getOperatorId())) {
            updateOrder.setOperatorId(dtbBookOrder.getOperatorId());
        }
        if (dtbBookOrder.getUserId() != null && !dtbBookOrder.getUserId().equals(order.getUserId())) {
            updateOrder.setUserId(dtbBookOrder.getUserId());
        }

        // 获取原价总和
        if (!CollectionUtils.isEmpty(dtbBookOrder.getBookIdList())) {
            updateOrder.setBookIdList(dtbBookOrder.getBookIdList());
            //售价*数量。计算出原价
            BigDecimal total = dtbBookOrder.getBookIdList().stream()
                    .map(product -> product.getPriceSale().multiply(BigDecimal.valueOf(product.getBookQuantity())))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            updateOrder.setPrice(total);
            //改完的单价*数量。计算出订单金额
            BigDecimal payAmount = dtbBookOrder.getBookIdList().stream()
                    .map(product -> (product.getPriceSale().multiply(product.getDiscount().divide(new BigDecimal("100")))).multiply(BigDecimal.valueOf(product.getBookQuantity())))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            updateOrder.setPayAmount(payAmount);
        }
        return updateOrder;
    }

    public DtbBookOrderAuditUser updatePendingOrder(DtbBookOrder dtbBookOrder,DtbBookOrder order,DtbBookOrder updateOrder,Integer state){
        getDiff(dtbBookOrder,order,updateOrder);
        // 更新订单详情
        if (!CollectionUtils.isEmpty(dtbBookOrder.getBookIdList())) {
            QueryWrapper<DtbBookOrderItem> updateWrapper = new QueryWrapper<>();
            updateWrapper.lambda().eq(DtbBookOrderItem::getOrderId,order.getOrderId());
            dtbBookOrderItemService.remove(updateWrapper);
            updateOrder.setBookIdList(dtbBookOrder.getBookIdList());
            dtbBookOrder.getBookIdList().stream().forEach(e -> {
                e.setOrderId(dtbBookOrder.getOrderId());
                e.setSchoolId(dtbBookOrder.getSchoolId());
                e.setPriceOrderItem(e.getPriceSale().multiply(e.getDiscount().divide(new BigDecimal("100"))));
                e.setItemStatus(OrderItemStatusEnum.NORMAL.getCode());
            });
            dtbBookOrderItemService.saveBatch(dtbBookOrder.getBookIdList());
        }
        dtbBookOrderMapper.updateById(updateOrder);
        dtbBookOrder.setPayAmount(updateOrder.getPayAmount());
        dtbBookOrder.setPrice(updateOrder.getPrice());
        DtbBookOrderAuditUser auditUser = updateAuditUser(dtbBookOrder,updateOrder.getOrderStatus(),state);
        return auditUser;
    }

    public DtbBookOrderAuditUser updateEditOrder(DtbBookOrder dtbBookOrder,DtbBookOrder order,DtbBookOrder updateOrder,Integer state){
        getDiff(dtbBookOrder,order,updateOrder);
        DtbBookOrder updateShopListOrder = new DtbBookOrder();
        updateShopListOrder.setOrderId(dtbBookOrder.getOrderId());
        updateShopListOrder.setOrderStatus(updateOrder.getOrderStatus());
        updateShopListOrder.setRemark(dtbBookOrder.getRemark());
        dtbBookOrderMapper.updateById(updateShopListOrder);
        dtbBookOrder.setPayAmount(updateOrder.getPayAmount());
        dtbBookOrder.setPrice(updateOrder.getPrice());
        DtbBookOrderAuditUser auditUser = updateAuditUser(dtbBookOrder,OrderStatusEnum.EDITING.getCode(),state);
        // 获取订单下作废的明细的未绑定的购书码（随机获取修改后的数量的购书码）
        List<DtbBookPurchaseCode> codeList = new ArrayList<>();
        // 获取原订单详情下的详情
        QueryWrapper<DtbBookOrderItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DtbBookOrderItem::getOrderId,dtbBookOrder.getOrderId());
        List<DtbBookOrderItem> oldList = dtbBookOrderItemMapper.selectList(queryWrapper);
        Long quantityDifference = DutpConstant.LONG_ZERO;
        for (DtbBookOrderItem item: dtbBookOrder.getBookIdList()) {
            // 获取数量的差值冻结购书码
            Optional<DtbBookOrderItem> countValue = oldList.stream()
                    .filter(obj -> obj.getOrderItemId().equals(item.getOrderItemId()))
                    .findFirst();
            if (countValue.isPresent()) {
                // 计算bookQuantity差值
                Long oldQuantity = countValue.get().getBookQuantity();  // 获取oldList中的bookQuantity
                Long newQuantity = item.getBookQuantity();  // 获取当前item中的bookQuantity
                // 计算差值
                quantityDifference = oldQuantity - newQuantity;
            }

            // 随机排序取出结果集中指定条数数据
            List<DtbBookPurchaseCode> list = dtbBookPurchaseCodeMapper.selectByOrderItemId(item.getOrderItemId()).stream().sorted((a, b) -> new Random().nextInt())
                    .limit(quantityDifference)
                    .collect(Collectors.toList());
            codeList.addAll(list);
        }
        if (!CollectionUtils.isEmpty(codeList)) {
            List<Long> codeIdList = codeList.stream().map(DtbBookPurchaseCode::getCodeId).collect(Collectors.toList());
            // 冻结购书码
            UpdateWrapper<DtbBookPurchaseCode> updatePurchaseCodeWrapper = new UpdateWrapper<>();
            updatePurchaseCodeWrapper.lambda().in(DtbBookPurchaseCode::getCodeId,codeIdList);
            updatePurchaseCodeWrapper.lambda().set(DtbBookPurchaseCode::getIsFrozen, 1);
            updatePurchaseCodeWrapper.lambda().set(DtbBookPurchaseCode::getUpdateTime, new Date());
            updatePurchaseCodeWrapper.lambda().set(DtbBookPurchaseCode::getUpdateBy, SecurityUtils.getUsername());
            dtbBookPurchaseCodeMapper.update(null, updatePurchaseCodeWrapper);
        }
        return auditUser;
    }

    public DtbBookOrderAuditUser updateCancelOrder(DtbBookOrder dtbBookOrder,DtbBookOrder order,DtbBookOrder updateOrder,Integer state){
        getDiff(dtbBookOrder,order,updateOrder);
        DtbBookOrder updateShopListOrder = new DtbBookOrder();
        updateShopListOrder.setOrderId(dtbBookOrder.getOrderId());
        updateShopListOrder.setOrderStatus(updateOrder.getOrderStatus());
        dtbBookOrderMapper.updateById(updateShopListOrder);
        // 更新订单状态,冻结购书码
        if (!CollectionUtils.isEmpty(dtbBookOrder.getCancelList())) {
            updateOrder.setCancelList(dtbBookOrder.getCancelList());
            // 作废的书籍
            List<Long> bookIdList = dtbBookOrder.getCancelList().stream().map(DtbBookOrderItem::getBookId).collect(Collectors.toList());
            // 订单的明细
            List<Long> orderItemId = dtbBookOrder.getCancelList().stream().map(DtbBookOrderItem::getOrderItemId).collect(Collectors.toList());
            UpdateWrapper<DtbBookOrderItem> itemUpdateWrapper = new UpdateWrapper<>();
            itemUpdateWrapper.lambda().in(DtbBookOrderItem::getBookId, bookIdList);
            itemUpdateWrapper.lambda().in(DtbBookOrderItem::getOrderItemId, orderItemId);
            itemUpdateWrapper.lambda().set(DtbBookOrderItem::getItemStatus, OrderItemBookStatusEnum.CANCELING.getCode());
            itemUpdateWrapper.lambda().set(DtbBookOrderItem::getUpdateBy, SecurityUtils.getUsername());
            itemUpdateWrapper.lambda().set(DtbBookOrderItem::getUpdateTime, new Date());
            dtbBookOrderItemMapper.update(null, itemUpdateWrapper);
            // 冻结购书码
            List<Long> canIds = dtbBookOrder.getCancelList().stream().map(DtbBookOrderItem::getOrderItemId).collect(Collectors.toList());
            List<DtbBookPurchaseCode> codeList = dtbBookPurchaseCodeMapper.selectByOrderItemIds(canIds);
            List<Long> codeIdList = codeList.stream().map(DtbBookPurchaseCode::getCodeId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(codeList)) {
                UpdateWrapper<DtbBookPurchaseCode> updatePurchaseCodeWrapper = new UpdateWrapper<>();
                updatePurchaseCodeWrapper.lambda().in(DtbBookPurchaseCode::getCodeId,codeIdList)
                        .set(DtbBookPurchaseCode::getIsFrozen, 1)
                        .set(DtbBookPurchaseCode::getUpdateBy,SecurityUtils.getUsername())
                        .set(DtbBookPurchaseCode::getUpdateTime,new Date());
                dtbBookPurchaseCodeMapper.update(null,updatePurchaseCodeWrapper);
            }
        }
        dtbBookOrder.setPayAmount(updateOrder.getPayAmount());
        dtbBookOrder.setPrice(updateOrder.getPrice());
        DtbBookOrderAuditUser auditUser = updateAuditUser(dtbBookOrder,updateOrder.getOrderStatus(),state);
        return auditUser;
    }

    public DtbBookOrderAuditUser updateAuditUser(DtbBookOrder updateOrder,String orderStatus,Integer state){
        QueryWrapper<DtbBookOrderAuditUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DtbBookOrderAuditUser::getOrderId, updateOrder.getOrderId());
        // 1订单详情2修改订单3作废订单4修改申请5取消订单6取消申请
        if (state == 2 || state == 4) {
            queryWrapper.lambda().eq(DtbBookOrderAuditUser::getAuditType, 2);
        }
        if (state == 3) {
            queryWrapper.lambda().eq(DtbBookOrderAuditUser::getAuditType, 3);
        }
        DtbBookOrderAuditUser auditUser = dtbBookOrderAuditUserMapper.selectOne(queryWrapper);
        if (ObjectUtil.isEmpty(auditUser)) {
            auditUser = new DtbBookOrderAuditUser();
            auditUser.setOrderId(updateOrder.getOrderId());
            auditUser.setUserId(getUserIdByAuditId(updateOrder.getAreaId()));
            if (OrderStatusEnum.PENDING.getCode().equals(orderStatus)) {
                auditUser.setAuditType(1);
            }
            if (OrderStatusEnum.EDITING.getCode().equals(orderStatus)) {
                auditUser.setAuditType(2);
            }
            if (OrderStatusEnum.CANCELING.getCode().equals(orderStatus)) {
                auditUser.setAuditType(3);
            }
            auditUser.setAuditStatus(0);
            auditUser.setEditContent(JSON.toJSONString(updateOrder));
            dtbBookOrderAuditUserMapper.insert(auditUser);
            // 更新审核信息
            UpdateWrapper<DtbBookOrder> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(DtbBookOrder::getOrderId, updateOrder.getOrderId())
                    .set(DtbBookOrder::getCurrentAuditUserId, auditUser.getAuditUserId())
                    .set(DtbBookOrder::getUpdateBy, SecurityUtils.getUsername())
                    .set(DtbBookOrder::getUpdateTime,new Date());
            dtbBookOrderMapper.update(null, updateWrapper);
            return auditUser;
        } else {
            auditUser.setAuditStatus(0);
            auditUser.setEditContent(JSON.toJSONString(updateOrder));
            UpdateWrapper<DtbBookOrderAuditUser> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(DtbBookOrderAuditUser::getAuditUserId, auditUser.getAuditUserId());
            dtbBookOrderAuditUserMapper.update(auditUser, updateWrapper);
//            dtbBookOrderAuditUserMapper.updateById(auditUser);
            return auditUser;
        }
    }

    private Long getUserIdByAuditId(Long areaId){
        QueryWrapper<DutpSaleArea> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DutpSaleArea::getAreaId, areaId)
                .eq(DutpSaleArea::getDelFlag, 0);
        DutpSaleArea saleArea = dutpSaleAreaMapper.selectOne(queryWrapper);
        return saleArea.getManagerId();
    }


    @Override
    @Transactional
    public Boolean updateShopOrder(DtbBookOrder dtbBookOrder) {
        QueryWrapper<DtbBookOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DtbBookOrder::getOrderId,dtbBookOrder.getOrderId());
        DtbBookOrder order = dtbBookOrderMapper.selectOne(queryWrapper);
        if (ObjectUtil.isEmpty(order)) {
            throw new ServiceException("未找到该笔订单");
        } else {
            updateOrderByState(dtbBookOrder,order,dtbBookOrder.getState());
        }
        return null;
    }

    @Override
    public TableDataInfo getBatchOrderList(BookOrderDto dto) {
        // 批量采购
        if (dto.getPageNum() != 1) {
            dto.setPageSize((dto.getPageNum() - 1) * dto.getPageSize());
        }
        List<BookOrderVo> list = dtbBookOrderMapper.getBatchOrderList(dto);
        if (!list.isEmpty()) {
            list.forEach(item -> {
                // 根据orderId查询当前订单的子订单是否被全部作废或者全部申请作废
                Integer itemIsAllVoid = dtbBookPurchaseCodeMapper.selectItemIsAllVoid(item.getOrderId());
                // 子订单是否全部被作废：1已全部作废
                item.setItemIsAllVoid(itemIsAllVoid);
            });
        }
        PageInfo<BookOrderVo> pageInfo = new PageInfo<>(list);
        TableDataInfo info = new TableDataInfo();
        info.setTotal(pageInfo.getTotal());
        info.setRows(pageInfo.getList());
        return info;
    }

    /**
     * 采购审核驳回
     *
     * @param orderReviewDto 驳回意见
     * @return 状态
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean purchaseApprovalReject(OrderReviewDto orderReviewDto) {
        boolean isResult;
        // 更新订单审核人表，审核状态改为2驳回，并添加审核备注
        orderReviewDto.setUpdateBy(SecurityUtils.getUsername());
        orderReviewDto.setUpdateTime(new Date());
        orderReviewDto.setAuditDate(new Date());
        isResult = dtbBookOrderAuditUserMapper.purchaseApprovalRejectAuditUser(orderReviewDto);
        // 更新订单表的订单状态为采购驳回：create_refused
        isResult = dtbBookOrderMapper.purchaseApprovalReject(orderReviewDto);
        DutpUserMessage dutpUserMessage = new DutpUserMessage();
        dutpUserMessage.setContent("您好，审批人:" + SecurityUtils.getUsername() + "已驳回你的采购申请，驳回原因：" + orderReviewDto.getAuditContent() + "。【订单号：" + orderReviewDto.getOrderNo() + "】");
        dutpUserMessage.setTitle("批量采购订单审核提醒");
        // 发送者
        dutpUserMessage.setFromUserId(orderReviewDto.getUserId());
        // 接收者
        dutpUserMessage.setToUserId(orderReviewDto.getToUserId());
        // 1系统消息2教务消息3推送消息
        dutpUserMessage.setMessageType(1);
        // 用户类型1后台用户2前台用户
        dutpUserMessage.setFromUserType(1);
        // 用户类型1后台用户2前台用户
        dutpUserMessage.setToUserType(1);
        R<Boolean> booleanR = remoteUserMessageService.addMessage(dutpUserMessage);
        System.out.println(booleanR.getData());
        if (booleanR.getCode() == 500) {
            log.error("Message模块未启动，无法发送消息，驳回失败！");
            isResult = false;
        }
        if (!isResult) {
            throw new RuntimeException(DutpConstant.REJECT_FAILED);
        }
        return isResult;
    }

    /**
     * 采购审核通过
     *
     * @param orderReviewDto 通过数据
     * @return 状态
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean purchaseApprovalPass(OrderReviewDto orderReviewDto) {
        boolean isResult = true;
        // 判断当前订单内教材的购书码是否充足
        for (DtbBookOrderItemVo dtbBookOrderItemVo : orderReviewDto.getPurchaseApprovalList()) {
            // 查询当前书籍的购书码id的list，和采购数量进行比较
            List<Long> codeList = dtbBookPurchaseCodeMapper.selectCodeIdListByBookId(dtbBookOrderItemVo.getBookId());
            if (null != codeList && dtbBookOrderItemVo.getBookQuantity() > codeList.size()) {
                // 只要有一本书不满足，进程直接终止，并且提示采购的书籍数量不够。
                throw new RuntimeException("购书码数量不足,请补充购书码后再进行审批!");
            }
        }
        // 向code与order的关系表（订单下的购书码表）中插入数据
        for (DtbBookOrderItemVo bookOrderItemVo : orderReviewDto.getPurchaseApprovalList()) {
            if (bookOrderItemVo.getBookQuantity() > 0) {
                // 查询当前书籍的购书码id的集合，插入code与order的关系表
                List<Long> purchaseCodeList = dtbBookPurchaseCodeMapper.selectCodeIdListByBookId(bookOrderItemVo.getBookId());
                List<Long> purchaseList = purchaseCodeList.subList(0, bookOrderItemVo.getBookQuantity());
                for (Long purchaseCode : purchaseList) {
                    DtbBookOrderCode dtbBookOrderCode = new DtbBookOrderCode();
                    dtbBookOrderCode.setOrderId(bookOrderItemVo.getOrderId());
                    dtbBookOrderCode.setOrderItemId(bookOrderItemVo.getOrderItemId());
                    dtbBookOrderCode.setCodeId(purchaseCode);
                    dtbBookOrderCode.setBookId(bookOrderItemVo.getBookId());
                    dtbBookOrderCode.setSchoolId(bookOrderItemVo.getSchoolId());
                    dtbBookOrderCode.setCreateBy(SecurityUtils.getUsername());
                    dtbBookOrderCode.setCreateTime(new Date());
                    dtbBookOrderCodeMapper.insert(dtbBookOrderCode);
                }
                // 将当前这本书的id的list中的购书码的状态改为2已绑定未兑换
                isResult = dtbBookPurchaseCodeMapper.cancelCodeByCodeIdList(SecurityUtils.getUsername(), purchaseList);
            }
        }
        orderReviewDto.setUpdateBy(SecurityUtils.getUsername());
        orderReviewDto.setUpdateTime(new Date());
        orderReviewDto.setAuditDate(new Date());
        // 更新订单审核人表，审核状态改为1通过
        isResult = dtbBookOrderAuditUserMapper.purchaseApprovalRejectAuditUser(orderReviewDto);
        // 更新订单表的订单状态为结算中：settlement
        isResult = dtbBookOrderMapper.purchaseApprovalReject(orderReviewDto);
        DutpUserMessage dutpUserMessage = new DutpUserMessage();
        dutpUserMessage.setContent("您好，您的采购申请已审核通过，【订单号：" + orderReviewDto.getOrderNo() + "】");
        dutpUserMessage.setTitle("批量采购订单审核提醒");
        // 发送者
        dutpUserMessage.setFromUserId(orderReviewDto.getUserId());
        // 接收者
        dutpUserMessage.setToUserId(orderReviewDto.getToUserId());
        // 1系统消息2教务消息3推送消息
        dutpUserMessage.setMessageType(1);
        // 用户类型1后台用户2前台用户
        dutpUserMessage.setFromUserType(1);
        // 用户类型1后台用户2前台用户
        dutpUserMessage.setToUserType(1);
        R<Boolean> booleanR = remoteUserMessageService.addMessage(dutpUserMessage);
        if (booleanR.getCode() == 500) {
            isResult = false;
            log.error("Message模块未启动，无法发送消息，审批失败！");
        }
        if (!isResult) {
            throw new RuntimeException(DutpConstant.PASS_FAILED);
        }
        return isResult;
    }

    /**
     * 电商中心订单申请售后
     */
    @Override
    @Transactional
    public DtbBookOrder addShopOrderRefund(DtbBookOrder dtbBookOrder) {
        if (!CollectionUtils.isEmpty(dtbBookOrder.getRefundBookList())){
            QueryWrapper<DtbBookRefundOrder> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(DtbBookRefundOrder::getOrderId,dtbBookOrder.getOrderId());
            DtbBookRefundOrder refund = dtbBookRefundOrderMapper.selectOne(queryWrapper);
            if (!ObjectUtil.isEmpty(refund)) {
                UpdateWrapper<DtbBookRefundOrder> updateWrapper = new UpdateWrapper<>();
                updateWrapper.lambda().eq(DtbBookRefundOrder::getOrderId,dtbBookOrder.getOrderId())
                        .set(DtbBookRefundOrder::getRefundStatus,0).set(DtbBookRefundOrder::getApplyUserId,SecurityUtils.getUserId())
                        .set(DtbBookRefundOrder::getApplyResource,RefundApplyTypeEnum.PAID.getCode())
                        .set(DtbBookRefundOrder::getRefundAmount,dtbBookOrder.getPayAmount())
                        .set(DtbBookRefundOrder::getCreateTime,new Date())
                        .set(DtbBookRefundOrder::getRemark,dtbBookOrder.getRefundRemark())
                        .set(DtbBookRefundOrder::getSchoolId,dtbBookOrder.getSchoolId())
                        .set(DtbBookRefundOrder::getUpdateBy,SecurityUtils.getUsername())
                        .set(DtbBookRefundOrder::getUpdateTime,new Date());
                dtbBookRefundOrderMapper.update(null,updateWrapper);
            } else {
                // 保存售后退款订单
                refund = new DtbBookRefundOrder();
                refund.setOrderId(dtbBookOrder.getOrderId());
                refund.setRefundStatus(0);
                refund.setApplyUserId(SecurityUtils.getUserId());
                refund.setApplyResource(RefundApplyTypeEnum.PAID.getCode());
                String no = orderRefundNumberGenerator();
                refund.setRefundOrderNo(no);
                refund.setRefundAmount(dtbBookOrder.getPayAmount());
                refund.setRemark(dtbBookOrder.getRefundRemark());
                refund.setSchoolId(dtbBookOrder.getSchoolId());
                dtbBookRefundOrderMapper.insert(refund);

                // 保存售后退款明细
                List<DtbBookRefundOrderItem> list = new ArrayList<>();
                DtbBookRefundOrder finalRefund = refund;
                dtbBookOrder.getRefundBookList().stream().forEach(e -> {
                    DtbBookRefundOrderItem item = new DtbBookRefundOrderItem();
                    item.setRefundOrderId(finalRefund.getRefundOrderId());
                    item.setOrderItemId(e.getOrderItemId());
                    item.setBookId(e.getBookId());
                    item.setRefundQuantity(e.getBookQuantity());
                    item.setRefundAmount(e.getPriceOrderItem());
                    item.setCreateBy(SecurityUtils.getUsername());
                    item.setCreateTime(new Date());
                    item.setRemark(dtbBookOrder.getRefundRemark());
                    list.add(item);
                });
                dtbBookRefundOrderItemService.saveBatch(list);
            }

        }
        // 消息发送
        String title = "售后提醒";
        String content = String.format("您好，申请人“%s”发起了售后申请，请及时处理。【电商中心-售后管理】内查看。", SecurityUtils.getUsername());
        // 发消息给管理员
        R<List<SysUser>> adminIdList = remoteUserService.listUserByRoleId(2L, SecurityConstants.FROM_SOURCE);
        if (R.FAIL == adminIdList.getCode())
        {
            throw new ServiceException("根据角色查获取用户列表失败");
        }
        List<SysUser> receiveList = adminIdList.getData();
        // 发送消息给售后管理模块的人
        R<List<SysUser>> menuUserIdList = remoteUserService.listUserByMenuId(2384L, SecurityConstants.FROM_SOURCE);
        if (R.FAIL == menuUserIdList.getCode())
        {
            throw new ServiceException("据菜单获取用户列表失败");
        }
        List<SysUser> menuUserList = menuUserIdList.getData();
        List<SysUser> allUserList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(receiveList)) {
            allUserList.addAll(receiveList);
        }
        if (!CollectionUtils.isEmpty(menuUserList)) {
            allUserList.addAll(menuUserList);
        }
        if (!CollectionUtils.isEmpty(allUserList)) {
            List<SysUser> distinctList = new ArrayList<>(
                    allUserList.stream()
                            .collect(Collectors.toMap(
                                    SysUser::getUserId,
                                    Function.identity(),
                                    (existing, replacement) -> existing
                            ))
                            .values()
            );
            distinctList.stream().forEach(e -> {
                sendOrderMsg(title, content, e.getUserId());
            });
        }
        return dtbBookOrder;
    }

    /**
     * 修改审核查看订单详细
     *
     * @param orderReviewDto 订单信息
     * @return 信息
     */
    @Override
    public DtbBookOrderItemEditApprovalVo selectEditApprovalView(OrderReviewDto orderReviewDto) {
        DtbBookOrderItemEditApprovalVo editApprovalVo = new DtbBookOrderItemEditApprovalVo();
        // 订单信息
        DtbBookOrder dtbBookOrder = dtbBookOrderMapper.selectById(orderReviewDto.getOrderId());
        // 查询订单详细表的List
        List<DtbBookOrderItemVo> oldOrderItem = dtbBookOrderItemMapper.getOrderItemByOrderId(orderReviewDto.getOrderId());
        // 查询修改的子订单数据
        DtbBookOrderAuditUser dtbBookOrderAuditUser = dtbBookOrderAuditUserMapper.selectById(orderReviewDto.getAuditUserId());
        // 解析为 JSONObject
        JSONObject data = JSON.parseObject(dtbBookOrderAuditUser.getEditContent());
        String remark = data.getString("remark");
        List<DtbBookOrderItem> newOrderItem = JSON.parseArray(data.get("bookIdList").toString(), DtbBookOrderItem.class);
        // 原始订单详情数据
        for (DtbBookOrderItemVo oldItemVo : oldOrderItem) {
            // 修改申请提交的数据
            for (DtbBookOrderItem newItem : newOrderItem) {
                // 当两者为同一个子订单时开始比较
                if (newItem.getOrderItemId().equals(oldItemVo.getOrderItemId())) {
                    // 两者折扣不同时,将新的折扣赋值给原始订单详情数据
                    if (newItem.getDiscount().compareTo(oldItemVo.getDiscount()) != 0) {
                        oldItemVo.setDiscount(newItem.getDiscount());
                    }
                    // 两者数量不同时,将新的折扣赋值给原始订单详情数据
                    boolean isEqual = newItem.getBookQuantity() == oldItemVo.getBookQuantity().intValue();
                    if (!isEqual) {
                        oldItemVo.setBookQuantity(Math.toIntExact(newItem.getBookQuantity()));
                    }
                    // 子订单计算合计
                    BigDecimal resultTotal = newItem.getPriceSale()
                            .multiply(BigDecimal.valueOf(newItem.getBookQuantity())) // 数量 * 价格
                            .multiply(newItem.getDiscount()) // * 折扣
                            .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP); // / 100，并四舍五入保留2位
                    oldItemVo.setEditTotal(resultTotal);
                }
            }
        }
        // 订单明细数据
        editApprovalVo.setEditApprovalList(oldOrderItem);
        // 学校
        Long schoolId = Long.valueOf(data.get("schoolId").toString());
        editApprovalVo.setSchoolId(schoolId);
        if (!dtbBookOrder.getSchoolId().equals(schoolId)) {
            // 查学校名称
            DutpSchoolVo dutpSchoolVo = dutpSchoolMapper.selectById(schoolId);
            if (dutpSchoolVo != null) {
                editApprovalVo.setSchoolName(dutpSchoolVo.getSchoolName());
            }
        }
        // 书商，因为代采订单没有书商，需要判断一下
        if (data.get("merchantId") != null && !data.get("merchantId").toString().isEmpty()) {
            Long merchantId = Long.valueOf(data.get("merchantId").toString());
            editApprovalVo.setMerchantId(merchantId);
            if (!dtbBookOrder.getMerchantId().equals(merchantId)) {
                // 查书商名称
                DtbBookMerchant dtbBookMerchant = dtbBookMerchantMapper.selectById(merchantId);
                if (dtbBookMerchant != null) {
                    editApprovalVo.setMerchanName(dtbBookMerchant.getMerchanName());
                }
            }
        }
        // 经办人
        Long operatorId = Long.valueOf(data.get("operatorId").toString());
        editApprovalVo.setOperatorId(operatorId);
        if (!dtbBookOrder.getOperatorId().equals(operatorId)) {
            SysUserVo sysUser = sysUserMapper.selectById(operatorId);
            if (sysUser != null) {
                editApprovalVo.setNickName(sysUser.getNickName());
            }
        }
        // 商品总额
        BigDecimal price = Convert.toBigDecimal(data.get("price"));
        if (dtbBookOrder.getPrice().compareTo(price) != 0) {
            // 新修改前后商品总额的值不同，就存新的值
            editApprovalVo.setPrice(price);
        } else {
            // 新修改前后商品总额的值相同，就存旧的值
            editApprovalVo.setPrice(dtbBookOrder.getPrice());
        }
        // 应付金额
        BigDecimal payAmount = new BigDecimal(data.get("payAmount").toString());
        if (dtbBookOrder.getPayAmount().compareTo(payAmount) != 0) {
            // 新修改前后应付金额的值不同，就存新的值
            editApprovalVo.setPayAmount(payAmount.setScale(2, RoundingMode.HALF_UP));
        } else {
            // 新修改前后应付金额的值相同，就存旧的值
            editApprovalVo.setPayAmount(dtbBookOrder.getPayAmount().setScale(2, RoundingMode.HALF_UP));
        }
        editApprovalVo.setRemark(remark);
        return editApprovalVo;
    }

    /**
     * 修改审核驳回
     *
     * @param orderReviewDto 信息
     * @return 驳回结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateEditApprovalReject(OrderReviewDto orderReviewDto) {
        boolean isResult = true;
        String updateBy = SecurityUtils.getUsername();
        // 根据订单id，查询此订单下所有绑定的购书码id的集合
        List<Long> purchaseCodeList = dtbBookOrderCodeMapper.selectPurchaseCodeByOrderId(orderReviewDto.getOrderId());
        // 将购书码表中id在集合中的且状态为冻结的数据，改为正常
        isResult = dtbBookPurchaseCodeMapper.updateIsFrozenByCodeIdList(updateBy, purchaseCodeList);
        orderReviewDto.setUpdateBy(updateBy);
        orderReviewDto.setUpdateTime(new Date());
        orderReviewDto.setAuditDate(new Date());
        // 更新订单审核人表，审核状态改为2驳回
        isResult = dtbBookOrderAuditUserMapper.purchaseApprovalRejectAuditUser(orderReviewDto);
        // 更新订单表的订单状态为edit_refused修改驳回,并更新商品总额和应付金额
        isResult = dtbBookOrderMapper.purchaseApprovalReject(orderReviewDto);
        DutpUserMessage dutpUserMessage = new DutpUserMessage();
        dutpUserMessage.setContent("您好，审批人:" + SecurityUtils.getUsername() + "已驳回你的修改申请，驳回原因：" + orderReviewDto.getAuditContent() + "。【订单号：" + orderReviewDto.getOrderNo() + "】");
        dutpUserMessage.setTitle("批量采购订单审核提醒");
        // 发送者
        dutpUserMessage.setFromUserId(orderReviewDto.getUserId());
        // 接收者
        dutpUserMessage.setToUserId(orderReviewDto.getToUserId());
        // 1系统消息2教务消息3推送消息
        dutpUserMessage.setMessageType(1);
        // 用户类型1后台用户2前台用户
        dutpUserMessage.setFromUserType(1);
        // 用户类型1后台用户2前台用户
        dutpUserMessage.setToUserType(1);
        // 发送消息
        R<Boolean> booleanR = remoteUserMessageService.addMessage(dutpUserMessage);
        if (booleanR.getCode() == 500) {
            isResult = false;
            log.error("Message模块未启动，无法发送消息，审批失败！");
        }
        if (!isResult) {
            throw new RuntimeException(DutpConstant.PASS_FAILED);
        }
        return isResult;
    }
	/**
     * 修改审核同意
     *
     * @param orderReviewDto 信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateEditApprovalPass(OrderReviewDto orderReviewDto) {
        boolean isResult = true;
        String updateBy = SecurityUtils.getUsername();
        // 根据订单id，查询此订单下所有绑定的购书码id的集合
        List<Long> purchaseCodeList = dtbBookOrderCodeMapper.selectPurchaseCodeByOrderId(orderReviewDto.getOrderId());
        // 根据购书码id的集合，删除集合内冻结状态的订单下的购书码的数据
        isResult = dtbBookOrderCodeMapper.deleteByCodeId(purchaseCodeList);
        // 将id的list中已经冻结的购书码的状态改为5作废 冻结状态改为正常
        isResult = dtbBookPurchaseCodeMapper.cancelIsFrozenByCodeIdList(updateBy, purchaseCodeList);
        orderReviewDto.setUpdateBy(updateBy);
        orderReviewDto.setUpdateTime(new Date());
        orderReviewDto.setAuditDate(new Date());
        // 更新订单审核人表，审核状态改为1同意
        isResult = dtbBookOrderAuditUserMapper.purchaseApprovalRejectAuditUser(orderReviewDto);
        // 更新订单表的订单状态为edit_refused修改驳回
        isResult = dtbBookOrderMapper.purchaseApprovalReject(orderReviewDto);
        for (DtbBookOrderItemVo dtbBookOrderItemVo : orderReviewDto.getPurchaseApprovalList()) {
            // 当数量和折扣其中一项有变化就更新订单详细表
            if (!Objects.equals(dtbBookOrderItemVo.getBookQuantity(), dtbBookOrderItemVo.getEditBookQuantity()) || !Objects.equals(dtbBookOrderItemVo.getDiscount(), dtbBookOrderItemVo.getEditDiscount())) {
                // 更新订单详细表的数量
                UpdateWrapper<DtbBookOrderItem> updateWrapper = new UpdateWrapper<>();
                if (!Objects.equals(dtbBookOrderItemVo.getBookQuantity(), dtbBookOrderItemVo.getEditBookQuantity())) {
                    updateWrapper.set("book_quantity", dtbBookOrderItemVo.getBookQuantity());
                }
                // 更新订单详细表的折扣
                if (!Objects.equals(dtbBookOrderItemVo.getDiscount(), dtbBookOrderItemVo.getEditDiscount())) {
                    updateWrapper.set("discount", dtbBookOrderItemVo.getDiscount());
                    // 当折扣变化，更新改完的单价字段
                    BigDecimal priceOrderItem = dtbBookOrderItemVo.getPriceSale()
                            .multiply(dtbBookOrderItemVo.getDiscount()) // * 折扣
                            .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP); // / 100，并四舍五入保留2位
                    updateWrapper.set("price_order_item", priceOrderItem);
                }
                updateWrapper.set("update_by", SecurityUtils.getUsername())
                        .set("update_time", new Date())
                        .eq("order_item_id", dtbBookOrderItemVo.getOrderItemId());
                int update = dtbBookOrderItemMapper.update(null, updateWrapper);
                if (update != 1) {
                    isResult = false;
                    log.error("子订单数量更新失败，审批失败！");
                }
            }
        }
        // 更新购书码学校信息
        UpdateWrapper<DtbBookOrderCode> queryWrapper = new UpdateWrapper<>();
        queryWrapper.lambda().eq(DtbBookOrderCode::getOrderId, orderReviewDto.getOrderId())
                .set(DtbBookOrderCode::getSchoolId, orderReviewDto.getSchoolId())
                .set(DtbBookOrderCode::getUpdateTime,new Date())
                .set(DtbBookOrderCode::getUpdateBy, SecurityUtils.getUsername());
        dtbBookOrderCodeMapper.update(null, queryWrapper);
        DutpUserMessage dutpUserMessage = new DutpUserMessage();
        dutpUserMessage.setContent("您好，您的修改申请已审核通过，【订单号：" + orderReviewDto.getOrderNo() + "】");
        dutpUserMessage.setTitle("批量采购订单审核提醒");
        // 发送者
        dutpUserMessage.setFromUserId(orderReviewDto.getUserId());
        // 接收者
        dutpUserMessage.setToUserId(orderReviewDto.getToUserId());
        // 1系统消息2教务消息3推送消息
        dutpUserMessage.setMessageType(1);
        // 用户类型1后台用户2前台用户
        dutpUserMessage.setFromUserType(1);
        // 用户类型1后台用户2前台用户
        dutpUserMessage.setToUserType(1);
        // 发送消息
        R<Boolean> booleanR = remoteUserMessageService.addMessage(dutpUserMessage);
        if (booleanR.getCode() == 500) {
            isResult = false;
            log.error("Message模块未启动，无法发送消息，审批失败！");
        }
        if (!isResult) {
            throw new RuntimeException(DutpConstant.PASS_FAILED);
        }
        return isResult;
    }

    /**
     * 作废审核驳回
     *
     * @param orderReviewDto 作废信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateCancelApprovalReject(OrderReviewDto orderReviewDto) {
        boolean isResult = true;
        String updateBy = SecurityUtils.getUsername();
        // 根据订单id，查询此订单下所有绑定的购书码id的集合
        List<Long> purchaseCodeList = dtbBookOrderCodeMapper.selectPurchaseCodeByOrderId(orderReviewDto.getOrderId());
        // 将购书码表中id在集合中的:不是5作废状态的购书码且状态为冻结的数据，改为正常
        isResult = dtbBookPurchaseCodeMapper.updateIsFrozenByCodeIdList(updateBy, purchaseCodeList);
        orderReviewDto.setUpdateBy(updateBy);
        orderReviewDto.setUpdateTime(new Date());
        orderReviewDto.setAuditDate(new Date());
        // 更新订单审核人表，审核状态改为2驳回
        isResult = dtbBookOrderAuditUserMapper.purchaseApprovalRejectAuditUser(orderReviewDto);
        // 更新订单表的订单状态为edit_refused修改驳回,并更新商品总额和应付金额
        isResult = dtbBookOrderMapper.purchaseApprovalReject(orderReviewDto);
        for (DtbBookOrderItemVo dtbBookOrderItemVo : orderReviewDto.getPurchaseApprovalList()) {
            // 找到修改中状态的子订单改为正常状态
            if (dtbBookOrderItemVo.getItemStatus().equals("canceling")) {
                // 更新订单详细表的状态为正常
                UpdateWrapper<DtbBookOrderItem> updateWrapper = new UpdateWrapper<>();
                updateWrapper.set("item_status", "normal")
                        .set("update_by", SecurityUtils.getUsername())
                        .set("update_time", new Date())
                        .eq("order_item_id", dtbBookOrderItemVo.getOrderItemId());
                int update = dtbBookOrderItemMapper.update(null, updateWrapper);
                if (update != 1) {
                    isResult = false;
                    log.error("子订单更新为正常状态失败，审批失败！");
                }
            }
        }
        DutpUserMessage dutpUserMessage = new DutpUserMessage();
        dutpUserMessage.setContent("您好，审批人:" + SecurityUtils.getUsername() + "已驳回您的作废申请，驳回原因：" + orderReviewDto.getAuditContent() + "。【订单号：" + orderReviewDto.getOrderNo() + "】");
        dutpUserMessage.setTitle("批量采购订单审核提醒");
        // 发送者
        dutpUserMessage.setFromUserId(orderReviewDto.getUserId());
        // 接收者
        dutpUserMessage.setToUserId(orderReviewDto.getToUserId());
        // 1系统消息2教务消息3推送消息
        dutpUserMessage.setMessageType(1);
        // 用户类型1后台用户2前台用户
        dutpUserMessage.setFromUserType(1);
        // 用户类型1后台用户2前台用户
        dutpUserMessage.setToUserType(1);
        // 发送消息
        R<Boolean> booleanR = remoteUserMessageService.addMessage(dutpUserMessage);
        if (booleanR.getCode() == 500) {
            isResult = false;
            log.error("Message模块未启动，无法发送消息，审批失败！");
        }
        if (!isResult) {
            throw new RuntimeException(DutpConstant.PASS_FAILED);
        }
        return isResult;
    }

    /**
     * 作废审核通过
     *
     * @param orderReviewDto 通过信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateCancelApprovalPass(OrderReviewDto orderReviewDto) {
        boolean isResult = true;
        String updateBy = SecurityUtils.getUsername();
        // 根据订单id，查询此订单下所有绑定的购书码id的集合
        List<Long> purchaseCodeList = dtbBookOrderCodeMapper.selectPurchaseCodeByOrderId(orderReviewDto.getOrderId());
        // 根据购书码id的集合，删除集合内冻结状态的订单下的购书码的数据
        isResult = dtbBookOrderCodeMapper.deleteByCodeId(purchaseCodeList);
        // 将id的list中已经冻结的购书码的状态改为5作废 冻结状态改为正常
        isResult = dtbBookPurchaseCodeMapper.cancelIsFrozenByCodeIdList(updateBy, purchaseCodeList);
        orderReviewDto.setUpdateBy(updateBy);
        orderReviewDto.setUpdateTime(new Date());
        orderReviewDto.setAuditDate(new Date());
        // 更新订单审核人表，审核状态改为2驳回
        isResult = dtbBookOrderAuditUserMapper.purchaseApprovalRejectAuditUser(orderReviewDto);
        // 更新订单表的订单状态为edit_refused修改驳回
        isResult = dtbBookOrderMapper.purchaseApprovalReject(orderReviewDto);
        for (DtbBookOrderItemVo dtbBookOrderItemVo : orderReviewDto.getPurchaseApprovalList()) {
            if (dtbBookOrderItemVo.getItemStatus().equals("canceling")) {
                // 更新订单详细表的状态为已作废
                UpdateWrapper<DtbBookOrderItem> updateWrapper = new UpdateWrapper<>();
                updateWrapper.set("item_status", "cancel")
                        .set("update_by", SecurityUtils.getUsername())
                        .set("update_time", new Date())
                        .eq("order_item_id", dtbBookOrderItemVo.getOrderItemId());
                int update = dtbBookOrderItemMapper.update(null, updateWrapper);
                if (update != 1) {
                    isResult = false;
                    log.error("子订单更新为已作废状态失败，审批失败！");
                }
            }
        }
        // 清除个人书架 发消息给绑定的用户
//        QueryWrapper<DtbUserBook> userBookQueryWrapper = new QueryWrapper<>();
//        userBookQueryWrapper.lambda().in(DtbUserBook::getCodeId, purchaseCodeList)
//                .eq(DtbUserBook::getDelFlag, 0);
        List<DtbUserBook> codeBookList = dtbUserBookMapper.selectByCodeList(purchaseCodeList);
        if (!CollectionUtils.isEmpty(codeBookList)) {
            for (DtbUserBook dtbUserBook : codeBookList) {
                DutpUserMessage dutpUserMessage = new DutpUserMessage();
                dutpUserMessage.setContent("同学你好：因教材访问权限调整，您账号下《" + dtbUserBook.getBookName() + "》的教材在线阅读权限已关闭。" +
                        "为保障学习权益，请通过以下方式处理：\n" +
                        "1. 联系教务处确认订单状态；\n" +
                        "2.通过教材中心重新提交有效订单，进行教材购买；");
                dutpUserMessage.setTitle("教材访问权限调整通知");
                // 发送者
                dutpUserMessage.setFromUserId(orderReviewDto.getUserId());
                // 接收者
                dutpUserMessage.setToUserId(dtbUserBook.getUserId());
                // 1系统消息2教务消息3推送消息
                dutpUserMessage.setMessageType(1);
                // 用户类型1后台用户2前台用户
                dutpUserMessage.setFromUserType(1);
                // 用户类型1后台用户2前台用户
                dutpUserMessage.setToUserType(2);
                // 发送消息
                R<Boolean> booleanR = remoteUserMessageService.addMessage(dutpUserMessage);
                if (booleanR.getCode() == 500) {
                    isResult = false;
                    log.error("Message模块未启动，无法发送消息，审批失败！");
                }
                if (!isResult) {
                    throw new RuntimeException(DutpConstant.PASS_FAILED);
                }
            }
        }
        UpdateWrapper<DtbUserBook> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().in(DtbUserBook::getCodeId,purchaseCodeList)
                .set(DtbUserBook::getDelFlag, 1)
                .set(DtbUserBook::getUpdateBy, SecurityUtils.getUsername())
                .set(DtbUserBook::getUpdateTime, new Date());
        dtbUserBookMapper.update(null, updateWrapper);
        DutpUserMessage dutpUserMessage = new DutpUserMessage();
        dutpUserMessage.setContent("您好，您的作废申请已审核通过，【订单号：" + orderReviewDto.getOrderNo() + "】");
        dutpUserMessage.setTitle("批量采购订单审核提醒");
        // 发送者
        dutpUserMessage.setFromUserId(orderReviewDto.getUserId());
        // 接收者
        dutpUserMessage.setToUserId(orderReviewDto.getToUserId());
        // 1系统消息2教务消息3推送消息
        dutpUserMessage.setMessageType(1);
        // 用户类型1后台用户2前台用户
        dutpUserMessage.setFromUserType(1);
        // 用户类型1后台用户2前台用户
        dutpUserMessage.setToUserType(1);
        // 发送消息
        R<Boolean> booleanR = remoteUserMessageService.addMessage(dutpUserMessage);
        if (booleanR.getCode() == 500) {
            isResult = false;
            log.error("Message模块未启动，无法发送消息，审批失败！");
        }
        if (!isResult) {
            throw new RuntimeException(DutpConstant.PASS_FAILED);
        }
        return isResult;
    }

    /**
     * 样书审核列表
     *
     * @param dto 查询信息
     * @return 列表信息
     */
    @Override
    public List<BookOrderVo> sampleBookApprovalList(BookOrderDto dto) {
        // 样书审核列表
        List<BookOrderVo> list = dtbBookOrderMapper.getSampleBookOrderReview(dto);
        if (list != null) {
            for (BookOrderVo bookOrderVo : list) {
                // 根据订单id，统计所有子订单的数量，计算出订单总的采购数量
                Integer quantity = dtbBookOrderMapper.getOrderQuantityByOrderId(bookOrderVo.getOrderId());
                bookOrderVo.setPurchaseQuantity(quantity);
            }
        }
        return list;
    }

    @Override
    public List<BookOrderVo> exportOrderList(BookOrderDto dto) {
        List<BookOrderVo> res = new ArrayList<>();
        if (dto.getOrderType() == null || dto.getOrderType() == 2 || dto.getOrderType() == 3 || dto.getOrderType() == 5) {
            // 批量采购
            res = dtbBookOrderMapper.getBatchOrderList(dto);
        } else if (dto.getOrderType() == 1) {
            // 零售
            res = dtbBookOrderMapper.getRetailOrderList(dto);
        } else if (dto.getOrderType() == 4) {
            // 样书
            res = dtbBookOrderMapper.getSampleOrderList(dto);
        }
        return res;
    }

    @Override
    @Transactional
    public Boolean updateSampleShopOrder(DtbBookOrder dtbBookOrder) {
        QueryWrapper<DtbBookOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DtbBookOrder::getOrderId,dtbBookOrder.getOrderId());
        DtbBookOrder order = dtbBookOrderMapper.selectOne(queryWrapper);
        if (ObjectUtil.isEmpty(order)) {
            throw new ServiceException("未找到该笔订单");
        } else {
            DtbBookOrder updateOrder = new DtbBookOrder();
            updateOrder.setOrderId(dtbBookOrder.getOrderId());
            updateOrder.setUpdateTime(new Date());
            updateOrder.setUpdateBy(SecurityUtils.getUsername());
            updateOrder.setOrderStatus(OrderStatusEnum.PENDING.getCode());
            getDiff(dtbBookOrder,order,updateOrder);
            // 更新明细
            if (!CollectionUtils.isEmpty(dtbBookOrder.getBookIdList())) {
                QueryWrapper<DtbBookOrderItem> updateWrapper = new QueryWrapper<>();
                updateWrapper.lambda().eq(DtbBookOrderItem::getOrderId,order.getOrderId());
                dtbBookOrderItemService.remove(updateWrapper);

                dtbBookOrder.getBookIdList().stream().forEach(e -> {
                    e.setOrderId(dtbBookOrder.getOrderId());
                    e.setSchoolId(dtbBookOrder.getSchoolId());
                    e.setPriceOrderItem(BigDecimal.ZERO);
                    e.setItemStatus(OrderItemStatusEnum.NORMAL.getCode());
                    e.setCreateTime(new Date());
                    e.setCreateBy(SecurityUtils.getUsername());
                });
                //售价*数量。计算出原价
                BigDecimal total = dtbBookOrder.getBookIdList().stream()
                        .map(product -> product.getPriceSale().multiply(BigDecimal.valueOf(product.getBookQuantity())))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                updateOrder.setPrice(total);
                //改完的单价*数量。计算出订单金额
                updateOrder.setPayAmount(BigDecimal.ZERO);
            }
            dtbBookOrderItemService.saveBatch(dtbBookOrder.getBookIdList());
            // 更新订单表状态
            dtbBookOrderMapper.updateById(updateOrder);
            // 更新审批 只要修改了都需要一级重新审批
            QueryWrapper<DtbBookOrderAuditUser> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(DtbBookOrderAuditUser::getOrderId, dtbBookOrder.getOrderId());
            wrapper.lambda().eq(DtbBookOrderAuditUser::getAuditType, 4);
            DtbBookOrderAuditUser auditUser = dtbBookOrderAuditUserMapper.selectOne(wrapper);
            if (!ObjectUtil.isEmpty(auditUser)) {
                auditUser.setAuditStatus(0);
                auditUser.setEditContent(JSON.toJSONString(dtbBookOrder));
                dtbBookOrderAuditUserMapper.updateById(auditUser);
            }
        }
        return null;
    }

    /**
     * 样书采购审核驳回
     *
     * @param orderReviewDto 驳回意见
     * @return 状态
     */
    @Transactional
    @Override
    public boolean sampleBookApprovalReject(OrderReviewDto orderReviewDto) {
        boolean isResult;
        // 更新订单审核人表，审核状态改为2驳回，并添加审核备注
        orderReviewDto.setUpdateBy(SecurityUtils.getUsername());
        orderReviewDto.setUpdateTime(new Date());
        orderReviewDto.setAuditDate(new Date());
        isResult = dtbBookOrderAuditUserMapper.purchaseApprovalRejectAuditUser(orderReviewDto);
        // 更新订单表的订单状态
        isResult = dtbBookOrderMapper.updateOrderBySampleBook(orderReviewDto);
        DutpUserMessage dutpUserMessage = new DutpUserMessage();
        dutpUserMessage.setContent("您好，审批人:" + SecurityUtils.getUsername() + "已驳回你的订单申请，驳回原因：" + orderReviewDto.getAuditContent() + "。【订单号：" + orderReviewDto.getOrderNo() + "】");
        dutpUserMessage.setTitle("样书订单审核提醒");
        // 发送者
        dutpUserMessage.setFromUserId(orderReviewDto.getUserId());
        // 接收者
        dutpUserMessage.setToUserId(orderReviewDto.getToUserId());
        // 1系统消息2教务消息3推送消息
        dutpUserMessage.setMessageType(1);
        // 用户类型1后台用户2前台用户
        dutpUserMessage.setFromUserType(1);
        // 用户类型1后台用户2前台用户
        dutpUserMessage.setToUserType(1);
        R<Boolean> booleanR = remoteUserMessageService.addMessage(dutpUserMessage);
        System.out.println(booleanR.getData());
        if (booleanR.getCode() == 500) {
            log.error("Message模块未启动，无法发送消息，驳回失败！");
            isResult = false;
        }
        if (!isResult) {
            throw new RuntimeException(DutpConstant.REJECT_FAILED);
        }
        return isResult;
    }

    /**
     * 样书采购一级审核通过
     *
     * @param orderReviewDto 通过数据
     * @return 状态
     */
    @Transactional
    @Override
    public boolean firstSampleBookPass(OrderReviewDto orderReviewDto) {
        boolean isResult = true;
        Long bookId = 0L;
        // 判断当前订单内教材的购书码是否充足
        for (DtbBookOrderItemVo dtbBookOrderItemVo : orderReviewDto.getPurchaseApprovalList()) {
            bookId = dtbBookOrderItemVo.getBookId();
            // 查询当前书籍的购书码id的list，和采购数量进行比较
            List<Long> codeList = dtbBookPurchaseCodeMapper.selectCodeIdListByBookId(bookId);
            if (null != codeList && dtbBookOrderItemVo.getBookQuantity() > codeList.size()) {
                // 只要有一本书不满足，进程直接终止，并且提示采购的书籍数量不够。
                throw new RuntimeException("购书码数量不足,请补充购书码后再进行审批!");
            }
        }
        orderReviewDto.setUpdateBy(SecurityUtils.getUsername());
        orderReviewDto.setUpdateTime(new Date());
        orderReviewDto.setAuditDate(new Date());
        // 更新订单审核人表的一级样书审核记录，审核状态改为1通过
        isResult = dtbBookOrderAuditUserMapper.purchaseApprovalRejectAuditUser(orderReviewDto);
        // 更新订单表的订单状态为 second_auditting二级审核中【样书】
        isResult = dtbBookOrderMapper.updateOrderBySampleBook(orderReviewDto);
        // 查询二级审核人
        Long leaderId = dtbBookMapper.selectLeaderIdByBookId(bookId);
        // 插入审核人表二级审核数据
        DtbBookOrderAuditUser dtbBookOrderAuditUser = new DtbBookOrderAuditUser();
        dtbBookOrderAuditUser.setOrderId(orderReviewDto.getOrderId());
        dtbBookOrderAuditUser.setUserId(leaderId);
        dtbBookOrderAuditUser.setAuditType(5);
        dtbBookOrderAuditUser.setCreateBy(SecurityUtils.getUsername());
        dtbBookOrderAuditUser.setCreateTime(new Date());
        dtbBookOrderAuditUserMapper.insert(dtbBookOrderAuditUser);
        // 给二级审核人发送消息
        DutpUserMessage dutpUserMessage = new DutpUserMessage();
        dutpUserMessage.setContent("您好，采购人" + orderReviewDto.getCreateBy() + "发起了订单审核，请及时处理。");
        dutpUserMessage.setTitle("样书订单审核提醒");
        // 发送者
        dutpUserMessage.setFromUserId(orderReviewDto.getUserId());
        // 接收者
        dutpUserMessage.setToUserId(leaderId);
        // 1系统消息2教务消息3推送消息
        dutpUserMessage.setMessageType(1);
        // 用户类型1后台用户2前台用户
        dutpUserMessage.setFromUserType(1);
        // 用户类型1后台用户2前台用户
        dutpUserMessage.setToUserType(1);
        R<Boolean> booleanR = remoteUserMessageService.addMessage(dutpUserMessage);
        if (booleanR.getCode() == 500) {
            isResult = false;
            log.error("Message模块未启动，无法发送消息，审批失败！");
        }
        if (!isResult) {
            throw new RuntimeException(DutpConstant.PASS_FAILED);
        }
        return isResult;
    }

    /**
     * 样书采购二级审核通过
     *
     * @param orderReviewDto 通过数据
     * @return 状态
     */
    @Transactional
    @Override
    public boolean secondSampleBookPass(OrderReviewDto orderReviewDto) {
        boolean isResult = true;
        // 判断当前订单内教材的购书码是否充足
        for (DtbBookOrderItemVo dtbBookOrderItemVo : orderReviewDto.getPurchaseApprovalList()) {
            // 查询当前书籍的购书码id的list，和采购数量进行比较
            List<Long> codeList = dtbBookPurchaseCodeMapper.selectCodeIdListByBookId(dtbBookOrderItemVo.getBookId());
            if (null != codeList && dtbBookOrderItemVo.getBookQuantity() > codeList.size()) {
                // 只要有一本书不满足，进程直接终止，并且提示采购的书籍数量不够。
                throw new RuntimeException("购书码数量不足,请补充购书码后再进行审批!");
            }
        }
        // 向code与order的关系表（订单下的购书码表）中插入数据
        for (DtbBookOrderItemVo bookOrderItemVo : orderReviewDto.getPurchaseApprovalList()) {
            // 查询当前书籍的购书码id的集合，插入code与order的关系表
            List<Long> purchaseCodeList = dtbBookPurchaseCodeMapper.selectCodeIdListByBookId(bookOrderItemVo.getBookId());
            List<Long> purchaseList = purchaseCodeList.subList(0, bookOrderItemVo.getBookQuantity());
            for (Long purchaseCode : purchaseList) {
                DtbBookOrderCode dtbBookOrderCode = new DtbBookOrderCode();
                dtbBookOrderCode.setOrderId(bookOrderItemVo.getOrderId());
                dtbBookOrderCode.setOrderItemId(bookOrderItemVo.getOrderItemId());
                dtbBookOrderCode.setCodeId(purchaseCode);
                dtbBookOrderCode.setBookId(bookOrderItemVo.getBookId());
                // 样书学校可能为空，存在学校的情况下，才存这个字段
                if (null != bookOrderItemVo.getSchoolId()) {
                    dtbBookOrderCode.setSchoolId(bookOrderItemVo.getSchoolId());
                }
                dtbBookOrderCode.setCreateBy(SecurityUtils.getUsername());
                dtbBookOrderCode.setCreateTime(new Date());
                dtbBookOrderCodeMapper.insert(dtbBookOrderCode);
            }
            // 将当前这本书的id的list中的购书码的状态改为2已绑定未兑换
            isResult = dtbBookPurchaseCodeMapper.cancelCodeByCodeIdList(SecurityUtils.getUsername(), purchaseList);
        }
        orderReviewDto.setUpdateBy(SecurityUtils.getUsername());
        orderReviewDto.setUpdateTime(new Date());
        orderReviewDto.setAuditDate(new Date());
        // 更新订单审核人表，审核状态改为1通过
        isResult = dtbBookOrderAuditUserMapper.purchaseApprovalRejectAuditUser(orderReviewDto);
        // 更新订单表的订单状态
        isResult = dtbBookOrderMapper.updateOrderBySampleBook(orderReviewDto);
        DutpUserMessage dutpUserMessage = new DutpUserMessage();
        dutpUserMessage.setContent("您好，您的订单申请已审核通过，【订单号：" + orderReviewDto.getOrderNo() + "】");
        dutpUserMessage.setTitle("样书订单审核提醒");
        // 发送者
        dutpUserMessage.setFromUserId(orderReviewDto.getUserId());
        // 接收者
        dutpUserMessage.setToUserId(orderReviewDto.getToUserId());
        // 1系统消息2教务消息3推送消息
        dutpUserMessage.setMessageType(1);
        // 用户类型1后台用户2前台用户
        dutpUserMessage.setFromUserType(1);
        // 用户类型1后台用户2前台用户
        dutpUserMessage.setToUserType(1);
        R<Boolean> booleanR = remoteUserMessageService.addMessage(dutpUserMessage);
        if (booleanR.getCode() == 500) {
            isResult = false;
            log.error("Message模块未启动，无法发送消息，审批失败！");
        }
        if (!isResult) {
            throw new RuntimeException(DutpConstant.PASS_FAILED);
        }
        return isResult;
    }

}

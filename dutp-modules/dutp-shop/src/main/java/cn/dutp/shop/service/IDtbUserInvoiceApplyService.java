package cn.dutp.shop.service;

import cn.dutp.shop.domain.DtbUserInvoiceApply;
import cn.dutp.shop.domain.vo.DtbUserInvoiceApplyVo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
/**
 * DUTP-DTB-035开票申请Service接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
public interface IDtbUserInvoiceApplyService extends IService<DtbUserInvoiceApply>
{
    /**
     * 查询DUTP-DTB-035开票申请
     *
     * @param applyId DUTP-DTB-035开票申请主键
     * @return DUTP-DTB-035开票申请
     */
    public DtbUserInvoiceApply selectDtbUserInvoiceApplyByApplyId(Long applyId);

    /**
     * 查询DUTP-DTB-035开票申请列表
     *
     * @param dtbUserInvoiceApply DUTP-DTB-035开票申请
     * @return DUTP-DTB-035开票申请集合
     */
    public List<DtbUserInvoiceApply> selectDtbUserInvoiceApplyList(DtbUserInvoiceApply dtbUserInvoiceApply);

    /**
     * 新增DUTP-DTB-035开票申请
     *
     * @param dtbUserInvoiceApply DUTP-DTB-035开票申请
     * @return 结果
     */
//    public boolean insertDtbUserInvoiceApply(DtbUserInvoiceApply dtbUserInvoiceApply);

    /**
     * 修改DUTP-DTB-035开票申请
     *
     * @param dtbUserInvoiceApply DUTP-DTB-035开票申请
     * @return 结果
     */
    public boolean updateDtbUserInvoiceApply(DtbUserInvoiceApply dtbUserInvoiceApply);

    /**
     * 批量删除DUTP-DTB-035开票申请
     *
     * @param applyIds 需要删除的DUTP-DTB-035开票申请主键集合
     * @return 结果
     */
    public boolean deleteDtbUserInvoiceApplyByApplyIds(List<Long> applyIds);

    /**
     * 订单结算：提交开票申请
     * @param dtbUserInvoiceApply 申请对象
     * @return 结果
     */
    boolean addInvoiceApply(DtbUserInvoiceApply dtbUserInvoiceApply);

    /**
     * 拒绝换开
     *
     * @param dtbUserInvoiceApply 对象
     * @return 结果
     */
    public boolean noChangeInvoice(DtbUserInvoiceApply dtbUserInvoiceApply);

    /**
     * 学生教师端新增开票申请
     * @param dtbUserInvoiceApply 申请对象
     * @return 结果
     */
    boolean addInvoiceApplyEducation(DtbUserInvoiceApply dtbUserInvoiceApply);

    /**
     * 学生教师端新查询开票申请
     *
     * @param dtbUserInvoiceApply 申请对象
     * @return DUTP-DTB-035开票申请
     */
    public  List<DtbUserInvoiceApplyVo> getInfoEducation(DtbUserInvoiceApply dtbUserInvoiceApply);

    public List<DtbUserInvoiceApply> getStatementInvoice(Long statementId);

}

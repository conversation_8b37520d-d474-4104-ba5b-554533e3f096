package cn.dutp.shop.controller;

import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.domain.DutpSaleArea;
import cn.dutp.shop.service.IDutpSaleAreaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 销售大区Controller
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
@RestController
@RequestMapping("/area")
public class DutpSaleAreaController extends BaseController
{
    @Autowired
    private IDutpSaleAreaService dutpSaleAreaService;

    /**
     * 查询销售大区列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DutpSaleArea dutpSaleArea)
    {
        startPage();
        List<DutpSaleArea> list = dutpSaleAreaService.selectDutpSaleAreaList(dutpSaleArea);
        return getDataTable(list);
    }

    /**
     * 导出销售大区列表
     */
    @RequiresPermissions("shop:area:export")
    @Log(title = "销售大区", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DutpSaleArea dutpSaleArea)
    {
        List<DutpSaleArea> list = dutpSaleAreaService.selectDutpSaleAreaList(dutpSaleArea);
        ExcelUtil<DutpSaleArea> util = new ExcelUtil<DutpSaleArea>(DutpSaleArea.class);
        util.exportExcel(response, list, "销售大区数据");
    }

    /**
     * 获取销售大区详细信息
     */
    @RequiresPermissions("shop:area:query")
    @GetMapping(value = "/{areaId}")
    public AjaxResult getInfo(@PathVariable("areaId") Long areaId)
    {
        return success(dutpSaleAreaService.selectDutpSaleAreaByAreaId(areaId));
    }

    /**
     * 新增销售大区
     */
    @RequiresPermissions("shop:area:add")
    @Log(title = "新增销售大区", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DutpSaleArea dutpSaleArea)
    {
        return toAjax(dutpSaleAreaService.insertDutpSaleArea(dutpSaleArea));
    }

    /**
     * 修改销售大区
     */
    @RequiresPermissions("shop:area:edit")
    @Log(title = "修改销售大区", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DutpSaleArea dutpSaleArea)
    {
        return toAjax(dutpSaleAreaService.updateDutpSaleArea(dutpSaleArea));
    }

    /**
     * 删除销售大区
     */
    @RequiresPermissions("shop:area:remove")
    @Log(title = "删除销售大区", businessType = BusinessType.DELETE)
    @DeleteMapping("/{areaIds}")
    public AjaxResult remove(@PathVariable Long[] areaIds)
    {
        return toAjax(dutpSaleAreaService.deleteDutpSaleAreaByAreaIds(Arrays.asList(areaIds)));
    }

    /**
     * 查询销售大区以及人员列表,过滤当前登陆人（不分页）
     */
    @GetMapping("/getSaleAreaAndMember")
    public AjaxResult getSaleAreaAndMember(DutpSaleArea dutpSaleArea)
    {
        List<DutpSaleArea> list = dutpSaleAreaService.getSaleAreaAndMember(dutpSaleArea);
        return success(list);
    }

    /**
     * 查询地区(无分页)
     */
    @GetMapping("/getSaleAreaNoPage")
    public AjaxResult getSaleAreaNoPage(DutpSaleArea dutpSaleArea)
    {
        List<DutpSaleArea> list = dutpSaleAreaService.selectDutpSaleAreaList(dutpSaleArea);
        return success(list);
    }
}

package cn.dutp.shop.service;

import java.util.List;

import cn.dutp.domain.DtbBookRefundOrder;
import cn.dutp.shop.domain.dto.BookRefundOrderVO;

import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 售后退款订单Service接口
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
public interface IDtbBookRefundOrderService extends IService<DtbBookRefundOrder>
{
    /**
     * 查询售后退款订单
     *
     * @param refundOrderId 售后退款订单主键
     * @return 售后退款订单
     */
    public DtbBookRefundOrder selectDtbBookRefundOrderByRefundOrderId(Long refundOrderId);

    /**
     * 查询售后退款订单列表
     *
     * @param dtbBookRefundOrder 售后退款订单
     * @return 售后退款订单集合
     */
    public List<DtbBookRefundOrder> selectDtbBookRefundOrderList(DtbBookRefundOrder dtbBookRefundOrder);

    /**
     * 新增售后退款订单
     *
     * @param dtbBookRefundOrder 售后退款订单
     * @return 结果
     */
    public boolean insertDtbBookRefundOrder(DtbBookRefundOrder dtbBookRefundOrder);

    /**
     * 修改售后退款订单
     *
     * @param dtbBookRefundOrder 售后退款订单
     * @return 结果
     */
    public boolean updateDtbBookRefundOrder(DtbBookRefundOrder dtbBookRefundOrder);

    /**
     * 批量删除售后退款订单
     *
     * @param refundOrderIds 需要删除的售后退款订单主键集合
     * @return 结果
     */
    public boolean deleteDtbBookRefundOrderByRefundOrderIds(List<Long> refundOrderIds);

    /**
     * 查询售后退款订单VO列表
     */
    List<BookRefundOrderVO> selectRefundOrderVOList(BookRefundOrderVO dtbBookRefundOrder);

    /**
     * 查询售后退款订单VO详细信息
     */
    BookRefundOrderVO selectRefundOrderVOByRefundOrderId(Long refundOrderId);

    /**
     * 处理退款
     */
    boolean refund(DtbBookRefundOrder dtbBookRefundOrder);

    /**
     * 批量处理退款
     *
     * @param refundOrderVO 退款订单信息（包含订单ID列表、退款状态和审核备注）
     * @return 处理结果
     */
    boolean batchRefund(BookRefundOrderVO refundOrderVO);
}

package cn.dutp.shop.domain.vo;

import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;

/**
 * DUTP-DTB-031订单对应的书籍明细
 *
 * @TableName dtb_book_order_item
 */
@TableName(value = "dtb_book_order_item")
@Data
public class DtbBookOrderItemVo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderItemId;
    /**
     * 教材名称
     */
    private String bookName;
    /**
     * ISBN
     */
    private String isbn;

    /**
     * ISSN
     */
    private String issn;


    /**
     * 书的售价
     */
    private BigDecimal priceSale;
    /**
     * 数量
     */
    private Integer bookQuantity;
    /**
     * 修改申请之前的数量
     */
    private Integer editBookQuantity;

    /**
     * 修改申请之前的折扣
     */
    private BigDecimal editDiscount;

    /**
     * 折扣
     */
    private BigDecimal discount;

    /**
     * 拼接的折扣
     */
    private String discountPercentage;

    /**
     * 总数
     */
    private BigDecimal total;
    /**
     * 修改申请之前的子订单合计
     */
    private BigDecimal editTotal;

    /**
     * 应付金额
     */
    private BigDecimal shouldPay;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 书商名称
     */
    private String merchanName;

    /**
     *学校名称
     */
    private String schoolName;

    /**
     *地区名称
     */
    private String areaName;

    /**
     *经办人
     */
    private String nickName;
    /**
     * book_id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long bookId;

    /**
     * 采购学校
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long schoolId;
    /**
     * 订单id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 改完的单价
     */
    private BigDecimal priceOrderItem;
    // 作废申请状态：'normal正常canceling申请作废cancel已作废'
    private String itemStatus;

}
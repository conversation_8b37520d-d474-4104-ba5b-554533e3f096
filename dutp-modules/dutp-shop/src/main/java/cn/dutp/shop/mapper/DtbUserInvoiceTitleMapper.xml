<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.shop.mapper.DtbUserInvoiceTitleMapper">
        <select id="selectSaleList" resultType="cn.dutp.shop.domain.DtbUserInvoiceTitle">
            SELECT
            uia.apply_id,
            uia.apply_status,
            uia.order_type,
            uia.upload_time,
            bo.order_no,
            MAX( uif.create_time ) AS create_time,
            uit.invoice_type,
            uit.title_type,
            uia.apply_type,
            uit.title_name,
            uit.tax_no,
            uit.regist_tel,
            uit.user_id,
            uia.user_email,
            uia.change_count,
            uia.invoice_amount,
            SUM( boi.book_quantity ) AS book_quantity,
            GROUP_CONCAT( DISTINCT b.book_name ) AS book_name,
            bo.order_id,
            GROUP_CONCAT( DISTINCT uif.invoice_code ) AS invoice_code
            FROM
            dtb_user_invoice_apply AS uia
            LEFT JOIN dtb_book_order AS bo ON bo.order_id = uia.order_id
            LEFT JOIN dtb_book_order_item AS boi ON boi.order_id = bo.order_id
            LEFT JOIN dtb_book AS b ON b.book_id = boi.book_id
            LEFT JOIN dtb_user_invoice_file AS uif ON uif.apply_id = uia.apply_id
            LEFT JOIN dtb_user_invoice_title AS uit ON uit.title_id = uia.title_id
            LEFT JOIN dutp_user AS u ON u.user_id = uit.user_id
            <where>
                <if test="orderNo != null">and bo.order_no = #{orderNo}</if>
                <if test="titleName != null and titleName != ''">and uit.title_name LIKE CONCAT('%',#{titleName},'%') </if>
                <if test="invoiceAmount != null and invoiceAmount != ''">and uia.invoice_amount LIKE CONCAT('%',#{invoiceAmount},'%') </if>
                <if test="applyStatus != null">and uia.apply_status = #{applyStatus}</if>
                <if test="invoiceType != null">and uit.invoice_type = #{invoiceType}</if>
                <if test="titleType != null">and uit.title_type = #{titleType}</if>
                <if test="startTime != null">AND uif.create_time >= #{startTime}</if>
                <if test="endTime != null">AND uif.create_time &lt;= #{endTime}</if>
                and uia.order_type = 1
            </where>
            GROUP BY
            uia.apply_id,
            uia.apply_status,
            uia.order_type,
            uia.upload_time,
            bo.order_no,
            uit.invoice_type,
            uit.title_type,
            uia.apply_type,
            uit.title_name,
            uit.tax_no,
            uit.regist_tel,
            uit.user_id,
            uia.user_email,
            uia.change_count,
            uia.invoice_amount,
            bo.order_id
            <if test="invoiceCode != null and invoiceCode != ''  ">
                            HAVING
                            <if test="invoiceCode != null and invoiceCode != ''">
                                GROUP_CONCAT(uif.invoice_code) LIKE CONCAT('%', #{invoiceCode}, '%')
                            </if>
                        </if>
            ORDER BY create_time DESC
        </select>
    <select id="seleEducationList" resultType="cn.dutp.shop.domain.DtbUserInvoiceTitle">
        SELECT
        uia.apply_id,
        uia.apply_status,
        uia.order_type,
        bo.order_type as orderTypes,
        uia.create_by,
        bs.statement_no,
        MAX(uif.create_time) AS create_time,
        bm.invoice_type,
        bm.title_type,
        bm.title_name,
        bm.apply_type,
        bm.tax_no,
        bm.tel,
        s.invoice_type AS schoolInvoiceType,
        s.title_type AS schoolTitleType,
        s.title_name AS schoolTitleName,
        s.tax_no AS schoolTaxNo,
        s.regist_address AS schoolRegistAddress,
        s.regist_tel AS schoolRegistTel,
        s.account_bank AS schoolAccountBank,
        s.account_no AS schoolAccountNo,
        s.user_email AS schoolUserEmail,
        bs.statement_id,
        uia.change_count,
        uia.invoice_amount,
        bm.user_email,
        SUM(boi.price_order_item * boi.book_quantity) as openPayAmount,
        SUM(boi.book_quantity) AS book_quantity,
        GROUP_CONCAT(DISTINCT b.book_name ORDER BY b.book_name SEPARATOR '、') AS book_name,
        GROUP_CONCAT(DISTINCT uif.invoice_code ORDER BY uif.invoice_code SEPARATOR '、') AS invoice_code,
        GROUP_CONCAT(DISTINCT bm.merchan_name ORDER BY bm.merchan_name SEPARATOR '、') AS merchan_name,
        GROUP_CONCAT(DISTINCT s.school_name ORDER BY s.school_name SEPARATOR '、') AS school_name
        FROM
        dtb_user_invoice_apply AS uia
        LEFT JOIN dtb_book_statement AS bs ON bs.statement_id = uia.statement_id
        LEFT JOIN dtb_book_statement_order AS bso ON bso.statement_id = bs.statement_id
        LEFT JOIN dtb_book_order AS bo ON bo.order_id = bso.order_id
        LEFT JOIN dtb_book_order_item AS boi ON boi.order_id = bo.order_id
        LEFT JOIN dutp_school AS s ON s.school_id = bo.school_id
        LEFT JOIN dtb_book_merchant AS bm ON bm.merchant_id = bo.merchant_id
        LEFT JOIN dtb_book AS b ON b.book_id = boi.book_id
        LEFT JOIN dtb_user_invoice_file AS uif ON uif.apply_id = uia.apply_id
        LEFT JOIN dtb_user_invoice_title AS uit ON uit.title_id = uia.title_id
        LEFT JOIN sys_user AS u ON u.user_id = uit.user_id
        <where>
            uia.order_type = 2
            <if test="statementNo != null and statementNo != ''">AND bs.statement_no LIKE CONCAT('%',#{statementNo},'%')</if>
            <if test="invoiceCode != null and invoiceCode != ''">AND uif.invoice_code LIKE CONCAT('%',#{invoiceCode},'%')</if>
            <if test="titleName != null and titleName != ''">AND bm.title_name LIKE CONCAT('%',#{titleName},'%')</if>
            <if test="invoiceType != null and invoiceType != ''">AND bm.invoice_type LIKE CONCAT('%',#{invoiceType},'%')</if>
            <if test="titleType != null and titleType != ''">AND bm.title_type LIKE CONCAT('%',#{titleType},'%')</if>
            <if test="applyStatus != null and applyStatus != ''">AND uia.apply_status = #{applyStatus}</if>
            <if test="startTime != null">AND uif.create_time >= #{startTime}</if>
            <if test="endTime != null">AND uif.create_time &lt;= #{endTime}</if>
            <if test="merchantId != null">AND bm.merchant_id = #{merchantId}</if>
            <if test="schoolId != null">AND bs.school_id = #{schoolId}</if>
        </where>
        GROUP BY
        uia.apply_id,
        uia.apply_status,
        uia.order_type,
        uia.create_by,
        bs.statement_no,
        bm.invoice_type,
        bm.title_type,
        bm.title_name,
        bm.tax_no,
        bm.tel,
        bm.apply_type,
        bs.statement_id,
        uia.change_count,
        uia.invoice_amount,
        bm.user_email,
        s.invoice_type,
        s.title_type,
        s.title_name,
        s.tax_no,
        s.regist_address,
        s.regist_tel,
        bo.order_type,
        s.account_bank,
        s.account_no,
        bm.merchant_id,
        s.user_email,
        bs.school_id
        <if test="openPayAmount != null">
            HAVING SUM(boi.price_order_item * boi.book_quantity) = #{openPayAmount}
        </if>
        ORDER BY
        create_time DESC
    </select>
    <select id="seleEducationExportList" resultType="cn.dutp.shop.domain.DtbUserInvoiceTitle">
        SELECT uia.apply_id,
               uia.title_id,
               uia.apply_status,
               uia.apply_type,
               uia.change_count,
               uia.upload_time,
               bo.order_no,
               b.book_name,
               bo.create_time                      as order_time,
               bo.order_type,
               bo.school_id,
               uit.invoice_type,
               uit.title_type,
               uit.title_name,
               uit.user_id,
               uit.tax_no,
               uit.regist_address,
               uit.regist_tel,
               uit.account_bank,
               uit.account_no,
               u.email,
               u.user_name,
               u.nick_name,
               bot.book_quantity,
               bot.price_order_item,
               dbso.order_id,
               (SELECT GROUP_CONCAT(uif.invoice_code SEPARATOR ', ')
                FROM dtb_user_invoice_file AS uif
                WHERE uif.apply_id = uia.apply_id) AS invoice_code
        FROM dtb_user_invoice_apply AS uia
                 left JOIN dtb_user_invoice_title AS uit ON uia.title_id = uit.title_id and uit.del_flag = 0
                 left join dtb_book_statement_order dbso on dbso.statement_id = uia.statement_id
                 LEFT JOIN dtb_book_order as bo on dbso.order_id = bo.order_id and bo.deleted = 1
                 left join dtb_book_order_item as bot on bot.order_id = bo.order_id
                 left join dtb_book as b on bo.book_id = b.book_id
                 LEFT JOIN dutp_user AS u ON u.user_id = uit.user_id and u.del_flag = 0
        where bo.order_type in (2, 3, 5)
    </select>

    <select id="selectByIds" resultType="cn.dutp.shop.domain.dto.DtbUserInvoiceTitleExport">
        SELECT
            <if test="type == 1">
                dit.invoice_type,
                (case when dit.title_type = 1 then '是' else '否' end) as naturalPersonName,
                dit.title_name as buyUserName,
                '--' as taxNo,
                '--' as buyUserPhone,
                '--' as accountBank,
                '--' as accountNo,
                '--' as email,
            </if>
            <if test="type == 2">
                (case
                when bor.order_type = 2 or bor.order_type = 3 then ds.invoice_type
                when bor.order_type = 5 then dbm.invoice_type end) as invoiceType,
                (case
                when bor.order_type = 2 or bor.order_type = 3 then
                (case when ds.title_type = 1 then '是' else '否' end)
                when bor.order_type = 5 then
                (case when dbm.title_type = 1 then '是' else '否' end)
                end) as naturalPersonName,
                (case
                when bor.order_type = 2 or bor.order_type = 3 then ds.title_name
                when bor.order_type = 5 then dbm.title_name
                end) as buyUserName,
                (case
                when bor.order_type = 2 or bor.order_type = 3 then ds.tax_no
                when bor.order_type = 5 then dbm.tax_no
                end) as taxNo,
                (case
                when bor.order_type = 2 or bor.order_type = 3 then ds.regist_tel
                when bor.order_type = 5 then dbm.regist_tel
                end) as buyUserPhone,
                (case
                when bor.order_type = 2 or bor.order_type = 3 then ds.account_bank
                when bor.order_type = 5 then dbm.account_bank
                end) as accountBank,
                (case
                when bor.order_type = 2 or bor.order_type = 3 then ds.account_no
                when bor.order_type = 5 then dbm.account_no
                end) as accountNo,
                (case
                when bor.order_type = 2 or bor.order_type = 3 then ds.user_email
                when bor.order_type = 5 then dbm.user_email
                end) as email,
            </if>
            bo.statement_id,
            bor.order_type,
            da.invoice_remark as remark
        FROM
            dtb_user_invoice_apply da
        left join
            dtb_book_statement bo on bo.statement_id = da.statement_id and bo.del_flag = 0
        left join
            dtb_book_statement_order dbso on dbso.statement_id = da.statement_id
        left join
            dtb_book_order bor on bor.order_id = dbso.order_id and bor.deleted = 1
        <if test="type == 1">
            left join dtb_user_invoice_title dit on da.title_id = dit.title_id
        </if>
        <if test="type == 2">
            left join dutp_school ds on ds.school_id = bo.school_id and ds.del_flag = 0
            left join dtb_book_merchant dbm on dbm.merchant_id = bo.merchant_id and dbm.del_flag = 0
        </if>
        WHERE
            da.apply_id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        GROUP BY bo.statement_id,bor.order_type,da.invoice_remark
    </select>


    <select id="selectOrderItemByStatementIds" resultType="cn.dutp.shop.domain.dto.DtbUserInvoiceTitleItemExport">
        SELECT
            <choose>
                <when test="applyType != 1">
                    '图书' as bookName,
                </when>
                <otherwise>
                    db.book_name as bookName,
                </otherwise>
            </choose>
            (case when doi.item_status != 'normal' then 0 else doi.book_quantity end) as bookQuantity,
            doi.order_id,
            doi.price_sale,
            ((case when doi.item_status != 'normal' then 0 else doi.book_quantity end) * doi.price_sale) as price
        from
            dtb_book_order_item doi
        inner join
            dtb_book db on doi.book_id = db.book_id
        where
            doi.item_status = 'normal' and doi.order_id in
            <foreach collection="orderList" item="item" open="(" separator="," close=")">
                #{item.orderId}
            </foreach>
    </select>
</mapper>
package cn.dutp.shop.service;

import java.util.List;

import cn.dutp.common.core.domain.R;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.domain.DtbBookPurchaseCode;
import cn.dutp.shop.domain.DtbUserInvoiceTitle;
import cn.dutp.shop.domain.DtbUserTrialApply;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * DUTP-DTB_025教师试用申请Service接口
 *
 * <AUTHOR>
 * @date 2024-11-07
 */
public interface IDtbUserTrialApplyService extends IService<DtbUserTrialApply>
{
    /**
     * 查询DUTP-DTB_025教师试用申请
     *
     * @param applyId DUTP-DTB_025教师试用申请主键
     * @return DUTP-DTB_025教师试用申请
     */
    public DtbUserTrialApply selectDtbUserTrialApplyByApplyId(Long applyId);

    /**
     * 查询DUTP-DTB_025教师试用申请列表
     *
     * @param dtbUserTrialApply DUTP-DTB_025教师试用申请
     * @return DUTP-DTB_025教师试用申请集合
     */
    public List<DtbUserTrialApply> selectDtbUserTrialApplyList(DtbUserTrialApply dtbUserTrialApply);

    /**
     * 学生教师端教师试用申请列表
     *
     * @param dtbUserTrialApply DUTP-DTB_025教师试用申请
     * @return DUTP-DTB_025教师试用申请集合
     */
    public List<DtbUserTrialApply> listEducation(DtbUserTrialApply dtbUserTrialApply);

    /**
     * 新增DUTP-DTB_025教师试用申请
     *
     * @param dtbUserTrialApply DUTP-DTB_025教师试用申请
     * @return 结果
     */
    public boolean insertDtbUserTrialApply(DtbUserTrialApply dtbUserTrialApply);

    /**
     * 修改DUTP-DTB_025教师试用申请
     *
     * @param dtbUserTrialApply DUTP-DTB_025教师试用申请
     * @return 结果
     */
    public boolean updateDtbUserTrialApply(DtbUserTrialApply dtbUserTrialApply);

    /**
     * 修改教师试用申请
     *
     * @param dtbUserTrialApply 教师试用申请
     * @return 结果
     */
    public boolean editEducation(DtbUserTrialApply dtbUserTrialApply);

    /**
     * 批量删除DUTP-DTB_025教师试用申请
     *
     * @param applyIds 需要删除的DUTP-DTB_025教师试用申请主键集合
     * @return 结果
     */
    public boolean deleteDtbUserTrialApplyByApplyIds(List<Long> applyIds);

    List<DtbUserTrialApply> TrialShopList(DtbUserTrialApply dtbUserTrialApply);

    DtbUserTrialApply binding(DtbUserTrialApply dtbUserTrialApply);

    List<DtbBookPurchaseCode> getTemporaryCode(DtbUserTrialApply dtbUserTrialApply);

    /**
     * 学生教师端教师提交试用申请
     *
     * @param dtbUserTrialApply 教师试用申请
     * @return 教师试用申请结果
     */
    R<String> submitTrialApplicationEducation(DtbUserTrialApply dtbUserTrialApply);

    AjaxResult agreeTrial(DtbUserTrialApply dtbUserTrialApply);
}

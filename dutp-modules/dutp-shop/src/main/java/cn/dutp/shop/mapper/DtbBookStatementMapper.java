package cn.dutp.shop.mapper;

import cn.dutp.shop.domain.DtbBookStatement;
import cn.dutp.shop.domain.dto.ReconcileLookDto;
import cn.dutp.shop.domain.dto.ReconciliationDto;
import cn.dutp.shop.domain.dto.StatementItemDto;
import cn.dutp.shop.domain.vo.DtbBookOrderItemVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 结算单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Repository
public interface DtbBookStatementMapper extends BaseMapper<DtbBookStatement>
{

    List<DtbBookStatement> selectStatementList(DtbBookStatement dtbBookStatement);

    @Update("update dtb_book_statement set invoice_status = 'pending' where statement_id = #{statementId}")
    boolean changeInvoiceStatus(Long statementId);

    @Update("update dtb_book_statement set del_flag = '2' where statement_id = #{statementId}")
    boolean delById(Long statementId);

    @Update("update dtb_book_statement set statement_status = 'settled' where statement_id = #{statementId}")
    boolean changeStatementStatus(Long statementId);

    List<ReconciliationDto> selectReconcileList(ReconciliationDto reconciliationDto);

    @Select("SELECT DISTINCT\n" +
            "            bo.order_no,\n" +
            "            b.book_name,\n" +
            "            b.isbn,\n" +
            "            b.issn,\n" +
            "            boi.book_quantity,\n" +
            "            ( SELECT COUNT(*) FROM dtb_book_order_code WHERE state = 3 AND order_id = bo.order_id AND book_id = boi.book_id ) AS useCode,\n" +
            "            ( SELECT COUNT(*) FROM dtb_book_order_code WHERE state = 2 AND order_id = bo.order_id AND book_id = boi.book_id ) AS noUseCode,\n" +
            "            s.school_name,\n" +
            "            bm.merchan_name \n" +
            "            FROM\n" +
            "            dtb_book_order AS bo\n" +
            "            LEFT JOIN dtb_book_order_item AS boi ON bo.order_id = boi.order_id\n" +
            "            LEFT JOIN dtb_book AS b ON b.book_id = boi.book_id\n" +
            "            LEFT JOIN dtb_book_order_code AS boc ON boc.book_id = b.book_id\n" +
            "            LEFT JOIN dutp_school AS s ON s.school_id = bo.school_id\n" +
            "            LEFT JOIN dtb_book_merchant AS bm ON bm.merchant_id = bo.merchant_id \n" +
            "            WHERE\n" +
            "            bo.order_id = #{orderId}\n")
    List<ReconcileLookDto> getReconcileListById(Long orderId);

    @Select("SELECT DISTINCT\n" +
            "        bo.order_no,\n" +
            "        bo.order_type,\n" +
            "        ba.area_name,\n" +
            "        boi.book_id,\n" +
            "        boi.order_id,\n" +
            "        b.cover,\n" +
            "        b.book_name,\n" +
            "        b.isbn,\n" +
            "        b.issn,\n" +
            "        boi.book_quantity, \n" +
            "        boi.price_sale,\n" +
            "        concat(boi.discount,'%') as discountPercentage,\n" +
            "        ( SELECT COUNT(*) FROM dtb_book_order_code WHERE state = 3 AND order_id = bo.order_id and book_id = boi.book_id) AS useCode,\n" +
            "        ( SELECT COUNT(*) FROM dtb_book_order_code WHERE state = 2 AND order_id = bo.order_id and book_id = boi.book_id) AS noUseCode,\n" +
            "        s.school_name,\n" +
            "        bm.merchan_name,\n" +
            "        ROUND((boi.book_quantity * boi.price_sale *(boi.discount/100)),2)  as shouldPay,\n" +
            "        (boi.book_quantity * boi.price_sale)  as total,\n" +
            "        u.nick_name\n" +
            "        FROM\n" +
            "        dtb_book_order AS bo\n" +
            "\t\t\t\tLEFT JOIN dtb_book_order_item AS boi ON bo.order_id = boi.order_id\n" +
            "        LEFT JOIN dtb_book AS b ON b.book_id = boi.book_id\n" +
            "        \n" +
            "        LEFT JOIN dtb_book_order_code AS boc ON boc.book_id = b.book_id\n" +
            "        LEFT JOIN dutp_school AS s ON s.school_id = bo.school_id\n" +
            "        LEFT JOIN dtb_book_merchant AS bm ON bm.merchant_id = bo.merchant_id \n" +
            "        LEFT JOIN dutp_sale_area as ba on ba.area_id = bo.area_id\n" +
            "        left join sys_user as u on u.user_id = bo.operator_id\n" +
            "        WHERE\n" +
            "        bo.order_id =#{orderId}")
    List<DtbBookOrderItemVo> selectOrderItemByOrderId(Long orderId);

    @Select("SELECT\n" +
            "\tbm.merchan_name,\n" +
            "\ts.school_name,\n" +
            "\tbo.order_no,\n" +
            "\tb.book_name,\n" +
            "\tb.issn,\n" +
            "\tb.isbn,\n" +
            "\tboi.book_quantity,\n" +
            "\tboi.price_order_item,\n" +
            "\tSUM( boi.price_order_item * boi.book_quantity ) AS totalAmount \n" +
            "FROM\n" +
            "\tdtb_book_statement AS bs\n" +
            "\tLEFT JOIN dtb_book_statement_order AS bso ON bso.statement_id = bs.statement_id\n" +
            "\tLEFT JOIN dtb_book_order AS bo ON bo.order_id = bso.order_id\n" +
            "\tLEFT JOIN dtb_book_merchant AS bm ON bm.merchant_id = bo.merchant_id\n" +
            "\tLEFT JOIN dutp_school AS s ON s.school_id = bo.school_id\n" +
            "\tLEFT JOIN dtb_book_order_item AS boi ON boi.order_id = bo.order_id\n" +
            "\tLEFT JOIN dtb_book AS b ON b.book_id = boi.book_id \n" +
            "WHERE\n" +
            "\tbs.statement_id = #{statementId} \n" +
            "\tAND bs.del_flag = 0 \n" +
            "GROUP BY\n" +
            "\tbm.merchan_name,\n" +
            "\ts.school_name,\n" +
            "\tbo.order_no,\n" +
            "\tb.book_name,\n" +
            "\tb.issn,\n" +
            "\tb.isbn,\n" +
            "\tboi.book_quantity,\n" +
            "\tboi.price_order_item")
    List<DtbBookStatement> selectStatmentExportList(Long statementId);

    @Select("SELECT DISTINCT\n" +
            "\tbo.order_no,\n" +
            "\tb.book_name,\n" +
            "\tb.isbn,\n" +
            "\tb.issn,\n" +
            "\tboi.book_quantity,\n" +
            "\t( SELECT COUNT(*) FROM dtb_book_order_code WHERE state = 3 AND order_id = bo.order_id AND book_id = boi.book_id ) AS useCode,\n" +
            "\t( SELECT COUNT(*) FROM dtb_book_order_code WHERE state = 2 AND order_id = bo.order_id AND book_id = boi.book_id ) AS noUseCode,\n" +
            "\ts.school_name,\n" +
            "\tbm.merchan_name \n" +
            "FROM\n" +
            "\tdtb_book_order AS bo\n" +
            "\tLEFT JOIN dtb_book AS b ON b.book_id = bo.book_id\n" +
            "\tLEFT JOIN dtb_book_order_item AS boi ON bo.order_id = boi.order_id\n" +
            "\tLEFT JOIN dtb_book_order_code AS boc ON boc.book_id = b.book_id\n" +
            "\tLEFT JOIN dutp_school AS s ON s.school_id = bo.school_id\n" +
            "\tLEFT JOIN dtb_book_merchant AS bm ON bm.merchant_id = bo.merchant_id \n" +
            "WHERE\n" +
            "\tbo.order_id = #{orderId}")
    List<DtbBookStatement> selectReconcileExportList(Long statementId);

    @Select("SELECT DISTINCT\n" +
            "\tbs.statement_id,\n" +
            "\tbo.order_no,\n" +
            "\tb.book_name,\n" +
            "\tb.issn,\n" +
            "\tb.isbn,\n" +
            "\tSUM( boi.price_order_item * boi.book_quantity ) AS payAmount,\n" +
            "\tboi.book_quantity,\n" +
            "\tboi.price_sale \n" +
            "FROM\n" +
            "\tdtb_book_statement AS bs\n" +
            "\tLEFT JOIN dtb_book_statement_order AS bso ON bso.statement_id = bs.statement_id\n" +
            "\tLEFT JOIN dtb_book_order AS bo ON bo.order_id = bso.order_id\n" +
            "\tLEFT JOIN dtb_book_order_item AS boi ON boi.order_id = bo.order_id\n" +
            "\tLEFT JOIN dtb_book AS b ON b.book_id = boi.book_id \n" +
            "WHERE\n" +
            "\tbs.statement_id = #{statementId} \n" +
            "GROUP BY " +
            "\tbs.statement_id,\n" +
            "\tboi.book_quantity,\n" +
            "\tboi.price_sale,\n" +
            "\tbo.order_no,\n" +
            "\tb.book_name,\n" +
            "\tb.issn,\n" +
            "\tb.isbn")
    List<StatementItemDto> selectStatementItem(Long statementId);

    @Select("SELECT\n" +
            "\tbo.order_id,\n" +
            "\tbo.order_no,\n" +
            "\tb.book_name,\n" +
            "\tb.issn,\n" +
            "\tb.isbn,\n" +
            "\tboi.price_order_item,\n" +
            "\tboi.book_quantity,\n" +
            "\tSUM( boi.book_quantity * boi.price_order_item ) AS payAmount \n" +
            "FROM\n" +
            "\tdtb_book_order AS bo\n" +
            "\tLEFT JOIN dtb_book_order_item AS boi ON boi.order_id = bo.order_id\n" +
            "\tLEFT JOIN dtb_book AS b ON b.book_id = bo.book_id \n" +
            "WHERE\n" +
            "\tbo.order_id = #{orderId}\n" +
            "GROUP BY\n" +
            "\tbo.order_id,\n" +
            "\tbo.order_no,\n" +
            "\tb.book_name,\n" +
            "\tb.issn,\n" +
            "\tb.isbn,\n" +
            "\tboi.book_quantity,\n" +
            "\tboi.price_order_item")
    List<StatementItemDto> selectOrderItem(Long orderId);
}


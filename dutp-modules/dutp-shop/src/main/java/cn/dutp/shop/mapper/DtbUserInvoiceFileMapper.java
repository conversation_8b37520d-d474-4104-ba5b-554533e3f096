package cn.dutp.shop.mapper;

import cn.dutp.shop.domain.DtbUserInvoiceFile;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * DUTP-DTB-036发票文件Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@Repository
public interface DtbUserInvoiceFileMapper extends BaseMapper<DtbUserInvoiceFile>
{

    @Delete("delete from dtb_user_invoice_file where file_id = #{fileId}")
    boolean deleteFileById(Long fileId);

    @Select("select * from dtb_user_invoice_file where apply_id = #{applyId}")
    List<DtbUserInvoiceFile> selectByApplyId(Long applyId);
}

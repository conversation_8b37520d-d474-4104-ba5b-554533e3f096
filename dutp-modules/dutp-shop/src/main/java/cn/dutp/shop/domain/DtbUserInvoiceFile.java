package cn.dutp.shop.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * DUTP-DTB-036发票文件对象 dtb_user_invoice_file
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@Data
@TableName("dtb_user_invoice_file")
public class DtbUserInvoiceFile extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** $column.columnComment */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long fileId;

    /** 发票申请ID */
    @Excel(name = "发票申请ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long applyId;

    @TableField(exist = false)
    private Long userId;

    /** 发票文件地址，图片，PDF */
    @Excel(name = "发票文件地址，图片，PDF")
    private String fileUrl;

    private String fileName;

    private String invoiceCode;

    @TableField(exist = false)
    private List<DtbUserInvoiceFile> fileList;

    @TableField(exist = false)
    private String titleName;

    private Integer orderType;

    /** 发票文件状态 */
    private Integer fileType;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("fileId", getFileId())
                .append("applyId", getApplyId())
                .append("fileUrl", getFileUrl())
                .append("fileType", getFileType())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

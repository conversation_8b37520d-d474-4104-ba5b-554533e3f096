package cn.dutp.shop.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.domain.DtbBookRefundOrderItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.shop.service.IDtbBookRefundOrderItemService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 退款明细Controller
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@RestController
@RequestMapping("/item")
public class DtbBookRefundOrderItemController extends BaseController
{
    @Autowired
    private IDtbBookRefundOrderItemService dtbBookRefundOrderItemService;

    /**
     * 查询退款明细列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbBookRefundOrderItem dtbBookRefundOrderItem)
    {
        startPage();
        List<DtbBookRefundOrderItem> list = dtbBookRefundOrderItemService.selectDtbBookRefundOrderItemList(dtbBookRefundOrderItem);
        return getDataTable(list);
    }

    /**
     * 导出退款明细列表
     */
    @RequiresPermissions("shop:item:export")
    @Log(title = "导出退款明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbBookRefundOrderItem dtbBookRefundOrderItem)
    {
        List<DtbBookRefundOrderItem> list = dtbBookRefundOrderItemService.selectDtbBookRefundOrderItemList(dtbBookRefundOrderItem);
        ExcelUtil<DtbBookRefundOrderItem> util = new ExcelUtil<DtbBookRefundOrderItem>(DtbBookRefundOrderItem.class);
        util.exportExcel(response, list, "退款明细数据");
    }

    /**
     * 获取退款明细详细信息
     */
    @RequiresPermissions("shop:item:query")
    @GetMapping(value = "/{refundItemId}")
    public AjaxResult getInfo(@PathVariable("refundItemId") Long refundItemId)
    {
        return success(dtbBookRefundOrderItemService.selectDtbBookRefundOrderItemByRefundItemId(refundItemId));
    }

    /**
     * 新增退款明细
     */
    @RequiresPermissions("shop:item:add")
    @Log(title = "新增退款明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookRefundOrderItem dtbBookRefundOrderItem)
    {
        return toAjax(dtbBookRefundOrderItemService.insertDtbBookRefundOrderItem(dtbBookRefundOrderItem));
    }

    /**
     * 修改退款明细
     */
    @RequiresPermissions("shop:item:edit")
    @Log(title = "修改退款明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookRefundOrderItem dtbBookRefundOrderItem)
    {
        return toAjax(dtbBookRefundOrderItemService.updateDtbBookRefundOrderItem(dtbBookRefundOrderItem));
    }

    /**
     * 删除退款明细
     */
    @RequiresPermissions("shop:item:remove")
    @Log(title = "删除退款明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{refundItemIds}")
    public AjaxResult remove(@PathVariable Long[] refundItemIds)
    {
        return toAjax(dtbBookRefundOrderItemService.deleteDtbBookRefundOrderItemByRefundItemIds(Arrays.asList(refundItemIds)));
    }
}

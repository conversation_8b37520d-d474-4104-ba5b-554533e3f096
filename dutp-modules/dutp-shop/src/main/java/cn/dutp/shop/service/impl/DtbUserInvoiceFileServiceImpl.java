package cn.dutp.shop.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import cn.dutp.api.common.constant.NotificationConstants;
import cn.dutp.common.core.domain.R;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.message.api.RemoteUserMessageService;
import cn.dutp.message.api.domain.DutpUserMessage;
import cn.dutp.shop.domain.*;
import cn.dutp.shop.mapper.*;
import cn.dutp.shop.service.IDtbBookStatementService;
import cn.dutp.shop.service.IDtbUserInvoiceFileService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * DUTP-DTB-036发票文件Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@Service
public class DtbUserInvoiceFileServiceImpl extends ServiceImpl<DtbUserInvoiceFileMapper, DtbUserInvoiceFile> implements IDtbUserInvoiceFileService {
    @Autowired
    private DtbUserInvoiceFileMapper dtbUserInvoiceFileMapper;
    @Autowired
    private DtbUserInvoiceApplyMapper dtbUserInvoiceApplyMapper;
    @Autowired
    private IDtbBookStatementService dtbBookStatementService;
    @Autowired
    private DtbUserInvoiceTitleMapper dtbUserInvoiceTitleMapper;
    @Autowired
    private RemoteUserMessageService remoteUserMessageService;


    /**
     * 查询DUTP-DTB-036发票文件
     *
     * @param fileId DUTP-DTB-036发票文件主键
     * @return DUTP-DTB-036发票文件
     */
    @Override
    public DtbUserInvoiceFile selectDtbUserInvoiceFileByFileId(Long fileId) {
        return this.getById(fileId);
    }

    /**
     * 查询DUTP-DTB-036发票文件列表
     *
     * @param dtbUserInvoiceFile DUTP-DTB-036发票文件
     * @return DUTP-DTB-036发票文件
     */
    @Override
    public List<DtbUserInvoiceFile> selectDtbUserInvoiceFileList(DtbUserInvoiceFile dtbUserInvoiceFile) {
        LambdaQueryWrapper<DtbUserInvoiceFile> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotEmpty(dtbUserInvoiceFile.getApplyId())) {
            lambdaQueryWrapper.eq(DtbUserInvoiceFile::getApplyId
                    , dtbUserInvoiceFile.getApplyId());
        }
        if (ObjectUtil.isNotEmpty(dtbUserInvoiceFile.getFileUrl())) {
            lambdaQueryWrapper.eq(DtbUserInvoiceFile::getFileUrl
                    , dtbUserInvoiceFile.getFileUrl());
        }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增DUTP-DTB-036发票文件
     *
     * @param dtbUserInvoiceFile DUTP-DTB-036发票文件
     * @return 结果
     */
    @Override
    public boolean  insertDtbUserInvoiceFile(DtbUserInvoiceFile dtbUserInvoiceFile) {
        if (ObjectUtil.isEmpty(dtbUserInvoiceFile.getInvoiceCode())) {
            throw new ServiceException("请填写发票编码");
        }
        if (ObjectUtil.isEmpty(dtbUserInvoiceFile.getFileList())) {
            throw new ServiceException("请上传发票文件");
        }
        // 删除与 applyId 相关的旧文件记录
//        dtbUserInvoiceFileMapper.delete(queryWrapper);

        boolean allSaved = true;

        //换开次数
        Long applyId1 = dtbUserInvoiceFile.getApplyId();
        DtbUserInvoiceApply dtbUserInvoiceApply = dtbUserInvoiceApplyMapper.selectById(applyId1);
        if (dtbUserInvoiceApply.getApplyStatus() == 3) {
            if (ObjectUtil.isNotEmpty(dtbUserInvoiceApply)) {
                Integer currentChangeCount = dtbUserInvoiceApply.getChangeCount();
                dtbUserInvoiceApply.setChangeCount(currentChangeCount + 1);
                dtbUserInvoiceApplyMapper.updateById(dtbUserInvoiceApply);
            }
        }

        if (ObjectUtil.isNotEmpty(dtbUserInvoiceFile)) {
            List<DtbUserInvoiceFile> fileList = dtbUserInvoiceFile.getFileList();
            // fileType有值list
            List<DtbUserInvoiceFile> haveFileType = fileList.stream().filter(o -> ObjectUtil.isNotEmpty(o.getFileType())).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(haveFileType)) {
                LambdaUpdateWrapper<DtbUserInvoiceFile> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(DtbUserInvoiceFile::getApplyId, dtbUserInvoiceFile.getApplyId()).set(DtbUserInvoiceFile::getFileType, 1);
                this.update(updateWrapper);
            }


            // fileType没有值list
            List<DtbUserInvoiceFile> noHaveFileType = fileList.stream().filter(o -> ObjectUtil.isEmpty(o.getFileType())).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(noHaveFileType)) {
                for (DtbUserInvoiceFile file1 : noHaveFileType) {
                    // set文件对像
                    DtbUserInvoiceFile invoiceFile = new DtbUserInvoiceFile();
                    invoiceFile.setFileUrl(file1.getFileUrl());
                    invoiceFile.setFileName(file1.getFileName());
                    invoiceFile.setInvoiceCode(file1.getInvoiceCode());
                    invoiceFile.setApplyId(dtbUserInvoiceFile.getApplyId());
                    invoiceFile.setFileType(0);
                    boolean save = this.save(invoiceFile); // 保存文件
                    if (!save) {
                        allSaved = false;
                    }
                }
            }
            //修改开票状态
            Long applyId = dtbUserInvoiceFile.getApplyId();
            dtbUserInvoiceApplyMapper.changStatus(applyId);

            //修改结算单开票状态
            Long applyId2 = dtbUserInvoiceApply.getApplyId();
            DtbUserInvoiceApply dtbUserInvoiceApply1 = dtbUserInvoiceApplyMapper.selectById(applyId2);
            Long statementId = dtbUserInvoiceApply1.getStatementId();
            LambdaUpdateWrapper<DtbBookStatement> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(DtbBookStatement::getStatementId, statementId).set(DtbBookStatement::getInvoiceStatus, "invoiced");
            dtbBookStatementService.update(updateWrapper);


            //发送消息
            if (dtbUserInvoiceApply.getOrderType() == 1) {
                //零售发送给前台
                String title = "开票提醒";
                //接受者id
                Long userId = dtbUserInvoiceFile.getUserId();
                String content = String.format(NotificationConstants.OPEN_INVOICE_SALE, dtbUserInvoiceApply.getOrderNo());
                // 给前台发送消息
                sendMessage(title, content, userId);
            } else {
                //批量采购发送消息
                String title = "开票提醒";
                //接受者id
                String createByName = dtbUserInvoiceApply.getCreateBy();
                Long userId = dtbUserInvoiceApplyMapper.selectUserIdByName(createByName);
                String content = String.format(NotificationConstants.OPEN_INVOICE_EDU, dtbUserInvoiceApply.getStatementNo());
                // 发送消息
                DutpUserMessage message = new DutpUserMessage();
                message.setTitle(title);
                message.setContent(content);
                //发送者
                message.setFromUserId(SecurityUtils.getUserId());
                //接受者
                message.setToUserId(userId);
                // 1系统消息2教务消息3推送消息
                message.setMessageType(1);
                //发送者 用户类型 1后台 2前台
                message.setFromUserType(1);
                //接受者 用户类型 1后台 2前台
                message.setToUserType(1);
                R<Boolean> booleanR = remoteUserMessageService.addMessage(message);
                if (booleanR.getCode() == 500) {
                    throw new ServiceException("Message模块未启动，无法发送消息，发票无法上传！");
                }
            }
        }
        return allSaved;

    }
public void sendMessage(String title,String content,Long toUserId){
    boolean isResult = false;
    DutpUserMessage message = new DutpUserMessage();
    message.setTitle(title);
    message.setContent(content);
    //发送者
    message.setFromUserId(SecurityUtils.getUserId());
    //接受者
    message.setToUserId(toUserId);
    // 1系统消息2教务消息3推送消息
    message.setMessageType(1);
    //发送者 用户类型 1后台 2前台
    message.setFromUserType(1);
    //接受者 用户类型 1后台 2前台
    message.setToUserType(2);
    R<Boolean> booleanR = remoteUserMessageService.addMessage(message);
    if(booleanR.getCode() == 500){
        isResult = false;
        throw new ServiceException("Message模块未启动，无法发送消息，发票无法上传！");
    }

}

    private boolean deleteFile(Long fileId) {
        return dtbUserInvoiceFileMapper.deleteFileById(fileId);
    }

    /**
     * 修改DUTP-DTB-036发票文件
     *
     * @param dtbUserInvoiceFile DUTP-DTB-036发票文件
     * @return 结果
     */
    @Override
    public boolean updateDtbUserInvoiceFile(DtbUserInvoiceFile dtbUserInvoiceFile) {
        return this.updateById(dtbUserInvoiceFile);
    }

    /**
     * 批量删除DUTP-DTB-036发票文件
     *
     * @param fileIds 需要删除的DUTP-DTB-036发票文件主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbUserInvoiceFileByFileIds(List<Long> fileIds) {
        return this.removeByIds(fileIds);
    }

    @Override
    public List<DtbUserInvoiceFile> selectByApplyId(Long applyId) {
        return dtbUserInvoiceFileMapper.selectByApplyId(applyId);
    }


    @Override
    public boolean addsSaleInvoice(List<DtbUserInvoiceFile> dtbUserInvoiceFile) {
        //没有上传文件
        if (dtbUserInvoiceFile.size() == 0 || dtbUserInvoiceFile == null) {
            throw new ServiceException("请添加上传的文件");
        }

        boolean allSaved = true;
        for (DtbUserInvoiceFile file : dtbUserInvoiceFile) {
            String titleName = file.getTitleName();

                if(file.getOrderType() == 1){
                    //零售
                    List<DtbUserInvoiceTitle> saleList = dtbUserInvoiceTitleMapper.selectSaleListByName();
                    if (ObjectUtil.isNotNull(saleList)){
                        //包含并且长度是1
                        List<String> saleTitleNmaeList = saleList.stream().map(item -> item.getTitleName()).collect(Collectors.toList());
                        if (saleTitleNmaeList.contains(titleName) && saleTitleNmaeList.size() == 1 ){
                            saleList.forEach(userInvoiceTitle -> {
                                if(userInvoiceTitle.getApplyStatus() == 1){
                                    Long applyId = userInvoiceTitle.getApplyId();
                                    DtbUserInvoiceFile invoiceFile = new DtbUserInvoiceFile();
                                    invoiceFile.setApplyId(applyId);
                                    invoiceFile.setFileUrl(file.getFileUrl());
                                    invoiceFile.setFileName(file.getTitleName());
                                    invoiceFile.setInvoiceCode(file.getInvoiceCode());
                                    invoiceFile.setFileType(0);
                                    this.save(invoiceFile);
                                }else{
                                    throw new ServiceException("当前发票已经上传,无法上传发票");
                                }
                            });
                        }else if(saleTitleNmaeList.contains(titleName) && saleTitleNmaeList.size() > 1){
                            throw new ServiceException("抬头名称不是唯一的，请对应上传");
                        }else{
                            throw new ServiceException("没有对应抬头名称，无法上传");
                        }
                    }
                }else {
                    List<DtbUserInvoiceApply> eduList = dtbUserInvoiceTitleMapper.selectEduListByName();
                    if (ObjectUtil.isNotNull(eduList)) {
                        // 找出匹配的商户和学校
                        List<DtbUserInvoiceApply> matchedMerchants = eduList.stream()
                                .filter(item -> item.getApplyStatus() == 1)
                                .filter(item -> ObjectUtil.isNotEmpty(item.getMerchantTitleName()))
                                .filter(item -> item.getMerchantTitleName().equals(titleName))
                                .collect(Collectors.toList());

                        List<DtbUserInvoiceApply> matchedSchools = eduList.stream()
                                .filter(item -> item.getApplyStatus() == 1)
                                .filter(item -> ObjectUtil.isNotEmpty(item.getSchoolTitleName()))
                                .filter(item -> item.getSchoolTitleName().equals(titleName))
                                .collect(Collectors.toList());

                        // 判断逻辑
                        if (matchedMerchants.size() + matchedSchools.size() == 0) {
                            throw new ServiceException("没有对应抬头名称，无法上传");
                        } else if (matchedMerchants.size() > 1 || matchedSchools.size() > 1) {
                            throw new ServiceException("抬头名称不是唯一的，请对应上传");
                        } else {
                            // 获取唯一匹配的记录
                            DtbUserInvoiceApply matchedRecord = matchedMerchants.size() == 1
                                    ? matchedMerchants.get(0)
                                    : matchedSchools.get(0);

                            if (matchedRecord.getApplyStatus() == 1) {
                                DtbUserInvoiceFile invoiceFile = new DtbUserInvoiceFile();
                                invoiceFile.setApplyId(matchedRecord.getApplyId());
                                invoiceFile.setFileUrl(file.getFileUrl());
                                invoiceFile.setFileName(file.getTitleName());
                                invoiceFile.setInvoiceCode(file.getInvoiceCode());
                                invoiceFile.setFileType(0);
                                this.save(invoiceFile);

                                //修改开票状态
                                Long applyId = invoiceFile.getApplyId();
                                dtbUserInvoiceApplyMapper.changStatus(applyId);

                            } else {
                                throw new ServiceException("当前发票已经上传，无法上传发票");
                            }
                        }
                    }
                }
        }
        return allSaved;
    }

}

package cn.dutp.shop.service;

import cn.dutp.shop.domain.DtbBookMerchant;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 书商Service接口
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
public interface IDtbBookMerchantService extends IService<DtbBookMerchant>
{
    /**
     * 查询书商
     *
     * @param merchantId 书商主键
     * @return 书商
     */
    public DtbBookMerchant selectDtbBookMerchantByMerchantId(Long merchantId);

    /**
     * 查询书商列表
     *
     * @param dtbBookMerchant 书商
     * @return 书商集合
     */
    public List<DtbBookMerchant> selectDtbBookMerchantList(DtbBookMerchant dtbBookMerchant);

    /**
     * 新增书商
     *
     * @param dtbBookMerchant 书商
     * @return 结果
     */
    public boolean insertDtbBookMerchant(DtbBookMerchant dtbBookMerchant);

    /**
     * 修改书商
     *
     * @param dtbBookMerchant 书商
     * @return 结果
     */
    public boolean updateDtbBookMerchant(DtbBookMerchant dtbBookMerchant);

    /**
     * 批量删除书商
     *
     * @param merchantIds 需要删除的书商主键集合
     * @return 结果
     */
    public boolean deleteDtbBookMerchantByMerchantIds(List<Long> merchantIds);

}

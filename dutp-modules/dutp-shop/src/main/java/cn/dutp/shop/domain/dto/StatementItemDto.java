package cn.dutp.shop.domain.dto;

import cn.dutp.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: dutp
 * @date: 2025/2/20 16:06
 */
@Data
public class StatementItemDto extends BaseEntity {

    /** 结算单id */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long statementId;

    /** 订单id */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderId;

    /** 订单号 */
    private String orderNo;

    /** 教材名称 */
    private String bookName;

    /** issn */
    private String issn;

    /** isbn */
    private String isbn;

    /** 结算单原价 */
    private BigDecimal priceSale;

    private BigDecimal price;

    /** 订单原价 */
    private BigDecimal priceOrderItem;

    /** 书籍数量 */
    private Integer bookQuantity;

    /** 支付金额 */
    private BigDecimal payAmount;
}

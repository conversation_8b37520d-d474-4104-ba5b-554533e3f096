package cn.dutp.shop.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 退款的二维码对象 dtb_book_refund_order_code
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@Data
@TableName("dtb_book_refund_order_code")
public class DtbBookRefundOrderCode extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long refundCodeId;

    /**
     *
     */
    @Excel(name = "")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long refundOrderId;

    /**
     *
     */
    @Excel(name = "")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long refundItemId;

    /**
     *
     */
    @Excel(name = "")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long codeId;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("refundCodeId", getRefundCodeId())
                .append("refundOrderId", getRefundOrderId())
                .append("refundItemId", getRefundItemId())
                .append("codeId", getCodeId())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

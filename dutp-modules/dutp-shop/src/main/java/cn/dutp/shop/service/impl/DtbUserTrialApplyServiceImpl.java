package cn.dutp.shop.service.impl;

import cn.dutp.api.common.constant.DutpConstant;
import cn.dutp.api.common.constant.NotificationConstants;
import cn.dutp.common.core.domain.R;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.utils.StringUtils;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBook;
import cn.dutp.domain.DtbBookPurchaseCode;
import cn.dutp.domain.DutpSiteConfig;
import cn.dutp.message.api.RemoteUserMessageService;
import cn.dutp.message.api.domain.DutpUserMessage;
import cn.dutp.shop.domain.DtbUserInvoiceTitle;
import cn.dutp.shop.domain.DtbUserTrialApply;
import cn.dutp.shop.mapper.DtbBookPurchaseCodeMapper;
import cn.dutp.shop.mapper.DtbShopBookMapper;
import cn.dutp.shop.mapper.DtbUserTrialApplyMapper;
import cn.dutp.shop.mapper.SiteConfigMapper;
import cn.dutp.shop.service.IDtbUserTrialApplyService;
import cn.dutp.system.api.domain.DutpUser;
import cn.dutp.system.api.model.LoginUser;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * DUTP-DTB_025教师试用申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-07
 */
@Service
public class DtbUserTrialApplyServiceImpl extends ServiceImpl<DtbUserTrialApplyMapper, DtbUserTrialApply> implements IDtbUserTrialApplyService
{
    @Autowired
    private DtbUserTrialApplyMapper dtbUserTrialApplyMapper;

    @Autowired
    private DtbShopBookMapper dtbBookMapper;

    @Autowired
    private DtbBookPurchaseCodeMapper dtbBookPurchaseCodeMapper;

    @Autowired
    private SiteConfigMapper dutpSiteConfigMapper;

    @Autowired
    private RemoteUserMessageService remoteUserMessageService;


    /**
     * 查询DUTP-DTB_025教师试用申请
     *
     * @param applyId DUTP-DTB_025教师试用申请主键
     * @return DUTP-DTB_025教师试用申请
     */
    @Override
    public DtbUserTrialApply selectDtbUserTrialApplyByApplyId(Long applyId)
    {
        return dtbUserTrialApplyMapper.selectDtbUserTrialApplyByApplyId(applyId);
    }

    /**
     * 查询DUTP-DTB_025教师试用申请列表
     *
     * @param dtbUserTrialApply DUTP-DTB_025教师试用申请
     * @return DUTP-DTB_025教师试用申请
     */
    @Override
    public List<DtbUserTrialApply> selectDtbUserTrialApplyList(DtbUserTrialApply dtbUserTrialApply)
    {
        LambdaQueryWrapper<DtbUserTrialApply> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(ObjectUtil.isNotEmpty(dtbUserTrialApply.getUserId())) {
            lambdaQueryWrapper.eq(DtbUserTrialApply::getUserId
                    ,dtbUserTrialApply.getUserId());
        }
        if(ObjectUtil.isNotEmpty(dtbUserTrialApply.getBookId())) {
            lambdaQueryWrapper.eq(DtbUserTrialApply::getBookId
                    ,dtbUserTrialApply.getBookId());
        }
        if(ObjectUtil.isNotEmpty(dtbUserTrialApply.getTrialTime())) {
            lambdaQueryWrapper.eq(DtbUserTrialApply::getTrialTime
                    ,dtbUserTrialApply.getTrialTime());
        }
        if(ObjectUtil.isNotEmpty(dtbUserTrialApply.getExamineTime())) {
            lambdaQueryWrapper.eq(DtbUserTrialApply::getExamineTime
                    ,dtbUserTrialApply.getExamineTime());
        }
        if(ObjectUtil.isNotEmpty(dtbUserTrialApply.getExamineFileUrl())) {
            lambdaQueryWrapper.eq(DtbUserTrialApply::getExamineFileUrl
                    ,dtbUserTrialApply.getExamineFileUrl());
        }
        if(ObjectUtil.isNotEmpty(dtbUserTrialApply.getTrialDays())) {
            lambdaQueryWrapper.eq(DtbUserTrialApply::getTrialDays
                    ,dtbUserTrialApply.getTrialDays());
        }
        if(ObjectUtil.isNotEmpty(dtbUserTrialApply.getStatus())) {
            lambdaQueryWrapper.eq(DtbUserTrialApply::getStatus
                    ,dtbUserTrialApply.getStatus());
        }
        if(ObjectUtil.isNotEmpty(dtbUserTrialApply.getReject())) {
            lambdaQueryWrapper.eq(DtbUserTrialApply::getReject
                    ,dtbUserTrialApply.getReject());
        }
        return this.list(lambdaQueryWrapper);
    }
    /**
     * 学生教师端教师试用申请列表
     *
     * @param dtbUserTrialApply DUTP-DTB_025教师试用申请
     * @return DUTP-DTB_025教师试用申请
     */
    @Override
    public List<DtbUserTrialApply> listEducation(DtbUserTrialApply dtbUserTrialApply)
    {
        // 获取用户Id
        dtbUserTrialApply.setUserId(SecurityUtils.getUserId());
        return dtbUserTrialApplyMapper.listEducation(dtbUserTrialApply);
    }
    /**
     * 新增DUTP-DTB_025教师试用申请
     *
     * @param dtbUserTrialApply DUTP-DTB_025教师试用申请
     * @return 结果
     */
    @Override
    public boolean insertDtbUserTrialApply(DtbUserTrialApply dtbUserTrialApply)
    {
        return this.save(dtbUserTrialApply);
    }

    /**
     * 修改DUTP-DTB_025教师试用申请
     *
     * @param dtbUserTrialApply DUTP-DTB_025教师试用申请
     * @return 结果
     */
    @Override
    public boolean updateDtbUserTrialApply(DtbUserTrialApply dtbUserTrialApply)
    {
        DtbUserTrialApply dtb = dtbUserTrialApplyMapper.selectDtbUserTrialApplyByApplyId(dtbUserTrialApply.getApplyId());
        if ((StringUtils.isNotBlank(dtbUserTrialApply.getReject()) && dtbUserTrialApply.getStatus() == 2) || (dtbUserTrialApply.getStatus() != dtb.getStatus() && dtbUserTrialApply.getStatus() != 3)) {
            dtbUserTrialApply.setExamineTime(new Date());
        }
        boolean flag = this.updateById(dtbUserTrialApply);
        // 审批驳回发送消息
        if (dtbUserTrialApply.getStatus() == 2) {
            String title = "试用审核提醒";
            String content = String.format("尊敬的用户，很抱歉，您的试用申请未通过审核，驳回原因：%s，感谢您的关注与支持。如有疑问，请联系我们。", dtbUserTrialApply.getReject());
            sendMsg(title, content, dtbUserTrialApply.getUserId());
        }
        return flag;
    }

    /**
     * 学生教师端修改教师试用申请
     *
     * @param dtbUserTrialApply DUTP-DTB_025教师试用申请
     * @return 结果
     */
    @Override
    public boolean editEducation(DtbUserTrialApply dtbUserTrialApply)
    {
        // 更新教师试用申请
        DtbUserTrialApply  updateApply = new DtbUserTrialApply();
        updateApply.setApplyId(dtbUserTrialApply.getApplyId());
        updateApply.setExamineFileUrl(dtbUserTrialApply.getExamineFileUrl());
        updateApply.setTrialTime(dtbUserTrialApply.getTrialTime());
        updateApply.setApplicationReason(dtbUserTrialApply.getApplicationReason());
        // 更新数字教材
        DtbBook updateDtbBook = new DtbBook();
        updateDtbBook.setBookId(dtbUserTrialApply.getBookId());
        updateDtbBook.setIsbn(dtbUserTrialApply.getIsbn());
        return baseMapper.updateById(updateApply) > 0 && dtbBookMapper.updateById(updateDtbBook) > 0;
    }

    /**
     * 批量删除DUTP-DTB_025教师试用申请
     *
     * @param applyIds 需要删除的DUTP-DTB_025教师试用申请主键
     * @return 结果
     */
    @Override
    @Transactional
    public boolean deleteDtbUserTrialApplyByApplyIds(List<Long> applyIds)
    {
        return this.removeByIds(applyIds);
    }

    @Override
    public List<DtbUserTrialApply> TrialShopList(DtbUserTrialApply dtbUserTrialApply) {
        return dtbUserTrialApplyMapper.TrialShopList(dtbUserTrialApply);
    }

    @Override
    @Transactional
    public DtbUserTrialApply binding(DtbUserTrialApply dtbUserTrialApply) {
        List<DtbBookPurchaseCode> list = getTemporaryCode(dtbUserTrialApply);
        if (!CollectionUtils.isEmpty(list)) {
            Random random = new Random();
            int randomIndex = random.nextInt(list.size());
            DtbBookPurchaseCode code = list.get(randomIndex);
            code.setUserId(dtbUserTrialApply.getUserId());
            code.setBindDate(new Date());
            code.setState(2);
            dtbBookPurchaseCodeMapper.updateById(code);
        }
        return dtbUserTrialApply;
    }

    @Override
    public List<DtbBookPurchaseCode> getTemporaryCode(DtbUserTrialApply dtbUserTrialApply) {
        QueryWrapper<DutpSiteConfig> configQueryWrapper = new QueryWrapper<>();
        configQueryWrapper.lambda().eq(DutpSiteConfig::getConfigId,1);
        DutpSiteConfig config = dutpSiteConfigMapper.selectOne(configQueryWrapper);
        QueryWrapper<DtbBookPurchaseCode> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DtbBookPurchaseCode::getBookId, dtbUserTrialApply.getBookId())
                .eq(DtbBookPurchaseCode::getState,1)
                .eq(DtbBookPurchaseCode::getCodeType,2)
                .eq(DtbBookPurchaseCode::getTimeLimit,dtbUserTrialApply.getTrialDays())
                .eq(DtbBookPurchaseCode::getIsFrozen,0);
        List<DtbBookPurchaseCode> list = dtbBookPurchaseCodeMapper.selectList(queryWrapper);
        return list;
    }



    /**
     * 学生教师端教师提交试用申请
     *
     * @param dtbUserTrialApply 教师试用申请
     * @return 教师试用申请结果
     */
    @Override
    public R<String> submitTrialApplicationEducation(DtbUserTrialApply dtbUserTrialApply)
    {
        QueryWrapper<DtbUserTrialApply> queryWrapper = new QueryWrapper<>();
        // 获取用户Id
        dtbUserTrialApply.setUserId(SecurityUtils.getUserId());
        queryWrapper.lambda().eq(DtbUserTrialApply::getUserId, dtbUserTrialApply.getUserId());
        queryWrapper.lambda().in(DtbUserTrialApply::getStatus, DutpConstant.NUM_ZERO,DutpConstant.NUM_ONE);

        List<DtbUserTrialApply>  list = dtbUserTrialApplyMapper.selectList(queryWrapper);
        // 按照 activationTime 时间倒序分组
        Map<Long, List<DtbUserTrialApply>> groupedByBookId = list.stream()
                .collect(Collectors.groupingBy(
                        DtbUserTrialApply::getBookId,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                lst -> lst.stream()
                                        .sorted(Comparator.comparing(
                                                DtbUserTrialApply::getTrialTime,
                                                // 处理 null 值
                                                Comparator.nullsLast(Comparator.naturalOrder())
                                        ).reversed())
                                        .collect(Collectors.toList())
                        )
                ));
        // 获取当前申请的bookId
        Long bookId = dtbUserTrialApply.getBookId();
        // 判断分组后的bookId是否包含当前申请的bookId
        boolean containsBookId = groupedByBookId.containsKey(bookId);
        if(groupedByBookId.size() >= DutpConstant.NUM_TWO  && !containsBookId) {
            return R.fail(DutpConstant.EXCEEDING_THE_TRIAL_QUANTITY);
        }
        // 试用时间是否过期
        else {
            // 取与bookId相同的分组数据，并取第一条
            List<DtbUserTrialApply> group = groupedByBookId.get(bookId);
            if (group != null && !group.isEmpty()) {
                DtbUserTrialApply firstApply = group.get(0);
                Date activationTime = firstApply.getActivationTime();
                // 激活时间为空
                if (activationTime == null) {
                    if(DutpConstant.LONG_ONE.equals(firstApply.getStatus())){
                        return R.fail(DutpConstant.NOT_ACTIVE);
                    }else if(DutpConstant.LONG_ZERO.equals(firstApply.getStatus())){
                        return R.fail(DutpConstant.PENDING_APPROVAL);
                    }
                } else {
                    int trialDays = Math.toIntExact(firstApply.getTrialDays());
                    // 使用Calendar计算试用结束时间
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(activationTime);
                    calendar.add(Calendar.DAY_OF_MONTH, trialDays);
                    Date trialEndTime = calendar.getTime();
                    // 获取当前日期时间
                    Date now = new Date();
                    // 比较试用结束时间与当前日期时间
                    if (trialEndTime.after(now) || trialEndTime.equals(now)) {
                        return R.fail(DutpConstant.TIME_IS_NOT_OVER_YET);
                    } else {
                        dtbUserTrialApply.setTrialTime(now);
                        dtbUserTrialApplyMapper.insert(dtbUserTrialApply);
                    }
                }
            }else {
                dtbUserTrialApply.setTrialTime(new Date());
                dtbUserTrialApplyMapper.insert(dtbUserTrialApply);
            }
        }
        // 发送消息
        // 查询管理员用户信息
        List<Long> superUser = baseMapper.getSuperUser();
        // 获取用户信息
        DutpUser dutpUser = baseMapper.getUserInfo(SecurityUtils.getUserId());
        // 发送消息
        superUser.forEach(userId -> {
            // 发送消息通知管理员
            DutpUserMessage dutpUserMessage = new DutpUserMessage();
            dutpUserMessage.setContent(String.format(NotificationConstants.TRIAL_REVIEW_REMINDER, dutpUser.getSchoolName(), dutpUser.getRealName()));
            dutpUserMessage.setTitle(NotificationConstants.TRIAL_REVIEW_REMINDER_TITLE);
            dutpUserMessage.setFromUserId(dutpUser.getUserId());
            dutpUserMessage.setToUserId(userId);
            dutpUserMessage.setMessageType(DutpConstant.NUM_ONE);
            dutpUserMessage.setReadFlag(DutpConstant.NUM_ZERO);
            dutpUserMessage.setFromUserType(DutpConstant.NUM_TWO);
            dutpUserMessage.setToUserType(DutpConstant.NUM_ONE);
            boolean isSendMessageSuccess = remoteUserMessageService.addMessage(dutpUserMessage).getCode() == 200;
            // 检查消息发送是否成功
            if (!isSendMessageSuccess) {
                throw new RuntimeException(DutpConstant.SENDING_MESSAGE_FAILED);
            }
        });
        return R.ok(String.valueOf(dtbUserTrialApply.getApplyId()),DutpConstant.OPERATION_SUCCESSFUL);
    }

    @Override
    public AjaxResult agreeTrial(DtbUserTrialApply dtbUserTrialApply) {
        //一年内已累计提交2本"不同"的教材试用申请，不允许再次申请不允许通过
        QueryWrapper<DtbUserTrialApply> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DtbUserTrialApply::getUserId, dtbUserTrialApply.getUserId())
                .in(DtbUserTrialApply::getStatus, 1,3);
        List<DtbUserTrialApply> dtbUserTrialApplyList = dtbUserTrialApplyMapper.selectList(queryWrapper);
        List<Long> bookIdList = dtbUserTrialApplyList.stream()
               .map(DtbUserTrialApply::getBookId).distinct()
               .collect(Collectors.toList());
        if (bookIdList.size() >= 2) {
            return AjaxResult.error("一年内已累计提交2本不同的教材试用申请，不允许再次申请");
        } else {
            DtbUserTrialApply dtb = dtbUserTrialApplyMapper.selectDtbUserTrialApplyByApplyId(dtbUserTrialApply.getApplyId());
            if ((StringUtils.isNotBlank(dtbUserTrialApply.getReject()) && dtbUserTrialApply.getStatus() == 2) || (dtbUserTrialApply.getStatus() != dtb.getStatus() && dtbUserTrialApply.getStatus() != 3)) {
                dtbUserTrialApply.setExamineTime(new Date());
            }
            this.updateById(dtbUserTrialApply);
            // 发送消息
            String title = "试用审核提醒";
            String content = String.format("尊敬的用户您好，您的试用申请已通过，试用时间为%s天，现在您就可以开启精彩的阅读之旅啦。", dtbUserTrialApply.getTrialDays());
            sendMsg(title, content, dtbUserTrialApply.getUserId());
            return AjaxResult.success();
        }
    }

    public void sendMsg(String title,String content,Long toUserId) {
        boolean isResult = false;
        DutpUserMessage dutpUserMessage = new DutpUserMessage();
        dutpUserMessage.setContent(content);
        dutpUserMessage.setTitle(title);
        // 发送者
        dutpUserMessage.setFromUserId(SecurityUtils.getUserId());
        // 接收者
        dutpUserMessage.setToUserId(toUserId);
        // 1系统消息2教务消息3推送消息
        dutpUserMessage.setMessageType(1);
        // 用户类型1后台用户2前台用户
        dutpUserMessage.setFromUserType(1);
        // 用户类型1后台用户2前台用户
        dutpUserMessage.setToUserType(2);
        R<Boolean> booleanR = remoteUserMessageService.addMessage(dutpUserMessage);
        if (booleanR.getCode() == 500) {
            isResult = false;
            throw new ServiceException("Message模块未启动，无法发送消息，订单提交失败！");
        }
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.shop.mapper.DtbShopBookMapper">
    
    <resultMap type="DtbBook" id="DtbBookResult">
        <result property="bookId"    column="book_id"    />
        <result property="bookName"    column="book_name"    />
        <result property="cover"    column="cover"    />
        <result property="ibsn"    column="ibsn"    />
        <result property="bookNo"    column="book_no"    />
        <result property="publishDate"    column="publish_date"    />
        <result property="publishOrganization"    column="publish_organization"    />
        <result property="publishStatus"    column="publish_status"    />
        <result property="schoolId"    column="school_id"    />
        <result property="bookType"    column="book_type"    />
        <result property="writerId"    column="writer_id"    />
        <result property="soldQuantity"    column="sold_quantity"    />
        <result property="readQuantity"    column="read_quantity"    />
        <result property="priceCounter"    column="price_counter"    />
        <result property="priceSale"    column="price_sale"    />
        <result property="bookOrganize"    column="book_organize"    />
        <result property="topicNo"    column="topic_no"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="shelfTime"    column="shelf_time"    />
        <result property="unshelfTime"    column="unshelf_time"    />
        <result property="shelfState"    column="shelf_state"    />
    </resultMap>

    <sql id="selectDtbBookVo">
        select book_id, book_name, cover, ibsn, book_no, publish_date, publish_organization, publish_status, school_id, book_type, writer_id, sold_quantity, read_quantity, price_counter, price_sale, book_organize, topic_no, del_flag, create_by, create_time, update_by, update_time, shelf_time, unshelf_time, shelf_state from dtb_book
    </sql>

    <select id="selectDtbBookList" parameterType="DtbBook" resultMap="DtbBookResult">
        <include refid="selectDtbBookVo"/>
        <where>  
            <if test="bookName != null  and bookName != ''"> and book_name like concat('%', #{bookName}, '%')</if>
            <if test="cover != null  and cover != ''"> and cover = #{cover}</if>
            <if test="ibsn != null  and ibsn != ''"> and ibsn = #{ibsn}</if>
            <if test="bookNo != null  and bookNo != ''"> and book_no = #{bookNo}</if>
            <if test="publishDate != null "> and publish_date = #{publishDate}</if>
            <if test="publishOrganization != null  and publishOrganization != ''"> and publish_organization = #{publishOrganization}</if>
            <if test="publishStatus != null "> and publish_status = #{publishStatus}</if>
            <if test="schoolId != null "> and school_id = #{schoolId}</if>
            <if test="bookType != null "> and book_type = #{bookType}</if>
            <if test="writerId != null "> and writer_id = #{writerId}</if>
            <if test="soldQuantity != null "> and sold_quantity = #{soldQuantity}</if>
            <if test="readQuantity != null "> and read_quantity = #{readQuantity}</if>
            <if test="priceCounter != null "> and price_counter = #{priceCounter}</if>
            <if test="priceSale != null "> and price_sale = #{priceSale}</if>
            <if test="bookOrganize != null "> and book_organize = #{bookOrganize}</if>
            <if test="topicNo != null  and topicNo != ''"> and topic_no = #{topicNo}</if>
            <if test="shelfTime != null "> and shelf_time = #{shelfTime}</if>
            <if test="unshelfTime != null "> and unshelf_time = #{unshelfTime}</if>
            <if test="shelfState != null "> and shelf_state = #{shelfState}</if>
        </where>
    </select>
    
    <select id="selectDtbBookByBookId" parameterType="Long" resultMap="DtbBookResult">
        <include refid="selectDtbBookVo"/>
        where book_id = #{bookId}
    </select>

    <insert id="insertDtbBook" parameterType="DtbBook" useGeneratedKeys="true" keyProperty="bookId">
        insert into dtb_book
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bookName != null">book_name,</if>
            <if test="cover != null">cover,</if>
            <if test="ibsn != null">ibsn,</if>
            <if test="bookNo != null">book_no,</if>
            <if test="publishDate != null">publish_date,</if>
            <if test="publishOrganization != null">publish_organization,</if>
            <if test="publishStatus != null">publish_status,</if>
            <if test="schoolId != null">school_id,</if>
            <if test="bookType != null">book_type,</if>
            <if test="writerId != null">writer_id,</if>
            <if test="soldQuantity != null">sold_quantity,</if>
            <if test="readQuantity != null">read_quantity,</if>
            <if test="priceCounter != null">price_counter,</if>
            <if test="priceSale != null">price_sale,</if>
            <if test="bookOrganize != null">book_organize,</if>
            <if test="topicNo != null">topic_no,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="shelfTime != null">shelf_time,</if>
            <if test="unshelfTime != null">unshelf_time,</if>
            <if test="shelfState != null">shelf_state,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bookName != null">#{bookName},</if>
            <if test="cover != null">#{cover},</if>
            <if test="ibsn != null">#{ibsn},</if>
            <if test="bookNo != null">#{bookNo},</if>
            <if test="publishDate != null">#{publishDate},</if>
            <if test="publishOrganization != null">#{publishOrganization},</if>
            <if test="publishStatus != null">#{publishStatus},</if>
            <if test="schoolId != null">#{schoolId},</if>
            <if test="bookType != null">#{bookType},</if>
            <if test="writerId != null">#{writerId},</if>
            <if test="soldQuantity != null">#{soldQuantity},</if>
            <if test="readQuantity != null">#{readQuantity},</if>
            <if test="priceCounter != null">#{priceCounter},</if>
            <if test="priceSale != null">#{priceSale},</if>
            <if test="bookOrganize != null">#{bookOrganize},</if>
            <if test="topicNo != null">#{topicNo},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="shelfTime != null">#{shelfTime},</if>
            <if test="unshelfTime != null">#{unshelfTime},</if>
            <if test="shelfState != null">#{shelfState},</if>
         </trim>
    </insert>

    <update id="updateDtbBook" parameterType="DtbBook">
        update dtb_book
        <trim prefix="SET" suffixOverrides=",">
            <if test="bookName != null">book_name = #{bookName},</if>
            <if test="cover != null">cover = #{cover},</if>
            <if test="ibsn != null">ibsn = #{ibsn},</if>
            <if test="bookNo != null">book_no = #{bookNo},</if>
            <if test="publishDate != null">publish_date = #{publishDate},</if>
            <if test="publishOrganization != null">publish_organization = #{publishOrganization},</if>
            <if test="publishStatus != null">publish_status = #{publishStatus},</if>
            <if test="schoolId != null">school_id = #{schoolId},</if>
            <if test="bookType != null">book_type = #{bookType},</if>
            <if test="writerId != null">writer_id = #{writerId},</if>
            <if test="soldQuantity != null">sold_quantity = #{soldQuantity},</if>
            <if test="readQuantity != null">read_quantity = #{readQuantity},</if>
            <if test="priceCounter != null">price_counter = #{priceCounter},</if>
            <if test="priceSale != null">price_sale = #{priceSale},</if>
            <if test="bookOrganize != null">book_organize = #{bookOrganize},</if>
            <if test="topicNo != null">topic_no = #{topicNo},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="shelfTime != null">shelf_time = #{shelfTime},</if>
            <if test="unshelfTime != null">unshelf_time = #{unshelfTime},</if>
            <if test="shelfState != null">shelf_state = #{shelfState},</if>
        </trim>
        where book_id = #{bookId}
    </update>

    <delete id="deleteDtbBookByBookId" parameterType="Long">
        delete from dtb_book where book_id = #{bookId}
    </delete>

    <delete id="deleteDtbBookByBookIds" parameterType="String">
        delete from dtb_book where book_id in 
        <foreach item="bookId" collection="array" open="(" separator="," close=")">
            #{bookId}
        </foreach>
    </delete>

    <select id="bookShopList" parameterType="cn.dutp.domain.DtbBook" resultType="cn.dutp.domain.DtbBook">
        select
            dtb.book_id,
            dtb.book_name,
            dtb.cover,
            dtb.book_no,
            dtb.school_id,
            IF(dtb.isbn IS NULL OR dtb.isbn = '', dtb.issn, dtb.isbn) AS isbn,
            dtb.book_type,
            dtb.shelf_time,
            dtb.shelf_time as shelfFullTime,
            dtb.price_counter,
            dtb.price_sale,
            dtb.master_flag,
            dtb.shelf_state,
            dtb.update_time,
            (select count(0) from dtb_book where master_flag = 3 and master_book_id = dtb.book_id) as childCount,
            (select count(0) from dtb_book_purchase_code where book_id = dtb.book_id and code_type = 1 and del_flag = 0 and state = 1) as inventory
        from
            dtb_book dtb
        <where>
            dtb.del_flag = 0 and dtb.master_flag != 3 and ((dtb.shelf_state in(1,2) and dtb.publish_status = 2) or (dtb.shelf_state = 4)) and dtb.book_organize = 1
            <if test="bookName != null and bookName != ''">and dtb.book_name like concat('%', #{bookName}, '%')</if>
            <if test="inventoryStatus == 1">and inventory <![CDATA[<]]> 5</if>
            <if test="inventoryStatus == 2">and inventory <![CDATA[>=]]> 5</if>
            <if test="shelfState != null">and dtb.shelf_state = #{shelfState}</if>
        </where>
        order by dtb.publish_date desc
    </select>

    <select id="selectLeaderIdByBookId" resultType="Long" parameterType="Long">
        SELECT sd.leader_id
        FROM dtb_book db
                 LEFT JOIN sys_dept sd
                           ON db.dept_id = sd.dept_id
        WHERE db.book_id = #{bookId}
          AND db.del_flag = '0'
    </select>
    <select id="selectPartnersDtbBookList" resultType="cn.dutp.domain.DtbBook" parameterType="cn.dutp.domain.DtbBook">
        SELECT
            b.book_id,
            b.book_name,
            b.author_label,
            b.author_value,
            b.cover,
            b.isbn,
            b.price_counter,
            b.price_sale,
            b.read_quantity
        FROM dtb_book b INNER JOIN dtb_book_school bs
            ON b.book_id = bs.book_id
        WHERE
        b.shelf_state = 1
        <if test="schoolId != null  and schoolId != ''"> and  bs.school_id = #{schoolId}</if>
    </select>
</mapper>
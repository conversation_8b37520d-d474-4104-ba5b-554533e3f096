package cn.dutp.shop.domain.vo;

import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * DUTP-DTB-031订单对应的书籍明细
 *
 * @TableName dtb_book_order_item
 */
@TableName(value = "dtb_book_order_item")
@Data
public class DtbBookOrderItemEditApprovalVo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 修改申请的采购学校
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long schoolId;

    /**
     * 学校名称
     */
    private String schoolName;

    /**
     * 经办人ID，大区下的人员
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long operatorId;

    /**
     * 修改申请的经办人
     */
    private String nickName;

    /**
     * 书商ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long merchantId;

    /**
     * 修改申请的书商名称
     */
    private String merchanName;

    /**
     * 修改申请的应付金额
     */
    private BigDecimal payAmount;

    /**
     * 修改申请的商品总额
     */
    private BigDecimal price;
    /**
     * 修改申请的订单详细list
     */
    private List<DtbBookOrderItemVo> editApprovalList;


}
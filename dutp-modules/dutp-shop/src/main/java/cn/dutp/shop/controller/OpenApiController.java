package cn.dutp.shop.controller;

import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.domain.DtbBook;
import cn.dutp.shop.service.IDtbBookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 数字商城，开发接口
 *
 * <AUTHOR>
 * @date 2024-11-14
 */

@RestController
@RequestMapping("/openApi")
public class OpenApiController extends BaseController {
    @Autowired
    private IDtbBookService dtbBookService;
    @GetMapping("/listByQuery")
    public AjaxResult listByQuery(DtbBook dtbBook)
    {
        List<DtbBook> list = dtbBookService.selectPartnersDtbBookList(dtbBook);
        return success(list);
    }
    /**
     * 查询购买教材
     *
     * @param dtbBook DUTP-DTB_002数字教材
     * @return 数字教材集合
     */
    @GetMapping("/getShopBook")
    public AjaxResult getShopBook(DtbBook dtbBook)
    {
        return success(dtbBookService.getShopBook(dtbBook));
    }
}

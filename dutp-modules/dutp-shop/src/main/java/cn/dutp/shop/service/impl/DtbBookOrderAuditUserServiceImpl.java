package cn.dutp.shop.service.impl;

import cn.dutp.domain.DtbBookOrderAuditUser;
import cn.dutp.shop.domain.vo.DtbBookOrderAuditUserVo;
import cn.dutp.shop.mapper.DtbBookOrderAuditUserMapper;
import cn.dutp.shop.service.IDtbBookOrderAuditUserService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 订单审核人Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Service
public class DtbBookOrderAuditUserServiceImpl extends ServiceImpl<DtbBookOrderAuditUserMapper, DtbBookOrderAuditUser> implements IDtbBookOrderAuditUserService
{
    @Autowired
    private DtbBookOrderAuditUserMapper dtbBookOrderAuditUserMapper;

    /**
     * 查询订单审核人
     *
     * @param auditUserId 订单审核人主键
     * @return 订单审核人
     */
    @Override
    public DtbBookOrderAuditUser selectDtbBookOrderAuditUserByAuditUserId(Long auditUserId)
    {
        return this.getById(auditUserId);
    }

    /**
     * 查询订单审核人列表
     *
     * @param dtbBookOrderAuditUser 订单审核人
     * @return 订单审核人
     */
    @Override
    public List<DtbBookOrderAuditUser> selectDtbBookOrderAuditUserList(DtbBookOrderAuditUser dtbBookOrderAuditUser)
    {
        LambdaQueryWrapper<DtbBookOrderAuditUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbBookOrderAuditUser.getOrderId())) {
                lambdaQueryWrapper.eq(DtbBookOrderAuditUser::getOrderId
                ,dtbBookOrderAuditUser.getOrderId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookOrderAuditUser.getUserId())) {
                lambdaQueryWrapper.eq(DtbBookOrderAuditUser::getUserId
                ,dtbBookOrderAuditUser.getUserId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookOrderAuditUser.getAuditType())) {
                lambdaQueryWrapper.eq(DtbBookOrderAuditUser::getAuditType
                ,dtbBookOrderAuditUser.getAuditType());
            }
                if(ObjectUtil.isNotEmpty(dtbBookOrderAuditUser.getAuditDate())) {
                lambdaQueryWrapper.eq(DtbBookOrderAuditUser::getAuditDate
                ,dtbBookOrderAuditUser.getAuditDate());
            }
                if(ObjectUtil.isNotEmpty(dtbBookOrderAuditUser.getAuditStatus())) {
                lambdaQueryWrapper.eq(DtbBookOrderAuditUser::getAuditStatus
                ,dtbBookOrderAuditUser.getAuditStatus());
            }
                if(ObjectUtil.isNotEmpty(dtbBookOrderAuditUser.getAuditContent())) {
                lambdaQueryWrapper.eq(DtbBookOrderAuditUser::getAuditContent
                ,dtbBookOrderAuditUser.getAuditContent());
            }
                if(ObjectUtil.isNotEmpty(dtbBookOrderAuditUser.getEditContent())) {
                lambdaQueryWrapper.eq(DtbBookOrderAuditUser::getEditContent
                ,dtbBookOrderAuditUser.getEditContent());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增订单审核人
     *
     * @param dtbBookOrderAuditUser 订单审核人
     * @return 结果
     */
    @Override
    public boolean insertDtbBookOrderAuditUser(DtbBookOrderAuditUser dtbBookOrderAuditUser)
    {
        return this.save(dtbBookOrderAuditUser);
    }

    /**
     * 修改订单审核人
     *
     * @param dtbBookOrderAuditUser 订单审核人
     * @return 结果
     */
    @Override
    public boolean updateDtbBookOrderAuditUser(DtbBookOrderAuditUser dtbBookOrderAuditUser)
    {
        return this.updateById(dtbBookOrderAuditUser);
    }

    /**
     * 批量删除订单审核人
     *
     * @param auditUserIds 需要删除的订单审核人主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookOrderAuditUserByAuditUserIds(List<Long> auditUserIds)
    {
        return this.removeByIds(auditUserIds);
    }

    /**
     * 根据订单id 查询样书一级审核信息
     *
     * @param orderId 订单id
     * @return 一级审核信息
     */
    @Override
    public DtbBookOrderAuditUserVo selectDtbBookOrderAuditUserByOrderId(Long orderId) {
        return dtbBookOrderAuditUserMapper.selectDtbBookOrderAuditUserByOrderId(orderId);
    }

}

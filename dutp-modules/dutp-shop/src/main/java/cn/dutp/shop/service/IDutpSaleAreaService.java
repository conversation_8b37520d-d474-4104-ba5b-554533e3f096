package cn.dutp.shop.service;

import cn.dutp.domain.DutpSaleArea;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 销售大区Service接口
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
public interface IDutpSaleAreaService extends IService<DutpSaleArea>
{
    /**
     * 查询销售大区
     *
     * @param areaId 销售大区主键
     * @return 销售大区
     */
    public DutpSaleArea selectDutpSaleAreaByAreaId(Long areaId);

    /**
     * 查询销售大区列表
     *
     * @param dutpSaleArea 销售大区
     * @return 销售大区集合
     */
    public List<DutpSaleArea> selectDutpSaleAreaList(DutpSaleArea dutpSaleArea);

    /**
     * 新增销售大区
     *
     * @param dutpSaleArea 销售大区
     * @return 结果
     */
    public boolean insertDutpSaleArea(DutpSaleArea dutpSaleArea);

    /**
     * 修改销售大区
     *
     * @param dutpSaleArea 销售大区
     * @return 结果
     */
    public boolean updateDutpSaleArea(DutpSaleArea dutpSaleArea);

    /**
     * 批量删除销售大区
     *
     * @param areaIds 需要删除的销售大区主键集合
     * @return 结果
     */
    public boolean deleteDutpSaleAreaByAreaIds(List<Long> areaIds);

    List<DutpSaleArea> getSaleAreaAndMember(DutpSaleArea dutpSaleArea);
}

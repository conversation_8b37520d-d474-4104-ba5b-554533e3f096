package cn.dutp.shop.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.common.log.enums.OperatorType;
import cn.dutp.domain.DtbBookRefundOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.shop.domain.dto.BookRefundOrderVO;
import cn.dutp.shop.service.IDtbBookRefundOrderService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.core.exception.ServiceException;

/**
 * 售后退款订单Controller
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@RestController
@RequestMapping("/refundOrder")
public class DtbBookRefundOrderController extends BaseController
{
    @Autowired
    private IDtbBookRefundOrderService dtbBookRefundOrderService;

    /**
     * 查询售后退款订单列表
     */
    @GetMapping("/list")
    public TableDataInfo list(BookRefundOrderVO dtbBookRefundOrder)
    {
        startPage();
        List<BookRefundOrderVO> list = dtbBookRefundOrderService.selectRefundOrderVOList(dtbBookRefundOrder);
        return getDataTable(list);
    }

    /**
     * 导出售后退款订单列表
     */
    @RequiresPermissions("shop:refundOrder:export")
    @Log(title = "导出售后退款订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbBookRefundOrder dtbBookRefundOrder)
    {
        List<DtbBookRefundOrder> list = dtbBookRefundOrderService.selectDtbBookRefundOrderList(dtbBookRefundOrder);
        ExcelUtil<DtbBookRefundOrder> util = new ExcelUtil<DtbBookRefundOrder>(DtbBookRefundOrder.class);
        util.exportExcel(response, list, "售后退款订单数据");
    }

    /**
     * 获取售后退款订单详细信息
     */
    @RequiresPermissions("shop:refundOrder:query")
    @GetMapping(value = "/{refundOrderId}")
    public AjaxResult getInfo(@PathVariable("refundOrderId") Long refundOrderId)
    {
        return success(dtbBookRefundOrderService.selectRefundOrderVOByRefundOrderId(refundOrderId));
    }

    /**
     * 学生教师端新增售后退款订单
     */
    @Log(title = "学生教师端新增售后退款订单", businessType = BusinessType.INSERT, operatorType = OperatorType.READER)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookRefundOrder dtbBookRefundOrder)
    {
        return toAjax(dtbBookRefundOrderService.insertDtbBookRefundOrder(dtbBookRefundOrder));
    }

    /**
     * 修改售后退款订单
     */
    @RequiresPermissions("shop:refundOrder:edit")
    @Log(title = "修改售后退款订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookRefundOrder dtbBookRefundOrder)
    {
        return toAjax(dtbBookRefundOrderService.updateDtbBookRefundOrder(dtbBookRefundOrder));
    }

    /**
     * 删除售后退款订单
     */
    @RequiresPermissions("shop:refundOrder:remove")
    @Log(title = "删除售后退款订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{refundOrderIds}")
    public AjaxResult remove(@PathVariable Long[] refundOrderIds)
    {
        return toAjax(dtbBookRefundOrderService.deleteDtbBookRefundOrderByRefundOrderIds(Arrays.asList(refundOrderIds)));
    }

    /**
     * 批量处理退款
     */
    @RequiresPermissions("shop:refundOrder:edit")
    @Log(title = "售后退款订单", businessType = BusinessType.UPDATE)
    @PutMapping("/batchRefund")
    public AjaxResult batchRefund(@RequestBody BookRefundOrderVO refundOrderVO) {
        try {
            if (refundOrderVO.getRefundOrderIds() == null || refundOrderVO.getRefundOrderIds().isEmpty()) {
                return error("退款订单ID不能为空");
            }
            return toAjax(dtbBookRefundOrderService.batchRefund(refundOrderVO));
        } catch (ServiceException e) {
            return error(e.getMessage());
        } catch (Exception e) {
            return error("批量处理退款失败，请联系管理员");
        }
    }
}

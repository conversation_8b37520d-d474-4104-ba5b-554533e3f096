package cn.dutp.shop.service;

import cn.dutp.shop.domain.DtbBookOrderCode;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
/**
 * 订单下的购书码Service接口
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
public interface IDtbBookOrderCodeService extends IService<DtbBookOrderCode>
{
    /**
     * 查询订单下的购书码
     *
     * @param orderCodeId 订单下的购书码主键
     * @return 订单下的购书码
     */
    public DtbBookOrderCode selectDtbBookOrderCodeByOrderCodeId(Long orderCodeId);

    /**
     * 查询订单下的购书码列表
     *
     * @param dtbBookOrderCode 订单下的购书码
     * @return 订单下的购书码集合
     */
    public List<DtbBookOrderCode> selectDtbBookOrderCodeList(DtbBookOrderCode dtbBookOrderCode);

    /**
     * 新增订单下的购书码
     *
     * @param dtbBookOrderCode 订单下的购书码
     * @return 结果
     */
    public boolean insertDtbBookOrderCode(DtbBookOrderCode dtbBookOrderCode);

    /**
     * 修改订单下的购书码
     *
     * @param dtbBookOrderCode 订单下的购书码
     * @return 结果
     */
    public boolean updateDtbBookOrderCode(DtbBookOrderCode dtbBookOrderCode);

    /**
     * 批量删除订单下的购书码
     *
     * @param orderCodeIds 需要删除的订单下的购书码主键集合
     * @return 结果
     */
    public boolean deleteDtbBookOrderCodeByOrderCodeIds(List<Long> orderCodeIds);

    /**
     * 更新子订单的导出次数+1
     *
     * @param orderItemList 子订单集合
     * @return 操作结果
     */
    boolean updateExportQuantity(List<Long> orderItemList);

}

package cn.dutp.shop.service.impl;

import cn.dutp.shop.domain.DtbBookRefundOrderCode;
import cn.dutp.shop.mapper.DtbBookRefundOrderCodeMapper;
import cn.dutp.shop.service.IDtbBookRefundOrderCodeService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 退款的二维码Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@Service
public class DtbBookRefundOrderCodeServiceImpl extends ServiceImpl<DtbBookRefundOrderCodeMapper, DtbBookRefundOrderCode> implements IDtbBookRefundOrderCodeService
{
    @Autowired
    private DtbBookRefundOrderCodeMapper dtbBookRefundOrderCodeMapper;

    /**
     * 查询退款的二维码
     *
     * @param refundCodeId 退款的二维码主键
     * @return 退款的二维码
     */
    @Override
    public DtbBookRefundOrderCode selectDtbBookRefundOrderCodeByRefundCodeId(Long refundCodeId)
    {
        return this.getById(refundCodeId);
    }

    /**
     * 查询退款的二维码列表
     *
     * @param dtbBookRefundOrderCode 退款的二维码
     * @return 退款的二维码
     */
    @Override
    public List<DtbBookRefundOrderCode> selectDtbBookRefundOrderCodeList(DtbBookRefundOrderCode dtbBookRefundOrderCode)
    {
        LambdaQueryWrapper<DtbBookRefundOrderCode> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbBookRefundOrderCode.getRefundOrderId())) {
                lambdaQueryWrapper.eq(DtbBookRefundOrderCode::getRefundOrderId
                ,dtbBookRefundOrderCode.getRefundOrderId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookRefundOrderCode.getRefundItemId())) {
                lambdaQueryWrapper.eq(DtbBookRefundOrderCode::getRefundItemId
                ,dtbBookRefundOrderCode.getRefundItemId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookRefundOrderCode.getCodeId())) {
                lambdaQueryWrapper.eq(DtbBookRefundOrderCode::getCodeId
                ,dtbBookRefundOrderCode.getCodeId());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增退款的二维码
     *
     * @param dtbBookRefundOrderCode 退款的二维码
     * @return 结果
     */
    @Override
    public boolean insertDtbBookRefundOrderCode(DtbBookRefundOrderCode dtbBookRefundOrderCode)
    {
        return this.save(dtbBookRefundOrderCode);
    }

    /**
     * 修改退款的二维码
     *
     * @param dtbBookRefundOrderCode 退款的二维码
     * @return 结果
     */
    @Override
    public boolean updateDtbBookRefundOrderCode(DtbBookRefundOrderCode dtbBookRefundOrderCode)
    {
        return this.updateById(dtbBookRefundOrderCode);
    }

    /**
     * 批量删除退款的二维码
     *
     * @param refundCodeIds 需要删除的退款的二维码主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookRefundOrderCodeByRefundCodeIds(List<Long> refundCodeIds)
    {
        return this.removeByIds(refundCodeIds);
    }

}

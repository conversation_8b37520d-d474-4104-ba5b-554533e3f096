package cn.dutp.shop.mapper;

import cn.dutp.domain.DtbBookRefundOrder;
import cn.dutp.shop.domain.dto.BookRefundOrderVO;

import java.util.List;

import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
/**
 * 售后退款订单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@Repository
public interface DtbBookRefundOrderMapper extends BaseMapper<DtbBookRefundOrder>
{

    List<BookRefundOrderVO> selectRefundOrderVOList(DtbBookRefundOrder dtbBookRefundOrder);

    BookRefundOrderVO selectRefundOrderVOByRefundOrderId(Long refundOrderId);

}

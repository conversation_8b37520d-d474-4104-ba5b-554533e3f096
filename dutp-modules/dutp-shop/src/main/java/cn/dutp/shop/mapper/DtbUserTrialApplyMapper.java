package cn.dutp.shop.mapper;

import cn.dutp.system.api.domain.DutpUser;
import org.springframework.stereotype.Repository;
import cn.dutp.shop.domain.DtbUserTrialApply;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * DUTP-DTB_025教师试用申请Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-07
 */
@Repository
public interface DtbUserTrialApplyMapper extends BaseMapper<DtbUserTrialApply>
{

    List<DtbUserTrialApply> TrialShopList(DtbUserTrialApply dtbUserTrialApply);

    DtbUserTrialApply selectDtbUserTrialApplyByApplyId(Long applyId);

    /**
     * 学生教师端申请历史列表
     *
     * @param dtbUserTrialApply 教师试用申请对象
     * @return 结果
     */
    List<DtbUserTrialApply> listEducation(DtbUserTrialApply dtbUserTrialApply);

    public List<Long> getSuperUser();

    public DutpUser getUserInfo(Long userId);
}

package cn.dutp.shop;

import cn.dutp.common.security.annotation.EnableCustomConfig;
import cn.dutp.common.security.annotation.EnableDutpFeignClients;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;

@EnableCustomConfig
@EnableDutpFeignClients
@SpringBootApplication
@EnableDiscoveryClient
//@ComponentScan(basePackages = {"cn.dutp.common", "cn.dutp.shop"})
public class DutpShopApplication {
    public static void main(String[] args) {
        SpringApplication.run(DutpShopApplication.class, args);
        System.out.println("电商中心模块启动成功");
    }
}

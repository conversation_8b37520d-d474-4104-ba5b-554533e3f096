package cn.dutp.shop.domain.dto;

import cn.dutp.shop.domain.vo.InvoiceTypeConverter;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

@Data
public class DtbUserInvoiceTitleExport {

    /** 发票类型 */
    @ExcelProperty(value = "发票类型", order = 1,converter = InvoiceTypeConverter.class)
    private Integer invoiceType;

//    /** 发票类型 */
//    @ExcelProperty(value = "发票类型", order = 1,converter = InvoiceTypeConverter.class)
//    private String invoiceTypeName;

    /** 是否含税 */
    @ExcelProperty(value = "是否含税", order = 2)
    private String isInclude = "是";

    /** 售票方自然人标识，个人是，企业否 */
    @ExcelIgnore
    private String naturalPersonFlag;

    /** 售票方自然人标识，个人是，企业否 */
    @ExcelProperty(value = "售票方自然人标识", order = 3)
    private String naturalPersonName;

    /**
     * 购买方id
     */
    @ExcelIgnore
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long buyUserId;

    /**
     * 购买方名称
     */
    @ExcelProperty(value = "购买方名称", order = 4)
    private String buyUserName;

    /**
     * 购买方纳税人识别号
     */
    @ExcelProperty(value = "购买方纳税人识别号", order = 5)
    private String taxNo;

    /**
     * 购买方电话
     */
    @ExcelProperty(value = "购买方电话", order = 6)
    private String buyUserPhone;

    /**
     * 开户银行
     */
    @ExcelProperty(value = "开户银行", order = 7)
    private String accountBank;

    /**
     * 银行账号
     */
    @ExcelProperty(value = "银行账号", order = 8)
    private String accountNo;

    /**
     * 购买方邮箱
     */
    @ExcelProperty(value = "购买方邮箱", order = 9)
    private String email;

    @ExcelIgnore
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long statementId;

    /**
     * 发票内容
     */
    @ExcelIgnore
    private Integer applyType;
}

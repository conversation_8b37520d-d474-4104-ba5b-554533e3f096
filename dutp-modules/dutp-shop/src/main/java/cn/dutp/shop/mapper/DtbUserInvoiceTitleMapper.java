package cn.dutp.shop.mapper;


import cn.dutp.shop.domain.DtbBookStatementOrder;
import cn.dutp.shop.domain.DtbUserInvoiceTitle;
import cn.dutp.shop.domain.dto.DtbUserInvoiceTitleExport;
import cn.dutp.shop.domain.dto.DtbUserInvoiceTitleItemExport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * DUTP-DTB-034发票抬头Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-18
 */
@Repository
public interface DtbUserInvoiceTitleMapper extends BaseMapper<DtbUserInvoiceTitle>
{

    List<DtbUserInvoiceTitle> selectSaleList(DtbUserInvoiceTitle dtbUserInvoiceTitle);

    List<DtbUserInvoiceTitle> seleEducationList(DtbUserInvoiceTitle dtbUserInvoiceTitle);

    List<DtbUserInvoiceTitle> seleEducationExportList(Long titleId);

    @Select("select DISTINCT uia.* from dtb_user_invoice_apply as uia\n" +
            "LEFT JOIN dtb_user_invoice_title as uit on uia.title_id = uit.title_id\n" +
            "LEFT JOIN dtb_user_invoice_file as uif on uif.apply_id = uia.apply_id\n" +
            "where uit.title_id = #{titleId}")
    DtbUserInvoiceTitle selectByTitleId(Long applyId);

    @Select("SELECT DISTINCT\n" +
            "\tuia.apply_id,\n" +
            "\tuia.apply_status,\n" +
            "\tuia.order_type,\n" +
            "\tbs.statement_no,\n" +
            "\tbm.title_name AS merchantTitleName,\n" +
            "\ts.title_name AS schoolTitleName \n" +
            "FROM\n" +
            "\tdtb_user_invoice_apply AS uia\n" +
            "\tLEFT JOIN dtb_book_statement AS bs ON bs.statement_id = uia.statement_id\n" +
            "\tLEFT JOIN dtb_book_statement_order AS bso ON bso.statement_id = bs.statement_id\n" +
            "\tLEFT JOIN dtb_book_order AS bo ON bo.order_id = bso.order_id\n" +
            "\tLEFT JOIN dtb_book_order_item AS boi ON boi.order_id = bo.order_id\n" +
            "\tLEFT JOIN dutp_school AS s ON s.school_id = bo.school_id\n" +
            "\tLEFT JOIN dtb_book_merchant AS bm ON bm.merchant_id = bo.merchant_id\n" +
            "\tLEFT JOIN dtb_user_invoice_file AS uif ON uif.apply_id = uia.apply_id\n" +
            "\tLEFT JOIN dtb_user_invoice_title AS uit ON uit.title_id = uia.title_id \n" +
            "WHERE\n" +
            "\tuia.order_type = 2 \n" +
            "\tAND bs.del_flag = 0 \n" +
            "\tAND ((\n" +
            "\t\t\tbm.title_name = #{titleName} \n" +
            "\t\t) \n" +
            "\tOR ( bm.title_name IS NULL AND s.title_name = #{titleName} ))" )
    List<DtbUserInvoiceTitle> selectListByName(String titleName);

    List<DtbUserInvoiceTitleExport> selectByIds(@Param("ids") List<Long> ids, @Param("type") Integer type, @Param("orderTypes") Integer orderTypes);

    List<DtbUserInvoiceTitleItemExport> selectOrderItemByStatementIds(@Param("orderList") List<DtbBookStatementOrder> orderList,@Param("applyType") Integer applyType);
}

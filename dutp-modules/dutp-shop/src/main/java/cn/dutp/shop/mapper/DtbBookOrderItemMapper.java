package cn.dutp.shop.mapper;

import cn.dutp.domain.DtbBookOrderItem;
import cn.dutp.shop.domain.vo.DtbBookOrderItemVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * DUTP-DTB-031订单对应的书籍明细Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Repository
public interface DtbBookOrderItemMapper extends BaseMapper<DtbBookOrderItem>
{
    /**
     * 根据orderId获取订单详细列表
     *
     * @param orderId 订单id
     * @return 详细列表
     */
    List<DtbBookOrderItemVo> getOrderItemByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据订单id查询子订单的idlist
     *
     * @param orderId 订单id
     * @return 子订单的idlist
     */
    List<Long> selectBookOrderItemByOrderId(@Param("orderId") Long orderId);

}

package cn.dutp.shop.service.impl;

import cn.dutp.domain.DtbBookOrderAuditUser;
import cn.dutp.domain.DtbBookOrderItem;
import cn.dutp.shop.domain.vo.DtbBookOrderItemEditApprovalVo;
import cn.dutp.shop.domain.vo.DtbBookOrderItemVo;
import cn.dutp.shop.mapper.DtbBookOrderAuditUserMapper;
import cn.dutp.shop.mapper.DtbBookOrderItemMapper;
import cn.dutp.shop.service.IDtbBookOrderItemService;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * DUTP-DTB-031订单对应的书籍明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Service
public class DtbBookOrderItemServiceImpl extends ServiceImpl<DtbBookOrderItemMapper, DtbBookOrderItem> implements IDtbBookOrderItemService
{
    @Autowired
    private DtbBookOrderItemMapper dtbBookOrderItemMapper;

    @Autowired
    private DtbBookOrderAuditUserMapper dtbBookOrderAuditUserMapper;

    /**
     * 查询DUTP-DTB-031订单对应的书籍明细
     *
     * @param orderItemId DUTP-DTB-031订单对应的书籍明细主键
     * @return DUTP-DTB-031订单对应的书籍明细
     */
    @Override
    public DtbBookOrderItem selectDtbBookOrderItemByOrderItemId(Long orderItemId)
    {
        return this.getById(orderItemId);
    }

    /**
     * 查询DUTP-DTB-031订单对应的书籍明细列表
     *
     * @param dtbBookOrderItem DUTP-DTB-031订单对应的书籍明细
     * @return DUTP-DTB-031订单对应的书籍明细
     */
    @Override
    public List<DtbBookOrderItem> selectDtbBookOrderItemList(DtbBookOrderItem dtbBookOrderItem)
    {
        LambdaQueryWrapper<DtbBookOrderItem> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbBookOrderItem.getOrderId())) {
                lambdaQueryWrapper.eq(DtbBookOrderItem::getOrderId
                ,dtbBookOrderItem.getOrderId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookOrderItem.getBookId())) {
                lambdaQueryWrapper.eq(DtbBookOrderItem::getBookId
                ,dtbBookOrderItem.getBookId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookOrderItem.getSchoolId())) {
                lambdaQueryWrapper.eq(DtbBookOrderItem::getSchoolId
                ,dtbBookOrderItem.getSchoolId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookOrderItem.getPriceSale())) {
                lambdaQueryWrapper.eq(DtbBookOrderItem::getPriceSale
                ,dtbBookOrderItem.getPriceSale());
            }
                if(ObjectUtil.isNotEmpty(dtbBookOrderItem.getPriceCounter())) {
                lambdaQueryWrapper.eq(DtbBookOrderItem::getPriceCounter
                ,dtbBookOrderItem.getPriceCounter());
            }
                if(ObjectUtil.isNotEmpty(dtbBookOrderItem.getPriceOrderItem())) {
                lambdaQueryWrapper.eq(DtbBookOrderItem::getPriceOrderItem
                ,dtbBookOrderItem.getPriceOrderItem());
            }
                if(ObjectUtil.isNotEmpty(dtbBookOrderItem.getBookQuantity())) {
                lambdaQueryWrapper.eq(DtbBookOrderItem::getBookQuantity
                ,dtbBookOrderItem.getBookQuantity());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增DUTP-DTB-031订单对应的书籍明细
     *
     * @param dtbBookOrderItem DUTP-DTB-031订单对应的书籍明细
     * @return 结果
     */
    @Override
    public boolean insertDtbBookOrderItem(DtbBookOrderItem dtbBookOrderItem)
    {
        return this.save(dtbBookOrderItem);
    }

    /**
     * 修改DUTP-DTB-031订单对应的书籍明细
     *
     * @param dtbBookOrderItem DUTP-DTB-031订单对应的书籍明细
     * @return 结果
     */
    @Override
    public boolean updateDtbBookOrderItem(DtbBookOrderItem dtbBookOrderItem)
    {
        return this.updateById(dtbBookOrderItem);
    }

    /**
     * 批量删除DUTP-DTB-031订单对应的书籍明细
     *
     * @param orderItemIds 需要删除的DUTP-DTB-031订单对应的书籍明细主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookOrderItemByOrderItemIds(List<Long> orderItemIds)
    {
        return this.removeByIds(orderItemIds);
    }

    /**
     * 采购审核订单详细
     *
     * @param orderId 订单id
     * @return 详细列表
     */
    @Override
    public List<DtbBookOrderItemVo> getOrderItemByOrderId(Long orderId) {
        return dtbBookOrderItemMapper.getOrderItemByOrderId(orderId);
    }

    /**
     * 作废审核订单详细
     *
     * @param orderId 订单id
     * @return 详细订单列表
     */
    @Override
    public DtbBookOrderItemEditApprovalVo selectCancelApprovalView(Long orderId) {
        // 根据orderId获取订单详细列表
        List<DtbBookOrderItemVo> orderItemByOrderList = dtbBookOrderItemMapper.getOrderItemByOrderId(orderId);
        QueryWrapper<DtbBookOrderAuditUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DtbBookOrderAuditUser::getOrderId, orderId).eq(DtbBookOrderAuditUser::getAuditType,3);
        DtbBookOrderAuditUser auditUser = dtbBookOrderAuditUserMapper.selectOne(queryWrapper);
        JSONObject data = JSON.parseObject(auditUser.getEditContent());
        String remark = data.getString("remark");
        DtbBookOrderItemEditApprovalVo vo = new DtbBookOrderItemEditApprovalVo();
        // 应付金额
        BigDecimal payAmount = BigDecimal.ZERO;
        // 商品总额
        BigDecimal price = BigDecimal.ZERO;
        if (orderItemByOrderList != null) {
            // 计算状态是正常的订单的价格合计，以及支付价格合计
            for (DtbBookOrderItemVo dtbBookOrderItemVo : orderItemByOrderList) {
                if (dtbBookOrderItemVo.getItemStatus().equals("normal")) {
                    // 子订单计算应付金额
                    BigDecimal payAmountTotal = dtbBookOrderItemVo.getPriceSale()
                            // 数量 * 价格
                            .multiply(BigDecimal.valueOf(dtbBookOrderItemVo.getBookQuantity()))
                            // * 折扣
                            .multiply(dtbBookOrderItemVo.getDiscount())
                            .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                    payAmount = payAmount.add(payAmountTotal);
                    // 子订单商品总额
                    BigDecimal priceTotal = dtbBookOrderItemVo.getPriceSale()
                            // 数量 * 价格
                            .multiply(BigDecimal.valueOf(dtbBookOrderItemVo.getBookQuantity()))
                            .setScale(2, RoundingMode.HALF_UP);
                    price = price.add(priceTotal);
                }
            }
        }
        vo.setPayAmount(payAmount);
        vo.setPrice(price);
        vo.setEditApprovalList(orderItemByOrderList);
        vo.setRemark(remark);
        return vo;
    }

    /**
     * 根据订单id查询子订单的idlist
     *
     * @param orderId 订单id
     * @return 子订单的idlist
     */
    @Override
    public List<Long> selectBookOrderItemByOrderId(Long orderId) {
        return dtbBookOrderItemMapper.selectBookOrderItemByOrderId(orderId);
    }

}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.shop.mapper.DtbBookOrderItemMapper">
    
    <resultMap type="DtbBookOrderItem" id="DtbBookOrderItemResult">
        <result property="orderItemId"    column="order_item_id"    />
        <result property="orderId"    column="order_id"    />
        <result property="bookId"    column="book_id"    />
        <result property="schoolId"    column="school_id"    />
        <result property="priceSale"    column="price_sale"    />
        <result property="priceCounter"    column="price_counter"    />
        <result property="priceOrderItem"    column="price_order_item"    />
        <result property="bookQuantity"    column="book_quantity"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDtbBookOrderItemVo">
        select order_item_id, order_id, book_id, school_id, price_sale, price_counter, price_order_item, book_quantity, create_by, create_time, update_by, update_time from dtb_book_order_item
    </sql>

    <select id="selectDtbBookOrderItemList" parameterType="DtbBookOrderItem" resultMap="DtbBookOrderItemResult">
        <include refid="selectDtbBookOrderItemVo"/>
        <where>  
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="bookId != null "> and book_id = #{bookId}</if>
            <if test="schoolId != null "> and school_id = #{schoolId}</if>
            <if test="priceSale != null "> and price_sale = #{priceSale}</if>
            <if test="priceCounter != null "> and price_counter = #{priceCounter}</if>
            <if test="priceOrderItem != null "> and price_order_item = #{priceOrderItem}</if>
            <if test="bookQuantity != null "> and book_quantity = #{bookQuantity}</if>
        </where>
    </select>
    
    <select id="selectDtbBookOrderItemByOrderItemId" parameterType="Long" resultMap="DtbBookOrderItemResult">
        <include refid="selectDtbBookOrderItemVo"/>
        where order_item_id = #{orderItemId}
    </select>

    <insert id="insertDtbBookOrderItem" parameterType="DtbBookOrderItem" useGeneratedKeys="true" keyProperty="orderItemId">
        insert into dtb_book_order_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="bookId != null">book_id,</if>
            <if test="schoolId != null">school_id,</if>
            <if test="priceSale != null">price_sale,</if>
            <if test="priceCounter != null">price_counter,</if>
            <if test="priceOrderItem != null">price_order_item,</if>
            <if test="bookQuantity != null">book_quantity,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="bookId != null">#{bookId},</if>
            <if test="schoolId != null">#{schoolId},</if>
            <if test="priceSale != null">#{priceSale},</if>
            <if test="priceCounter != null">#{priceCounter},</if>
            <if test="priceOrderItem != null">#{priceOrderItem},</if>
            <if test="bookQuantity != null">#{bookQuantity},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDtbBookOrderItem" parameterType="DtbBookOrderItem">
        update dtb_book_order_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="schoolId != null">school_id = #{schoolId},</if>
            <if test="priceSale != null">price_sale = #{priceSale},</if>
            <if test="priceCounter != null">price_counter = #{priceCounter},</if>
            <if test="priceOrderItem != null">price_order_item = #{priceOrderItem},</if>
            <if test="bookQuantity != null">book_quantity = #{bookQuantity},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where order_item_id = #{orderItemId}
    </update>

    <delete id="deleteDtbBookOrderItemByOrderItemId" parameterType="Long">
        delete from dtb_book_order_item where order_item_id = #{orderItemId}
    </delete>

    <delete id="deleteDtbBookOrderItemByOrderItemIds" parameterType="String">
        delete from dtb_book_order_item where order_item_id in 
        <foreach item="orderItemId" collection="array" open="(" separator="," close=")">
            #{orderItemId}
        </foreach>
    </delete>
    <!--根据orderId获取订单详细列表-->
    <select id="getOrderItemByOrderId" resultType="cn.dutp.shop.domain.vo.DtbBookOrderItemVo">
        SELECT book.book_id,
            book.book_name,
               ifnull(book.isbn,book.issn) as isbn,
               item.order_item_id,
               item.school_id,
               item.order_id,
            item.price_sale,
            item.book_quantity,
               item.book_quantity AS editBookQuantity,
            item.discount,
               item.discount      AS editDiscount,
            item.item_status,
            ( item.price_order_item * item.book_quantity ) AS total,
            ROUND(( item.price_order_item * item.book_quantity * (item.discount / 100)),2) AS shouldPay,
            orders.order_no,
            orders.order_type,
            m.merchan_name,
            s.school_name,
            sa.area_name,
            u.nick_name
        FROM
            dtb_book_order_item item
                LEFT JOIN dtb_book book ON item.book_id = book.book_id
                LEFT JOIN dtb_book_order AS orders ON orders.order_id = item.order_id
                LEFT JOIN dtb_book_merchant AS m ON m.merchant_id = orders.merchant_id
                LEFT JOIN dutp_school AS s ON s.school_id = item.school_id
                LEFT JOIN dutp_sale_area AS sa ON sa.area_id = orders.area_id
                LEFT JOIN dutp_sale_area_member AS sam ON sam.member_id = orders.operator_id
                LEFT JOIN sys_user AS u ON u.user_id = sam.user_id
        WHERE item.order_id = #{orderId}
    </select>

    <!--根据订单id查询子订单的idlist-->
    <select id="selectBookOrderItemByOrderId" parameterType="Long" resultType="Long">
        select order_item_id
        from dtb_book_order_item
        where order_id = #{orderId}
    </select>
</mapper>
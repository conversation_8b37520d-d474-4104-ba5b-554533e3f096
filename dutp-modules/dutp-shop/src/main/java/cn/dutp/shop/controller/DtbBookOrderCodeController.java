package cn.dutp.shop.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.shop.domain.DtbBookOrderCode;
import cn.dutp.shop.service.IDtbBookOrderCodeService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 订单下的购书码Controller
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@RestController
@RequestMapping("/shop/code")
public class DtbBookOrderCodeController extends BaseController
{
    @Autowired
    private IDtbBookOrderCodeService dtbBookOrderCodeService;

    /**
     * 查询订单下的购书码列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbBookOrderCode dtbBookOrderCode)
    {
        startPage();
        List<DtbBookOrderCode> list = dtbBookOrderCodeService.selectDtbBookOrderCodeList(dtbBookOrderCode);
        return getDataTable(list);
    }

    /**
     * 导出订单下的购书码列表
     */
    @RequiresPermissions("shop:code:export")
    @Log(title = "导出订单下的购书码", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbBookOrderCode dtbBookOrderCode)
    {
        List<DtbBookOrderCode> list = dtbBookOrderCodeService.selectDtbBookOrderCodeList(dtbBookOrderCode);
        ExcelUtil<DtbBookOrderCode> util = new ExcelUtil<DtbBookOrderCode>(DtbBookOrderCode.class);
        util.exportExcel(response, list, "订单下的购书码数据");
    }

    /**
     * 获取订单下的购书码详细信息
     */
    @RequiresPermissions("shop:code:query")
    @GetMapping(value = "/{orderCodeId}")
    public AjaxResult getInfo(@PathVariable("orderCodeId") Long orderCodeId)
    {
        return success(dtbBookOrderCodeService.selectDtbBookOrderCodeByOrderCodeId(orderCodeId));
    }

    /**
     * 新增订单下的购书码
     */
    @RequiresPermissions("shop:code:add")
    @Log(title = "新增订单下的购书码", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookOrderCode dtbBookOrderCode)
    {
        return toAjax(dtbBookOrderCodeService.insertDtbBookOrderCode(dtbBookOrderCode));
    }

    /**
     * 修改订单下的购书码
     */
    @RequiresPermissions("shop:code:edit")
    @Log(title = "修改订单下的购书码", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookOrderCode dtbBookOrderCode)
    {
        return toAjax(dtbBookOrderCodeService.updateDtbBookOrderCode(dtbBookOrderCode));
    }

    /**
     * 删除订单下的购书码
     */
    @RequiresPermissions("shop:code:remove")
    @Log(title = "删除订单下的购书码", businessType = BusinessType.DELETE)
    @DeleteMapping("/{orderCodeIds}")
    public AjaxResult remove(@PathVariable Long[] orderCodeIds)
    {
        return toAjax(dtbBookOrderCodeService.deleteDtbBookOrderCodeByOrderCodeIds(Arrays.asList(orderCodeIds)));
    }
}

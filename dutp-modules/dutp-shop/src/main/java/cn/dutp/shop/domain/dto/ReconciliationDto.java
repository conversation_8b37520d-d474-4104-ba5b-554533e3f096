package cn.dutp.shop.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 对账列表对象
 *
 * @author: dutp
 * @date: 2025/2/8 8:44
 */
@Data
public class ReconciliationDto {


    /**
     * 订单id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderId;

    /**
     * 书商id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long merchantId;

    /**
     * 学校id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolId;


    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 采购数量
     */
    private Long payTotal;

    /**
     * 已使用购数码
     */
    private Integer useCode;

    /**
     * 剩余购数码
     */
    private Integer remainingCode;

    /**
     * 商品总额
     */
    private BigDecimal goodsTotals;

    /**
     * 应付金额
     */
    private BigDecimal shouldPay;

    /**
     * 书商名称
     */
    private String merchanName;

    /**
     * 学校名称
     */
    private String schoolName;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 经办人
     */
    private String nickName;

    /**
     * 经办人
     */
    private String areaName;

    /**
     * 经办人
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private List<Date> createTimeList;

    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
}

package cn.dutp.shop.service.impl;


import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.shop.domain.DtbBookStatement;
import cn.dutp.shop.domain.DtbBookStatementOrder;
import cn.dutp.shop.domain.DtbUserInvoiceApply;
import cn.dutp.shop.domain.DtbUserInvoiceTitle;
import cn.dutp.shop.domain.dto.BookOrderPurchaseCodeExport;
import cn.dutp.shop.domain.dto.DtbUserInvoiceTitleExport;
import cn.dutp.shop.domain.dto.DtbUserInvoiceTitleItemExport;
import cn.dutp.shop.mapper.DtbBookStatementMapper;
import cn.dutp.shop.mapper.DtbBookStatementOrderMapper;
import cn.dutp.shop.mapper.DtbUserInvoiceApplyMapper;
import cn.dutp.shop.mapper.DtbUserInvoiceTitleMapper;
import cn.dutp.shop.service.IDtbUserInvoiceTitleService;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.druid.sql.visitor.functions.If;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * DUTP-DTB-034发票抬头Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-18
 */
@Service
public class DtbUserInvoiceTitleServiceImpl extends ServiceImpl<DtbUserInvoiceTitleMapper, DtbUserInvoiceTitle> implements IDtbUserInvoiceTitleService {
    @Autowired
    private DtbUserInvoiceTitleMapper dtbUserInvoiceTitleMapper;
    @Autowired
    private DtbUserInvoiceApplyMapper dtbUserInvoiceApplyMapper;
    @Autowired
    private DtbBookStatementMapper dtbBookStatementMapper;
    @Autowired
    private DtbBookStatementOrderMapper dtbBookStatementOrderMapper;

    /**
     * 查询DUTP-DTB-034发票抬头
     *
     * @param titleId DUTP-DTB-034发票抬头主键
     * @return DUTP-DTB-034发票抬头
     */
    @Override
    public DtbUserInvoiceTitle selectDtbUserInvoiceTitleByTitleId(Long titleId) {
        return this.getById(titleId);
    }

    /**
     * 查询DUTP-DTB-034发票抬头列表
     *
     * @param dtbUserInvoiceTitle DUTP-DTB-034发票抬头
     * @return DUTP-DTB-034发票抬头
     */
    @Override
    public List<DtbUserInvoiceTitle> selectSaleList(DtbUserInvoiceTitle dtbUserInvoiceTitle) {

        return dtbUserInvoiceTitleMapper.selectSaleList(dtbUserInvoiceTitle);
    }

    @Override
    public List<DtbUserInvoiceTitle> selectEducationList(DtbUserInvoiceTitle dtbUserInvoiceTitle) {
        return dtbUserInvoiceTitleMapper.seleEducationList(dtbUserInvoiceTitle);
    }

    /**
     * 新增DUTP-DTB-034发票抬头
     *
     * @param dtbUserInvoiceTitle DUTP-DTB-034发票抬头
     * @return 结果
     */
    @Override
    public boolean insertDtbUserInvoiceTitle(DtbUserInvoiceTitle dtbUserInvoiceTitle) {
        dtbUserInvoiceTitle.setUserId(SecurityUtils.getUserId());
        return this.save(dtbUserInvoiceTitle);
    }

    /**
     * 修改DUTP-DTB-034发票抬头
     *
     * @param dtbUserInvoiceTitle DUTP-DTB-034发票抬头
     * @return 结果
     */
    @Override
    public boolean updateDtbUserInvoiceTitle(DtbUserInvoiceTitle dtbUserInvoiceTitle) {
        return this.updateById(dtbUserInvoiceTitle);
    }

    /**
     * 批量删除DUTP-DTB-034发票抬头
     *
     * @param titleIds 需要删除的DUTP-DTB-034发票抬头主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbUserInvoiceTitleByTitleIds(List<Long> titleIds) {
        return this.removeByIds(titleIds);
    }

    @Override
    public void exportEdu(HttpServletResponse response, DtbUserInvoiceTitle dtbUserInvoiceTitle) {
        if(CollectionUtils.isEmpty(dtbUserInvoiceTitle.getIds())){
            throw new ServiceException("请选择要导出的数据");
        }
        try {
            // sheet角标
            int sheetIndex = 0;
            // 获取第一页内容（发票内容）
            List<DtbUserInvoiceTitleExport> exportInvoiceList = dtbUserInvoiceTitleMapper.selectByIds(dtbUserInvoiceTitle.getIds(), dtbUserInvoiceTitle.getType(),dtbUserInvoiceTitle.getOrderTypes());
            if(!CollectionUtils.isEmpty(exportInvoiceList)){
                ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
                try {
                    response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                    response.setCharacterEncoding("utf-8");
                    String fileName = URLEncoder.encode("教务采购发票信息", "UTF-8").replaceAll("\\+", "%20");
                    response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");

                    // 设置表头
                    WriteSheet writeSheet = EasyExcel.writerSheet(sheetIndex++, "发票基本信息")
                            .head(DtbUserInvoiceTitleExport.class)
                            .build();
                    excelWriter.write(exportInvoiceList, writeSheet);
                    List<Long> statementIds = exportInvoiceList.stream().map(DtbUserInvoiceTitleExport::getStatementId).collect(Collectors.toList());
                    // 获取结算单的订单
                    QueryWrapper<DtbBookStatementOrder> queryWrapper = new QueryWrapper<>();
                    queryWrapper.lambda().in(DtbBookStatementOrder::getStatementId, statementIds);
                    List<DtbBookStatementOrder> bookStatementOrderList = dtbBookStatementOrderMapper.selectList(queryWrapper);
                    List<DtbUserInvoiceTitleItemExport> exportInvoiceItemList = new ArrayList<>();
                    if(!CollectionUtils.isEmpty(bookStatementOrderList)){
                        for (DtbUserInvoiceTitleExport e : exportInvoiceList) {
                            List<DtbBookStatementOrder> collect = bookStatementOrderList.stream().filter(item -> item.getStatementId().equals(e.getStatementId())).collect(Collectors.toList());
                            // 订单下明细
                            List<DtbUserInvoiceTitleItemExport> list = dtbUserInvoiceTitleMapper.selectOrderItemByStatementIds(collect,e.getApplyType());
                            if(!CollectionUtils.isEmpty(list)){
                                exportInvoiceItemList.addAll(list);
                            }
                        }
                        if(!CollectionUtils.isEmpty(exportInvoiceItemList)){
                            if (dtbUserInvoiceTitle.getType() == 1) {
                                // 零售开票没有备注
                                Set<String> excludeFields = new HashSet<>();
                                excludeFields.add("remark");
                                // 设置表头
                                WriteSheet writeItemSheet = EasyExcel.writerSheet(sheetIndex++, "发票明细信息")
                                        .head(DtbUserInvoiceTitleItemExport.class)
                                        .excludeColumnFieldNames(excludeFields)
                                        .build();
                                excelWriter.write(exportInvoiceItemList, writeItemSheet);
                            } else {
                                // 设置表头
                                WriteSheet writeItemSheet = EasyExcel.writerSheet(sheetIndex++, "发票明细信息")
                                        .head(DtbUserInvoiceTitleItemExport.class)
                                        .build();
                                excelWriter.write(exportInvoiceItemList, writeItemSheet);
                            }

                        }
                    }
                } catch (Exception e) {
                    throw new ServiceException("导出失败----"+e.getMessage());
                } finally {
                    if(excelWriter != null) {
                        excelWriter.finish();
                    }
                }
            }
        } catch (Exception e) {
            throw new ServiceException("导出失败----"+e.getMessage());
        }

    }

    /**
     * 学生教师端查询发票抬头列表
     * @param dtbUserInvoiceTitle 对象
     * @return 结果
     */
    @Override
    public List<DtbUserInvoiceTitle> getInvoiceTitleEducation(DtbUserInvoiceTitle dtbUserInvoiceTitle) {
        LambdaQueryWrapper<DtbUserInvoiceTitle> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DtbUserInvoiceTitle::getUserId, SecurityUtils.getUserId());
        return dtbUserInvoiceTitleMapper.selectList(queryWrapper);
    }


}

package cn.dutp.shop.mapper;

import cn.dutp.domain.DtbBook;
import cn.dutp.domain.DtbBookPurchaseCode;
import cn.dutp.system.api.domain.DutpUser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
/**
 * 购书码发行管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Repository
public interface DtbBookPurchaseCodeMapper extends BaseMapper<DtbBookPurchaseCode>
{
    /**
     * 学生教师端查询用户购书码发行管理列表
     *
     * @param dtbBookPurchaseCode 购书码发行管理
     * @return 购书码发行管理集合
     */
    public List<DtbBookPurchaseCode> listEducation(DtbBookPurchaseCode dtbBookPurchaseCode);

    /**
     * 教师学生端获取购书码发行管理详细信息
     *
     * @param dtbBookPurchaseCode 购书码发行管理
     * @return 购书码发行管理
     */
    public DtbBookPurchaseCode getInfoEducation(DtbBookPurchaseCode dtbBookPurchaseCode);
    /**
     * 后台管理电商中心随机获取指定数量的购书码
     *
     */
    List<DtbBookPurchaseCode> selectByOrderItemId(Long orderItemId);
	
	List<Long> selectCodeIdListByBookId(Long bookId);

    /**
     * 将id的list中的购书码的状态改为2已绑定未兑换
     *
     * @param codeIdList 购书码id的list
     * @return 结果
     */
    boolean cancelCodeByCodeIdList(@Param("updateBy") String updateBy, @Param("codeIdList") List<Long> codeIdList);

    /**
     * 将购书码表中id在集合中的且状态为冻结的数据，改为正常
     *
     * @param codeIdList
     * @return
     */
    boolean updateIsFrozenByCodeIdList(@Param("updateBy") String updateBy, @Param("codeIdList") List<Long> codeIdList);

    /**
     * 将id的list中已经冻结的购书码的状态改为5作废 冻结状态改为正常
     *
     * @param codeIdList
     * @return
     */
    boolean cancelIsFrozenByCodeIdList(@Param("updateBy") String updateBy, @Param("codeIdList") List<Long> codeIdList);

    /**
     * 后台管理电商中心作废订单获取指定明细下的购书码
     *
     */
    List<DtbBookPurchaseCode> selectByOrderItemIds(@Param("codeIdList") List<Long> codeIdList);

    /**
     * 后台管理电商中心获取订单下未作废的购书码
     */
    List<DtbBookPurchaseCode> selectByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据orderId查询当前订单的子订单是否被全部作废
     *
     * @param orderId 订单id
     * @return 是否被全部作废
     */
    Integer selectItemIsAllVoid(Long orderId);
    /**
     * 查询教材信息
     *
     * @param bookId 椒草id
     * @return 教材信息
     */
    DtbBook selectDtbBookById(Long bookId);

    /**
     * 获取最新的用户信息
     *
     * @param userId 用户Id
     * @return 用户信息
     */
    DutpUser getUserInfo(Long userId);
}



package cn.dutp.shop.service.impl;

import java.util.List;


import cn.dutp.shop.domain.DtbBookMerchant;
import cn.dutp.shop.mapper.DtbBookMerchantMapper;
import cn.dutp.shop.service.IDtbBookMerchantService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 书商Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
@Service
public class DtbBookMerchantServiceImpl extends ServiceImpl<DtbBookMerchantMapper, DtbBookMerchant> implements IDtbBookMerchantService
{
    @Autowired
    private DtbBookMerchantMapper dtbBookMerchantMapper;

    /**
     * 查询书商
     *
     * @param merchantId 书商主键
     * @return 书商
     */
    @Override
    public DtbBookMerchant selectDtbBookMerchantByMerchantId(Long merchantId)
    {
        return this.getById(merchantId);
    }

    /**
     * 查询书商列表
     *
     * @param dtbBookMerchant 书商
     * @return 书商
     */
    @Override
    public List<DtbBookMerchant> selectDtbBookMerchantList(DtbBookMerchant dtbBookMerchant)
    {
        LambdaQueryWrapper<DtbBookMerchant> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbBookMerchant.getMerchanName())) {
                lambdaQueryWrapper.like(DtbBookMerchant::getMerchanName
                ,dtbBookMerchant.getMerchanName());
            }
                if(ObjectUtil.isNotEmpty(dtbBookMerchant.getChargeMan())) {
                lambdaQueryWrapper.like(DtbBookMerchant::getChargeMan
                ,dtbBookMerchant.getChargeMan());
            }
                lambdaQueryWrapper.orderByAsc(DtbBookMerchant::getSort);


        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增书商
     *
     * @param dtbBookMerchant 书商
     * @return 结果
     */
    @Override
    public boolean insertDtbBookMerchant(DtbBookMerchant dtbBookMerchant)
    {
        return this.save(dtbBookMerchant);
    }

    /**
     * 修改书商
     *
     * @param dtbBookMerchant 书商
     * @return 结果
     */
    @Override
    public boolean updateDtbBookMerchant(DtbBookMerchant dtbBookMerchant)
    {
        return this.updateById(dtbBookMerchant);
    }

    /**
     * 批量删除书商
     *
     * @param merchantIds 需要删除的书商主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookMerchantByMerchantIds(List<Long> merchantIds)
    {
        return this.removeByIds(merchantIds);
    }

}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.shop.mapper.DtbBookAveragePriceMapper">
    
    <resultMap type="DtbBookAveragePrice" id="DtbBookAveragePriceResult">
        <result property="priceId"    column="price_id"    />
        <result property="bookId"    column="book_id"    />
        <result property="startDate"    column="start_date"    />
        <result property="endDate"    column="end_date"    />
        <result property="orderType"    column="order_type"    />
        <result property="avgPrice"    column="avg_price"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDtbBookAveragePriceVo">
        select price_id, book_id, start_date, end_date, order_type, avg_price, create_by, create_time, update_by, update_time from dtb_book_average_price
    </sql>
    <select id="calcDtbBookAveragePrice" resultType="java.math.BigDecimal">
        SELECT
        IFNULL(ROUND(
        SUM(item.price_order_item * item.book_quantity)  / SUM(item.book_quantity),2
        ),0) order_price
        FROM
        dtb_book_order ord
        INNER JOIN dtb_book_order_item item ON ord.order_id = item.order_id
        WHERE
        1 = 1
        AND item.item_status != 'cancel'
        AND ord.create_time > #{startDate}
        AND ord.create_time &lt; date_format(#{endDate}, '%Y-%m-%d 23:59:59')
        AND ord.order_type IN (${orderType})
        AND item.book_id = #{bookId}
    </select>
    <select id="selectDtbBookAveragePriceList" parameterType="DtbBookAveragePrice" resultType="cn.dutp.shop.domain.DtbBookAveragePrice">
        SELECT
            IFNULL(avg.price_id,0) price_id,
            IFNULL(avg.be_edited, 0) be_edited,
            ROUND(
            SUM(item.price_order_item * item.book_quantity) / SUM(item.book_quantity), 2
            ) order_price,
            book.book_id,
            book.book_name,
            book.price_sale,
            book.isbn,
            book.issn,
            avg.update_time,
            IFNULL(avg.avg_price, -1) avg_price
        FROM
            dtb_book_order ord
        INNER JOIN dtb_book_order_item item ON ord.order_id = item.order_id
        INNER JOIN dtb_book book ON item.book_id = book.book_id
        LEFT JOIN (
            SELECT
            p.price_id,
            b.book_id,
            b.book_name,
            b.isbn,
            b.issn,
            p.avg_price,
            p.be_edited,
            p.update_time
        FROM
            dtb_book_average_price p
        INNER JOIN dtb_book b ON p.book_id = b.book_id
        WHERE 1 = 1
            AND p.start_date = date_format(#{startDate}, '%Y-%m-%d')
            AND p.end_date = date_format(#{endDate}, '%Y-%m-%d')
            AND p.order_type = #{orderType}
            <if test="bookName !=null and bookName !=''">
            AND b.book_name LIKE CONCAT('%',#{bookName},'%')
            </if>
            <if test="isbn!=null and isbn!=''">
            AND (b.isbn = #{isbn} OR b.issn = #{isbn})
            </if>
        ) avg ON item.book_id = avg.book_id
        WHERE
            ord.order_status = 'completed'
            AND item.item_status != 'cancel'
            AND ord.create_time > #{startDate}
            AND ord.create_time &lt;  #{endDate}
            AND ord.order_type IN (${orderType})
            <if test="bookName !=null and bookName !=''">
                AND book.book_name LIKE CONCAT('%',#{bookName},'%')
            </if>
            <if test="isbn!=null and isbn!=''">
                AND (book.isbn = #{isbn} OR book.issn = #{isbn})
            </if>
        GROUP BY
            book.book_id,
            book.book_name,
            book.isbn,
            book.issn,
            avg.avg_price,
            avg.update_time,
            avg.price_id,
            avg.be_edited
    </select>
    <select id="selectDtbBookAveragePriceOrderList" resultType="cn.dutp.shop.domain.dto.DtbBookPriceOrderVo">
        SELECT
        ord.order_no,
        ord.order_type,
        book.book_name,
        book.isbn,
        book.issn,
        item.book_quantity,
        item.price_order_item
        FROM
        dtb_book_order ord
        INNER JOIN dtb_book_order_item item ON ord.order_id = item.order_id
        INNER JOIN dtb_book book ON item.book_id = book.book_id
        WHERE
        ord.order_status = 'completed'
        AND ord.create_time > #{startDate}
        AND ord.create_time &lt; date_format(#{endDate}, '%Y-%m-%d 23:59:59')
        AND ord.order_type IN  (${orderType})
        AND item.item_status != 'cancel'
        AND item.book_id = #{bookId}
    </select>
    <select id="selectDtbBookAveragePriceOrderData" resultType="cn.dutp.shop.domain.dto.DtbBookPriceOrderVo">
        SELECT
        COUNT(DISTINCT item.order_id) order_quantity,
        SUM(item.book_quantity) book_quantity,
        SUM(item.price_order_item * item.book_quantity) total_money
        FROM
        dtb_book_order ord
        INNER JOIN dtb_book_order_item item ON ord.order_id = item.order_id
        INNER JOIN dtb_book book ON item.book_id = book.book_id
        WHERE
            ord.order_status = 'completed'
        AND ord.create_time > #{startDate}
        AND ord.create_time &lt; date_format(#{endDate}, '%Y-%m-%d 23:59:59')
        AND ord.order_type IN  (${orderType})
        AND item.item_status != 'cancel'
        AND item.book_id = #{bookId}
    </select>
    <select id="selectDtbBookAveragePriceByPriceId" parameterType="Long" resultMap="DtbBookAveragePriceResult">
        <include refid="selectDtbBookAveragePriceVo"/>
        where price_id = #{priceId}
    </select>

    <insert id="insertDtbBookAveragePrice" parameterType="DtbBookAveragePrice" useGeneratedKeys="true" keyProperty="priceId">
        insert into dtb_book_average_price
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bookId != null">book_id,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="orderType != null">order_type,</if>
            <if test="avgPrice != null">avg_price,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bookId != null">#{bookId},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="orderType != null">#{orderType},</if>
            <if test="avgPrice != null">#{avgPrice},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDtbBookAveragePrice" parameterType="DtbBookAveragePrice">
        update dtb_book_average_price
        <trim prefix="SET" suffixOverrides=",">
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="orderType != null">order_type = #{orderType},</if>
            <if test="avgPrice != null">avg_price = #{avgPrice},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where price_id = #{priceId}
    </update>

    <delete id="deleteDtbBookAveragePriceByPriceId" parameterType="Long">
        delete from dtb_book_average_price where price_id = #{priceId}
    </delete>

    <delete id="deleteDtbBookAveragePriceByPriceIds" parameterType="String">
        delete from dtb_book_average_price where price_id in 
        <foreach item="priceId" collection="array" open="(" separator="," close=")">
            #{priceId}
        </foreach>
    </delete>
</mapper>
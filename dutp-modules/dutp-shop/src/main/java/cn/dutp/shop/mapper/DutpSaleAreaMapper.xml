<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.shop.mapper.DutpSaleAreaMapper">

    <select id="selectAreaList" resultType="cn.dutp.domain.DutpSaleArea">
        SELECT
            sa.* ,
            u.nick_name,
            u.phonenumber
        FROM
            dutp_sale_area AS sa
        LEFT JOIN sys_user AS u ON sa.manager_id = u.user_id and u.del_flag = 0
        <where>
            <if test="areaName != null and areaName != ''">and sa.area_name like concat('%',#{areaName},'%')</if>
            <if test="nickName != null and nickName != ''">and u.nick_name like concat('%',#{nickName},'%')</if>
            <if test="managerId != null">and sa.manager_id like concat('%',#{managerId},'%')</if>
            and sa.del_flag = 0
        </where>
    </select>
</mapper>
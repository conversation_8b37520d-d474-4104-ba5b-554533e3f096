package cn.dutp.shop.service.impl;

import cn.dutp.domain.DtbBookCodeExportInfo;
import cn.dutp.shop.mapper.IDtbBookCodeExportInfoMapper;
import cn.dutp.shop.service.IDtbBookCodeExportInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 购书码导出记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-21
 */
@Service
public class IDtbBookCodeExportInfoServiceImpl extends ServiceImpl<IDtbBookCodeExportInfoMapper, DtbBookCodeExportInfo> implements IDtbBookCodeExportInfoService
{
    @Autowired
    private IDtbBookCodeExportInfoMapper dtbBookCodeExportInfoMapper;


    @Override
    public boolean addExportInfo(DtbBookCodeExportInfo exportInfo) {
        int insert = dtbBookCodeExportInfoMapper.insert(exportInfo);
        System.out.println(exportInfo.getExportDataId());
        return true;
    }
}






package cn.dutp.shop.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.shop.domain.DtbUserInvoiceFile;
import cn.dutp.shop.service.IDtbUserInvoiceFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;

import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * DUTP-DTB-036发票文件Controller
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@RestController
@RequestMapping("/file")
public class DtbUserInvoiceFileController extends BaseController
{
    @Autowired
    private IDtbUserInvoiceFileService dtbUserInvoiceFileService;

/**
 * 查询DUTP-DTB-036发票文件列表
 */
@RequiresPermissions("shop:file:list")
@GetMapping("/list")
    public TableDataInfo list(DtbUserInvoiceFile dtbUserInvoiceFile)
    {
        startPage();
        List<DtbUserInvoiceFile> list = dtbUserInvoiceFileService.selectDtbUserInvoiceFileList(dtbUserInvoiceFile);
        return getDataTable(list);
    }

    /**
     * 导出DUTP-DTB-036发票文件列表
     */
    @RequiresPermissions("shop:file:export")
    @Log(title = "导出发票文件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbUserInvoiceFile dtbUserInvoiceFile)
    {
        List<DtbUserInvoiceFile> list = dtbUserInvoiceFileService.selectDtbUserInvoiceFileList(dtbUserInvoiceFile);
        ExcelUtil<DtbUserInvoiceFile> util = new ExcelUtil<DtbUserInvoiceFile>(DtbUserInvoiceFile.class);
        util.exportExcel(response, list, "DUTP-DTB-036发票文件数据");
    }

    /**
     * 获取DUTP-DTB-036发票文件详细信息
     */
    @RequiresPermissions("shop:file:query")
    @GetMapping(value = "/{fileId}")
    public AjaxResult getInfo(@PathVariable("fileId") Long fileId)
    {
        return success(dtbUserInvoiceFileService.selectDtbUserInvoiceFileByFileId(fileId));
    }

    /**
     * 通过申请id查详情
     */
    @RequiresPermissions("shop:file:query")
    @GetMapping(value = "/getInfoByApplyId/{applyId}")
    public AjaxResult getInfoByApplyId(@PathVariable("applyId") Long applyId)
    {
        return success(dtbUserInvoiceFileService.selectByApplyId(applyId));
    }

    /**
     * 新增DUTP-DTB-036发票文件
     */
    @RequiresPermissions("shop:file:add")
    @Log(title = "添加发票文件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbUserInvoiceFile dtbUserInvoiceFile)
    {
        return toAjax(dtbUserInvoiceFileService.insertDtbUserInvoiceFile(dtbUserInvoiceFile));
    }


    /**
     * 零售批量上传发票文件
     */
    @PostMapping("/addsSaleInvoice")
    public AjaxResult addsSaleInvoice(@RequestBody List<DtbUserInvoiceFile> dtbUserInvoiceFile)
    {
        return toAjax(dtbUserInvoiceFileService.addsSaleInvoice(dtbUserInvoiceFile));
    }

    /**
     * 修改DUTP-DTB-036发票文件
     */
    @RequiresPermissions("shop:file:edit")
    @Log(title = "修改发票文件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbUserInvoiceFile dtbUserInvoiceFile)
    {
        return toAjax(dtbUserInvoiceFileService.updateDtbUserInvoiceFile(dtbUserInvoiceFile));
    }

    /**
     * 删除DUTP-DTB-036发票文件
     */
    @RequiresPermissions("shop:file:remove")
    @Log(title = "删除发票文件", businessType = BusinessType.DELETE)
    @DeleteMapping("/{fileIds}")
    public AjaxResult remove(@PathVariable Long[] fileIds)
    {
        return toAjax(dtbUserInvoiceFileService.deleteDtbUserInvoiceFileByFileIds(Arrays.asList(fileIds)));
    }
}

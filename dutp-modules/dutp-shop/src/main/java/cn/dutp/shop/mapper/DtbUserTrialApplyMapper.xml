<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.shop.mapper.DtbUserTrialApplyMapper">
    
    <resultMap type="DtbUserTrialApply" id="DtbUserTrialApplyResult">
        <result property="applyId"    column="apply_id"    />
        <result property="userId"    column="user_id"    />
        <result property="bookId"    column="book_id"    />
        <result property="trialTime"    column="trial_time"    />
        <result property="examineTime"    column="examine_time"    />
        <result property="examineFileUrl"    column="examine_file_url"    />
        <result property="remark"    column="remark"    />
        <result property="trialDays"    column="trial_days"    />
        <result property="status"    column="status"    />
        <result property="reject"    column="reject"    />
    </resultMap>

    <sql id="selectDtbUserTrialApplyVo">
        select apply_id, user_id, book_id, trial_time, examine_time, activation_time, examine_file_url,remark, trial_days, status, reject from dtb_user_trial_apply
    </sql>

    <select id="selectDtbUserTrialApplyList" parameterType="cn.dutp.shop.domain.DtbUserTrialApply" resultMap="DtbUserTrialApplyResult">
        <include refid="selectDtbUserTrialApplyVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="bookId != null "> and book_id = #{bookId}</if>
            <if test="trialTime != null "> and trial_time = #{trialTime}</if>
            <if test="examineTime != null "> and examine_time = #{examineTime}</if>
            <if test="examineFileUrl != null  and examineFileUrl != ''"> and examine_file_url = #{examineFileUrl}</if>
            <if test="trialDays != null "> and trial_days = #{trialDays}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="reject != null  and reject != ''"> and reject = #{reject}</if>
        </where>
    </select>
    
    <select id="selectDtbUserTrialApplyByApplyId" parameterType="Long" resultType="cn.dutp.shop.domain.DtbUserTrialApply">
        select
            dua.apply_id,
            dua.user_id,
            dua.remark,
            dua.reject,
            df.file_url as examineFileUrl,
            df.file_name as fileName,
            dua.application_reason,
            du.user_name,
            du.real_name as nickName,
            ds.school_name,
            dua.book_id,
            dtb.book_name,
            ifnull(dtb.isbn,dtb.issn) as isbn,
            dua.trial_time,
            dua.examine_time,
            dua.activation_time,
            dua.trial_days,
            dua.status,
            (select count(0) from dtb_user_trial_apply where book_id = dua.book_id and status in(1,3) and user_id = dua.user_id) as trialCount
        from
            dtb_user_trial_apply dua
        left join
            dutp_user du on du.user_id = dua.user_id and du.del_flag = 0
        left join
            dutp_school ds on ds.school_id = du.school_id and ds.del_flag = 0
        left join
            dtb_book dtb on dtb.book_id = dua.book_id and dtb.del_flag = 0
        left join
            dutp_user_common_file df on df.business_id = dua.apply_id and df.del_flag = 0
        where
            dua.apply_id = #{applyId}
    </select>

    <insert id="insertDtbUserTrialApply" parameterType="cn.dutp.shop.domain.DtbUserTrialApply" useGeneratedKeys="true" keyProperty="applyId">
        insert into dtb_user_trial_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="bookId != null">book_id,</if>
            <if test="trialTime != null">trial_time,</if>
            <if test="examineTime != null">examine_time,</if>
            <if test="examineFileUrl != null">examine_file_url,</if>
            <if test="trialDays != null">trial_days,</if>
            <if test="status != null">status,</if>
            <if test="reject != null">reject,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="bookId != null">#{bookId},</if>
            <if test="trialTime != null">#{trialTime},</if>
            <if test="examineTime != null">#{examineTime},</if>
            <if test="examineFileUrl != null">#{examineFileUrl},</if>
            <if test="trialDays != null">#{trialDays},</if>
            <if test="status != null">#{status},</if>
            <if test="reject != null">#{reject},</if>
         </trim>
    </insert>

    <update id="updateDtbUserTrialApply" parameterType="cn.dutp.shop.domain.DtbUserTrialApply">
        update dtb_user_trial_apply
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="trialTime != null">trial_time = #{trialTime},</if>
            <if test="examineTime != null">examine_time = #{examineTime},</if>
            <if test="examineFileUrl != null">examine_file_url = #{examineFileUrl},</if>
            <if test="trialDays != null">trial_days = #{trialDays},</if>
            <if test="status != null">status = #{status},</if>
            <if test="reject != null">reject = #{reject},</if>
        </trim>
        where apply_id = #{applyId}
    </update>

    <delete id="deleteDtbUserTrialApplyByApplyId" parameterType="Long">
        delete from dtb_user_trial_apply where apply_id = #{applyId}
    </delete>

    <delete id="deleteDtbUserTrialApplyByApplyIds" parameterType="String">
        delete from dtb_user_trial_apply where apply_id in 
        <foreach item="applyId" collection="array" open="(" separator="," close=")">
            #{applyId}
        </foreach>
    </delete>

    <select id="TrialShopList" parameterType="cn.dutp.shop.domain.DtbUserTrialApply" resultType="cn.dutp.shop.domain.DtbUserTrialApply">
        select
            dua.apply_id,
            dua.user_id,
            du.user_name,
            du.real_name as nickName,
            ds.school_name,
            dua.book_id,
            dtb.book_name,
            ifnull(dtb.isbn,dtb.issn) as isbn,
            dua.trial_time,
            dua.examine_time,
            dua.activation_time,
            dua.trial_days,
            (select count(0) from dtb_user_trial_apply where book_id = dua.book_id and user_id = dua.user_id and status = 1) as trialCount,
            dua.status
        from
            dtb_user_trial_apply dua
        left join
            dutp_user du on du.user_id = dua.user_id and du.del_flag = 0
        left join
            dtb_book dtb on dtb.book_id = dua.book_id and dtb.del_flag = 0
        left join
            dutp_school ds on ds.school_id = du.school_id and ds.del_flag = 0
        <where>
            <if test="bookName != null and bookName != ''">and dtb.book_name like concat('%', #{bookName}, '%')</if>
            <if test="nickName != null and nickName != ''">and du.real_name like concat('%', #{nickName}, '%')</if>
            <if test="status != null">and dua.status = #{status}</if>
            <if test="schoolName != null and schoolName != ''">and ds.school_name like concat('%', #{schoolName}, '%')</if>
        </where>
        order by dua.trial_time desc
    </select>
    <select id="listEducation" parameterType="cn.dutp.shop.domain.DtbUserTrialApply" resultType="cn.dutp.shop.domain.DtbUserTrialApply">
        select
            dtb.book_name,
            dtb.cover,
            IFNULL(dtb.isbn, dtb.issn) AS isbn,
            dua.apply_id,
            dua.user_id,
            dua.book_id,
            dua.trial_time,
            dua.examine_time,
            dua.activation_time,
            dua.examine_file_url,
            dua.remark,
            dua.trial_days,
            dua.STATUS,
            dua.reject,
            dua.application_reason
        from
            dtb_user_trial_apply dua
        left join
            dtb_book dtb on dtb.book_id = dua.book_id and dtb.del_flag = 0
        <where>
            <if test="userId != null and userId != ''">and dua.user_id = #{userId}</if>
            <if test="bookName != null and bookName != '' or isbn != null and isbn != ''">
                and (
                <if test="bookName != null and bookName != ''">dtb.book_name like concat('%', #{bookName}, '%')</if>
                <if test="bookName != null and bookName != '' and isbn != null and isbn != ''">or</if>
                <if test="isbn != null and isbn != ''">dtb.isbn like concat('%', #{isbn}, '%')</if>
                <if test="bookName != null and bookName != '' and isbn != null and isbn != '' and issn != null and issn != ''">or</if>
                <if test="issn != null and issn != ''">dtb.issn like concat('%', #{issn}, '%')</if>
                )
            </if>
            <if test="bookId != null and bookId != ''">and dtb.book_id = #{bookId}</if>
            <if test="status != null">and dua.STATUS = #{status}</if>
        </where>
    </select>
    <select id="getSuperUser"  resultType="Long">
        SELECT
            sys_user_role.user_id
        FROM
            sys_user_role
                LEFT JOIN
            sys_role
            ON
                sys_user_role.role_id = sys_role.role_id
        WHERE
            sys_role.role_key = 'super'
    </select>
    <select id="getUserInfo"  parameterType="Long" resultType="cn.dutp.system.api.domain.DutpUser">
        SELECT
            u.real_name,
            s.school_name
        FROM
            `dutp-cloud`.dutp_user AS u
                LEFT JOIN
            `dutp-cloud`.dutp_school AS s
            ON
                u.school_id = s.school_id
        WHERE
            u.user_id =  #{userId}
    </select>
</mapper>
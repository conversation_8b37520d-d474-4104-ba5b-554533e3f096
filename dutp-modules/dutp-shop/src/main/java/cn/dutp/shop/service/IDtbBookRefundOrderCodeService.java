package cn.dutp.shop.service;

import cn.dutp.shop.domain.DtbBookRefundOrderCode;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 退款的二维码Service接口
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
public interface IDtbBookRefundOrderCodeService extends IService<DtbBookRefundOrderCode>
{
    /**
     * 查询退款的二维码
     *
     * @param refundCodeId 退款的二维码主键
     * @return 退款的二维码
     */
    public DtbBookRefundOrderCode selectDtbBookRefundOrderCodeByRefundCodeId(Long refundCodeId);

    /**
     * 查询退款的二维码列表
     *
     * @param dtbBookRefundOrderCode 退款的二维码
     * @return 退款的二维码集合
     */
    public List<DtbBookRefundOrderCode> selectDtbBookRefundOrderCodeList(DtbBookRefundOrderCode dtbBookRefundOrderCode);

    /**
     * 新增退款的二维码
     *
     * @param dtbBookRefundOrderCode 退款的二维码
     * @return 结果
     */
    public boolean insertDtbBookRefundOrderCode(DtbBookRefundOrderCode dtbBookRefundOrderCode);

    /**
     * 修改退款的二维码
     *
     * @param dtbBookRefundOrderCode 退款的二维码
     * @return 结果
     */
    public boolean updateDtbBookRefundOrderCode(DtbBookRefundOrderCode dtbBookRefundOrderCode);

    /**
     * 批量删除退款的二维码
     *
     * @param refundCodeIds 需要删除的退款的二维码主键集合
     * @return 结果
     */
    public boolean deleteDtbBookRefundOrderCodeByRefundCodeIds(List<Long> refundCodeIds);

}

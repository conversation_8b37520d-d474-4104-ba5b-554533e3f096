package cn.dutp.shop.service.impl;

import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.shop.domain.DtbBookOrderCode;
import cn.dutp.shop.mapper.DtbBookOrderCodeMapper;
import cn.dutp.shop.service.IDtbBookOrderCodeService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 订单下的购书码Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Service
public class DtbBookOrderCodeServiceImpl extends ServiceImpl<DtbBookOrderCodeMapper, DtbBookOrderCode> implements IDtbBookOrderCodeService
{
    @Autowired
    private DtbBookOrderCodeMapper dtbBookOrderCodeMapper;

    /**
     * 查询订单下的购书码
     *
     * @param orderCodeId 订单下的购书码主键
     * @return 订单下的购书码
     */
    @Override
    public DtbBookOrderCode selectDtbBookOrderCodeByOrderCodeId(Long orderCodeId)
    {
        return this.getById(orderCodeId);
    }

    /**
     * 查询订单下的购书码列表
     *
     * @param dtbBookOrderCode 订单下的购书码
     * @return 订单下的购书码
     */
    @Override
    public List<DtbBookOrderCode> selectDtbBookOrderCodeList(DtbBookOrderCode dtbBookOrderCode)
    {
        LambdaQueryWrapper<DtbBookOrderCode> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbBookOrderCode.getOrderId())) {
                lambdaQueryWrapper.eq(DtbBookOrderCode::getOrderId
                ,dtbBookOrderCode.getOrderId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookOrderCode.getOrderItemId())) {
                lambdaQueryWrapper.eq(DtbBookOrderCode::getOrderItemId
                ,dtbBookOrderCode.getOrderItemId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookOrderCode.getCodeId())) {
                lambdaQueryWrapper.eq(DtbBookOrderCode::getCodeId
                ,dtbBookOrderCode.getCodeId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookOrderCode.getState())) {
                lambdaQueryWrapper.eq(DtbBookOrderCode::getState
                ,dtbBookOrderCode.getState());
            }
                if(ObjectUtil.isNotEmpty(dtbBookOrderCode.getRefundState())) {
                lambdaQueryWrapper.eq(DtbBookOrderCode::getRefundState
                ,dtbBookOrderCode.getRefundState());
            }
                if(ObjectUtil.isNotEmpty(dtbBookOrderCode.getBookId())) {
                lambdaQueryWrapper.eq(DtbBookOrderCode::getBookId
                ,dtbBookOrderCode.getBookId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookOrderCode.getSchoolId())) {
                lambdaQueryWrapper.eq(DtbBookOrderCode::getSchoolId
                ,dtbBookOrderCode.getSchoolId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookOrderCode.getExportQuantity())) {
                lambdaQueryWrapper.eq(DtbBookOrderCode::getExportQuantity
                ,dtbBookOrderCode.getExportQuantity());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增订单下的购书码
     *
     * @param dtbBookOrderCode 订单下的购书码
     * @return 结果
     */
    @Override
    public boolean insertDtbBookOrderCode(DtbBookOrderCode dtbBookOrderCode)
    {
        return this.save(dtbBookOrderCode);
    }

    /**
     * 修改订单下的购书码
     *
     * @param dtbBookOrderCode 订单下的购书码
     * @return 结果
     */
    @Override
    public boolean updateDtbBookOrderCode(DtbBookOrderCode dtbBookOrderCode)
    {
        return this.updateById(dtbBookOrderCode);
    }

    /**
     * 批量删除订单下的购书码
     *
     * @param orderCodeIds 需要删除的订单下的购书码主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookOrderCodeByOrderCodeIds(List<Long> orderCodeIds)
    {
        return this.removeByIds(orderCodeIds);
    }

    /**
     * 更新子订单的导出次数+1
     *
     * @param orderItemList 子订单集合
     * @return 操作结果
     */
    @Override
    public boolean updateExportQuantity(List<Long> orderItemList) {
        return dtbBookOrderCodeMapper.updateExportQuantity(SecurityUtils.getUsername(), orderItemList);
    }

}

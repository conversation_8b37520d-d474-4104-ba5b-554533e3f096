package cn.dutp.shop.service;

import cn.dutp.shop.domain.DtbUserInvoiceTitle;
import com.baomidou.mybatisplus.extension.service.IService;


import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * DUTP-DTB-034发票抬头Service接口
 *
 * <AUTHOR>
 * @date 2024-11-18
 */
public interface IDtbUserInvoiceTitleService extends IService<DtbUserInvoiceTitle>
{
    /**
     * 查询DUTP-DTB-034发票抬头
     *
     * @param titleId DUTP-DTB-034发票抬头主键
     * @return DUTP-DTB-034发票抬头
     */
    public DtbUserInvoiceTitle selectDtbUserInvoiceTitleByTitleId(Long titleId);

    /**
     * 查询零售列表
     *
     * @param dtbUserInvoiceTitle DUTP-DTB-034发票抬头
     * @return DUTP-DTB-034发票抬头集合
     */
    public List<DtbUserInvoiceTitle> selectSaleList(DtbUserInvoiceTitle dtbUserInvoiceTitle);

    /**
     * 查询教务采购列表
     * @param dtbUserInvoiceTitle 对象
     * @return 结果
     */
    List<DtbUserInvoiceTitle> selectEducationList(DtbUserInvoiceTitle dtbUserInvoiceTitle);

    /**
     * 新增DUTP-DTB-034发票抬头
     *
     * @param dtbUserInvoiceTitle DUTP-DTB-034发票抬头
     * @return 结果
     */
    public boolean insertDtbUserInvoiceTitle(DtbUserInvoiceTitle dtbUserInvoiceTitle);

    /**
     * 修改DUTP-DTB-034发票抬头
     *
     * @param dtbUserInvoiceTitle DUTP-DTB-034发票抬头
     * @return 结果
     */
    public boolean updateDtbUserInvoiceTitle(DtbUserInvoiceTitle dtbUserInvoiceTitle);

    /**
     * 批量删除DUTP-DTB-034发票抬头
     *
     * @param titleIds 需要删除的DUTP-DTB-034发票抬头主键集合
     * @return 结果
     */
    public boolean deleteDtbUserInvoiceTitleByTitleIds(List<Long> titleIds);

    /**
     * 批量采购导出
     * @param response 结果
     * @param dtbUserInvoiceTitle 对象
     */
    void exportEdu(HttpServletResponse response, DtbUserInvoiceTitle dtbUserInvoiceTitle);


    /**
     * 学生教师端查询发票抬头列表
     * @param dtbUserInvoiceTitle 对象
     * @return 结果
     */
    List<DtbUserInvoiceTitle> getInvoiceTitleEducation(DtbUserInvoiceTitle dtbUserInvoiceTitle);


}

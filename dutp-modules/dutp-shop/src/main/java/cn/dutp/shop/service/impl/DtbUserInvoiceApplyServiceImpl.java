package cn.dutp.shop.service.impl;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import cn.dutp.api.common.constant.DutpConstant;
import cn.dutp.api.common.constant.NotificationConstants;
import cn.dutp.common.core.constant.SecurityConstants;
import cn.dutp.common.core.domain.R;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBookOrder;
import cn.dutp.message.api.RemoteUserMessageService;
import cn.dutp.message.api.domain.DutpUserMessage;
import cn.dutp.shop.domain.DtbBookMerchant;
import cn.dutp.shop.domain.DtbUserInvoiceApply;
import cn.dutp.shop.domain.DtbUserInvoiceFile;
import cn.dutp.shop.domain.vo.DtbUserInvoiceApplyVo;
import cn.dutp.shop.mapper.*;
import cn.dutp.shop.service.IDtbUserInvoiceApplyService;
import cn.dutp.system.api.RemoteUserService;
import cn.dutp.system.api.domain.SysUser;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections.list.TreeList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * DUTP-DTB-035开票申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Service
public class DtbUserInvoiceApplyServiceImpl extends ServiceImpl<DtbUserInvoiceApplyMapper, DtbUserInvoiceApply> implements IDtbUserInvoiceApplyService
{
    @Autowired
    private DtbUserInvoiceApplyMapper dtbUserInvoiceApplyMapper;
    @Autowired
    private DtbBookMerchantMapper dtbBookMerchantMapper;
    @Autowired
    private DtbBookStatementMapper dtbBookStatementMapper;
    @Autowired
    private DtbUserInvoiceFileMapper dtbUserInvoiceFileMapper;
    @Autowired
    private RemoteUserMessageService remoteUserMessageService;
    @Autowired
    private RemoteUserService remoteUserService;



    /**
     * 查询DUTP-DTB-035开票申请
     *
     * @param applyId DUTP-DTB-035开票申请主键
     * @return DUTP-DTB-035开票申请
     */
    @Override
    public DtbUserInvoiceApply selectDtbUserInvoiceApplyByApplyId(Long applyId)
    {
        return this.getById(applyId);
    }

    /**
     * 查询DUTP-DTB-035开票申请列表
     *
     * @param dtbUserInvoiceApply DUTP-DTB-035开票申请
     * @return DUTP-DTB-035开票申请
     */
    @Override
    public List<DtbUserInvoiceApply> selectDtbUserInvoiceApplyList(DtbUserInvoiceApply dtbUserInvoiceApply)
    {
        LambdaQueryWrapper<DtbUserInvoiceApply> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbUserInvoiceApply.getTitleId())) {
                lambdaQueryWrapper.eq(DtbUserInvoiceApply::getTitleId
                ,dtbUserInvoiceApply.getTitleId());
            }
                if(ObjectUtil.isNotEmpty(dtbUserInvoiceApply.getOrderId())) {
                lambdaQueryWrapper.eq(DtbUserInvoiceApply::getOrderId
                ,dtbUserInvoiceApply.getOrderId());
            }
                if(ObjectUtil.isNotEmpty(dtbUserInvoiceApply.getApplyStatus())) {
                lambdaQueryWrapper.eq(DtbUserInvoiceApply::getApplyStatus
                ,dtbUserInvoiceApply.getApplyStatus());
            }
                if(ObjectUtil.isNotEmpty(dtbUserInvoiceApply.getApplyType())) {
                lambdaQueryWrapper.eq(DtbUserInvoiceApply::getApplyType
                ,dtbUserInvoiceApply.getApplyType());
            }
                if(ObjectUtil.isNotEmpty(dtbUserInvoiceApply.getUploadTime())) {
                lambdaQueryWrapper.eq(DtbUserInvoiceApply::getUploadTime
                ,dtbUserInvoiceApply.getUploadTime());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增DUTP-DTB-035开票申请
     *
     * @param dtbUserInvoiceApply DUTP-DTB-035开票申请
     * @return 结果
     */
//    @Override
//    public boolean insertDtbUserInvoiceApply(DtbUserInvoiceApply dtbUserInvoiceApply)
//    {
//        return this.save(dtbUserInvoiceApply);
//    }

    /**
     * 修改DUTP-DTB-035开票申请
     *
     * @param dtbUserInvoiceApply DUTP-DTB-035开票申请
     * @return 结果
     */
    @Override
    public boolean updateDtbUserInvoiceApply(DtbUserInvoiceApply dtbUserInvoiceApply)
    {
        return this.updateById(dtbUserInvoiceApply);
    }

    /**
     * 批量删除DUTP-DTB-035开票申请
     *
     * @param applyIds 需要删除的DUTP-DTB-035开票申请主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbUserInvoiceApplyByApplyIds(List<Long> applyIds)
    {
        return this.removeByIds(applyIds);
    }

    /**
     * 订单计算：提交开票申请
     * @param dtbUserInvoiceApply 申请对象
     * @return 结果
     */
    @Override
    public boolean addInvoiceApply(DtbUserInvoiceApply dtbUserInvoiceApply) {
        boolean save = false;
        //书商采购
        if(ObjectUtil.isNotEmpty(dtbUserInvoiceApply.getMerchantId())){
            Long merchantId = dtbUserInvoiceApply.getMerchantId();
            DtbBookMerchant merchant = dtbBookMerchantMapper.getByMerchantId(merchantId);
            DtbUserInvoiceApply apply = new DtbUserInvoiceApply();
            apply.setInvoiceRemark(dtbUserInvoiceApply.getInvoiceRemark());
            apply.setStatementId(dtbUserInvoiceApply.getStatementId());
            apply.setOrderType(2);
            apply.setApplyType(merchant.getApplyType());
            save = this.save(apply);
        }
        //学校采购
        if(ObjectUtil.isEmpty(dtbUserInvoiceApply.getMerchantId())){
            Long schoolId = dtbUserInvoiceApply.getSchoolId();
            String statementNo = dtbUserInvoiceApply.getStatementNo();
            List<DtbBookOrder> bookOrderList = dtbUserInvoiceApplyMapper.selectBySchoolId(schoolId, statementNo);
            DtbUserInvoiceApply apply = new DtbUserInvoiceApply();
            apply.setInvoiceRemark(dtbUserInvoiceApply.getInvoiceRemark());
            apply.setStatementId(dtbUserInvoiceApply.getStatementId());
            apply.setOrderType(2);
            if(ObjectUtil.isNotEmpty(bookOrderList)){
                for(DtbBookOrder order : bookOrderList){
                    if(order.getOrderType() == 3 || order.getOrderType() == 4 || order.getOrderType() ==5){
                        apply.setApplyType(2);
                    }
                }

            }
            save = this.save(apply);
        }
        //修改状态为待开票
        dtbBookStatementMapper.changeInvoiceStatus(dtbUserInvoiceApply.getStatementId());

        //发送消息
        String title = "开票申请提醒";
        String content = String.format(NotificationConstants.REISSUE_INVOICE, SecurityUtils.getUsername());

        // 管理员
        R<List<SysUser>> adminIdList = remoteUserService.listUserByRoleId(2L, SecurityConstants.FROM_SOURCE);
        if (R.FAIL == adminIdList.getCode())
        {
            throw new ServiceException("根据角色查获取用户列表失败");
        }
        List<SysUser> receiveList = adminIdList.getData();

        //有发票管理权限的人
        R<List<SysUser>> menuUserIdList = remoteUserService.listUserByMenuId(2139L, SecurityConstants.FROM_SOURCE);
        if (R.FAIL == menuUserIdList.getCode())
        {
            throw new ServiceException("据菜单获取用户列表失败");
        }
        List<SysUser> menuUserList = menuUserIdList.getData();

        //合并 receiveList 和 menuUserList得到的list
        List<SysUser> mergedList = Stream.concat(receiveList.stream(), menuUserList.stream())
                .collect(Collectors.toCollection(() ->
                        new TreeSet<>(Comparator.comparing(SysUser::getUserId))
                )).stream().collect(Collectors.toList());
        //合并管理员和有权限的人
        if (!CollectionUtils.isEmpty(mergedList)) {
            mergedList.stream().forEach(e -> {
                boolean isResult1 = false;
                DutpUserMessage message = new DutpUserMessage();
                message.setTitle(title);
                message.setContent(content);
                //发送者
                message.setFromUserId(SecurityUtils.getUserId());
                //接受者
                message.setToUserId(e.getUserId());
                // 1系统消息2教务消息3推送消息
                message.setMessageType(1);
                //发送者 用户类型 1后台 2前台
                message.setFromUserType(1);
                //接受者 用户类型 1后台 2前台
                message.setToUserType(1);
                message.setBusinessId(dtbUserInvoiceApply.getStatementId());
                R<Boolean> booleanR = remoteUserMessageService.addMessage(message);
                if(booleanR.getCode() == 500){
                    isResult1 = false;
                    throw new ServiceException("Message模块未启动，无法发送消息，申请无法提交！");
                }
            });
        }
        // 发送消息给售后管理模块的人
//        R<List<SysUser>> menuUserIdList = remoteUserService.listUserByMenuId(2139L, SecurityConstants.FROM_SOURCE);
//        if (R.FAIL == menuUserIdList.getCode())
//        {
//            throw new ServiceException("据菜单获取用户列表失败");
//        }
//        List<SysUser> menuUserList = menuUserIdList.getData();
//        if (!CollectionUtils.isEmpty(menuUserList)) {
//            menuUserList.stream().forEach(e -> {
//                boolean isResult1 = false;
//                DutpUserMessage message = new DutpUserMessage();
//                message.setTitle(title);
//                message.setContent(content);
//                //发送者
//                message.setFromUserId(SecurityUtils.getUserId());
//                //接受者
//                message.setToUserId(e.getUserId());
//                // 1系统消息2教务消息3推送消息
//                message.setMessageType(1);
//                //发送者 用户类型 1后台 2前台
//                message.setFromUserType(1);
//                //接受者 用户类型 1后台 2前台
//                message.setToUserType(1);
//                message.setBusinessId(dtbUserInvoiceApply.getStatementId());
//                R<Boolean> booleanR = remoteUserMessageService.addMessage(message);
//                if(booleanR.getCode() == 500){
//                    isResult1 = false;
//                    throw new ServiceException("Message模块未启动，无法发送消息，申请无法提交！");
//                }
//            });
//        }
        return save;
    }

    @Override
    public List<DtbUserInvoiceApply> getStatementInvoice(Long statementId) {
        return dtbUserInvoiceApplyMapper.getStatementInvoice(statementId);
    }

    /**
     * 拒绝换开
     *
     * @param dtbUserInvoiceApply 对象
     * @return 结果
     */
    @Override
    public boolean noChangeInvoice(DtbUserInvoiceApply dtbUserInvoiceApply) {
        LambdaUpdateWrapper<DtbUserInvoiceApply> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DtbUserInvoiceApply::getApplyId, dtbUserInvoiceApply.getApplyId()).set(DtbUserInvoiceApply::getApplyStatus,2);
        boolean isResult = this.update(updateWrapper);
        if (dtbUserInvoiceApply.getOrderType() == 1){
            //零售发送给前台

            String title = "发票换开提醒";
            //接受者id
            Long userId = dtbUserInvoiceApply.getUserId();
            String content = String.format(NotificationConstants.INVOICE_REJECTED,dtbUserInvoiceApply.getOrderNo(),"暂不支持换开，如有问题请联系工作人员");
            // 给前台发送消息
            sendMessage(title,content,userId);
        }else {
            //批量采购发送消息

            String title = "发票换开提醒";
            //接受者id
            String createByName = dtbUserInvoiceApply.getCreateBy();
            Long userId = dtbUserInvoiceApplyMapper.selectUserIdByName(createByName);
            String content = String.format(NotificationConstants.INVOICE_REJECTED_SALES,dtbUserInvoiceApply.getStatementNo(),"暂不支持换开，如有问题请联系工作人员");
            // 发送消息
            DutpUserMessage message = new DutpUserMessage();
            message.setTitle(title);
            message.setContent(content);
            //发送者
            message.setFromUserId(SecurityUtils.getUserId());
            //接受者
            message.setToUserId(userId);
            // 1系统消息2教务消息3推送消息
            message.setMessageType(1);
            //发送者 用户类型 1后台 2前台
            message.setFromUserType(1);
            //接受者 用户类型 1后台 2前台
            message.setToUserType(1);
            R<Boolean> booleanR = remoteUserMessageService.addMessage(message);
            if(booleanR.getCode() == 500){
                throw new ServiceException("Message模块未启动，无法发送消息，发票无法提交！");
            }
        }
        return isResult;
    }

    public void sendMessage(String title,String content,Long toUserId){
        boolean isResult = false;
        DutpUserMessage message = new DutpUserMessage();
        message.setTitle(title);
        message.setContent(content);
        //发送者
        message.setFromUserId(SecurityUtils.getUserId());
        //接受者
        message.setToUserId(toUserId);
        // 1系统消息2教务消息3推送消息
        message.setMessageType(1);
        //发送者 用户类型 1后台 2前台
        message.setFromUserType(1);
        //接受者 用户类型 1后台 2前台
        message.setToUserType(2);
        R<Boolean> booleanR = remoteUserMessageService.addMessage(message);
        if(booleanR.getCode() == 500){
            isResult = false;
            throw new ServiceException("Message模块未启动，无法发送消息，发票无法提交！");
        }

    }

    /**
     * 学生教师端新增开票申请
     * @param dtbUserInvoiceApply 申请对象
     * @return 结果
     */
    @Override
    public boolean addInvoiceApplyEducation(DtbUserInvoiceApply dtbUserInvoiceApply) {
        // 申请状态1申请2已开票3换票
        dtbUserInvoiceApply.setApplyStatus(DutpConstant.NUM_ONE);
        // 订单类型
        dtbUserInvoiceApply.setOrderType(DutpConstant.NUM_ONE);
        // 开票时间
        dtbUserInvoiceApply.setUploadTime(new Date());
        return this.save(dtbUserInvoiceApply);
    }
    /**
     * 学生教师端查询开票申请
     *
     * @param dtbUserInvoiceApply 申请对象
     * @return DUTP-DTB-035开票申请
     */
    @Override
    public List<DtbUserInvoiceApplyVo> getInfoEducation(DtbUserInvoiceApply dtbUserInvoiceApply)
    {
        List<DtbUserInvoiceApplyVo> res = baseMapper.getInfoEducation(dtbUserInvoiceApply);
        res.forEach(item->{
            LambdaQueryWrapper<DtbUserInvoiceFile> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(DtbUserInvoiceFile::getApplyId,item.getApplyId());
            lambdaQueryWrapper.eq(DtbUserInvoiceFile::getFileType,DutpConstant.NUM_ZERO);
            item.setInvoiceFileList(dtbUserInvoiceFileMapper.selectList(lambdaQueryWrapper));
        });
        return res;
    }
}

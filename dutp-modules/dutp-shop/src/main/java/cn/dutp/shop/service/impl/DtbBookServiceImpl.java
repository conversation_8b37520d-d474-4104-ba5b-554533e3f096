package cn.dutp.shop.service.impl;

import cn.dutp.api.common.constant.DutpConstant;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBook;
import cn.dutp.shop.mapper.DtbShopBookMapper;
import cn.dutp.shop.service.IDtbBookService;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * DUTP-DTB_002数字教材Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-04
 */
@Service
public class DtbBookServiceImpl extends ServiceImpl<DtbShopBookMapper, DtbBook> implements IDtbBookService
{
    @Autowired
    private DtbShopBookMapper dtbBookMapper;

    /**
     * 查询DUTP-DTB_002数字教材
     *
     * @param bookId DUTP-DTB_002数字教材主键
     * @return DUTP-DTB_002数字教材
     */
    @Override
    public DtbBook selectDtbBookByBookId(Long bookId)
    {
        return this.getById(bookId);
    }

    /**
     * 查询DUTP-DTB_002数字教材列表
     *
     * @param dtbBook DUTP-DTB_002数字教材
     * @return DUTP-DTB_002数字教材
     */
    @Override
    public List<DtbBook> selectDtbBookList(DtbBook dtbBook)
    {
        LambdaQueryWrapper<DtbBook> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(ObjectUtil.isNotEmpty(dtbBook.getBookName())) {
            lambdaQueryWrapper.like(DtbBook::getBookName
                    ,dtbBook.getBookName());
        }

        if(ObjectUtil.isNotEmpty(dtbBook.getIsbn())) {
            lambdaQueryWrapper.eq(DtbBook::getIsbn
                    ,dtbBook.getIsbn());
        }
        if(ObjectUtil.isNotEmpty(dtbBook.getBookNo())) {
            lambdaQueryWrapper.eq(DtbBook::getBookNo
                    ,dtbBook.getBookNo());
        }
        if(ObjectUtil.isNotEmpty(dtbBook.getPublishDate())) {
            lambdaQueryWrapper.eq(DtbBook::getPublishDate
                    ,dtbBook.getPublishDate());
        }
        if(ObjectUtil.isNotEmpty(dtbBook.getPublishOrganization())) {
            lambdaQueryWrapper.eq(DtbBook::getPublishOrganization
                    ,dtbBook.getPublishOrganization());
        }
        if(ObjectUtil.isNotEmpty(dtbBook.getPublishStatus())) {
            lambdaQueryWrapper.eq(DtbBook::getPublishStatus
                    ,dtbBook.getPublishStatus());
        }
        if(ObjectUtil.isNotEmpty(dtbBook.getSchoolId())) {
            lambdaQueryWrapper.eq(DtbBook::getSchoolId
                    ,dtbBook.getSchoolId());
        }
        if(ObjectUtil.isNotEmpty(dtbBook.getBookType())) {
            lambdaQueryWrapper.eq(DtbBook::getBookType
                    ,dtbBook.getBookType());
        }

        if(dtbBook.getShelfState() != null) {
            lambdaQueryWrapper.eq(DtbBook::getShelfState
                    ,dtbBook.getShelfState());
        }

        if(ObjectUtil.isNotEmpty(dtbBook.getTopicNo())) {
            lambdaQueryWrapper.eq(DtbBook::getTopicNo
                    ,dtbBook.getTopicNo());
        }

        if(ObjectUtil.isNotEmpty(dtbBook.getBookId())) {
            lambdaQueryWrapper.eq(DtbBook::getBookId
                    ,dtbBook.getBookId());
        }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增DUTP-DTB_002数字教材
     *
     * @param dtbBook DUTP-DTB_002数字教材
     * @return 结果
     */
    @Override
    public boolean insertDtbBook(DtbBook dtbBook)
    {
        return this.save(dtbBook);
    }

    /**
     * 修改DUTP-DTB_002数字教材
     *
     * @param dtbBook DUTP-DTB_002数字教材
     * @return 结果
     */
    @Override
    public boolean updateDtbBook(DtbBook dtbBook)
    {
        return this.updateById(dtbBook);
    }

    /**
     * 批量删除DUTP-DTB_002数字教材
     *
     * @param bookIds 需要删除的DUTP-DTB_002数字教材主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookByBookIds(List<Long> bookIds)
    {
        return this.removeByIds(bookIds);
    }

    @Override
    public List<DtbBook> bookShopList(DtbBook dtbBook) {
        List<DtbBook> list = dtbBookMapper.bookShopList(dtbBook);
        List<Long> ids = list.stream().map(DtbBook::getBookId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(ids)) {
            LambdaQueryWrapper<DtbBook> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.in(DtbBook::getMasterBookId, ids).eq(DtbBook::getMasterFlag,3)
                    .eq(DtbBook::getPublishStatus,2);
            List<DtbBook> childList = dtbBookMapper.selectList(lambdaQueryWrapper);
            if (!CollectionUtils.isEmpty(childList)) {
                list.stream().forEach(e -> {
                    List<DtbBook> child = childList.stream().filter(c -> c.getMasterBookId().equals(e.getBookId())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(child)) {
                        e.setChildren(child);
                    }
                });
            }
        }
        return list;
    }

    @Override
    public List<Tree<String>> selectShopDtbBookList(DtbBook dtbBook) {
        List<DtbBook> list = dtbBookMapper.bookShopList(dtbBook);
        List<Long> ids = list.stream().map(DtbBook::getBookId).collect(Collectors.toList());
        LambdaQueryWrapper<DtbBook> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(DtbBook::getMasterBookId, ids).eq(DtbBook::getMasterFlag,3);
        List<DtbBook> childList = dtbBookMapper.selectList(lambdaQueryWrapper);
        List<DtbBook> allList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(childList)) {
            allList.addAll(childList);
            allList.addAll(list);
        }
        return getTreeList(allList);
    }

    @Override
    public Integer bookShopListCount(DtbBook dtbBook) {
        List<DtbBook> list = dtbBookMapper.bookShopList(dtbBook);
        return list.size();
    }

    private List<Tree<String>> getTreeList(List<DtbBook> list) {
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setChildrenKey("children");
        // 格式化树形结构
        List<Tree<String>> treeNodes = TreeUtil.build(list,"0",treeNodeConfig,
                (treeNode, tree) -> {

                    tree.setId(treeNode.getSchoolId().toString());
                    if (treeNode.getMasterBookId() != null) {
                        tree.setParentId(treeNode.getMasterBookId().toString());
                    } else {
                        tree.setParentId(null);
                    }
                    tree.setName(treeNode.getBookName());
                    tree.setWeight(treeNode.getCreateTime());

                    tree.putExtra("bookId", treeNode.getBookId());
                    tree.putExtra("bookName", treeNode.getBookName());
                    tree.putExtra("cover", treeNode.getCover());
                    tree.putExtra("isbn", treeNode.getIsbn());
                    tree.putExtra("issn", treeNode.getIssn());
                    tree.putExtra("bookNo", treeNode.getBookNo());
                    tree.putExtra("masterFlag", treeNode.getMasterFlag());
                    tree.putExtra("masterBookId", treeNode.getMasterBookId());
                    tree.putExtra("publishDate", treeNode.getPublishDate());
                    tree.putExtra("publishOrganization", treeNode.getPublishOrganization());
                    tree.putExtra("publishStatus", treeNode.getPublishStatus());
                    tree.putExtra("schoolId", treeNode.getSchoolId());
                    tree.putExtra("bookType", treeNode.getBookType());
                    tree.putExtra("soldQuantity", treeNode.getSoldQuantity());
                    tree.putExtra("readQuantity", treeNode.getReadQuantity());
                    tree.putExtra("priceCounter", treeNode.getPriceCounter());
                    tree.putExtra("priceSale", treeNode.getPriceSale());
                    tree.putExtra("bookOrganize", treeNode.getBookOrganize());
                    tree.putExtra("topicNo", treeNode.getTopicNo());
                    tree.putExtra("delFlag", treeNode.getDelFlag());
                    tree.putExtra("shelfTime", treeNode.getShelfTime());
                    tree.putExtra("unshelfTime", treeNode.getUnshelfTime());
                    tree.putExtra("shelfState", treeNode.getShelfState());
                    tree.putExtra("inventory", treeNode.getInventory());
                    tree.putExtra("inventoryStatus", treeNode.getInventoryStatus());
                    tree.putExtra("createBy", treeNode.getCreateBy());
                    tree.putExtra("createTime", treeNode.getCreateTime());
                    tree.putExtra("updateTime", treeNode.getUpdateTime());
                    tree.putExtra("updateBy", treeNode.getUpdateBy());
                });
        return treeNodes;
    }

    /**
     * 查询合作院校数字教材集合
     *
     * @param dtbBook 数字教材
     * @return 数字教材集合
     */
    @Override
    public List<DtbBook> selectPartnersDtbBookList(DtbBook dtbBook)
    {
        return baseMapper.selectPartnersDtbBookList(dtbBook);
    }

    /**
     * 查询购买教材
     *
     * @param dtbBook 数字教材
     * @return 数字教材集合
     */
    @Override
    public DtbBook getShopBook(DtbBook dtbBook)
    {
        LambdaQueryWrapper<DtbBook> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DtbBook::getBookId,dtbBook.getBookId());
        queryWrapper.eq(DtbBook::getShelfState, DutpConstant.NUM_ONE);
        return baseMapper.selectOne(queryWrapper);
    }
}

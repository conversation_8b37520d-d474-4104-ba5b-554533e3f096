package cn.dutp.shop.controller;

import cn.dutp.api.common.constant.DutpConstant;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.log.enums.OperatorType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.shop.domain.DtbUserInvoiceApply;
import cn.dutp.shop.domain.DtbUserInvoiceTitle;
import cn.dutp.shop.service.IDtbUserInvoiceApplyService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * DUTP-DTB-035开票申请Controller
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Slf4j
@RestController
@RequestMapping("/apply")
public class DtbUserInvoiceApplyController extends BaseController {
    @Resource
    private IDtbUserInvoiceApplyService dtbUserInvoiceApplyService;

    /**
     * 查询DUTP-DTB-035开票申请列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbUserInvoiceApply dtbUserInvoiceApply) {
        startPage();
        List<DtbUserInvoiceApply> list = dtbUserInvoiceApplyService.selectDtbUserInvoiceApplyList(dtbUserInvoiceApply);
        return getDataTable(list);
    }

    /**
     * 导出DUTP-DTB-035开票申请列表
     */
    @RequiresPermissions("shop:apply:export")
    @Log(title = "导出开票申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbUserInvoiceApply dtbUserInvoiceApply) {
        List<DtbUserInvoiceApply> list = dtbUserInvoiceApplyService.selectDtbUserInvoiceApplyList(dtbUserInvoiceApply);
        ExcelUtil<DtbUserInvoiceApply> util = new ExcelUtil<DtbUserInvoiceApply>(DtbUserInvoiceApply.class);
        util.exportExcel(response, list, "DUTP-DTB-035开票申请数据");
    }

    /**
     * 获取DUTP-DTB-035开票申请详细信息
     */
    @RequiresPermissions("shop:apply:query")
    @GetMapping(value = "/{applyId}")
    public AjaxResult getInfo(@PathVariable("applyId") Long applyId) {
        return success(dtbUserInvoiceApplyService.selectDtbUserInvoiceApplyByApplyId(applyId));
    }

    /**
     * 新增DUTP-DTB-035开票申请
     */
    @RequiresPermissions("shop:apply:add")
    @Log(title = "添加开票申请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbUserInvoiceApply dtbUserInvoiceApply)
    {
        return toAjax(dtbUserInvoiceApplyService.addInvoiceApply(dtbUserInvoiceApply));
    }

    /**
     * 修改DUTP-DTB-035开票申请
     */
    @RequiresPermissions("shop:apply:edit")
    @Log(title = "修改开票申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbUserInvoiceApply dtbUserInvoiceApply) {
        return toAjax(dtbUserInvoiceApplyService.updateDtbUserInvoiceApply(dtbUserInvoiceApply));
    }

    /**
     * 删除DUTP-DTB-035开票申请
     */
    @RequiresPermissions("shop:apply:remove")
    @Log(title = "删除开票申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/{applyIds}")
    public AjaxResult remove(@PathVariable Long[] applyIds) {
        return toAjax(dtbUserInvoiceApplyService.deleteDtbUserInvoiceApplyByApplyIds(Arrays.asList(applyIds)));
    }

    /**
     * 订单结算：提交开票申请
     *
     * @param dtbUserInvoiceApply 开票申请
     * @return 结果
     */
    @PostMapping("/addInvoiceApply")
    public AjaxResult addInvoiceApply(@RequestBody DtbUserInvoiceApply dtbUserInvoiceApply) {
        return toAjax(dtbUserInvoiceApplyService.addInvoiceApply(dtbUserInvoiceApply));
    }


    /**
     * 拒绝换开
     */
    @PutMapping("noChangeInvoice")
    public AjaxResult noChangeInvoice(@RequestBody DtbUserInvoiceApply dtbUserInvoiceApply)
    {
        return toAjax(dtbUserInvoiceApplyService.noChangeInvoice(dtbUserInvoiceApply));
    }


    /**
     * 学生教师端新增开票申请
     */
    @Log(title = "学生教师端新增开票申请", businessType = BusinessType.INSERT, operatorType = OperatorType.READER)
    @PostMapping("/addInvoiceApplyEducation")
    public AjaxResult addInvoiceApplyEducation(@RequestBody DtbUserInvoiceApply dtbUserInvoiceApply)
    {
        LambdaQueryWrapper<DtbUserInvoiceApply> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DtbUserInvoiceApply::getOrderId,dtbUserInvoiceApply.getOrderId());
        DtbUserInvoiceApply res = dtbUserInvoiceApplyService.getBaseMapper().selectOne(lambdaQueryWrapper);
        if(ObjectUtil.isNotNull(res)){
            return AjaxResult.error(DutpConstant.APPLY_FOR_INVOICE_FAILED);
        }
        return toAjax(dtbUserInvoiceApplyService.addInvoiceApplyEducation(dtbUserInvoiceApply));
    }

    /**
     * 学生教师端查询发票
     */
    @Log(title = "学生教师端查询发票", operatorType = OperatorType.READER)
    @PostMapping(value = "/getInfoEducation")
    public AjaxResult getInfoEducation(@RequestBody DtbUserInvoiceApply dtbUserInvoiceApply) {
        return success(dtbUserInvoiceApplyService.getInfoEducation(dtbUserInvoiceApply));
    }

    /**
     * 学生教师端换开申请
     */
    @Log(title = "学生教师端换开申请", businessType = BusinessType.UPDATE, operatorType = OperatorType.READER)
    @PutMapping("/editEducation")
    public AjaxResult editEducation(@RequestBody DtbUserInvoiceApply dtbUserInvoiceApply) {

        DtbUserInvoiceApply res = dtbUserInvoiceApplyService.selectDtbUserInvoiceApplyByApplyId(dtbUserInvoiceApply.getApplyId());
        if(res.getChangeCount() == 1){
            return AjaxResult.error(DutpConstant.CHANGE_INVOICE_FAILED);
        }
        return toAjax(dtbUserInvoiceApplyService.updateDtbUserInvoiceApply(dtbUserInvoiceApply));
    }
}

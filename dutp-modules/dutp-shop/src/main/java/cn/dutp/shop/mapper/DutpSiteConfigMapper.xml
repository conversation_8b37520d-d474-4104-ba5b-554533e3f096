<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.shop.mapper.SiteConfigMapper">
    
    <resultMap type="DutpSiteConfig" id="DutpSiteConfigResult">
        <result property="configId"    column="config_id"    />
        <result property="teacherCopyCountLimit"    column="teacher_copy_count_limit"    />
        <result property="teacherCopyWordLimit"    column="teacher_copy_word_limit"    />
        <result property="studentCopyCountLimit"    column="student_copy_count_limit"    />
        <result property="studentCopyWordLimit"    column="student_copy_word_limit"    />
        <result property="readerCopyCountLimit"    column="reader_copy_count_limit"    />
        <result property="readerCopyWordLimit"    column="reader_copy_word_limit"    />
        <result property="exchangeLimit"    column="exchange_limit"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDutpSiteConfigVo">
        select config_id, teacher_copy_count_limit, teacher_copy_word_limit, student_copy_count_limit, student_copy_word_limit, reader_copy_count_limit, reader_copy_word_limit, exchange_limit, create_by, create_time, update_by, update_time from dutp_site_config
    </sql>

    <select id="selectDutpSiteConfigList" parameterType="DutpSiteConfig" resultMap="DutpSiteConfigResult">
        <include refid="selectDutpSiteConfigVo"/>
        <where>  
            <if test="teacherCopyCountLimit != null "> and teacher_copy_count_limit = #{teacherCopyCountLimit}</if>
            <if test="teacherCopyWordLimit != null "> and teacher_copy_word_limit = #{teacherCopyWordLimit}</if>
            <if test="studentCopyCountLimit != null "> and student_copy_count_limit = #{studentCopyCountLimit}</if>
            <if test="studentCopyWordLimit != null "> and student_copy_word_limit = #{studentCopyWordLimit}</if>
            <if test="readerCopyCountLimit != null "> and reader_copy_count_limit = #{readerCopyCountLimit}</if>
            <if test="readerCopyWordLimit != null "> and reader_copy_word_limit = #{readerCopyWordLimit}</if>
            <if test="exchangeLimit != null "> and exchange_limit = #{exchangeLimit}</if>
        </where>
    </select>
    
    <select id="selectDutpSiteConfigByConfigId" parameterType="Long" resultMap="DutpSiteConfigResult">
        <include refid="selectDutpSiteConfigVo"/>
        where config_id = #{configId}
    </select>

    <insert id="insertDutpSiteConfig" parameterType="DutpSiteConfig" useGeneratedKeys="true" keyProperty="configId">
        insert into dutp_site_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherCopyCountLimit != null">teacher_copy_count_limit,</if>
            <if test="teacherCopyWordLimit != null">teacher_copy_word_limit,</if>
            <if test="studentCopyCountLimit != null">student_copy_count_limit,</if>
            <if test="studentCopyWordLimit != null">student_copy_word_limit,</if>
            <if test="readerCopyCountLimit != null">reader_copy_count_limit,</if>
            <if test="readerCopyWordLimit != null">reader_copy_word_limit,</if>
            <if test="exchangeLimit != null">exchange_limit,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherCopyCountLimit != null">#{teacherCopyCountLimit},</if>
            <if test="teacherCopyWordLimit != null">#{teacherCopyWordLimit},</if>
            <if test="studentCopyCountLimit != null">#{studentCopyCountLimit},</if>
            <if test="studentCopyWordLimit != null">#{studentCopyWordLimit},</if>
            <if test="readerCopyCountLimit != null">#{readerCopyCountLimit},</if>
            <if test="readerCopyWordLimit != null">#{readerCopyWordLimit},</if>
            <if test="exchangeLimit != null">#{exchangeLimit},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDutpSiteConfig" parameterType="DutpSiteConfig">
        update dutp_site_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherCopyCountLimit != null">teacher_copy_count_limit = #{teacherCopyCountLimit},</if>
            <if test="teacherCopyWordLimit != null">teacher_copy_word_limit = #{teacherCopyWordLimit},</if>
            <if test="studentCopyCountLimit != null">student_copy_count_limit = #{studentCopyCountLimit},</if>
            <if test="studentCopyWordLimit != null">student_copy_word_limit = #{studentCopyWordLimit},</if>
            <if test="readerCopyCountLimit != null">reader_copy_count_limit = #{readerCopyCountLimit},</if>
            <if test="readerCopyWordLimit != null">reader_copy_word_limit = #{readerCopyWordLimit},</if>
            <if test="exchangeLimit != null">exchange_limit = #{exchangeLimit},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where config_id = #{configId}
    </update>

    <delete id="deleteDutpSiteConfigByConfigId" parameterType="Long">
        delete from dutp_site_config where config_id = #{configId}
    </delete>

    <delete id="deleteDutpSiteConfigByConfigIds" parameterType="String">
        delete from dutp_site_config where config_id in 
        <foreach item="configId" collection="array" open="(" separator="," close=")">
            #{configId}
        </foreach>
    </delete>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.shop.mapper.DutpSaleAreaMemberMapper">

    <select id="selectByMemberList" parameterType="Long" resultType="cn.dutp.domain.DutpSaleAreaMember">
        select
            sam.*,
            u.nick_name,
            u.phonenumber
        from
            dutp_sale_area_member AS sam
        LEFT JOIN
            sys_user AS u ON sam.user_id = u.user_id AND u.del_flag = 0
        LEFT JOIN
            dutp_sale_area as sa on sa.area_id = sam.area_id and sa.del_flag = 0
        <where>
            sam.del_flag = 0 and sa.area_id in
            <foreach item="item" collection="areaIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>

</mapper>
package cn.dutp.shop.domain.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 微信支付对象
 *
as */
@Data
public class AlPayDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 由商家自定义，64个字符以内，仅支持字母、数字、下划线且需保证在商户端不重复
     */
    private String outTradeNo;
    /**
     * 订单总金额，单位为元，精确到小数点后两位
     */
    private String totalFee;
    /**
     * 订单标题，用于在支付宝支付页面显示,不可使用特殊字符，如 /，=，& 等。
     */
    private String subject;
    /**
     * 订单支付超时时间，格式为yyyy-MM-dd HH:mm:ss，超过该时间后订单自动关闭
     */
    private String timeExpire;
    /**
     * 订单描述，对订单的描述，可以是商品名称等信息
     */
    private String body;
    /**
     * 支付宝销售产品码，商家和支付宝签约的产品码
     */
    private String productCode;

}
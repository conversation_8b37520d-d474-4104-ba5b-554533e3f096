package cn.dutp.shop.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 订单下的购书码对象 dtb_book_order_code
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Data
@TableName("dtb_book_order_code")
public class DtbBookOrderCode extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderCodeId;

    /**
     *
     */
    @Excel(name = "")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderId;

    /**
     *
     */
    @Excel(name = "")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderItemId;

    /**
     * 二维码ID
     */
    @Excel(name = "二维码ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long codeId;

    /**
     * 2未绑定3已绑定【对应CODE表未兑换，已兑换】
     */
    @Excel(name = "2未绑定3已绑定【对应CODE表未兑换，已兑换】")
    private Integer state;

    /**
     * 1未退款2申请退款3已退款
     */
    @Excel(name = "1未退款2申请退款3已退款")
    private Integer refundState;

    /**
     * 教材ID
     */
    @Excel(name = "教材ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 采购学校
     */
    @Excel(name = "采购学校")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolId;

    /**
     * 导出次数
     */
    @Excel(name = "导出次数")
    private Long exportQuantity;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("orderCodeId", getOrderCodeId())
                .append("orderId", getOrderId())
                .append("orderItemId", getOrderItemId())
                .append("codeId", getCodeId())
                .append("state", getState())
                .append("refundState", getRefundState())
                .append("bookId", getBookId())
                .append("schoolId", getSchoolId())
                .append("exportQuantity", getExportQuantity())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

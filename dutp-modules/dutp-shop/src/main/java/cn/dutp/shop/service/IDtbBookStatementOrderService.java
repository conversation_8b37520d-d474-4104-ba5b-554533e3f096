package cn.dutp.shop.service;

import cn.dutp.domain.DtbBookOrder;
import cn.dutp.shop.domain.DtbBookStatementOrder;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 结算单的订单Service接口
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface IDtbBookStatementOrderService extends IService<DtbBookStatementOrder>
{
    /**
     * 查询结算单的订单
     *
     * @param statementOrderId 结算单的订单主键
     * @return 结算单的订单
     */
    public DtbBookStatementOrder selectDtbBookStatementOrderByStatementOrderId(Long statementOrderId);

    /**
     * 查询结算单的订单列表
     *
     * @param dtbBookStatementOrder 结算单的订单
     * @return 结算单的订单集合
     */
    public List<DtbBookStatementOrder> selectDtbBookStatementOrderList(DtbBookStatementOrder dtbBookStatementOrder);

    /**
     * 新增结算单的订单
     *
     * @param dtbBookStatementOrder 结算单的订单
     * @return 结果
     */
    public boolean insertDtbBookStatementOrder(DtbBookStatementOrder dtbBookStatementOrder);

    /**
     * 修改结算单的订单
     *
     * @param dtbBookStatementOrder 结算单的订单
     * @return 结果
     */
    public boolean updateDtbBookStatementOrder(DtbBookStatementOrder dtbBookStatementOrder);

    /**
     * 批量删除结算单的订单
     *
     * @param statementOrderIds 需要删除的结算单的订单主键集合
     * @return 结果
     */
    public boolean deleteDtbBookStatementOrderByStatementOrderIds(List<Long> statementOrderIds);

    public List<DtbBookStatementOrder> getStatementOrderByStatementId(Long statementId);

}

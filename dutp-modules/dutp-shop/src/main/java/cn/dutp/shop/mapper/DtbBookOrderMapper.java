package cn.dutp.shop.mapper;

import cn.dutp.domain.DtbBookOrder;
import cn.dutp.domain.DtbBookOrderItem;
import cn.dutp.domain.DtbBookRefundOrderItem;
import cn.dutp.domain.vo.BookOrderVo;
import cn.dutp.shop.domain.dto.BookOrderDto;
import cn.dutp.shop.domain.dto.OrderReviewDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
/**
 * DUTP-DTB_012订单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Repository
public interface DtbBookOrderMapper extends BaseMapper<DtbBookOrder>
{

    //书商书籍和对应折扣
    List<BookOrderVo> getOrderMerchantBookList(BookOrderDto dto);

    // 零售订单列表
    List<BookOrderVo> getRetailOrderList(BookOrderDto dto);

    BookOrderVo getRetailOrderDetail(BookOrderDto dto);

    BookOrderVo getEducationalDetail(BookOrderDto dto);

    List<DtbBookOrderItem> getItemByOrderId(Long orderId);

    List<DtbBookOrderItem> getChildItemByOrderId(List<Long> bookIds);

    List<DtbBookRefundOrderItem> getRefundByOrderId(Long orderId);

    // 学生教师端查询订单列表
    List<BookOrderVo>  getOrderInfo(BookOrderDto dto);

    //代采(学校采购)书籍和对应折扣
    List<BookOrderVo> getOrderSchoolBookList(BookOrderDto dto);

    // 批量采购订单列表
    List<BookOrderVo> getBatchOrderList(BookOrderDto dto);

    /**
     * 订单审核列表
     *
     * @param dto 查询信息
     * @return 列表信息
     */
    List<BookOrderVo> getOrderReview(BookOrderDto dto);

    /**
     * 根据订单id，统计所有子订单的数量，计算出订单总的采购数量
     *
     * @param orderId 订单id
     * @return 总的采购数量
     */
    Integer getOrderQuantityByOrderId(@Param("orderId") Long orderId);

    /**
     * 采购审核
     * 驳回：更新订单表的订单状态为采购驳回：create_refused
     * 通过：更新订单表的订单状态为结算中：settlement
     * @param orderReviewDto 驳回意见
     * @return 状态
     */
    boolean purchaseApprovalReject(OrderReviewDto orderReviewDto);

    // 样书订单列表
    List<BookOrderVo> getSampleOrderList(BookOrderDto dto);

    List<BookOrderVo> getOrderSampleBookList(BookOrderDto dto);

    /**
     * 样书审核列表
     *
     * @param dto 查询信息
     * @return 列表信息
     */
    List<BookOrderVo> getSampleBookOrderReview(BookOrderDto dto);

    /**
     * 更新订单表的订单状态
     *
     * @param orderReviewDto 更新信息
     * @return 结果
     */
    boolean updateOrderBySampleBook(OrderReviewDto orderReviewDto);
    /**
     * 更新子订单的订单状态
     *
     * @param codeId 购书码Id
     * @return 操作结果
     */
    boolean updateBookOrderCodeId(@Param("updateBy") String updateBy, @Param("codeId") Long codeId);
}




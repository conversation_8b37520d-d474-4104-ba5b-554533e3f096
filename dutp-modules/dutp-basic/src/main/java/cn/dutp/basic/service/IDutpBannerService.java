package cn.dutp.basic.service;

import java.util.List;
import cn.dutp.basic.domain.DutpBanner;
import cn.dutp.basic.domain.vo.DutpBannerVo;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 轮播广告Service接口
 *
 * <AUTHOR>
 * @date 2024-10-22
 */
public interface IDutpBannerService extends IService<DutpBanner>
{
    /**
     * 查询轮播广告
     *
     * @param bannerId 轮播广告主键
     * @return 轮播广告
     */
    public DutpBanner selectDutpBannerByBannerId(Long bannerId);

    /**
     * 查询轮播广告列表
     *
     * @param dutpBanner 轮播广告
     * @return 轮播广告集合
     */
    public List<DutpBanner> selectDutpBannerList(DutpBanner dutpBanner);

    /**
     * 新增轮播广告
     *
     * @param dutpBanner 轮播广告
     * @return 结果
     */
    public boolean insertDutpBanner(DutpBanner dutpBanner);

    /**
     * 修改轮播广告
     *
     * @param dutpBanner 轮播广告
     * @return 结果
     */
    public boolean updateDutpBanner(DutpBanner dutpBanner);

    /**
     * 批量删除轮播广告
     *
     * @param bannerIds 需要删除的轮播广告主键集合
     * @return 结果
     */
    public boolean deleteDutpBannerByBannerIds(List<Long> bannerIds);

    /**
     * 发布轮播广告信息
     *
     * @param bannerId 轮播广告主键
     * @return 结果
     */
    public boolean pushDutpBannerByBannerId(Long bannerId);

    /**
     * 撤销已经发布的轮播广告信息
     * @param bannerId
     */
    public boolean cancelDutpBannerByBannerId(Long bannerId);

    List<DutpBannerVo> getHomeBannerList(DutpBanner banner);
}

package cn.dutp.basic.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.basic.domain.DutpBanner;
import cn.dutp.basic.service.IDutpBannerService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 轮播广告Controller
 *
 * <AUTHOR>
 * @date 2024-10-22
 */
@RestController
@RequestMapping("/banner")
public class DutpBannerController extends BaseController
{
    @Autowired
    private IDutpBannerService dutpBannerService;

/**
 * 查询轮播广告列表
 */
@RequiresPermissions("basic:banner:list")
@GetMapping("/list")
    public TableDataInfo list(DutpBanner dutpBanner)
    {
        startPage();
        List<DutpBanner> list = dutpBannerService.selectDutpBannerList(dutpBanner);
        return getDataTable(list);
    }

    /**
     * 导出轮播广告列表
     */
    @RequiresPermissions("basic:banner:export")
    @Log(title = "导出轮播广告", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DutpBanner dutpBanner)
    {
        List<DutpBanner> list = dutpBannerService.selectDutpBannerList(dutpBanner);
        ExcelUtil<DutpBanner> util = new ExcelUtil<>(DutpBanner.class);
        util.exportExcel(response, list, "轮播广告数据");
    }

    /**
     * 获取轮播广告详细信息
     */
    @RequiresPermissions("basic:banner:query")
    @GetMapping(value = "/{bannerId}")
    public AjaxResult getInfo(@PathVariable("bannerId") Long bannerId)
    {
        return success(dutpBannerService.selectDutpBannerByBannerId(bannerId));
    }

    /**
     * 新增轮播广告
     */
    @RequiresPermissions("basic:banner:add")
    @Log(title = "新增轮播广告", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DutpBanner dutpBanner)
    {
        return toAjax(dutpBannerService.insertDutpBanner(dutpBanner));
    }

    /**
     * 修改轮播广告
     */
    @RequiresPermissions("basic:banner:edit")
    @Log(title = "修改轮播广告", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DutpBanner dutpBanner)
    {
        return toAjax(dutpBannerService.updateDutpBanner(dutpBanner));
    }

    /**
     * 删除轮播广告
     */
    @RequiresPermissions("basic:banner:remove")
    @Log(title = "删除轮播广告", businessType = BusinessType.DELETE)
    @DeleteMapping("/{bannerIds}")
    public AjaxResult remove(@PathVariable Long[] bannerIds)
    {
        return toAjax(dutpBannerService.deleteDutpBannerByBannerIds(Arrays.asList(bannerIds)));
    }

    /**
     * 发布轮播广告详细信息
     */
    @RequiresPermissions("basic:banner:push")
    @Log(title = "发布轮播广告", businessType = BusinessType.DELETE)
    @GetMapping(value = "/push/{bannerId}")
    public AjaxResult push(@PathVariable("bannerId") Long bannerId)
    {
        return toAjax(dutpBannerService.pushDutpBannerByBannerId(bannerId));
    }

    /**
     * 撤销轮播广告详细信息
     */
    @RequiresPermissions("basic:banner:push")
    @Log(title = "撤销轮播广告", businessType = BusinessType.DELETE)
    @GetMapping(value = "/cancel/{bannerId}")
    public AjaxResult cancel(@PathVariable("bannerId") Long bannerId)
    {
        return toAjax(dutpBannerService.cancelDutpBannerByBannerId(bannerId));
    }
}

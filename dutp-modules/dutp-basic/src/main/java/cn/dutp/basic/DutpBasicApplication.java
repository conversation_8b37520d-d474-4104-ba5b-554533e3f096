package cn.dutp.basic;

import cn.dutp.common.security.annotation.EnableCustomConfig;
import cn.dutp.common.security.annotation.EnableDutpFeignClients;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

@EnableCustomConfig
@EnableDutpFeignClients
@SpringBootApplication
@EnableDiscoveryClient
public class DutpBasicApplication {
    public static void main(String[] args) {
        SpringApplication.run(DutpBasicApplication.class, args);
        System.out.println("基础信息模块启动成功");
    }
}

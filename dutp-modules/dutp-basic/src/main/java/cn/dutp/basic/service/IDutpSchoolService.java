package cn.dutp.basic.service;


import cn.dutp.basic.domain.DutpSchool;
import cn.dutp.basic.domain.dto.DtbSchoolDTO;
import cn.dutp.basic.domain.dto.DutpAcademyDto;
import cn.dutp.basic.domain.vo.DutpSchoolVo;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 学校管理Service接口
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
public interface IDutpSchoolService extends IService<DutpSchool>
{
    /**
     * 查询学校管理
     *
     * @param schoolId 学校管理主键
     * @return 学校管理
     */
    public DutpSchool selectDutpSchoolBySchoolId(Long schoolId);

    /**
     * 查询学校管理列表
     *
     * @param dutpSchool 学校管理
     * @return 学校管理集合
     */
    public List<Tree<String>> selectDutpSchoolList(DutpSchool dutpSchool);

    /**
     * 学生教师端查询学校管理列表
     *
     * @param dutpSchool 学校管理
     * @return 学校管理集合
     */
    public List<Tree<String>> selectDutpSchoolListEducation(DutpSchool dutpSchool);

    /**
     * 查询学校管理列表
     *
     * @param dutpSchool 学校管理
     * @return 学校管理集合
     */
    public List<DutpSchool> selectDutpSchoolListWithOutTree(DutpSchool dutpSchool);

    /**
     * 查询学校管理列表（筛选条件）
     *
     * @param dutpSchool 学校管理
     * @return 学校管理集合，或合作院校
     */
    public List<DutpSchool> listSchoolByQuery(DutpSchool dutpSchool);


    /**
     * 新增学校管理
     *
     * @param dutpSchool 学校管理
     * @return 结果
     */
    public boolean insertDutpSchool(DutpSchool dutpSchool);

    /**
     * 修改学校管理
     *
     * @param dutpSchool 学校管理
     * @return 结果
     */
    public boolean updateDutpSchool(DutpSchool dutpSchool);

    /**
     * 批量删除学校管理
     *
     * @param schoolIds 需要删除的学校管理主键集合
     * @return 结果
     */
    public AjaxResult deleteDutpSchoolBySchoolIds(List<Long> schoolIds);

    String insertSchoolList(List<DtbSchoolDTO> list);

    List<Tree<String>> listNoPage(DutpSchool dutpSchool);

    List<DutpSchool> selectAcademyList(DutpSchool dutpSchoold);

    public boolean updateAcademy(DutpSchool dutpSchool);

    public boolean addAcademy(DutpSchool dutpSchool);

    public boolean addSubject(DutpSchool dutpSchool);

    public boolean updateSubject(DutpSchool dutpSchool);

    List<DutpSchool> selectSubjectList(DutpSchool dutpSchool);


    List<Tree<String>> getAllWithOutLast(DutpSchool dutpSchool);

    List<DutpSchoolVo> getHomePartnerList(Integer limit);

    List<DutpSchool> listNoPageOfBook(DutpSchool dutpSchool);

    String addAcademyList(List<DutpSchool> list);

    List<Tree<String>> selectByParentId(DutpSchool dutpSchool);

    List<DutpSchool> selectSubject(DutpSchool dutpSchool);

    List<DutpSchool> selectAcademyListNopage(DutpSchool dutpSchool);

    List<DutpAcademyDto> selectAcademyListExport(DutpSchool dutpSchool);

    List<DutpSchool> selectAcademyListNopageEdu(DutpSchool dutpSchool);
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.basic.mapper.DutpSchoolMapper">

    <resultMap type="cn.dutp.basic.domain.DutpSchool" id="DutpSchoolResult">
        <result property="schoolId"    column="school_id"    />
        <result property="schoolName"    column="school_name"    />
        <result property="parentId"    column="parent_id"    />
        <result property="schoolCode"    column="school_code"    />
        <result property="logoUrl"    column="logo_url"    />
        <result property="isPartner"    column="is_partner"    />
        <result property="dataType"    column="data_type"    />
        <result property="degreeId"    column="degree_id"    />
        <result property="sort"    column="sort"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDutpSchoolVo">
        select school_id, school_name, parent_id, school_code, logo_url, is_partner, data_type, degree_id, sort, del_flag, create_by, create_time, update_by, update_time from dutp_school
    </sql>

    <select id="selectDutpSchoolList" parameterType="cn.dutp.basic.domain.DutpSchool" resultType="cn.dutp.basic.domain.DutpSchool">
        WITH RECURSIVE SchoolHierarchy AS (
        SELECT
        ds.school_id,
        ds.school_name,
        ds.school_code,
        ds.parent_id,
        ds.data_type,
        ds.logo_url,
        ds.is_partner,
        ds.degree_id,
        ds.sort,
        ds.del_flag,
        ds.create_time,
        ds.default_discount,
        ds.invoice_type,
        ds.title_type,
        ds.title_name,
        ds.tax_no,
        ds.regist_address,
        ds.regist_tel,
        ds.account_bank,
        ds.account_no,
        ds.apply_type,
        ds.user_email,
        du.subject_name AS degreeName,
        (SELECT COUNT(0) FROM dtb_book WHERE school_id = ds.school_id) AS bookCount
        FROM
        dutp_school ds
        LEFT JOIN
        dutp_subject du ON du.subject_id = ds.degree_id AND du.subject_level = 1
        WHERE
        ds.del_flag = 0
        <if test="schoolName != null  and schoolName != ''"> AND ds.school_name LIKE CONCAT('%', #{schoolName}, '%')</if>
        <if test="schoolCode != null  and schoolCode != ''"> AND ds.school_code = #{schoolCode}</if>
        <if test="degreeId != null"> AND ds.degree_id = #{degreeId}</if>
        <if test="isPartner != null"> AND ds.is_partner = #{isPartner}</if>

        UNION ALL

        SELECT
        ds.school_id,
        ds.school_name,
        ds.school_code,
        ds.parent_id,
        ds.data_type,
        ds.logo_url,
        ds.is_partner,
        ds.degree_id,
        ds.sort,
        ds.del_flag,
        ds.create_time,
        ds.default_discount,
        ds.invoice_type,
        ds.title_type,
        ds.title_name,
        ds.tax_no,
        ds.regist_address,
        ds.regist_tel,
        ds.account_bank,
        ds.account_no,
        ds.apply_type,
        ds.user_email,
        null AS degreeName,
        (SELECT COUNT(0) FROM dtb_book WHERE school_id = ds.school_id) AS bookCount
        FROM
        dutp_school ds
        INNER JOIN
        SchoolHierarchy sh ON ds.school_id = sh.parent_id
        )
        SELECT
        school_id,
        MAX(school_name) AS school_name,
        MAX(school_code) AS school_code,
        MAX(parent_id) AS parent_id,
        MAX(data_type) AS data_type,
        MAX(logo_url) AS logo_url,
        MAX(is_partner) AS is_partner,
        MAX(degree_id) AS degree_id,
        MAX(sort) AS sort,
        MAX(del_flag) AS del_flag,
        MAX(create_time) AS create_time,
        MAX(default_discount) AS default_discount,
        MAX(invoice_type) AS invoice_type,
        MAX(title_type) AS title_type,
        MAX(title_name) AS title_name,
        MAX(tax_no) AS tax_no,
        MAX(regist_address) AS regist_address,
        MAX(regist_tel) AS regist_tel,
        MAX(account_bank) AS account_bank,
        MAX(account_no) AS account_no,
        MAX(apply_type) AS apply_type,
        MAX(user_email) AS user_email,
        MAX(degreeName) AS degreeName,
        SUM(bookCount) AS bookCount
        FROM
        SchoolHierarchy
        GROUP BY
        school_id;
    </select>

    <select id="listNoPage"  parameterType="cn.dutp.basic.domain.DutpSchool" resultType="cn.dutp.basic.domain.DutpSchool">
        select
        ds.school_id,
        ds.school_name,
        ds.school_code,
        ds.parent_id,
        ds.data_type,
        ds.logo_url,
        ds.is_partner,
        ds.degree_id,
        ds.sort,
        ds.del_flag,
        ds.create_time,
        du.subject_name as degreeName,
        (select count(0) from dtb_book where school_id = ds.school_id) as bookCount
        from dutp_school ds
        left join dutp_subject du on du.subject_id = ds.degree_id and du.parent_id is null
        <where>
            ds.del_flag = 0 and ds.data_type != 2
            <if test="schoolId != null  and schoolId != ''"> and ds.school_id != #{schoolId} and ds.parent_id != #{schoolId}</if>
            <if test="schoolName != null  and schoolName != ''"> and school_name like concat('%', #{schoolName}, '%')</if>
            <if test="schoolCode != null  and schoolCode != ''"> and school_code = #{schoolCode}</if>
        </where>
    </select>
    
    <select id="selectDutpSchoolBySchoolId" parameterType="Long" resultMap="DutpSchoolResult">
        <include refid="selectDutpSchoolVo"/>
        where school_id = #{schoolId}
    </select>
    <select id="selectAcademyBySchool" resultType="cn.dutp.basic.domain.DutpSchool">
        select * from dutp_school
        <where>
            <if test="schoolName != null and schoolName != ''"> and school_name like concat('%', #{schoolName}, '%')</if>
            and data_type = 1 and parent_id = #{parentId}  and del_flag = 0
        </where>
    </select>
    <select id="selectSubjectList" resultType="cn.dutp.basic.domain.DutpSchool">
        SELECT
        s1.school_id,
        s1.school_name,
        s1.parent_id,
        s1.data_type,
        s1.school_code,
        s1.description,
        s1.create_by,
        s2.school_name as  subject_name,
        s1.create_time
        FROM
        dutp_school s1
        INNER JOIN dutp_school s2 ON s1.parent_id = s2.school_id
        AND s2.data_type = 1
        INNER JOIN sys_user u ON u.school_id = s2.parent_id
        <where>
            <if test="parentId != null"> and s1.parent_id = #{parentId}</if>
            <if test="schoolName != null  and schoolName != ''"> and s1.school_name like concat('%', #{schoolName}, '%')</if>
            and s1.data_type = 2
            AND s2.parent_id = #{schoolId}
            and s1.del_flag = '0'
        </where>
        GROUP BY
        s1.school_id
    </select>
    <select id="listNoPageOfBook" resultType="cn.dutp.basic.domain.DutpSchool">
        SELECT
            s.school_id,
            s.school_name
        FROM
            dutp_school s
                INNER JOIN dtb_book_school bs ON s.school_id = bs.school_id
        WHERE
            s.data_type = 0
          and bs.book_id = #{bookId}
    </select>
    <select id="selectAcademyBySchoolNoPage" resultType="cn.dutp.basic.domain.DutpSchool">
        select * from dutp_school
        where
             data_type = 1 and parent_id = #{schoolId}  and del_flag = 0
    </select>
    <select id="selectAcademyIdBySchoolName" resultType="java.lang.Long">
        SELECT school_id from Dutp_school where school_name = #{schoolName} and parent_id = #{schoolId} and data_type = 1 and del_flag = 0
    </select>

    <insert id="insertDutpSchool" parameterType="DutpSchool" useGeneratedKeys="true" keyProperty="schoolId">
        insert into dutp_school
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="schoolName != null">school_name,</if>
            <if test="schoolCode != null">school_code,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="schoolName != null">#{schoolName},</if>
            <if test="schoolCode != null">#{schoolCode},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDutpSchool" parameterType="DutpSchool">
        update dutp_school
        <trim prefix="SET" suffixOverrides=",">
            <if test="schoolName != null">school_name = #{schoolName},</if>
            <if test="schoolCode != null">school_code = #{schoolCode},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where school_id = #{schoolId}
    </update>

    <delete id="deleteDutpSchoolBySchoolId" parameterType="Long">
        delete from dutp_school where school_id = #{schoolId}
    </delete>

    <delete id="deleteDutpSchoolBySchoolIds" parameterType="String">
        delete from dutp_school where school_id in 
        <foreach item="schoolId" collection="array" open="(" separator="," close=")">
            #{schoolId}
        </foreach>
    </delete>
</mapper>
package cn.dutp.basic.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.basic.domain.DtbBookTemplate;
import cn.dutp.basic.service.IDtbBookTemplateService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * DUTP-DTB-029教材模板Controller
 *
 * <AUTHOR>
 * @date 2024-10-30
 */
@RestController
@RequestMapping("/template")
public class DtbBookTemplateController extends BaseController
{
    @Autowired
    private IDtbBookTemplateService dtbBookTemplateService;

/**
 * 查询DUTP-DTB-029教材模板列表
 */
@RequiresPermissions("system:template:list")
@GetMapping("/list")
    public TableDataInfo list(DtbBookTemplate dtbBookTemplate)
    {
        startPage();
        List<DtbBookTemplate> list = dtbBookTemplateService.selectDtbBookTemplateList(dtbBookTemplate);
        return getDataTable(list);
    }

    /**
     * 导出DUTP-DTB-029教材模板列表
     */
    @RequiresPermissions("system:template:export")
    @Log(title = "DUTP-DTB-029教材模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbBookTemplate dtbBookTemplate)
    {
        List<DtbBookTemplate> list = dtbBookTemplateService.selectDtbBookTemplateList(dtbBookTemplate);
        ExcelUtil<DtbBookTemplate> util = new ExcelUtil<DtbBookTemplate>(DtbBookTemplate.class);
        util.exportExcel(response, list, "DUTP-DTB-029教材模板数据");
    }

    /**
     * 获取DUTP-DTB-029教材模板详细信息
     */
    @RequiresPermissions("system:template:query")
    @GetMapping(value = "/{templateId}")
    public AjaxResult getInfo(@PathVariable("templateId") Long templateId)
    {
        return success(dtbBookTemplateService.selectDtbBookTemplateByTemplateId(templateId));
    }

    /**
     * 新增DUTP-DTB-029教材模板
     */
    @RequiresPermissions("system:template:add")
    @Log(title = "DUTP-DTB-029教材模板", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbBookTemplate dtbBookTemplate)
    {
        return toAjax(dtbBookTemplateService.insertDtbBookTemplate(dtbBookTemplate));
    }

    /**
     * 修改DUTP-DTB-029教材模板
     */
    @RequiresPermissions("system:template:edit")
    @Log(title = "DUTP-DTB-029教材模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookTemplate dtbBookTemplate)
    {
        return toAjax(dtbBookTemplateService.updateDtbBookTemplate(dtbBookTemplate));
    }

    /**
     * 删除DUTP-DTB-029教材模板
     */
    @RequiresPermissions("system:template:remove")
    @Log(title = "DUTP-DTB-029教材模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/{templateIds}")
    public AjaxResult remove(@PathVariable Long[] templateIds)
    {
        return toAjax(dtbBookTemplateService.deleteDtbBookTemplateByTemplateIds(Arrays.asList(templateIds)));
    }
}

package cn.dutp.basic.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * 教育类型对象 dutp_degree
 *
 * <AUTHOR>
 * &#064;date  2024-10-28
 */
@Data
@TableName("dutp_degree")
public class DutpDegree extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** 教育类型id */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long degreeId;

    /** 教育类型名称 */
        @Excel(name = "教育类型名称")
    private String degreeName;

    /** 教育类型排序 */
        @Excel(name = "教育类型排序")
    private Integer sort;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("degreeId", getDegreeId())
            .append("degreeName", getDegreeName())
            .append("sort", getSort())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
        .toString();
        }
        }

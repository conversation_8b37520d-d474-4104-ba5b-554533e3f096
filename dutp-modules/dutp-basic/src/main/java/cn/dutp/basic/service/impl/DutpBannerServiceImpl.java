package cn.dutp.basic.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import cn.dutp.basic.domain.vo.DutpBannerVo;
import cn.dutp.basic.enums.BannerStatusEnum;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import cn.dutp.basic.mapper.DutpBannerMapper;
import cn.dutp.basic.domain.DutpBanner;
import cn.dutp.basic.service.IDutpBannerService;

/**
 * 轮播广告Service业务层处理
 *
 * <AUTHOR>
 * &#064;date  2024-10-22
 */
@Service
public class DutpBannerServiceImpl extends ServiceImpl<DutpBannerMapper, DutpBanner> implements IDutpBannerService {

    static final int STATUS_DEFAULT = BannerStatusEnum.UNSTART.getCode();

    /**
     * 查询轮播广告
     *
     * @param bannerId 轮播广告主键
     * @return 轮播广告
     */
    @Override
    public DutpBanner selectDutpBannerByBannerId(Long bannerId) {
        return this.getById(bannerId);
    }

    /**
     * 查询轮播广告列表
     *
     * @param dutpBanner 轮播广告
     * @return 轮播广告，根据原型按照创建时间倒序
     */
    @Override
    public List<DutpBanner> selectDutpBannerList(DutpBanner dutpBanner) {


        LambdaQueryWrapper<DutpBanner> lambdaQueryWrapper = new LambdaQueryWrapper<>();

        //创建时间倒序
        lambdaQueryWrapper.orderByAsc(DutpBanner::getSort);

        if (ObjectUtil.isNotEmpty(dutpBanner.getBannerName())) {
            lambdaQueryWrapper.like(DutpBanner::getBannerName
                    , dutpBanner.getBannerName());
        }
        if (ObjectUtil.isNotEmpty(dutpBanner.getDevice())) {
            lambdaQueryWrapper.like(DutpBanner::getDevice
                    , dutpBanner.getDevice());
        }
        if (ObjectUtil.isNotEmpty(dutpBanner.getBannerPosition())) {
            lambdaQueryWrapper.eq(DutpBanner::getBannerPosition
                    , dutpBanner.getBannerPosition());
        }
        if (ObjectUtil.isNotEmpty(dutpBanner.getImageUrl())) {
            lambdaQueryWrapper.eq(DutpBanner::getImageUrl
                    , dutpBanner.getImageUrl());
        }
        if (ObjectUtil.isNotEmpty(dutpBanner.getUrlType())) {
            lambdaQueryWrapper.eq(DutpBanner::getUrlType
                    , dutpBanner.getUrlType());
        }
        if (ObjectUtil.isNotEmpty(dutpBanner.getJumpUrl())) {
            lambdaQueryWrapper.eq(DutpBanner::getJumpUrl
                    , dutpBanner.getJumpUrl());
        }
        if (ObjectUtil.isNotEmpty(dutpBanner.getSort())) {
            lambdaQueryWrapper.eq(DutpBanner::getSort
                    , dutpBanner.getSort());
        }
        if (ObjectUtil.isNotEmpty(dutpBanner.getStartDate())) {
            lambdaQueryWrapper.eq(DutpBanner::getStartDate
                    , dutpBanner.getStartDate());
        }
        if (ObjectUtil.isNotEmpty(dutpBanner.getEndDate())) {
            lambdaQueryWrapper.eq(DutpBanner::getEndDate
                    , dutpBanner.getEndDate());
        }
        if (ObjectUtil.isNotEmpty(dutpBanner.getStatus())) {
            lambdaQueryWrapper.eq(DutpBanner::getStatus
                    , dutpBanner.getStatus());
        }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增轮播广告
     *
     * @param dutpBanner 轮播广告
     * @return 结果
     */
    @Override
    public boolean insertDutpBanner(DutpBanner dutpBanner) {
        dutpBanner.setCreateBy(SecurityUtils.getUsername());
        dutpBanner.setCreateTime(new Date());
        dutpBanner.setStatus(STATUS_DEFAULT);

        // 校验时间范围与现在时间的关系，决定状态
        handleTimeStatus(dutpBanner);

        return this.save(dutpBanner);
    }

    /**
     * 校验时间范围与现在时间的关系，决定状态
     * 如果开始时间在现在时间之前，则状态为已发布，否则为未开始
     * 如果结束时间在现在时间之前，则状态为已下线，否则为已发布
     * 1未开始2已发布3已下线4已撤销
     *
     * @param dutpBanner DutpBanner
     */
    private void handleTimeStatus(DutpBanner dutpBanner) {


        if (dutpBanner.getStartDate() == null || dutpBanner.getEndDate() == null) {
            return;
        }

        //如果状态是4，则直接返回，如果是空代表不需要修改，也返回
        if (Objects.equals(dutpBanner.getStatus(), BannerStatusEnum.REVOKE.getCode()) || null == dutpBanner.getStatus()) {
            return;
        }

        Date now = new Date();

        //由于结束时间到那天的23:59:59，所以要加一天
        Date endDateForCompare = new Date(dutpBanner.getEndDate().getTime() + 24 * 60 * 60 * 1000);

        if (now.compareTo(dutpBanner.getStartDate()) < 0) {
            dutpBanner.setStatus(BannerStatusEnum.UNSTART.getCode());
        } else if (now.compareTo(endDateForCompare) > 0) {
            dutpBanner.setStatus(BannerStatusEnum.OFFLINE.getCode());
        } else {
            dutpBanner.setStatus(BannerStatusEnum.PUBLISHED.getCode());
        }

    }

    /**
     * 修改轮播广告
     *
     * @param dutpBanner 轮播广告
     * @return 结果
     */
    @Override
    public boolean updateDutpBanner(DutpBanner dutpBanner) {
        dutpBanner.setUpdateBy(SecurityUtils.getUsername());
        dutpBanner.setUpdateTime(new Date());

        // 校验时间范围与现在时间的关系，决定状态
        handleTimeStatus(dutpBanner);

        return this.updateById(dutpBanner);
    }

    /**
     * 批量删除轮播广告
     *
     * @param bannerIds 需要删除的轮播广告主键
     * @return 结果
     */
    @Override
    public boolean deleteDutpBannerByBannerIds(List<Long> bannerIds) {
        return this.removeByIds(bannerIds);
    }

    @Override
    public boolean pushDutpBannerByBannerId(Long bannerId) {
        //调用获取和更新，将状态改为已发布,出处查询的意义是拉取设定的开始和结束时间，用于更新的时候确认状态是未上线还是已发布等
        DutpBanner dutpBanner = this.selectDutpBannerByBannerId(bannerId);
        dutpBanner.setStatus(STATUS_DEFAULT);
        return this.updateDutpBanner(dutpBanner);
    }

    @Override
    public boolean cancelDutpBannerByBannerId(Long bannerId) {
        //调用更新，将状态改为已撤销
        DutpBanner dutpBanner = new DutpBanner();
        dutpBanner.setBannerId(bannerId);
        dutpBanner.setStatus(BannerStatusEnum.REVOKE.getCode());
        return this.updateDutpBanner(dutpBanner);
    }

    @Override
    public List<DutpBannerVo> getHomeBannerList(DutpBanner banner) {
        LambdaQueryWrapper<DutpBanner> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        //创建时间倒序
        lambdaQueryWrapper.orderByAsc(DutpBanner::getSort);

        if (ObjectUtil.isNotEmpty(banner.getDevice())) {
            lambdaQueryWrapper.like(DutpBanner::getDevice
                    , banner.getDevice());
        }
        if (ObjectUtil.isNotEmpty(banner.getBannerPosition())) {
            lambdaQueryWrapper.like(DutpBanner::getBannerPosition
                    , banner.getBannerPosition());
        }
        lambdaQueryWrapper.eq(DutpBanner::getStatus,2);
        List<DutpBanner> dbList = this.list(lambdaQueryWrapper);
        List<DutpBannerVo> bannerVos = new ArrayList<>();
        dbList.forEach(item -> {
            DutpBannerVo dutpBannerVo = new DutpBannerVo();
            BeanUtil.copyProperties(item,dutpBannerVo);
            bannerVos.add(dutpBannerVo);
        });
        return bannerVos;
    }

}

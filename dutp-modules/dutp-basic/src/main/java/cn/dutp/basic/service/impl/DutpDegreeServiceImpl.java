package cn.dutp.basic.service.impl;

import java.util.List;

import cn.dutp.basic.domain.DutpDegree;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.utils.StringUtils;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.basic.mapper.DutpDegreeMapper;
import cn.dutp.basic.service.IDutpDegreeService;

/**
 * 教育类型Service业务层处理
 *
 * <AUTHOR>
 * &#064;date  2024-10-28
 */
@Service
public class DutpDegreeServiceImpl extends ServiceImpl<DutpDegreeMapper, DutpDegree> implements IDutpDegreeService
{
    @Autowired
    private DutpDegreeMapper dutpDegreeMapper;

    /**
     * 查询教育类型
     *
     * @param degreeId 教育类型主键
     * @return 教育类型
     */
    @Override
    public DutpDegree selectDutpDegreeByDegreeId(Long degreeId)
    {
        return this.getById(degreeId);
    }

    /**
     * 查询教育类型列表
     *
     * @param dutpDegree 教育类型
     * @return 教育类型
     */
    @Override
    public List<DutpDegree> selectDutpDegreeList(DutpDegree dutpDegree)
    {
        LambdaQueryWrapper<DutpDegree> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dutpDegree.getDegreeName())) {
                lambdaQueryWrapper.like(DutpDegree::getDegreeName
                ,dutpDegree.getDegreeName());
            }
                if(ObjectUtil.isNotEmpty(dutpDegree.getSort())) {
                lambdaQueryWrapper.eq(DutpDegree::getSort
                ,dutpDegree.getSort());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增教育类型
     *
     * @param dutpDegree 教育类型
     * @return 结果
     */
    @Override
    public boolean insertDutpDegree(DutpDegree dutpDegree)
    {
        return this.save(dutpDegree);
    }

    /**
     * 修改教育类型
     *
     * @param dutpDegree 教育类型
     * @return 结果
     */
    @Override
    public boolean updateDutpDegree(DutpDegree dutpDegree)
    {
        return this.updateById(dutpDegree);
    }

    /**
     * 批量删除教育类型
     *
     * @param degreeIds 需要删除的教育类型主键
     * @return 结果
     */
    @Override
    public boolean deleteDutpDegreeByDegreeIds(List<Long> degreeIds)
    {
        return this.removeByIds(degreeIds);
    }


    @Override
    public String importUser(List<DutpDegree> subjects, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(subjects) || subjects.isEmpty()) {
            throw new ServiceException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (DutpDegree degree : subjects) {
            try {
                // 验证是否存在
                DutpDegree querySubject = new DutpDegree();
                querySubject.setDegreeName(degree.getDegreeName());
                querySubject.setDelFlag("0");

                QueryWrapper<DutpDegree> queryWrapper = new QueryWrapper<>(querySubject);
                DutpDegree u = dutpDegreeMapper.selectOne(queryWrapper);
                if (StringUtils.isNull(u)) {
                    insertDutpDegree(degree);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、学科 ").append(degree.getDegreeName()).append(" 导入成功");
                } else if (isUpdateSupport) {
                    u.setDegreeName(degree.getDegreeName());
                    u.setSort(degree.getSort());
                    updateDutpDegree(u);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、学科 ").append(degree.getDegreeName()).append(" 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、学科 ").append(degree.getDegreeName()).append(" 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、学科 " + degree.getDegreeName() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();

    }

    @Override
    public AjaxResult listForSelect(DutpDegree dutpDegree) {
        QueryWrapper<DutpDegree> queryWrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(dutpDegree.getDegreeName())){
            queryWrapper.lambda().like(DutpDegree::getDegreeName, dutpDegree.getDegreeName());
        }
        if(ObjectUtil.isNotEmpty(dutpDegree.getDegreeId())){
            queryWrapper.lambda().eq(DutpDegree::getDegreeId, dutpDegree.getDegreeId());
        }
        return AjaxResult.success(dutpDegreeMapper.selectList(queryWrapper));
    }
}

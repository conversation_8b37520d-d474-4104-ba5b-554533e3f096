package cn.dutp.basic.controller;

import cn.dutp.basic.domain.DutpSpeciality;
import cn.dutp.basic.service.IDutpSpecialityService;
import cn.dutp.common.core.web.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.core.web.controller.BaseController;

import java.util.List;

/**
 * DUTP-BASE-007专业Controller
 *
 * <AUTHOR>
 * @date 2024-10-30
 */
@RestController
@RequestMapping("/speciality")
public class DutpSpecialityController extends BaseController
{
    @Autowired
    private IDutpSpecialityService dutpSpecialityService;

    /**
     * 查询专业列表 无分页，用于下拉列表
     */
    @GetMapping("/listForSelect")
    public AjaxResult listForSelect(DutpSpeciality dutpSpeciality)
    {
        return dutpSpecialityService.listForSelect(dutpSpeciality);
    }
}

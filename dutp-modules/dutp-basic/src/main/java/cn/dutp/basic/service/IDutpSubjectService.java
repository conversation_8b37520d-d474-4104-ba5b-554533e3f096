package cn.dutp.basic.service;

import java.util.List;
import java.util.Map;

import cn.dutp.basic.domain.DutpSubjectExcel;
import cn.dutp.basic.domain.vo.DutpPublishingHouseVo;
import cn.dutp.domain.DutpSubject;
import cn.dutp.domain.DutpSubjectVo;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 学科信息Service接口
 *
 * <AUTHOR>
 * &#064;date  2024-10-28
 */
public interface IDutpSubjectService extends IService<DutpSubject>
{
    /**
     * 查询学科信息
     *
     * @param subjectId 学科信息主键
     * @return 学科信息
     */
    public DutpSubject selectDutpSubjectBySubjectId(Long subjectId);

    /**
     * 查询学科信息列表
     *
     * @param dutpSubject 学科信息
     * @return 学科信息集合
     */
    public List<DutpSubject> selectDutpSubjectList(DutpSubject dutpSubject);

    /**
     * 新增学科信息
     *
     * @param dutpSubject 学科信息
     * @return 结果
     */
    public boolean insertDutpSubject(DutpSubject dutpSubject);

    /**
     * 修改学科信息
     *
     * @param dutpSubject 学科信息
     * @return 结果
     */
    public boolean updateDutpSubject(DutpSubject dutpSubject);

    /**
     * 批量删除学科信息
     *
     * @param subjectIds 需要删除的学科信息主键集合
     * @return 结果
     */
    public boolean deleteDutpSubjectBySubjectIds(List<Long> subjectIds);

    /**
     * 导入用户数据
     *
     * @param subjects 用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importUser(List<DutpSubjectExcel> subjects, Boolean isUpdateSupport, String operName);

    List<DutpSubject> listForSelect(DutpSubject dutpSubject);

    List<DutpSubjectVo> selectCmsFirstSubjects();

    List<DutpSubject> listNotPage();

    /**
     * 学生教师端查询学科信息列表
     *
     * @param dutpSubject 学科信息
     * @return 学科信息集合
     */
     Map<Integer, List<DutpSubject>> listEducation(DutpSubject dutpSubject);

    List<DutpSubject> allSubjects();
}

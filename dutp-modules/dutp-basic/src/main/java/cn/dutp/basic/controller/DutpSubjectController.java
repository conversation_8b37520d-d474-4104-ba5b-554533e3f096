package cn.dutp.basic.controller;

import cn.dutp.basic.domain.DutpSubjectExcel;
import cn.dutp.basic.service.IDutpSubjectService;
import cn.dutp.common.core.domain.R;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DutpSubject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 学科信息Controller
 *
 * <AUTHOR>
 * &#064;date  2024-10-28
 */
@RestController
@RequestMapping("/subject")
public class DutpSubjectController extends BaseController {
    @Autowired
    private IDutpSubjectService dutpSubjectService;

    /**
     * 查询学科信息列表
     */
    @RequiresPermissions("basic:subject:list")
    @GetMapping("/list")
    public TableDataInfo list(DutpSubject dutpSubject) {
        List<DutpSubject> list = dutpSubjectService.selectDutpSubjectList(dutpSubject);
        return getDataTable(list);
    }

    /**
     * 查询学科信息列表 为了下拉
     */
    @GetMapping("/listNotPage")
    public AjaxResult listNotPage() {
        List<DutpSubject> list = dutpSubjectService.listNotPage();
        return success(list);
    }


    /**
     * 导出学科信息列表
     */
    @RequiresPermissions("basic:subject:export")
    @Log(title = "导出学科信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DutpSubject dutpSubject) {
        List<DutpSubject> list = dutpSubjectService.selectDutpSubjectList(dutpSubject);
        ExcelUtil<DutpSubject> util = new ExcelUtil<>(DutpSubject.class);
        util.exportExcel(response, list, "学科信息数据");
    }

    /**
     * 获取学科信息详细信息
     */
    @RequiresPermissions("basic:subject:query")
    @GetMapping(value = "/{subjectId}")
    public AjaxResult getInfo(@PathVariable("subjectId") Long subjectId) {
        return success(dutpSubjectService.selectDutpSubjectBySubjectId(subjectId));
    }

    /**
     * 新增学科信息
     */
    @RequiresPermissions("basic:subject:add")
    @Log(title = "新增学科信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DutpSubject dutpSubject) {
        return toAjax(dutpSubjectService.insertDutpSubject(dutpSubject));
    }

    /**
     * 修改学科信息
     */
    @RequiresPermissions("basic:subject:edit")
    @Log(title = "编辑学科信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DutpSubject dutpSubject) {
        return toAjax(dutpSubjectService.updateDutpSubject(dutpSubject));
    }

    /**
     * 删除学科信息
     */
    @RequiresPermissions("basic:subject:remove")
    @Log(title = "删除学科信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{subjectIds}")
    public AjaxResult remove(@PathVariable Long[] subjectIds) {
        return toAjax(dutpSubjectService.deleteDutpSubjectBySubjectIds(Arrays.asList(subjectIds)));
    }

    @RequiresPermissions("basic:subject:import")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DutpSubjectExcel> util = new ExcelUtil<>(DutpSubjectExcel.class);
        util.importTemplateExcel(response, "教育学科分类");
    }


    @Log(title = "导入学科信息", businessType = BusinessType.IMPORT)
    @RequiresPermissions("basic:subject:import")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<DutpSubjectExcel> util = new ExcelUtil<>(DutpSubjectExcel.class);
        List<DutpSubjectExcel> userList = util.importExcel(file.getInputStream());
        String operName = SecurityUtils.getUsername();
        String message = dutpSubjectService.importUser(userList, updateSupport, operName);
        return success(message);
    }

    @GetMapping("/listForSelect")
    public AjaxResult listForSelect(DutpSubject dutpSubject) {
        List<DutpSubject> list = dutpSubjectService.listForSelect(dutpSubject);
        return success(list);
    }

    /**
     * 学生教师端查询学科信息列表
     */
    @GetMapping("/listTreeEducation")
    public TableDataInfo listTreeEducation(DutpSubject dutpSubject)
    {
        List<DutpSubject> list = dutpSubjectService.selectDutpSubjectList(dutpSubject);
        return getDataTable(list);
    }

    /**
     * 学生教师端查询学科信息列表
     */
    @GetMapping("/listEducation")
    public R<Map<Integer, List<DutpSubject>>> listEducation(DutpSubject dutpSubject)
    {
        return  R.ok(dutpSubjectService.listEducation(dutpSubject));
    }

    /**
     * 查询学科信息列表 为了下拉
     */
    @GetMapping("/allSubjects")
    public AjaxResult allSubjects() {
        List<DutpSubject> list = dutpSubjectService.allSubjects();
        return success(list);
    }
}

package cn.dutp.basic.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * 出版社对象 dutp_publishing_house
 *
 * <AUTHOR>
 * @date 2024-11-12
 */
@Data
@TableName("dutp_publishing_house")
public class DutpPublishingHouse extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** 出版社名称 */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long houseId;

    /** $column.columnComment */
        @Excel(name = "出版社名单")
    private String houseName;

    /** logo地址 */
        @Excel(name = "logo地址")
    private String logoUrl;

    /** 负责人 */
    private String chargeMan;

    /** 联系方式 */
    private String tel;

    /** 地址 */
    private String address;

    /** 介绍 */
    private String introduce;

    /** 排序 */
        @Excel(name = "排序")
    private Integer sort;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("houseId", getHouseId())
            .append("houseName", getHouseName())
            .append("logoUrl", getLogoUrl())
            .append("sort", getSort())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
        .toString();
        }
        }

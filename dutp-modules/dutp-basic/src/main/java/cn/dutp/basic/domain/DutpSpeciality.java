package cn.dutp.basic.domain;

import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * <p>
 * DUTP-BASE-007专业表
 * </p>
 *
 * <AUTHOR> @since 2024-10-30
 */
@Data
@TableName("dutp_speciality")
public class DutpSpeciality extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long specialityId;

    private String specialityName;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    @Override
    public String toString() {
        return "DutpSpeciality{" +
                "specialityId=" + specialityId +
                ", specialityName='" + specialityName + '\'' +
                ", delFlag='" + delFlag + '\'' +
                '}';
    }
}

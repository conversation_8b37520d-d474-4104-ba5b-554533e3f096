package cn.dutp.basic.service;

import java.util.List;
import cn.dutp.basic.domain.DutpArticleType;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 文章类型Service接口
 *
 * <AUTHOR>
 * @date 2024-11-07
 */
public interface IDutpArticleTypeService extends IService<DutpArticleType>
{
    /**
     * 查询文章类型
     *
     * @param typeId 文章类型主键
     * @return 文章类型
     */
    public DutpArticleType selectDutpArticleTypeByTypeId(Long typeId);

    /**
     * 查询文章类型列表
     *
     * @param dutpArticleType 文章类型
     * @return 文章类型集合
     */
    public List<DutpArticleType> selectDutpArticleTypeList(DutpArticleType dutpArticleType);

    /**
     * 新增文章类型
     *
     * @param dutpArticleType 文章类型
     * @return 结果
     */
    public boolean insertDutpArticleType(DutpArticleType dutpArticleType);

    /**
     * 修改文章类型
     *
     * @param dutpArticleType 文章类型
     * @return 结果
     */
    public boolean updateDutpArticleType(DutpArticleType dutpArticleType);

    /**
     * 批量删除文章类型
     *
     * @param typeIds 需要删除的文章类型主键集合
     * @return 结果
     */
    public boolean deleteDutpArticleTypeByTypeIds(List<Long> typeIds);

}

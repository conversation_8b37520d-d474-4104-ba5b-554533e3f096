package cn.dutp.basic.service;

import cn.dutp.basic.domain.DutpFriendLink;
import cn.dutp.basic.domain.vo.DutpFriendLinkVo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 友情链接模块
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
public interface IDutpFriendLinkService extends IService<DutpFriendLink> {

    /**
     * 查询友情链接列表
     *
     * @param dutpFriendLink 友情链接
     * @return 结果
     */
    public List<DutpFriendLink> linkList(DutpFriendLink dutpFriendLink);

    /**
     * 通过linkId查询友情链接详情
     * @param linkId
     * @return
     */
    public DutpFriendLink getLinkByLinkId(Long linkId);

    /**
     * 新增友情链接信息
     * @param dutpFriendLink 友情链接
     * @return 结果
     */
    public boolean addLink(DutpFriendLink dutpFriendLink);

    /**
     * 修改友情链接信息
     * @param dutpFriendLink 友情链接
     * @return 结果
     */
    public boolean editLink(DutpFriendLink dutpFriendLink);

    /**
     * 通过linkId删除友情链接
     * @param linkId 链接id
     * @return 结果
     */
    public boolean deleteLinkById(Long linkId);

    /**
     * 批量删除友情链接
     * @param linkIds 多个友情链接id
     * @return 结果
     */
    public boolean deleteLinkByIds(List<Long> linkIds);


    List<DutpFriendLinkVo> getHomeLinkList();
}

package cn.dutp.basic.controller;

import cn.dutp.basic.domain.DtbTemplateStyleType;
import cn.dutp.basic.service.IDtbTemplateStyleTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;


/**
 * 模板风格类型Controller
 *
 * <AUTHOR>
 * @date 2024-10-30
 */
@RestController
@RequestMapping("/TemplateStyleType")
public class DtbTemplateStyleTypeController extends BaseController
{
    @Autowired
    private IDtbTemplateStyleTypeService dtbTemplateStyleTypeService;

    /**
     * 查询模板风格类型列表
     */
    @GetMapping("/listForSelect")
    public AjaxResult listForSelect(DtbTemplateStyleType dtbTemplateStyleType)
    {
        return dtbTemplateStyleTypeService.listForSelect(dtbTemplateStyleType);
    }

}

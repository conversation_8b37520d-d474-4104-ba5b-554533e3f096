package cn.dutp.basic.controller;

import cn.dutp.basic.domain.AppAdv;
import cn.dutp.basic.service.IAppAdvService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 移动端开屏图Controller
 *
 * <AUTHOR>
 * @date 2025-01-17
 */
@RestController
@RequestMapping("/adv")
public class AppAdvController extends BaseController
{
    @Autowired
    private IAppAdvService appAdvService;

    /**
     * 查询移动端开屏图列表
     */
    @GetMapping("/list")
    public TableDataInfo list(AppAdv appAdv)
    {
        startPage();
        List<AppAdv> list = appAdvService.selectAppAdvList(appAdv);
        return getDataTable(list);
    }

    /**
     * 导出移动端开屏图列表
     */
    @RequiresPermissions("basic:adv:export")
    @Log(title = "导出移动端开屏图", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppAdv appAdv)
    {
        List<AppAdv> list = appAdvService.selectAppAdvList(appAdv);
        ExcelUtil<AppAdv> util = new ExcelUtil<AppAdv>(AppAdv.class);
        util.exportExcel(response, list, "移动端开屏图数据");
    }

    /**
     * 获取移动端开屏图详细信息
     */
    @RequiresPermissions("basic:adv:query")
    @GetMapping(value = "/{advId}")
    public AjaxResult getInfo(@PathVariable("advId") Long advId)
    {
        return success(appAdvService.selectAppAdvByAdvId(advId));
    }

    /**
     * 新增移动端开屏图
     */
    @RequiresPermissions("basic:adv:add")
    @Log(title = "新增移动端开屏图", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppAdv appAdv)
    {
        return toAjax(appAdvService.insertAppAdv(appAdv));
    }

    /**
     * 修改移动端开屏图
     */
    @RequiresPermissions("basic:adv:edit")
    @Log(title = "修改移动端开屏图", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppAdv appAdv)
    {
        return toAjax(appAdvService.updateAppAdv(appAdv));
    }

    /**
     * 删除移动端开屏图
     */
    @RequiresPermissions("basic:adv:remove")
    @Log(title = "删除移动端开屏图", businessType = BusinessType.DELETE)
    @DeleteMapping("/{advIds}")
    public AjaxResult remove(@PathVariable Long[] advIds)
    {
        return toAjax(appAdvService.deleteAppAdvByAdvIds(Arrays.asList(advIds)));
    }
}

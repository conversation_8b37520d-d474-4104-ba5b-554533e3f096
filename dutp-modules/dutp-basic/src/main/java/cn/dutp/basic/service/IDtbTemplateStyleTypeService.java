package cn.dutp.basic.service;

import cn.dutp.basic.domain.DtbTemplateStyleType;
import cn.dutp.common.core.web.domain.AjaxResult;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 模板风格类型Service接口
 *
 * <AUTHOR>
 * @date 2024-10-30
 */
public interface IDtbTemplateStyleTypeService extends IService<DtbTemplateStyleType>
{


    AjaxResult listForSelect(DtbTemplateStyleType dtbTemplateStyleType);
}

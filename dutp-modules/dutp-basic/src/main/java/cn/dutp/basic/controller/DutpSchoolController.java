package cn.dutp.basic.controller;

import cn.dutp.basic.domain.DutpSchool;
import cn.dutp.basic.domain.dto.DtbSchoolDTO;
import cn.dutp.basic.domain.dto.DutpAcademyDto;
import cn.dutp.basic.service.IDutpSchoolService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.system.api.model.LoginUser;
import cn.hutool.core.lang.tree.Tree;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 学校管理Controller
 *
 * <AUTHOR>
 * &#064;date  2024-10-15
 */
@RestController
@RequestMapping("/school")
public class DutpSchoolController extends BaseController
{
    @Autowired
    private IDutpSchoolService dutpSchoolService;

    /**
     * 查询学校管理列表
     */
    @RequiresPermissions("basic:school:list")
    @GetMapping("/list")
    public AjaxResult list(DutpSchool dutpSchool)
    {
        List<Tree<String>> list = dutpSchoolService.selectDutpSchoolList(dutpSchool);
        return success(list);
    }
    /**
     * 学生教师端查询学校管理列表
     */
    @GetMapping("/listEducation")
    public AjaxResult listEducation(DutpSchool dutpSchool)
    {
        List<Tree<String>> list = dutpSchoolService.selectDutpSchoolListEducation(dutpSchool);
        return success(list);
    }

    /**
     * 查询学校管理列表(用于编辑下拉框,过滤三级数据)
     */
    // @RequiresPermissions("basic:school:list")
    @GetMapping("/listNoPage")
    public AjaxResult listNoPage(DutpSchool dutpSchool)
    {
        List<Tree<String>> list = dutpSchoolService.listNoPage(dutpSchool);
        return success(list);
    }

    /**
     * 查询教材关联的学校列表(用于编辑下拉框,过滤三级数据)
     */
    @GetMapping("/listNoPageOfBook")
    public AjaxResult listNoPageOfBook(DutpSchool dutpSchool)
    {
        List<DutpSchool> list = dutpSchoolService.listNoPageOfBook(dutpSchool);
        return success(list);
    }

    /**
     * 获取学院列表
     * @param dutpSchool 学校
     * @return 结果
     */
    @RequiresPermissions("basic:school:selAcademy")
    @GetMapping("/selAcademy")
    public TableDataInfo selAcademy(DutpSchool dutpSchool){
        startPage();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long schoolId = loginUser.getSysUser().getSchoolId();
        dutpSchool.setParentId(schoolId);
        List<DutpSchool> list = dutpSchoolService.selectAcademyList(dutpSchool);
        return getDataTable(list);
    }

    /**
     * 获取学院列表(用于下拉框) -- 后端
     * @param dutpSchool 学校
     * @return 结果
     */
    @GetMapping("/selAcademyNoPage")
    public AjaxResult selAcademyNoPage(DutpSchool dutpSchool){
        List<DutpSchool> list = dutpSchoolService.selectAcademyListNopage(dutpSchool);
        return success(list);
    }
    /**
     * 获取学院列表(用于下拉框) -- 教务
     * @param dutpSchool 学校
     * @return 结果
     */
    @GetMapping("/selectEduAcademyList")
    public AjaxResult selAcademyNoPageEdu(DutpSchool dutpSchool){
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long schoolId = loginUser.getSysUser().getSchoolId();
        dutpSchool.setParentId(schoolId);
        List<DutpSchool> list = dutpSchoolService.selectAcademyListNopageEdu(dutpSchool);
        return success(list);
    }

    /**
     * 获取专业列表
     * @param dutpSchool 学校
     * @return 结果
     */
    @RequiresPermissions("basic:school:subject")
    @GetMapping("/selectSubjectList")
    public TableDataInfo selectSubjectList(DutpSchool dutpSchool){
        startPage();
        dutpSchool.setSchoolId(SecurityUtils.getLoginUser().getSchoolId());
        List<DutpSchool> list = dutpSchoolService.selectSubjectList(dutpSchool);
        return getDataTable(list);
    }

    /**
     * 获取专业下拉框 --后端
     * @param dutpSchool
     * @return 结果
     */
    @GetMapping("/selectSubjectNoPage")
    public AjaxResult selectSubjectNoPage(DutpSchool dutpSchool){
        List<DutpSchool> list = dutpSchoolService.selectSubject(dutpSchool);
        return success(list);
    }

    /**
     * 获取学院下拉框
     * @param dutpSchool
     * @return
     */
    @RequiresPermissions("basic:school:list")
    @GetMapping("/selectAcademy")
    public AjaxResult selectAcademy(DutpSchool dutpSchool){
        List<DutpSchool> list = dutpSchoolService.selectAcademyList(dutpSchool);
        return success(list);
    }

    /**
     * 批量导入学校信息
     */
    @RequiresPermissions("basic:school:import")
    @Log(title = "导入学校", businessType = BusinessType.EXPORT)
    @PostMapping("/insertSchoolList")
    public AjaxResult insertSchoolList(@RequestBody List<DtbSchoolDTO> list)
    {
        return success(dutpSchoolService.insertSchoolList(list));
    }

    /**
     * 批量导入学院信息
     */
    @RequiresPermissions("basic:school:import")
    @Log(title = "导入学院", businessType = BusinessType.EXPORT)
    @PostMapping("/importAcademy")
    public AjaxResult importAcademy(@RequestBody List<DutpSchool> list)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long schoolId = loginUser.getSysUser().getSchoolId();
        for (DutpSchool dutpSchool : list){
            dutpSchool.setParentId(schoolId);
            dutpSchool.setDataType(1);
        }
        return success(dutpSchoolService.addAcademyList(list));
    }


    /**
     * 获取学校管理详细信息
     */
    @GetMapping(value = "/{schoolId}")
    public AjaxResult getInfo(@PathVariable("schoolId") Long schoolId)
    {
        return AjaxResult.success(dutpSchoolService.selectDutpSchoolBySchoolId(schoolId));
    }

    /**
     * 新增学校管理
     */
    @RequiresPermissions("basic:school:add")
    @Log(title = "新增学校", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DutpSchool dutpSchool)
    {
        return toAjax(dutpSchoolService.insertDutpSchool(dutpSchool));
    }

    /**
     * 新增学院管理
     */
    @RequiresPermissions("basic:school:add")
    @Log(title = "学院管理", businessType = BusinessType.INSERT)
    @PostMapping("addAcademy")
    public AjaxResult addAcademy(@RequestBody DutpSchool dutpSchool) {
        dutpSchool.setDataType(1);
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long schoolId = loginUser.getSysUser().getSchoolId();
        dutpSchool.setParentId(schoolId);
        return toAjax(dutpSchoolService.addAcademy(dutpSchool));
    }

    /**
     * 新增专业管理
     */
    @RequiresPermissions("basic:school:add")
    @Log(title = "专业管理", businessType = BusinessType.INSERT)
    @PostMapping("addSubject")
    public AjaxResult addSubject(@RequestBody DutpSchool dutpSchool) {
        dutpSchool.setDataType(2);
        return toAjax(dutpSchoolService.addSubject(dutpSchool));

    }

    /**
     * 修改学校管理
     */
    @RequiresPermissions("basic:school:edit")
    @Log(title = "修改学校", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DutpSchool dutpSchool)
    {
        return toAjax(dutpSchoolService.updateDutpSchool(dutpSchool));
    }

    /**
     * 修改学院
     */
    @RequiresPermissions("basic:school:edit")
    @Log(title = "学院管理", businessType = BusinessType.UPDATE)
    @PutMapping("editAcademy")
    public AjaxResult editAcademy(@RequestBody DutpSchool dutpSchool)
    {
        return toAjax(dutpSchoolService.updateAcademy(dutpSchool));
    }


    /**
     * 修改专业
     */
    @RequiresPermissions("basic:school:edit")
    @Log(title = "专业管理", businessType = BusinessType.UPDATE)
    @PutMapping("updateSubject")
    public AjaxResult updateSubject(@RequestBody DutpSchool dutpSchool)
    {
        return toAjax(dutpSchoolService.updateSubject(dutpSchool));
    }

    /**
     * 删除学校管理
     */
    @RequiresPermissions("basic:school:remove")
    @Log(title = "删除学校", businessType = BusinessType.DELETE)
    @DeleteMapping("/{schoolIds}")
    public AjaxResult remove(@PathVariable Long[] schoolIds)
    {
        return dutpSchoolService.deleteDutpSchoolBySchoolIds(Arrays.asList(schoolIds));
    }

    /**
     * 查询学校管理列表-用于下拉框
     */
    @GetMapping("/all")
    public TableDataInfo listAll(DutpSchool dutpSchool)
    {
        List<DutpSchool> list = dutpSchoolService.selectDutpSchoolListWithOutTree(dutpSchool);
        return getDataTable(list);
    }

    /**
     * 查询学校管理列表-用于新增下级，排除末级数据
     */
    @GetMapping("/getAllWithOutLast")
    public AjaxResult getAllWithOutLast(DutpSchool dutpSchool)
    {
        List<Tree<String>> list = dutpSchoolService.getAllWithOutLast(dutpSchool);
        return success(list);
    }

    /**
     * 导出专业信息管理
     */
    @RequiresPermissions("basic:school:export")
    @Log(title = "导出专业信息", businessType = BusinessType.EXPORT)
    @PostMapping("/subjectExport")
    public void export(HttpServletResponse response, DutpSchool dutpSchool) {
        dutpSchool.setSchoolId(SecurityUtils.getLoginUser().getSchoolId());
        List<DutpSchool> list = dutpSchoolService.selectSubjectList(dutpSchool);
        ExcelUtil<DutpSchool> util =  new ExcelUtil<DutpSchool>(DutpSchool.class);
        util.exportExcel(response, list, "专业管理信息");
    }

    /**
     * 导出学院信息管理
     */
    @RequiresPermissions("basic:school:export")
    @Log(title = "导出专业信息", businessType = BusinessType.EXPORT)
    @PostMapping("/academyExport")
    public void exportAcademy(HttpServletResponse response, DutpSchool dutpSchool) {
        dutpSchool.setSchoolId(SecurityUtils.getLoginUser().getSchoolId());
        List<DutpAcademyDto> list = dutpSchoolService.selectAcademyListExport(dutpSchool);
        ExcelUtil<DutpAcademyDto> util =  new ExcelUtil<DutpAcademyDto>(DutpAcademyDto.class);
        util.exportExcel(response, list, "学院管理信息");
    }

    /**
     * 根据学校id获取学院/专业下拉框(树形结构)
     */
    @GetMapping("/selectByParentId")
    public AjaxResult selectByParentId(DutpSchool dutpSchool){
        if (dutpSchool.getParentId() == null) {
            // 获取学院
            dutpSchool.setParentId(SecurityUtils.getLoginUser().getSchoolId());
        }
        List<Tree<String>> list = dutpSchoolService.selectByParentId(dutpSchool);
        return success(list);
    }



}

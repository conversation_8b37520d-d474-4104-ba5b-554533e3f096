package cn.dutp.basic.domain;

    import java.util.Date;
    import com.fasterxml.jackson.annotation.JsonFormat;
import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * 轮播广告对象 dutp_banner
 *
 * <AUTHOR>
 * @date 2024-10-22
 */
@Data
@TableName("dutp_banner")
public class DutpBanner extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** $column.columnComment */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bannerId;

    /** $column.columnComment */
        @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String bannerName;

    /** 轮播设备 */
        @Excel(name = "轮播设备")
    private String device;

    /** 1首页 */
        @Excel(name = "1首页")
    private String bannerPosition;

    /** 图片地址 */
        @Excel(name = "图片地址")
    private String imageUrl;

    /** 跳转类型1内部链接2外部链接 */
        @Excel(name = "跳转类型1内部链接2外部链接")
    private Integer urlType;

    /** 跳转地址 */
        @Excel(name = "跳转地址")
    private String jumpUrl;

    /** 展示顺序 */
        @Excel(name = "展示顺序")
    private Integer sort;

    /** 显示开始日期 */
        @JsonFormat(pattern = "yyyy-MM-dd")
        @Excel(name = "显示开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startDate;

    /** 展示结束日期 */
        @JsonFormat(pattern = "yyyy-MM-dd")
        @Excel(name = "展示结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endDate;

    /** 1未开始2已发布3已下线4已撤销 */
        @Excel(name = "1未开始2已发布3已下线4已撤销")
    private Integer status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("bannerId", getBannerId())
            .append("bannerName", getBannerName())
            .append("device", getDevice())
            .append("bannerPositon", getBannerPosition())
            .append("imageUrl", getImageUrl())
            .append("urlType", getUrlType())
            .append("jumpUrl", getJumpUrl())
            .append("sort", getSort())
            .append("startDate", getStartDate())
            .append("endDate", getEndDate())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
        .toString();
        }
        }

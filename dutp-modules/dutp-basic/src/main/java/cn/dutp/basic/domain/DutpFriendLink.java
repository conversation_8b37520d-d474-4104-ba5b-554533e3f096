package cn.dutp.basic.domain;

import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

@TableName("dutp_friend_link")
@Data
public class DutpFriendLink extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 连接id
     */
    @TableId(type = IdType.AUTO)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long linkId;

    /**
     * 连接文本
     */
    private String linkLabel;

    /**
     * 跳转地址
     */
    private String linkUrl;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 删除标志
     */
    private String delFlag;

}

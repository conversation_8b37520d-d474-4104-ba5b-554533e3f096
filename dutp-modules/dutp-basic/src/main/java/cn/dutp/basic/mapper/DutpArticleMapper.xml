<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.basic.mapper.DutpArticleMapper">
    
    <resultMap type="DutpArticle" id="DutpArticleResult">
        <result property="id"    column="id"    />
        <result property="articleTitle"    column="article_title"    />
        <result property="content"    column="content"    />
        <result property="url"    column="url"    />
        <result property="articleContentType"    column="article_content_type"    />
        <result property="typeId"    column="type_id"    />
        <result property="sort"    column="sort"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDutpArticleVo">
        select id, article_title, content, url, article_content_type, type_id, sort, del_flag, create_by, create_time, update_by, update_time from dutp_article
    </sql>

    <select id="selectDutpArticleList" parameterType="DutpArticle" resultMap="DutpArticleResult">
        <include refid="selectDutpArticleVo"/>
        <where>  
            <if test="articleTitle != null  and articleTitle != ''"> and article_title like concat('%', #{articleTitle}, '%')</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
            <if test="articleContentType != null "> and article_content_type = #{articleContentType}</if>
            <if test="typeId != null "> and type_id = #{typeId}</if>
            <if test="sort != null "> and sort = #{sort}</if>
        </where>
    </select>
    
    <select id="selectDutpArticleById" parameterType="Long" resultMap="DutpArticleResult">
        <include refid="selectDutpArticleVo"/>
        where id = #{id}
    </select>

    <insert id="insertDutpArticle" parameterType="DutpArticle" useGeneratedKeys="true" keyProperty="id">
        insert into dutp_article
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="articleTitle != null">article_title,</if>
            <if test="content != null">content,</if>
            <if test="url != null">url,</if>
            <if test="articleContentType != null">article_content_type,</if>
            <if test="typeId != null">type_id,</if>
            <if test="sort != null">sort,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="articleTitle != null">#{articleTitle},</if>
            <if test="content != null">#{content},</if>
            <if test="url != null">#{url},</if>
            <if test="articleContentType != null">#{articleContentType},</if>
            <if test="typeId != null">#{typeId},</if>
            <if test="sort != null">#{sort},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDutpArticle" parameterType="DutpArticle">
        update dutp_article
        <trim prefix="SET" suffixOverrides=",">
            <if test="articleTitle != null">article_title = #{articleTitle},</if>
            <if test="content != null">content = #{content},</if>
            <if test="url != null">url = #{url},</if>
            <if test="articleContentType != null">article_content_type = #{articleContentType},</if>
            <if test="typeId != null">type_id = #{typeId},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDutpArticleById" parameterType="Long">
        delete from dutp_article where id = #{id}
    </delete>

    <delete id="deleteDutpArticleByIds" parameterType="String">
        delete from dutp_article where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
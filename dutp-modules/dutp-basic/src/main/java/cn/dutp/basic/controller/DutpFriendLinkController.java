package cn.dutp.basic.controller;

import cn.dutp.basic.domain.DutpFriendLink;
import cn.dutp.basic.service.IDutpFriendLinkService;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 友情链接
 *
 * @author: dutp
 * @date: 2024/10/28
 */
@RequestMapping("/friendLink")
@RestController
public class DutpFriendLinkController extends BaseController {

    @Autowired
    private IDutpFriendLinkService friendLinkService;

    /**
     * 查询友情链接列表
     * @param dutpFriendLink 友情链接
     * @return 结果
     */
    @GetMapping("/list")
    public TableDataInfo list(DutpFriendLink dutpFriendLink){
        startPage();
        List<DutpFriendLink> list = friendLinkService.linkList(dutpFriendLink);
        return getDataTable(list);
    }

    /**
     * 获取友情链接详情
     * @param linKId 友情链接id
     * @return 结果
     */
    @RequiresPermissions("basic:link:query")
    @GetMapping("/getInfo/{linkId}")
    public AjaxResult getInfo(@PathVariable("linkId") Long linKId){
        return success(friendLinkService.getLinkByLinkId(linKId));
    }

    /**
     * 新增友情链接信息
     * @param dutpFriendLink 友情链接
     * @return 结果
     */
    @RequiresPermissions("basic:link:add")
    @Log(title = "添加友情链接", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult addLink(@RequestBody DutpFriendLink dutpFriendLink){
        return toAjax(friendLinkService.addLink(dutpFriendLink));
    }

    /**
     * 修改友情链接信息
     * @param dutpFriendLink 友情链接
     * @return 结果
     */
    @RequiresPermissions("basic:link:edit")
    @Log(title = "修改友情链接", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult editLink(@RequestBody DutpFriendLink dutpFriendLink){
        return toAjax(friendLinkService.editLink(dutpFriendLink));
    }

    /**
     * 删除友情链接
     * @param linkIds 多个 id
     * @return 结果
     */
    @RequiresPermissions("basic:link:remove")
    @Log(title = "删除友情链接", businessType = BusinessType.DELETE)
    @DeleteMapping("/{linkIds}")
    public AjaxResult remove(@PathVariable("linkIds") Long[] linkIds){
        return toAjax(friendLinkService.deleteLinkByIds(Arrays.asList(linkIds)));
    }
}

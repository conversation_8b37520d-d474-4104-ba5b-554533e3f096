package cn.dutp.basic.service.impl;

import cn.dutp.basic.domain.DutpDegree;
import cn.dutp.basic.domain.DutpSchool;
import cn.dutp.basic.domain.dto.DtbSchoolDTO;
import cn.dutp.basic.domain.dto.DutpAcademyDto;
import cn.dutp.basic.domain.vo.DutpSchoolVo;
import cn.dutp.basic.mapper.DutpDegreeMapper;
import cn.dutp.basic.mapper.DutpSchoolMapper;
import cn.dutp.basic.service.IDutpSchoolService;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.utils.StringUtils;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.system.api.domain.DutpUser;
import cn.dutp.system.api.model.LoginUser;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 学校管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
@Slf4j
@Service
public class DutpSchoolServiceImpl extends ServiceImpl<DutpSchoolMapper, DutpSchool> implements IDutpSchoolService {
    @Autowired
    private DutpSchoolMapper dutpSchoolMapper;
    @Autowired
    private DutpDegreeMapper dutpDegreeMapper;

    /**
     * 查询学校管理
     *
     * @param schoolId 学校管理主键
     * @return 学校管理
     */
    @Override
    public DutpSchool selectDutpSchoolBySchoolId(Long schoolId) {
        QueryWrapper<DutpSchool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DutpSchool::getSchoolId, schoolId).eq(DutpSchool::getDelFlag, 0);
        DutpSchool school = dutpSchoolMapper.selectOne(queryWrapper);
        return school;
    }

    /**
     * 查询学校管理列表
     *
     * @param dutpSchool 学校管理
     * @return 学校管理
     */
    @Override
    public List<Tree<String>> selectDutpSchoolList(DutpSchool dutpSchool) {
        List<DutpSchool> list = dutpSchoolMapper.selectDutpSchoolList(dutpSchool);
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setChildrenKey("children");
        try {
            List<Tree<String>> treeNodes = TreeUtil.build(list, "0", treeNodeConfig,
                    (treeNode, tree) -> {
                        tree.setId(treeNode.getSchoolId().toString());
                        tree.setParentId(treeNode.getParentId().toString());
                        tree.setName(treeNode.getSchoolName());
                        tree.setWeight(treeNode.getSort());
                        tree.putExtra("schoolId", treeNode.getSchoolId().toString());
                        tree.putExtra("schoolName", treeNode.getSchoolName());
                        tree.putExtra("schoolCode", StringUtils.isBlank(treeNode.getSchoolCode()) ? "" : treeNode.getSchoolCode());
                        tree.putExtra("dataType", treeNode.getDataType());
                        tree.putExtra("logoUrl", StringUtils.isBlank(treeNode.getLogoUrl()) ? "" : treeNode.getLogoUrl());
                        tree.putExtra("isPartner", treeNode.getIsPartner());
                        tree.putExtra("degreeId", treeNode.getDegreeId() == null ? null : treeNode.getDegreeId());
                        tree.putExtra("sort", treeNode.getSort() == null ? 0 : treeNode.getSort());
                        tree.putExtra("delFlag", treeNode.getDelFlag());
                        tree.putExtra("createTime", treeNode.getCreateTime());
                        tree.putExtra("degreeName", StringUtils.isBlank(treeNode.getDegreeName()) ? "" : treeNode.getDegreeName());
                        tree.putExtra("bookCount", treeNode.getBookCount() == null ? 0 : treeNode.getBookCount());
                        tree.putExtra("accountBank", treeNode.getAccountBank());
                        tree.putExtra("accountNo", treeNode.getAccountNo());
                        tree.putExtra("taxNo", treeNode.getTaxNo());
                        tree.putExtra("titleName", treeNode.getTitleName());
                        tree.putExtra("registAddress", treeNode.getRegistAddress());
                        tree.putExtra("registTel", treeNode.getRegistTel());
                        if (treeNode.getDataType() == 2 || treeNode.getDataType() == 1) {
                            tree.putExtra("defaultDiscount", null);
                            tree.putExtra("invoiceType", null);
                            tree.putExtra("titleType", null);
                            tree.putExtra("userEmail", null);
                            tree.putExtra("applyType", null);
                        } else {
                            tree.putExtra("defaultDiscount", treeNode.getDefaultDiscount());
                            tree.putExtra("invoiceType", treeNode.getInvoiceType());
                            tree.putExtra("titleType", treeNode.getTitleType());
                            tree.putExtra("userEmail", treeNode.getUserEmail());
                            tree.putExtra("applyType", treeNode.getApplyType());
                        }
                    });
            return treeNodes;
        } catch (Exception e) {
            System.err.println("Error processing DutpSchool list: " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 查询学校管理列表
     *
     * @param dutpSchool 学校管理
     * @return 学校管理
     */
    @Override
    public List<Tree<String>> selectDutpSchoolListEducation(DutpSchool dutpSchool) {
        List<DutpSchool> list = dutpSchoolMapper.selectDutpSchoolList(dutpSchool);
        Set<Long> seenIds = new HashSet<>();
        List<DutpSchool> uniqueList = new ArrayList<>();

        for (DutpSchool school : list) {
            if (!seenIds.contains(school.getSchoolId())) {
                seenIds.add(school.getSchoolId());
                uniqueList.add(school);
            }
        }

        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setChildrenKey("children");
        try {
            List<Tree<String>> treeNodes = TreeUtil.build(uniqueList, "0", treeNodeConfig,
                    (treeNode, tree) -> {
                        tree.setId(treeNode.getSchoolId().toString());
                        tree.setParentId(treeNode.getParentId().toString());
                        tree.setName(treeNode.getSchoolName());
                        tree.setWeight(treeNode.getSort());

                        tree.putExtra("schoolId", treeNode.getSchoolId().toString());
                        tree.putExtra("value", treeNode.getSchoolId());
                        tree.putExtra("schoolName", treeNode.getSchoolName());
                        tree.putExtra("label", treeNode.getSchoolName());
                        tree.putExtra("schoolCode", treeNode.getSchoolCode());
                        tree.putExtra("dataType", treeNode.getDataType());
                        tree.putExtra("logoUrl", treeNode.getLogoUrl());
                        tree.putExtra("isPartner", treeNode.getIsPartner());
                        tree.putExtra("degreeId", treeNode.getDegreeId());
                        tree.putExtra("sort", treeNode.getSort());
                        tree.putExtra("delFlag", treeNode.getDelFlag());
                        tree.putExtra("createTime", treeNode.getCreateTime());
                        tree.putExtra("degreeName", treeNode.getDegreeName());
                        tree.putExtra("bookCount", treeNode.getBookCount());
                    });
            return treeNodes;
        } catch (Exception e) {
            System.err.println("Error processing DutpSchool list: " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public List<DutpSchool> selectDutpSchoolListWithOutTree(DutpSchool dutpSchool) {
        return this.list();
    }

    @Override
    public List<DutpSchool> listSchoolByQuery(DutpSchool dutpSchool) {
        List<DutpSchool> list = dutpSchoolMapper.selectDutpSchoolList(dutpSchool);
        return list;
    }

    /**
     * 新增学校管理
     *
     * @param dutpSchool 学校管理
     * @return 结果
     */
    @Override
    @Transactional
    public boolean insertDutpSchool(DutpSchool dutpSchool) {
        DutpSchool school = getByNameAndType(dutpSchool.getSchoolName(), dutpSchool.getDataType(),dutpSchool.getParentId());

        if (ObjectUtil.isNotEmpty(school)) {
            throw new ServiceException("名称已存在");
        } else {
            dutpSchool.setCreateBy(SecurityUtils.getUsername());
            dutpSchool.setCreateTime(new Date());
            //
            if(dutpSchool.getTitleType() == 1){
                dutpSchool.setInvoiceType(null);
                dutpSchool.setApplyType(null);
            }
            return this.save(dutpSchool);
        }
    }

    /**
     * 修改学校管理
     *
     * @param dutpSchool 学校管理
     * @return 结果
     */
    @Override
    @Transactional
    public boolean updateDutpSchool(DutpSchool dutpSchool) {
        List<DutpSchool> school = getAllByName(dutpSchool.getSchoolName(), dutpSchool.getDataType());
        if (!CollectionUtils.isEmpty(school)) {
            boolean hasOtherIds = school.stream()
                    .anyMatch(obj -> obj.getSchoolId() != dutpSchool.getSchoolId());
            if (hasOtherIds) {
                throw new ServiceException("名称已存在");
            } else {
                dutpSchool.setUpdateBy(SecurityUtils.getUsername());
                dutpSchool.setUpdateTime(new Date());
                return this.updateById(dutpSchool);
            }
        } else {
            dutpSchool.setUpdateBy(SecurityUtils.getUsername());
            dutpSchool.setUpdateTime(new Date());
            return this.updateById(dutpSchool);
        }
    }

    private List<DutpSchool> getAllByName(String schoolName, Integer dataType) {
        QueryWrapper<DutpSchool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DutpSchool::getSchoolName, schoolName)
                .eq(DutpSchool::getDelFlag, 0)
                .ne(DutpSchool::getDataType, dataType);
        return dutpSchoolMapper.selectList(queryWrapper);
    }

    /**
     * 批量删除学校管理
     *
     * @param schoolIds 需要删除的学校管理主键
     * @return 结果
     */
    @Override
    @Transactional
    public AjaxResult deleteDutpSchoolBySchoolIds(List<Long> schoolIds) {
        QueryWrapper<DutpSchool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(DutpSchool::getParentId, schoolIds)
                .eq(DutpSchool::getDelFlag, 0);
        List<DutpSchool> list = dutpSchoolMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            return AjaxResult.error("当前院系下存在一个或多个专业。请先删除所有相关专业，然后再尝试删除院系。");
        }
        return AjaxResult.success(this.removeByIds(schoolIds));
    }

    @Override
    @Transactional
    public String insertSchoolList(List<DtbSchoolDTO> list) {
        if (StringUtils.isNull(list) || list.size() == 0) {
            throw new ServiceException("导入学校数据不能为空！");
        }

        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        try {
            for (int i = 0; i < list.size(); i++) {
                if (StringUtils.isNotBlank(list.get(i).getMajorlName()) && StringUtils.isBlank(list.get(i).getSchoolName()) && StringUtils.isBlank(list.get(i).getCollegeName())) {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、第 " + (i + 1) + "行 导入失败：学校名称不能为空,院系名称不能为空！";
                    failureMsg.append(msg);
                    continue;
                }
                if (StringUtils.isBlank(list.get(i).getSchoolName())) {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、第 " + (i + 1) + "行 导入失败：学校名称不能为空！";
                    failureMsg.append(msg);
                    continue;
                }
                if (StringUtils.isNotBlank(list.get(i).getMajorlName())) {
                    if (StringUtils.isBlank(list.get(i).getSchoolName())) {
                        failureNum++;
                        String msg = "<br/>" + failureNum + "、第 " + (i + 1) + "行 导入失败：学校名称不能为空！";
                        failureMsg.append(msg);
                        continue;
                    }
                    if (StringUtils.isBlank(list.get(i).getCollegeName())) {
                        failureNum++;
                        String msg = "<br/>" + failureNum + "、第 " + (i + 1) + "行 导入失败：院系名称不能为空！";
                        failureMsg.append(msg);
                        continue;
                    }
                }
                DutpSchool firstschool = getByNameAndType(list.get(i).getSchoolName(), 0,0L);
                if (ObjectUtil.isEmpty(firstschool)) {
                    DutpSchool school = new DutpSchool();
                    school.setSchoolName(list.get(i).getSchoolName());
                    school.setSchoolCode(list.get(i).getSchoolCode());
                    school.setCreateBy(SecurityUtils.getUsername());
                    school.setDataType(0);
                    school.setCreateTime(new Date());
                    school.setParentId(0L);
                    this.save(school);
                    if (StringUtils.isNotBlank(list.get(i).getCollegeName())) {
                        DutpSchool secondSchool = new DutpSchool();
                        secondSchool.setSchoolName(list.get(i).getCollegeName());
                        secondSchool.setDataType(1);
                        secondSchool.setParentId(school.getSchoolId());
                        secondSchool.setCreateBy(SecurityUtils.getUsername());
                        secondSchool.setCreateTime(new Date());
                        this.save(secondSchool);
                        if (StringUtils.isNotBlank(list.get(i).getMajorlName())) {
                            DutpSchool childSchool = new DutpSchool();
                            childSchool.setSchoolName(list.get(i).getMajorlName());
                            childSchool.setDataType(2);
                            childSchool.setParentId(secondSchool.getSchoolId());
                            childSchool.setCreateBy(SecurityUtils.getUsername());
                            childSchool.setCreateTime(new Date());
                            this.save(childSchool);
                            successNum++;
                        }
                    }
                } else {
                    if (!StringUtils.isBlank(list.get(i).getCollegeName())) {
                        DutpSchool secondSchool = getByNameAndType(list.get(i).getCollegeName(), 1, firstschool.getSchoolId());
                        if (ObjectUtil.isNotEmpty(secondSchool)) {
                            if (StringUtils.isNotBlank(list.get(i).getMajorlName())) {
                                DutpSchool childSchools = getByNameAndType(list.get(i).getMajorlName(), 2, secondSchool.getSchoolId());
                                if (StringUtils.isNotBlank(list.get(i).getMajorlName()) && ObjectUtil.isEmpty(childSchools)) {
                                    DutpSchool childSchool = new DutpSchool();
                                    childSchool.setSchoolName(list.get(i).getMajorlName());
                                    childSchool.setDataType(2);

                                    childSchool.setParentId(secondSchool.getSchoolId());
                                    childSchool.setCreateBy(SecurityUtils.getUsername());
                                    childSchool.setCreateTime(new Date());
                                    this.save(childSchool);
                                    successNum++;
                                }
                                if (StringUtils.isNotBlank(list.get(i).getMajorlName()) && !ObjectUtil.isEmpty(childSchools)) {
                                    failureNum++;
                                    String msg = "<br/>" + failureNum + "、第 " + (i + 1) + "行 导入失败：专业名称已存在！";
                                    failureMsg.append(msg);
                                    continue;
                                }
                            } else {
                                failureNum++;
                                String msg = "<br/>" + failureNum + "、第 " + (i + 1) + "行 导入失败：院系名称已存在！";
                                failureMsg.append(msg);
                                continue;
                            }

                        } else {
                            secondSchool = new DutpSchool();
                            secondSchool.setSchoolName(list.get(i).getCollegeName());
                            secondSchool.setDataType(1);
                            secondSchool.setParentId(firstschool.getSchoolId());
                            secondSchool.setCreateBy(SecurityUtils.getUsername());
                            secondSchool.setCreateTime(new Date());
                            this.save(secondSchool);
                            if (StringUtils.isNotBlank(list.get(i).getMajorlName())) {
                                DutpSchool childSchool = getByNameAndType(list.get(i).getMajorlName(), 2,secondSchool.getSchoolId());
                                if (!ObjectUtil.isEmpty(childSchool)) {
                                    failureNum++;
                                    String msg = "<br/>" + failureNum + "、第 " + (i + 1) + "行 导入失败：专业名称已存在！";
                                    failureMsg.append(msg);
                                    continue;
                                } else {
                                    childSchool = new DutpSchool();
                                    childSchool.setSchoolName(list.get(i).getMajorlName());
                                    childSchool.setDataType(2);
                                    childSchool.setParentId(secondSchool.getSchoolId());
                                    childSchool.setCreateBy(SecurityUtils.getUsername());
                                    childSchool.setCreateTime(new Date());
                                    this.save(childSchool);
                                    successNum++;
                                }
                            } else {
                                successNum++;
                            }
                        }
                    }
                    if (StringUtils.isBlank(list.get(i).getCollegeName())) {
                        failureNum++;
                        String msg = "<br/>" + failureNum + "、第 " + (i + 1) + "行 导入失败：学校名称已存在！";
                        failureMsg.append(msg);
                        continue;
                    }
                }
            }
        } catch (NullPointerException e) {
            e.printStackTrace();
            failureMsg.insert(0, "很抱歉，导入失败！请确保所有字段都已填写！");
            return failureMsg.toString();
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "共 " + list.size() + " 条数据,其中" + successNum + "条导入成功," + failureNum + "条导入失败，错误如下：");
            return failureMsg.toString();
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + list.size() + " 条");
            return successMsg.toString();
        }
    }

    private DutpSchool getByName(String name, Integer dataType) {
        QueryWrapper<DutpSchool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DutpSchool::getSchoolName, name)
                .eq(DutpSchool::getDelFlag, 0)
                .ne(DutpSchool::getDataType, dataType);
        return dutpSchoolMapper.selectOne(queryWrapper);
    }

    private DutpSchool getByNameAndType(String name, Integer dataType,Long parentId) {
        QueryWrapper<DutpSchool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DutpSchool::getSchoolName, name)
                .eq(DutpSchool::getParentId, parentId)
                .eq(DutpSchool::getDelFlag, 0)
                .eq(DutpSchool::getDataType, dataType);
        return dutpSchoolMapper.selectOne(queryWrapper);
    }

    @Override
    public List<Tree<String>> listNoPage(DutpSchool dutpSchool) {
        List<DutpSchool> list = dutpSchoolMapper.listNoPage(dutpSchool);
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setChildrenKey("children");
        // 格式化树形结构
        List<Tree<String>> treeNodes = TreeUtil.build(list, "0", treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getSchoolId().toString());
                    tree.setParentId(treeNode.getParentId().toString());
                    tree.setName(treeNode.getSchoolName());
                    tree.setWeight(treeNode.getSort());
                    tree.putExtra("schoolId", treeNode.getSchoolId().toString());
                    tree.putExtra("schoolIdStr", treeNode.getSchoolId().toString());
                    tree.putExtra("schoolName", treeNode.getSchoolName());
                    tree.putExtra("schoolCode", StringUtils.isBlank(treeNode.getSchoolCode()) ? "" : treeNode.getSchoolCode());
                    tree.putExtra("dataType", treeNode.getDataType());
                    tree.putExtra("logoUrl", treeNode.getLogoUrl());
                    tree.putExtra("isPartner", treeNode.getIsPartner());
                    tree.putExtra("degreeId", treeNode.getDegreeId()!= null ? treeNode.getDegreeId().toString() : "");
                    tree.putExtra("sort", treeNode.getSort());
                    tree.putExtra("delFlag", treeNode.getDelFlag());
                    tree.putExtra("createTime", treeNode.getCreateTime());
                    tree.putExtra("degreeName", treeNode.getDegreeName());
                    tree.putExtra("bookCount", treeNode.getBookCount());

                });
        return treeNodes;
    }

    @Override
    public List<DutpSchool> selectAcademyList(DutpSchool dutpSchool) {
        return dutpSchoolMapper.selectAcademyBySchool(dutpSchool);
    }

    /**
     * 修改学院
     *
     * @param dutpSchool
     * @return
     */
    @Override
    public boolean updateAcademy(DutpSchool dutpSchool) {
        if (ObjectUtil.isNotEmpty(dutpSchool)) {
            LambdaQueryWrapper<DutpSchool> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DutpSchool::getSchoolCode, dutpSchool.getSchoolCode());
            List<DutpSchool> dutpSchools = dutpSchoolMapper.selectList(wrapper);
            if (!dutpSchools.isEmpty() && !dutpSchools.get(0).getSchoolId().equals(dutpSchool.getSchoolId())) {
                throw new ServiceException("院系编码不能重复");
            }
        }
        return this.updateById(dutpSchool);
    }

    /**
     * 添加学院
     *
     * @param dutpSchool
     * @return
     */
    @Override
    public boolean addAcademy(DutpSchool dutpSchool) {
        if (ObjectUtil.isNotEmpty(dutpSchool)) {
            String schoolCode = dutpSchool.getSchoolCode();
            LambdaQueryWrapper<DutpSchool> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DutpSchool::getSchoolCode, schoolCode);
            wrapper.eq(DutpSchool::getDelFlag, 0);
            wrapper.eq(DutpSchool::getDataType, dutpSchool.getDataType());
            wrapper.eq(DutpSchool::getParentId, dutpSchool.getParentId());
            List<DutpSchool> dutpSchools = dutpSchoolMapper.selectList(wrapper);
            if (dutpSchools.size() > 0) {
                throw new ServiceException("院系编码不能重复");
            }
        }
        return this.save(dutpSchool);
    }

    @Override
    public boolean addSubject(DutpSchool dutpSchool) {
        // 校验
        LambdaQueryWrapper<DutpSchool> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DutpSchool::getSchoolCode, dutpSchool.getSchoolCode());
        wrapper.eq(DutpSchool::getDelFlag, 0);
        wrapper.eq(DutpSchool::getDataType, dutpSchool.getDataType());
        wrapper.eq(DutpSchool::getParentId, dutpSchool.getParentId());

        List<DutpSchool> dutpSchools = dutpSchoolMapper.selectList(wrapper);
        if (!dutpSchools.isEmpty()) {
            throw new ServiceException("专业编码不能重复");
        }
        return this.save(dutpSchool);
    }

    /**
     * 修改专业
     *
     * @param dutpSchool
     * @return
     */
    @Override
    public boolean updateSubject(DutpSchool dutpSchool) {
        // 校验
        LambdaQueryWrapper<DutpSchool> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DutpSchool::getSchoolCode, dutpSchool.getSchoolCode());
        List<DutpSchool> dutpSchools = dutpSchoolMapper.selectList(wrapper);
        if (!dutpSchools.isEmpty() && !dutpSchools.get(0).getSchoolId().equals(dutpSchool.getSchoolId())) {
            throw new ServiceException("专业编码不能重复");
        }
        return this.updateById(dutpSchool);
    }

    /**
     * 查询专业列表
     *
     * @param dutpSchool
     * @return
     */
    @Override
    public List<DutpSchool> selectSubjectList(DutpSchool dutpSchool) {
        return dutpSchoolMapper.selectSubjectList(dutpSchool);
    }

    @Override
    public List<Tree<String>> getAllWithOutLast(DutpSchool dutpSchool) {
        QueryWrapper<DutpSchool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DutpSchool::getDelFlag, 0)
                .ne(DutpSchool::getDataType, 2);
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setChildrenKey("children");
        List<DutpSchool> list = dutpSchoolMapper.selectList(queryWrapper);
        // 格式化树形结构
        List<Tree<String>> treeNodes = TreeUtil.build(list, "0", treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getSchoolId().toString());
                    tree.setParentId(treeNode.getParentId().toString());
                    tree.setName(treeNode.getSchoolName());
                    tree.setWeight(treeNode.getSort());

                    tree.putExtra("schoolId", treeNode.getSchoolId().toString());
                    tree.putExtra("schoolName", treeNode.getSchoolName());
                    tree.putExtra("schoolCode", treeNode.getSchoolCode());
                    tree.putExtra("dataType", treeNode.getDataType());
                    tree.putExtra("logoUrl", treeNode.getLogoUrl());
                    tree.putExtra("isPartner", treeNode.getIsPartner());
                    tree.putExtra("degreeId", treeNode.getDegreeId());
                    tree.putExtra("sort", treeNode.getSort());
                    tree.putExtra("delFlag", treeNode.getDelFlag());
                    tree.putExtra("createTime", treeNode.getCreateTime());
                    tree.putExtra("degreeName", treeNode.getDegreeName());
                    tree.putExtra("bookCount", treeNode.getBookCount());

                });
        return treeNodes;
    }

    @Override
    public List<DutpSchoolVo> getHomePartnerList(Integer limit) {
        List<DutpSchoolVo> result = new ArrayList<>();
        LambdaQueryWrapper<DutpSchool> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DutpSchool::getParentId, 0)
                .eq(DutpSchool::getIsPartner, 2);
        if (limit != -1) {
            lambdaQueryWrapper.last(" limit " + limit);
        }
        List<DutpSchool> dbSchools = this.list(lambdaQueryWrapper);
        for (DutpSchool dbSchool : dbSchools) {
            DutpSchoolVo dutpSchoolVo = new DutpSchoolVo();
            BeanUtil.copyProperties(dbSchool, dutpSchoolVo);
            result.add(dutpSchoolVo);
        }
        return result;
    }

    @Override
    public List<DutpSchool> listNoPageOfBook(DutpSchool dutpSchool) {
        List<DutpSchool> list = dutpSchoolMapper.listNoPageOfBook(dutpSchool);
        return list;
    }

    @Override
    public String addAcademyList(List<DutpSchool> list) {
        if (StringUtils.isNull(list) || list.size() == 0) {
            throw new ServiceException("导入院系管理数据不能为空");
        }

        int successNum = 0;
        int failNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failMsg = new StringBuilder();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long schoolId = loginUser.getSysUser().getSchoolId();

        try {
            for (int i = 0; i < list.size(); i++) {
                DutpSchool school = list.get(i);

                if (StringUtils.isBlank(list.get(i).getSchoolName())) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败,院系名称不能为空!";
                    failMsg.append(msg);
                    continue;
                }
                if (StringUtils.isBlank(list.get(i).getSchoolCode())) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败,院系编码不能为空!";
                    failMsg.append(msg);
                    continue;
                }
                LambdaQueryWrapper<DutpSchool> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(DutpSchool::getSchoolCode, school.getSchoolCode()).eq(DutpSchool::getParentId,schoolId);
                List<DutpSchool> existingCoder = this.list(queryWrapper);
                if (existingCoder.size() > 0) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败, 院系编码已存在!";
                    failMsg.append(msg);
                    continue;
                }
                LambdaQueryWrapper<DutpSchool> Wrapper = new LambdaQueryWrapper<>();
                Wrapper.eq(DutpSchool::getSchoolName, school.getSchoolName()).eq(DutpSchool::getParentId,schoolId);;
                List<DutpSchool> existingSchoolName = this.list(Wrapper);
                if (existingSchoolName.size() > 0) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败, 院系名称已存在!";
                    failMsg.append(msg);
                    continue;
                }
                try {
                    list.get(i).setCreateTime(new Date());
                    this.save(list.get(i));
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、第" + (i + 1) + "行 导入成功");
                    failMsg.append("<br/>" + successNum + "、第" + (i + 1) + "行 导入成功");
                } catch (Exception e) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败";
                    failMsg.append(msg + e.getMessage());
                    log.error(msg, e);
                }
            }

        } catch (NullPointerException e) {
            failMsg.insert(0, "很抱歉,导入失败! 请确定所有字段都已填写!");
            return failMsg.toString();
        }
        if (failNum > 0) {
            failMsg.insert(0, "共" + list.size() + "条数据,其中" + successNum + "条导入成功," + failNum + "条导入失败，错误如下：");
            return failMsg.toString();
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
            return successMsg.toString();
        }
    }

    @Override
    public List<Tree<String>> selectByParentId(DutpSchool dutpSchool) {
        QueryWrapper<DutpSchool> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DutpSchool::getParentId, dutpSchool.getParentId())
                .eq(DutpSchool::getDelFlag, 0);
        List<DutpSchool> list = dutpSchoolMapper.selectList(queryWrapper);
        list.stream().forEach(e -> {
            e.setParentId(0L);
        });
        if (!CollectionUtils.isEmpty(list)) {
            List<Long> ids = list.stream().map(DutpSchool::getSchoolId).collect(Collectors.toList());
            queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(DutpSchool::getParentId, ids)
                    .eq(DutpSchool::getDelFlag, 0);
            List<DutpSchool> childList = dutpSchoolMapper.selectList(queryWrapper);
            list.addAll(childList);
        }
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setChildrenKey("children");
        // 格式化树形结构
        List<Tree<String>> treeNodes = TreeUtil.build(list, "0", treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getSchoolId().toString());
                    tree.setParentId(treeNode.getParentId().toString());
                    tree.setName(treeNode.getSchoolName());

                    tree.putExtra("schoolId", treeNode.getSchoolId().toString());
                    tree.putExtra("schoolName", treeNode.getSchoolName());
                    tree.putExtra("schoolCode", treeNode.getSchoolCode());
                });
        return treeNodes;
    }

    /**
     * 查询专业列表（用于下拉框）
     *
     * @param dutpSchool
     */
    @Override
    public List<DutpSchool> selectSubject(DutpSchool dutpSchool) {
        LambdaQueryWrapper<DutpSchool> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DutpSchool::getParentId,dutpSchool.getSchoolId()).eq(DutpSchool::getDataType,2).eq(DutpSchool::getDelFlag, 0);
        return dutpSchoolMapper.selectList(wrapper);
    }

    @Override
    public List<DutpSchool> selectAcademyListNopage(DutpSchool dutpSchool) {
        return dutpSchoolMapper.selectAcademyBySchoolNoPage(dutpSchool);
    }

    @Override
    public List<DutpAcademyDto> selectAcademyListExport(DutpSchool dutpSchool) {
        return dutpSchoolMapper.selectAcademyExport(dutpSchool);
    }

    @Override
    public List<DutpSchool> selectAcademyListNopageEdu(DutpSchool dutpSchool) {
        return dutpSchoolMapper.selectEduAcademyList(dutpSchool);
    }

    @Override
    public String addSubjectList(List<DutpSchool> list) {
        if (StringUtils.isNull(list) || list.size() == 0) {
            throw new ServiceException("导入专业管理数据不能为空");
        }

        int successNum = 0;
        int failNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failMsg = new StringBuilder();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long schoolId = loginUser.getSysUser().getSchoolId();

        try {
            for (int i = 0; i < list.size(); i++) {
                DutpSchool school = list.get(i);

                if (StringUtils.isBlank(list.get(i).getSubjectName())) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败,专业名称不能为空!";
                    failMsg.append(msg);
                    continue;
                }
                if (StringUtils.isBlank(list.get(i).getSchoolCode())) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败,专业编码不能为空!";
                    failMsg.append(msg);
                    continue;
                }
                if (StringUtils.isBlank(list.get(i).getSchoolName())) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败,所属院系不能为空!";
                    failMsg.append(msg);
                    continue;
                }
                //专业编码是否存在
                LambdaQueryWrapper<DutpSchool> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(DutpSchool::getParentId, schoolId).eq(DutpSchool::getDataType,1).eq(DutpSchool::getDelFlag, 0);
                List<DutpSchool> existingAcademy = this.list(queryWrapper);
                    LambdaQueryWrapper<DutpSchool> subjectWrapper = new LambdaQueryWrapper<>();
                    subjectWrapper.eq(DutpSchool::getSchoolCode,school.getSchoolCode()).eq(DutpSchool::getDelFlag, 0).eq(DutpSchool::getDataType,2).in(DutpSchool::getParentId,
                            existingAcademy.stream().map(DutpSchool::getSchoolId).collect(Collectors.toList()));
                if (this.count(subjectWrapper) > 0) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败, 专业编码已存在!";
                    failMsg.append(msg);
                    continue;
                }

                //学院不存在
                Long academyId = dutpSchoolMapper.selectAcademyIdBySchoolName(school.getSchoolName(), schoolId);
                if (ObjectUtil.isEmpty(academyId)) {
                        failNum++;
                        String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败, 院系名称不存在!";
                        failMsg.append(msg);
                        continue;
                }

                // 查询当前学校下的所有学院
                LambdaQueryWrapper<DutpSchool> academyQueryWrapper1 = new LambdaQueryWrapper<>();
                academyQueryWrapper1.eq(DutpSchool::getParentId, schoolId)
                        .eq(DutpSchool::getDataType, 1);
                List<DutpSchool> academyList1 = this.list(academyQueryWrapper1);

                // 检查专业名称在所有学院下是否已存在
                LambdaQueryWrapper<DutpSchool> subject = new LambdaQueryWrapper<>();
                subject.eq(DutpSchool::getSubjectName, school.getSubjectName())
                        .eq(DutpSchool::getDataType, 2).eq(DutpSchool::getDelFlag, 0)
                        .in(DutpSchool::getParentId,
                                academyList1.stream().map(DutpSchool::getSchoolId).collect(Collectors.toList()));

                if (this.count(subjectWrapper) > 0) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败, 专业名称已存在!";
                    failMsg.append(msg);
                    continue;
                }
                try {
                    list.get(i).setCreateTime(new Date());
                    list.get(i).setParentId(academyId);
                    list.get(i).setSchoolName(school.getSubjectName());
                    this.save(list.get(i));
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、第" + (i + 1) + "行 导入成功");
                    failMsg.append("<br/>" + successNum + "、第" + (i + 1) + "行 导入成功");
                } catch (Exception e) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败";
                    failMsg.append(msg + e.getMessage());
                    log.error(msg, e);
                }
            }

        } catch (NullPointerException e) {
            failMsg.insert(0, "很抱歉,导入失败! 请确定所有字段都已填写!");
            return failMsg.toString();
        }
        if (failNum > 0) {
            failMsg.insert(0, "共" + list.size() + "条数据,其中" + successNum + "条导入成功," + failNum + "条导入失败，错误如下：");
            return failMsg.toString();
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
            return successMsg.toString();
        }
    }

    private DutpDegree getDegreeByName(String degreeName) {
        QueryWrapper<DutpDegree> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DutpDegree::getDegreeName, degreeName)
                .eq(DutpDegree::getDelFlag, 0);
        return dutpDegreeMapper.selectOne(queryWrapper);
    }

}

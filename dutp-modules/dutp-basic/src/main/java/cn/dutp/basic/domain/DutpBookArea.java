package cn.dutp.basic.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("dutp_book_area")
public class DutpBookArea extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 专区id
     */
    @TableId(type = IdType.AUTO)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long areaId;

    /**
     * 专区分类名称
     */
    private String areaName;

    /**
     * 排序
     */
    private Integer sort;

    /**
     *
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableField(exist = false)
    private Long relationId;

    /**
     *
     */
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 教材名称
     */
    @Excel(name = "教材名称")
    @TableField(exist = false)
    private String bookName;

    @TableField(exist = false)
    private String authorLabel;

    @TableField(exist = false)
    private String authorValue;
    /**
     * 封面图片地址
     */
    @Excel(name = "封面图片地址")
    @TableField(exist = false)
    private String cover;

    /**
     * ISBN序列号
     */
    @Excel(name = "ISBN序列号")
    @TableField(exist = false)
    private String isbn;

    /**
     * ISSN序列号
     */
    @Excel(name = "ISSN序列号")
    @TableField(exist = false)
    private String issn;

    /**
     * 教材编号
     */
    @Excel(name = "教材编号")
    @TableField(exist = false)
    private String bookNo;

    /**
     * 出版日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出版日期", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField(exist = false)
    private Date publishDate;

    /**
     * 当前版本ID
     */
    @Excel(name = "当前版本ID")
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long currentVersionId;

    /**
     * 最新的版本ID 如果跟当前版本ID一致无修正
     */
    @Excel(name = "最新的版本ID 如果跟当前版本ID一致无修正")
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long lastVersionId;

    /**
     * 上架时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "上架时间", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField(exist = false)
    private Date shelfTime;

    /**
     * 下架时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "下架时间", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField(exist = false)
    private Date unshelfTime;

    /**
     * 出版单位
     */
    @Excel(name = "出版单位")
    @TableField(exist = false)
    private String publishOrganization;

    /**
     * 出版状态1未出版2已出版
     */
    @Excel(name = "出版状态1未出版2已出版")
    @TableField(exist = false)
    private Integer publishStatus;

    /**
     * 上架状态1已上架2未上架3召回4即将上架
     */
    @Excel(name = "上架状态1已上架2未上架3召回4即将上架")
    @TableField(exist = false)
    private Integer shelfState;

    /**
     * 学校id 0不是校本教材
     */
    @Excel(name = "学校id 0不是校本教材")
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolId;

    /**
     * 出版社ID
     */
    @Excel(name = "出版社ID")
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long houseId;

    /**
     * 教材类型,关联dtb_book_type
     */
    @Excel(name = "教材类型,关联dtb_book_type")
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookType;

    /**
     * 1其他2主教材3副教材
     */
    @Excel(name = "1其他2主教材3副教材")
    @TableField(exist = false)
    private Integer masterFlag;

    /**
     * 主教材ID
     */
    @Excel(name = "主教材ID")
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long masterBookId;

    /**
     * 销量
     */
    @Excel(name = "销量")
    @TableField(exist = false)
    private Long soldQuantity;

    /**
     * 阅读量
     */
    @Excel(name = "阅读量")
    @TableField(exist = false)
    private Long readQuantity;

    /**
     * 定价
     */
    @Excel(name = "定价")
    @TableField(exist = false)
    private BigDecimal priceCounter;

    /**
     * 售价
     */
    @Excel(name = "售价")
    @TableField(exist = false)
    private BigDecimal priceSale;

    /**
     * 1公开教材2校本教材
     */
    @Excel(name = "1公开教材2校本教材")
    @TableField(exist = false)
    private Integer bookOrganize;

    /**
     * 选题号
     */
    @Excel(name = "选题号")
    @TableField(exist = false)
    private String topicNo;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 主编
     */
    @Excel(name = "主编")
    @TableField(exist = false)
    private String editorInChief;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("relationId", getRelationId())
                .append("areaId", getAreaId())
                .append("areaName", getAreaName())
                .append("sort", getSort())
                .append("bookId", getBookId())
                .append("bookName", getBookName())
                .append("cover", getCover())
                .append("isbn", getIsbn())
                .append("issn", getIssn())
                .append("bookNo", getBookNo())
                .append("publishDate", getPublishDate())
                .append("currentVersionId", getCurrentVersionId())
                .append("lastVersionId", getLastVersionId())
                .append("shelfTime", getShelfTime())
                .append("unshelfTime", getUnshelfTime())
                .append("publishOrganization", getPublishOrganization())
                .append("publishStatus", getPublishStatus())
                .append("shelfState", getShelfState())
                .append("schoolId", getSchoolId())
                .append("houseId", getHouseId())
                .append("bookType", getBookType())
                .append("masterFlag", getMasterFlag())
                .append("masterBookId", getMasterBookId())
                .append("soldQuantity", getSoldQuantity())
                .append("readQuantity", getReadQuantity())
                .append("priceCounter", getPriceCounter())
                .append("priceSale", getPriceSale())
                .append("bookOrganize", getBookOrganize())
                .append("topicNo", getTopicNo())
                .append("delFlag", getDelFlag())
                .append("editorInChief", getEditorInChief())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

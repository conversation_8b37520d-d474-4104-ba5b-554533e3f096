package cn.dutp.basic.service;

import cn.dutp.basic.domain.DutpBookArea;
import cn.dutp.basic.domain.vo.DutpBookAreaVo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface IDutpBookAreaService extends IService<DutpBookArea> {

    /**
     * 查询专区类型列表
     * @param dutpBookArea 专区类型
     * @return 结果
     */
    public List<DutpBookArea> areaList (DutpBookArea dutpBookArea);

    /**
     * 学生教师端查询类型专区列表
     * @param dutpBookArea 专区类型
     * @return 结果
     */
    public List<DutpBookAreaVo> listEducation (DutpBookArea dutpBookArea);

    /**
     * 获取专区类型详情
     * @param areaId 专区id
     * @return 结果
     */
    public DutpBookArea getAreaByAreaId(Long areaId);

    /**
     * 新增专区类型信息
     * @param dutpBookArea 专区类型
     * @return 结果
     */
    public boolean addArea(DutpBookArea dutpBookArea);

    /**
     * 修改专区类型信息
     * @param dutpBookArea 专区类型
     * @return 结果
     */
    public boolean editArea(DutpBookArea dutpBookArea);

    /**
     * 通过areaId删除专区类型数据
     * @param areaId
     * @return
     */
    public boolean deleteArea(Long  areaId);

    String imports(List<DutpBookArea> list);
}

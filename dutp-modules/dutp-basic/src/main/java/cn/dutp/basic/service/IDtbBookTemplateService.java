package cn.dutp.basic.service;

import cn.dutp.basic.domain.DtbBookTemplate;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
/**
 * DUTP-DTB-029教材模板Service接口
 *
 * <AUTHOR>
 * @date 2024-10-30
 */
public interface IDtbBookTemplateService extends IService<DtbBookTemplate>
{
    /**
     * 查询DUTP-DTB-029教材模板
     *
     * @param templateId DUTP-DTB-029教材模板主键
     * @return DUTP-DTB-029教材模板
     */
    public DtbBookTemplate selectDtbBookTemplateByTemplateId(Long templateId);

    /**
     * 查询DUTP-DTB-029教材模板列表
     *
     * @param dtbBookTemplate DUTP-DTB-029教材模板
     * @return DUTP-DTB-029教材模板集合
     */
    public List<DtbBookTemplate> selectDtbBookTemplateList(DtbBookTemplate dtbBookTemplate);

    /**
     * 新增DUTP-DTB-029教材模板
     *
     * @param dtbBookTemplate DUTP-DTB-029教材模板
     * @return 结果
     */
    public boolean insertDtbBookTemplate(DtbBookTemplate dtbBookTemplate);

    /**
     * 修改DUTP-DTB-029教材模板
     *
     * @param dtbBookTemplate DUTP-DTB-029教材模板
     * @return 结果
     */
    public boolean updateDtbBookTemplate(DtbBookTemplate dtbBookTemplate);

    /**
     * 批量删除DUTP-DTB-029教材模板
     *
     * @param templateIds 需要删除的DUTP-DTB-029教材模板主键集合
     * @return 结果
     */
    public boolean deleteDtbBookTemplateByTemplateIds(List<Long> templateIds);

}

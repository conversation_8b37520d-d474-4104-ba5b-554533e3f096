package cn.dutp.basic.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.domain.DutpSubject;
import lombok.Data;

@Data
public class DutpSubjectExcel extends DutpSubject {


    /** 学科名称 */
    @Excel(name = "一级分类")
    private String topParentName;

    /** 学科名称 */
    @Excel(name = "二级分类")
    private String secondParentName;

    /** 学科名称 */
    @Excel(name = "三级分类")
    private String thirdParentName;

    /** 学科名称 */
    @Excel(name = "四级分类")
    private String lastName;

}

package cn.dutp.basic.controller;

import cn.dutp.basic.domain.DutpBanner;
import cn.dutp.basic.domain.DutpFriendLink;
import cn.dutp.basic.domain.DutpPublishingHouse;
import cn.dutp.basic.domain.vo.DutpBannerVo;
import cn.dutp.basic.domain.vo.DutpFriendLinkVo;
import cn.dutp.basic.domain.vo.DutpPublishingHouseVo;
import cn.dutp.basic.domain.vo.DutpSchoolVo;
import cn.dutp.basic.service.*;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.domain.DutpSubjectVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 友情链接
 *
 * @author: dutp
 * @date: 2024/10/28
 */
@RequestMapping("/openApi")
@RestController
public class DutpBasicOpenApiController extends BaseController {

    @Autowired
    private IDutpFriendLinkService friendLinkService;

    @Autowired
    private IDutpBannerService bannerService;

    @Autowired
    private IDutpSchoolService schoolService;

    @Autowired
    private IDutpSubjectService subjectService;

    @Autowired
    private IDutpPublishingHouseService publishingHouseService;

    @GetMapping("/friends")
    public AjaxResult friends(){
        List<DutpFriendLinkVo> list = friendLinkService.getHomeLinkList();
        return AjaxResult.success(list);
    }

    @GetMapping("/banners")
    public AjaxResult banners(DutpBanner banner){
        List<DutpBannerVo> list = bannerService.getHomeBannerList(banner);
        return AjaxResult.success(list);
    }

    /**
     * 获取合作院校
     * @param limit -1表示全部数据
     * @return
     */
    @GetMapping("/partners/{limit}")
    public AjaxResult partners(@PathVariable Integer limit){
        List<DutpSchoolVo> list = schoolService.getHomePartnerList(limit);
        return AjaxResult.success(list);
    }

    @GetMapping("/firstLevelSubject")
    public AjaxResult firstLevelSubject()
    {
        List<DutpSubjectVo> list = subjectService.selectCmsFirstSubjects();
        return AjaxResult.success(list);
    }
    @GetMapping("/houseList")
    public AjaxResult houseList()
    {
        List<DutpPublishingHouseVo> list = publishingHouseService.selectCmsHouseList();
        return AjaxResult.success(list);
    }


}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.basic.mapper.DutpDegreeMapper">
    
    <resultMap type="DutpDegree" id="DutpDegreeResult">
        <result property="degreeId"    column="degree_id"    />
        <result property="degreeName"    column="degree_name"    />
        <result property="sort"    column="sort"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDutpDegreeVo">
        select degree_id, degree_name, sort, del_flag, create_by, create_time, update_by, update_time from dutp_degree
    </sql>

    <select id="selectDutpDegreeList" parameterType="DutpDegree" resultMap="DutpDegreeResult">
        <include refid="selectDutpDegreeVo"/>
        <where>  
            <if test="degreeName != null  and degreeName != ''"> and degree_name like concat('%', #{degreeName}, '%')</if>
            <if test="sort != null "> and sort = #{sort}</if>
        </where>
    </select>
    
    <select id="selectDutpDegreeByDegreeId" parameterType="Long" resultMap="DutpDegreeResult">
        <include refid="selectDutpDegreeVo"/>
        where degree_id = #{degreeId}
    </select>

    <insert id="insertDutpDegree" parameterType="DutpDegree" useGeneratedKeys="true" keyProperty="degreeId">
        insert into dutp_degree
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="degreeName != null">degree_name,</if>
            <if test="sort != null">sort,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="degreeName != null">#{degreeName},</if>
            <if test="sort != null">#{sort},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDutpDegree" parameterType="DutpDegree">
        update dutp_degree
        <trim prefix="SET" suffixOverrides=",">
            <if test="degreeName != null">degree_name = #{degreeName},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where degree_id = #{degreeId}
    </update>

    <delete id="deleteDutpDegreeByDegreeId" parameterType="Long">
        delete from dutp_degree where degree_id = #{degreeId}
    </delete>

    <delete id="deleteDutpDegreeByDegreeIds" parameterType="String">
        delete from dutp_degree where degree_id in 
        <foreach item="degreeId" collection="array" open="(" separator="," close=")">
            #{degreeId}
        </foreach>
    </delete>
</mapper>
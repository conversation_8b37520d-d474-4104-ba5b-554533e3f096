<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.basic.mapper.DutpBannerMapper">
    
    <resultMap type="DutpBanner" id="DutpBannerResult">
        <result property="bannerId"    column="banner_id"    />
        <result property="bannerName"    column="banner_name"    />
        <result property="device"    column="device"    />
        <result property="bannerPosition"    column="banner_position"    />
        <result property="imageUrl"    column="image_url"    />
        <result property="urlType"    column="url_type"    />
        <result property="jumpUrl"    column="jump_url"    />
        <result property="sort"    column="sort"    />
        <result property="startDate"    column="start_date"    />
        <result property="endDate"    column="end_date"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDutpBannerVo">
        select banner_id, banner_name, device, banner_position, image_url, url_type, jump_url, sort, start_date, end_date, status, del_flag, create_by, create_time, update_by, update_time from dutp_banner
    </sql>

    <select id="selectDutpBannerList" parameterType="DutpBanner" resultMap="DutpBannerResult">
        <include refid="selectDutpBannerVo"/>
        <where>  
            <if test="bannerName != null  and bannerName != ''"> and banner_name like concat('%', #{bannerName}, '%')</if>
            <if test="device != null  and device != ''"> and device = #{device}</if>
            <if test="bannerPosition != null "> and banner_position = #{bannerPosition}</if>
            <if test="imageUrl != null  and imageUrl != ''"> and image_url = #{imageUrl}</if>
            <if test="urlType != null "> and url_type = #{urlType}</if>
            <if test="jumpUrl != null  and jumpUrl != ''"> and jump_url = #{jumpUrl}</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="startDate != null "> and start_date = #{startDate}</if>
            <if test="endDate != null "> and end_date = #{endDate}</if>
            <if test="status != null "> and status = #{status}</if>
             and del_flag = '0'
        </where>
    </select>
    
    <select id="selectDutpBannerByBannerId" parameterType="Long" resultMap="DutpBannerResult">
        <include refid="selectDutpBannerVo"/>
        where banner_id = #{bannerId}
    </select>

    <insert id="insertDutpBanner" parameterType="DutpBanner" useGeneratedKeys="true" keyProperty="bannerId">
        insert into dutp_banner
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bannerName != null">banner_name,</if>
            <if test="device != null">device,</if>
            <if test="bannerPosition != null">banner_position,</if>
            <if test="imageUrl != null">image_url,</if>
            <if test="urlType != null">url_type,</if>
            <if test="jumpUrl != null">jump_url,</if>
            <if test="sort != null">sort,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bannerName != null">#{bannerName},</if>
            <if test="device != null">#{device},</if>
            <if test="bannerPosition != null">#{bannerPosition},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="urlType != null">#{urlType},</if>
            <if test="jumpUrl != null">#{jumpUrl},</if>
            <if test="sort != null">#{sort},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDutpBanner" parameterType="DutpBanner">
        update dutp_banner
        <trim prefix="SET" suffixOverrides=",">
            <if test="bannerName != null">banner_name = #{bannerName},</if>
            <if test="device != null">device = #{device},</if>
            <if test="bannerPosition != null">banner_position = #{bannerPosition},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="urlType != null">url_type = #{urlType},</if>
            <if test="jumpUrl != null">jump_url = #{jumpUrl},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where banner_id = #{bannerId}
    </update>

    <delete id="deleteDutpBannerByBannerId" parameterType="Long">
        delete from dutp_banner where banner_id = #{bannerId}
    </delete>

    <delete id="deleteDutpBannerByBannerIds" parameterType="String">
        delete from dutp_banner where banner_id in 
        <foreach item="bannerId" collection="array" open="(" separator="," close=")">
            #{bannerId}
        </foreach>
    </delete>
</mapper>
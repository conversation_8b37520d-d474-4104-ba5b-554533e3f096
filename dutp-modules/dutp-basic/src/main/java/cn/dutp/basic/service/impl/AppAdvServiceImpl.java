package cn.dutp.basic.service.impl;

import cn.dutp.basic.domain.AppAdv;
import cn.dutp.basic.mapper.AppAdvMapper;
import cn.dutp.basic.service.IAppAdvService;
import cn.dutp.common.core.exception.ServiceException;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 移动端开屏图Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-17
 */
@Service
public class AppAdvServiceImpl extends ServiceImpl<AppAdvMapper, AppAdv> implements IAppAdvService
{
    @Autowired
    private AppAdvMapper appAdvMapper;

    /**
     * 查询移动端开屏图
     *
     * @param advId 移动端开屏图主键
     * @return 移动端开屏图
     */
    @Override
    public AppAdv selectAppAdvByAdvId(Long advId)
    {
        return this.getById(advId);
    }

    /**
     * 查询移动端开屏图列表
     *
     * @param appAdv 移动端开屏图
     * @return 移动端开屏图
     */
    @Override
    public List<AppAdv> selectAppAdvList(AppAdv appAdv)
    {
        LambdaQueryWrapper<AppAdv> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(appAdv.getAdvTitle())) {
                lambdaQueryWrapper.like(AppAdv::getAdvTitle
                ,appAdv.getAdvTitle());
            }
                    lambdaQueryWrapper.orderByAsc(AppAdv::getSort);

        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增移动端开屏图
     *
     * @param appAdv 移动端开屏图
     * @return 结果
     */
    @Override
    public boolean insertAppAdv(AppAdv appAdv)
    {
        LambdaQueryWrapper<AppAdv> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(ObjectUtil.isNotEmpty(appAdv.getAdvTitle())){
            lambdaQueryWrapper.eq(AppAdv::getAdvTitle,appAdv.getAdvTitle());
            List<AppAdv> list = this.list(lambdaQueryWrapper);
            if (list.size() > 0) {
                throw new ServiceException("广告名称不能重复");
            }
        }
        return this.save(appAdv);
    }

    /**
     * 修改移动端开屏图
     *
     * @param appAdv 移动端开屏图
     * @return 结果
     */
    @Override
    public boolean updateAppAdv(AppAdv appAdv)
    {
        LambdaQueryWrapper<AppAdv> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(ObjectUtil.isNotEmpty(appAdv.getAdvTitle())){
            lambdaQueryWrapper.eq(AppAdv::getAdvTitle,appAdv.getAdvTitle());
            List<AppAdv> list = this.list(lambdaQueryWrapper);
            if (list.size() > 0) {
                throw new ServiceException("广告名称不能重复");
            }
        }
        return this.updateById(appAdv);
    }

    /**
     * 批量删除移动端开屏图
     *
     * @param advIds 需要删除的移动端开屏图主键
     * @return 结果
     */
    @Override
    public boolean deleteAppAdvByAdvIds(List<Long> advIds)
    {
        return this.removeByIds(advIds);
    }

}

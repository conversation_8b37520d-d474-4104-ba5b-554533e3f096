package cn.dutp.basic.controller;

import cn.dutp.basic.domain.AppAdv;
import cn.dutp.basic.domain.DutpArticleType;
import cn.dutp.basic.domain.DutpSchool;
import cn.dutp.basic.service.IAppAdvService;
import cn.dutp.basic.service.IDutpArticleTypeService;
import cn.dutp.basic.service.IDutpSchoolService;
import cn.dutp.basic.service.IDutpSubjectService;
import cn.dutp.common.core.domain.R;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.domain.DutpSubject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * DUTP-DTB_002数字教材Controller
 *
 * <AUTHOR>
 * @date 2024-11-14
 */
@RestController
@RequestMapping("/openApi")
public class OpenApiController extends BaseController
{
    @Autowired
    private IDutpSubjectService dutpSubjectService;

    @Autowired
    private IDutpArticleTypeService dutpArticleTypeService;

    @Autowired
    private IDutpSchoolService dutpSchoolService;

    @Autowired
    private IAppAdvService appAdvService;

    /**
     * 查询移动端开屏图列表
     */
    @GetMapping("/advList")
    public TableDataInfo list(AppAdv appAdv)
    {
        startPage();
        List<AppAdv> list = appAdvService.selectAppAdvList(appAdv);
        return getDataTable(list);
    }

    /**
     * 学生教师端查询学科信息列表
     */
    @GetMapping("/listDutpSubjectTreeEducation")
    public TableDataInfo listDutpSubjectTreeEducation(DutpSubject dutpSubject)
    {
        List<DutpSubject> list = dutpSubjectService.selectDutpSubjectList(dutpSubject);
        return getDataTable(list);
    }

    /**
     * 学生教师端查询学科信息列表
     */
    @GetMapping("/listDutpSubjectEducation")
    public R<Map<Integer, List<DutpSubject>>> listDutpSubjectEducation(DutpSubject dutpSubject)
    {
        return  R.ok(dutpSubjectService.listEducation(dutpSubject));
    }

    /**
     * 查询学生教师端文章类型列表
     */
    @GetMapping("/listArticleTypeEducation")
    public TableDataInfo listArticleTypeEducation(DutpArticleType dutpArticleType)
    {
        List<DutpArticleType> list = dutpArticleTypeService.selectDutpArticleTypeList(dutpArticleType);
        return getDataTable(list);
    }

    /**
     * 查询学校管理列表-用户筛选类似合作院校
     */
    @GetMapping("/listSchoolByQuery")
    public TableDataInfo listSchoolByQuery(DutpSchool dutpSchool)
    {
        List<DutpSchool> list = dutpSchoolService.listSchoolByQuery(dutpSchool);
        return getDataTable(list);
    }
}

package cn.dutp.basic.controller;

import cn.dutp.basic.domain.DutpArticle;
import cn.dutp.basic.service.IDutpArticleService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 文章管理Controller
 *
 * <AUTHOR>
 * @date 2024-11-07
 */
@RestController
@RequestMapping("/article")
public class DutpArticleController extends BaseController
{
    @Autowired
    private IDutpArticleService dutpArticleService;

/**
 * 查询文章管理列表
 */
@RequiresPermissions("basic:article:list")
@GetMapping("/list")
    public TableDataInfo list(DutpArticle dutpArticle)
    {
        startPage();
        List<DutpArticle> list = dutpArticleService.selectDutpArticleList(dutpArticle);
        return getDataTable(list);
    }
    /**
     * 查询学生教师端文章管理列表
     */
    @GetMapping("/listEducation")
    public TableDataInfo listEducation(DutpArticle dutpArticle)
    {
        startPage();
        List<DutpArticle> list = dutpArticleService.selectDutpArticleList(dutpArticle);
        return getDataTable(list);
    }
    /**
     * 导出文章管理列表
     */
    @RequiresPermissions("basic:article:export")
    @Log(title = "文章管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DutpArticle dutpArticle)
    {
        List<DutpArticle> list = dutpArticleService.selectDutpArticleList(dutpArticle);
        ExcelUtil<DutpArticle> util = new ExcelUtil<DutpArticle>(DutpArticle.class);
        util.exportExcel(response, list, "文章管理数据");
    }

    /**
     * 获取文章管理详细信息
     */
    @RequiresPermissions("basic:article:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dutpArticleService.selectDutpArticleById(id));
    }

    /**
     * 新增文章管理
     */
    @RequiresPermissions("basic:article:add")
    @Log(title = "新增文章", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DutpArticle dutpArticle)
    {
        return toAjax(dutpArticleService.insertDutpArticle(dutpArticle));
    }

    /**
     * 修改文章管理
     */
    @RequiresPermissions("basic:article:edit")
    @Log(title = "编辑文章", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DutpArticle dutpArticle)
    {
        return toAjax(dutpArticleService.updateDutpArticle(dutpArticle));
    }

    /**
     * 删除文章管理
     */
    @RequiresPermissions("basic:article:remove")
    @Log(title = "删除文章", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dutpArticleService.deleteDutpArticleByIds(Arrays.asList(ids)));
    }
}

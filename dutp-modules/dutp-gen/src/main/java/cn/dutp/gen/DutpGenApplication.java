package cn.dutp.gen;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import cn.dutp.common.security.annotation.EnableCustomConfig;
import cn.dutp.common.security.annotation.EnableDutpFeignClients;

/**
 * 代码生成
 * 
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableDutpFeignClients
@SpringBootApplication
public class DutpGenApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(DutpGenApplication.class, args);
        System.out.println("代码生成模块启动成功");
    }
}

package ${packageName}.mapper;

import java.util.List;
import org.springframework.stereotype.Repository;
import ${packageName}.domain.${ClassName};
    #if($table.sub)
    import ${packageName}.domain.${subClassName};
    #end
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
/**
 * ${functionName}Mapper接口
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Repository
public interface ${ClassName}Mapper extends BaseMapper<${ClassName}>
{

}

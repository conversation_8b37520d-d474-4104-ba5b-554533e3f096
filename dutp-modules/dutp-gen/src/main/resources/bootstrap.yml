# Tomcat
server:
  port: 9202

# Spring
spring: 
  application:
    # 应用名称
    name: dutp-gen
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 192.168.0.222:8848
        namespace: 4063714a-1a13-4560-922a-f6bd1e0382ec
        group: DEFAULT_GROUP
      config:
        # 配置中心地址
        server-addr: 192.168.0.222:8848
        namespace: 4063714a-1a13-4560-922a-f6bd1e0382ec
        group: DEFAULT_GROUP
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}

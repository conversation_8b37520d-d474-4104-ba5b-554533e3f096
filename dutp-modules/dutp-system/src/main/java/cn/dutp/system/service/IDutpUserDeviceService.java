package cn.dutp.system.service;

import java.util.List;

import cn.dutp.system.domain.DutpUserDevice;
import cn.dutp.system.domain.vo.DutpUserDeviceVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * DUTP-BASE-019用户设备Service接口
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface IDutpUserDeviceService extends IService<DutpUserDevice> {

    /**
     * 查询记录数量，可查询（delete_flag=1）
     *
     * @param dutpUserDevice
     * @return int
     */
    public int getDutpUserDeviceNum(DutpUserDevice dutpUserDevice);

    /**
     * 查询DUTP-BASE-019用户设备列表
     *
     * @param dutpUserDevice DUTP-BASE-019用户设备
     * @return DUTP-BASE-019用户设备集合
     */
    public List<DutpUserDevice> selectDutpUserDeviceList(DutpUserDevice dutpUserDevice);

    public List<DutpUserDeviceVo> getDutpUserDeviceList(DutpUserDevice dutpUserDevice);


    /**
     * 新增DUTP-BASE-019用户设备
     *
     * @param dutpUserDevice DUTP-BASE-019用户设备
     * @return 结果
     */
    public DutpUserDevice insertDutpUserDevice(DutpUserDevice dutpUserDevice);

    /**
     * 修改DUTP-BASE-019用户设备
     *
     * @param dutpUserDevice DUTP-BASE-019用户设备
     * @return 结果
     */
    public boolean updateDutpUserDevice(DutpUserDevice dutpUserDevice);

    /**
     * 批量删除DUTP-BASE-019用户设备
     *
     * @param deviceIds 需要删除的DUTP-BASE-019用户设备主键集合
     * @return 结果
     */
    public boolean deleteDutpUserDeviceByDeviceIds(List<Long> deviceIds);


    /**
     * 修改DUTP-BASE-019用户设备
     *
     * @param dutpUserDevice DUTP-BASE-019用户设备
     * @return 结果
     */
    public boolean updateDutpUserDeviceDelFlag(DutpUserDevice dutpUserDevice);

    /**
     * 查询DUTP-BASE-019用户设备
     *
     * @param deviceId DUTP-BASE-019用户设备主键
     * @return DUTP-BASE-019用户设备
     */
    public DutpUserDevice selectDutpUserDeviceByDeviceId(Long deviceId);


    public List<DutpUserDevice> getDeviceList();

    Integer removeDevice(DutpUserDevice dutpUserDevice);
}

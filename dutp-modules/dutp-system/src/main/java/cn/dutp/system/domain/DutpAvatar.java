package cn.dutp.system.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 头像对象 dutp_avatar
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Data
@TableName("dutp_avatar")
public class DutpAvatar extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** $column.columnComment */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long avatarId;

    /** 头像地址 */
        @Excel(name = "头像地址")
    private String avatarUrl;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("avatarId", getAvatarId())
            .append("avatarUrl", getAvatarUrl())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
        .toString();
        }
        }

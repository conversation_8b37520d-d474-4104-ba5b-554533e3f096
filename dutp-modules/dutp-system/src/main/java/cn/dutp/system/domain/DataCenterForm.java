package cn.dutp.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.List;

@Data
public class DataCenterForm {
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolId;
    private String visitDateType; // 访问图表参数 day，week，month
    private Integer bookProcessStep;
    private Integer schoolBookProcessStep;
    private Integer teacherRankingLimit;
    private Integer studentRankingLimit;
    private String orderDateType;// 订单图表参数 day，week，month
    private List<String> logDate;
    private List<String> logMonth;
    private List<String> logYear;
    private Integer pageNum;
    private Integer pageSize;
    private Integer userType;
    private String orderDate;
    private String startOrderDate;
    private String endOrderDate;
    private String orderMonth;
    private String orderLastMonth;
    private Integer bookOrganize;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long topSubjectId, secondSubjectId, thirdSubjectId, forthSubjectId;
    private String startOrderMonth;
    private String endOrderMonth;
}

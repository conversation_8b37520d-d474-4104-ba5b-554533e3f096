package cn.dutp.system.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.system.domain.DutpUserBlackList;
import cn.dutp.system.service.IDutpUserBlackListService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 用户黑名单Controller
 *
 * <AUTHOR>
 * @date 2024-12-16
 */
@RestController
@RequestMapping("/list")
public class DutpUserBlackListController extends BaseController
{
    @Autowired
    private IDutpUserBlackListService dutpUserBlackListService;

    /**
     * 查询用户黑名单列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DutpUserBlackList dutpUserBlackList)
    {
        startPage();
        List<DutpUserBlackList> list = dutpUserBlackListService.selectDutpUserBlackListList(dutpUserBlackList);
        return getDataTable(list);
    }

    /**
     * 导出用户黑名单列表
     */
    @RequiresPermissions("system:list:export")
    @Log(title = "用户黑名单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DutpUserBlackList dutpUserBlackList)
    {
        List<DutpUserBlackList> list = dutpUserBlackListService.selectDutpUserBlackListList(dutpUserBlackList);
        ExcelUtil<DutpUserBlackList> util = new ExcelUtil<DutpUserBlackList>(DutpUserBlackList.class);
        util.exportExcel(response, list, "用户黑名单数据");
    }

    /**
     * 获取用户黑名单详细信息
     */
    @RequiresPermissions("system:list:query")
    @GetMapping(value = "/{blackListId}")
    public AjaxResult getInfo(@PathVariable("blackListId") Long blackListId)
    {
        return success(dutpUserBlackListService.selectDutpUserBlackListByBlackListId(blackListId));
    }

    /**
     * 获取禁止兑换黑名单详细信息
     */
    @GetMapping(value = "/getProhibitionOfExchange/{userId}")
    public AjaxResult getProhibitionOfExchange(@PathVariable("userId") Long userId)
    {
        return success(dutpUserBlackListService.getProhibitionOfExchange(userId));
    }

    /**
     * 新增禁止兑换黑名单详细信息
     */
    @Log(title = "新增禁止兑换黑名单详细信息", businessType = BusinessType.INSERT)
    @PostMapping("/addProhibitionOfExchange")
    public AjaxResult addProhibitionOfExchange(@RequestBody DutpUserBlackList dutpUserBlackList)
    {
        return toAjax(dutpUserBlackListService.addProhibitionOfExchange(dutpUserBlackList));
    }

    /**
     * 新增用户黑名单
     */
    @RequiresPermissions("system:list:add")
    @Log(title = "用户黑名单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DutpUserBlackList dutpUserBlackList)
    {
        return toAjax(dutpUserBlackListService.insertDutpUserBlackList(dutpUserBlackList));
    }

    /**
     * 修改用户黑名单
     */
    @RequiresPermissions("system:list:edit")
    @Log(title = "用户黑名单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DutpUserBlackList dutpUserBlackList)
    {
        return toAjax(dutpUserBlackListService.updateDutpUserBlackList(dutpUserBlackList));
    }

    /**
     * 删除用户黑名单
     */
    @RequiresPermissions("system:list:remove")
    @Log(title = "用户黑名单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{blackListIds}")
    public AjaxResult remove(@PathVariable Long[] blackListIds)
    {
        return toAjax(dutpUserBlackListService.deleteDutpUserBlackListByBlackListIds(Arrays.asList(blackListIds)));
    }
}

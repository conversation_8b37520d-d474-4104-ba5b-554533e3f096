package cn.dutp.system.service.impl;

import java.util.ArrayList;
import java.util.List;

import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.system.api.domain.DtbBookCodeExchangeLog;
import cn.dutp.system.api.model.LoginUser;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.seata.core.context.RootContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.system.mapper.DtbBookCodeExchangeLogMapper;
import cn.dutp.system.service.IDtbBookCodeExchangeLogService;

/**
 * 购书码兑换记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@Service
public class DtbBookCodeExchangeLogServiceImpl extends ServiceImpl<DtbBookCodeExchangeLogMapper, DtbBookCodeExchangeLog> implements IDtbBookCodeExchangeLogService
{
    @Autowired
    private DtbBookCodeExchangeLogMapper dtbBookCodeExchangeLogMapper;

    /**
     * 查询购书码兑换记录
     *
     * @param logId 购书码兑换记录主键
     * @return 购书码兑换记录
     */
    @Override
    public DtbBookCodeExchangeLog selectDtbBookCodeExchangeLogByLogId(Long logId)
    {
        return this.getById(logId);
    }

    /**
     * 查询购书码兑换记录列表
     *
     * @param dtbBookCodeExchangeLog 购书码兑换记录
     * @return 购书码兑换记录
     */
    @Override
    public List<DtbBookCodeExchangeLog> selectDtbBookCodeExchangeLogList(DtbBookCodeExchangeLog dtbBookCodeExchangeLog)
    {
        List<DtbBookCodeExchangeLog> res = new ArrayList<>();
        // 获取用户Id
        dtbBookCodeExchangeLog.setUserId(SecurityUtils.getUserId());
        LambdaQueryWrapper<DtbBookCodeExchangeLog> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbBookCodeExchangeLog.getUserId())) {
                lambdaQueryWrapper.eq(DtbBookCodeExchangeLog::getUserId
                ,dtbBookCodeExchangeLog.getUserId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookCodeExchangeLog.getBookId())) {
                lambdaQueryWrapper.eq(DtbBookCodeExchangeLog::getBookId
                ,dtbBookCodeExchangeLog.getBookId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookCodeExchangeLog.getCodeId())) {
                lambdaQueryWrapper.eq(DtbBookCodeExchangeLog::getCodeId
                ,dtbBookCodeExchangeLog.getCodeId());
            }
                if(ObjectUtil.isNotEmpty(dtbBookCodeExchangeLog.getExchangeDate())) {
                lambdaQueryWrapper.eq(DtbBookCodeExchangeLog::getExchangeDate
                ,dtbBookCodeExchangeLog.getExchangeDate());
            }
            if(ObjectUtil.isNotEmpty(dtbBookCodeExchangeLog.getExchangeDate())) {
                lambdaQueryWrapper.eq(DtbBookCodeExchangeLog::getExchangeDate, dtbBookCodeExchangeLog.getExchangeDate());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增购书码兑换记录
     *
     * @param dtbBookCodeExchangeLog 购书码兑换记录
     * @return 结果
     */
    @Override
    public boolean insertDtbBookCodeExchangeLog(DtbBookCodeExchangeLog dtbBookCodeExchangeLog)
    {
        return this.save(dtbBookCodeExchangeLog);
    }

    /**
     * 修改购书码兑换记录
     *
     * @param dtbBookCodeExchangeLog 购书码兑换记录
     * @return 结果
     */
    @Override
    public boolean updateDtbBookCodeExchangeLog(DtbBookCodeExchangeLog dtbBookCodeExchangeLog)
    {
        return this.updateById(dtbBookCodeExchangeLog);
    }

    /**
     * 批量删除购书码兑换记录
     *
     * @param logIds 需要删除的购书码兑换记录主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookCodeExchangeLogByLogIds(List<Long> logIds)
    {
        return this.removeByIds(logIds);
    }

}

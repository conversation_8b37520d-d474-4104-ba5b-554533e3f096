package cn.dutp.system.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.common.log.enums.OperatorType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.system.domain.DutpVisitLog;
import cn.dutp.system.service.IDutpVisitLogService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 系统访问日志Controller
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@RestController
@RequestMapping("/system/visitLog")
public class DutpVisitLogController extends BaseController
{
    @Autowired
    private IDutpVisitLogService dutpVisitLogService;

    /**
     * 查询系统访问日志列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DutpVisitLog dutpVisitLog)
    {
        startPage();
        List<DutpVisitLog> list = dutpVisitLogService.selectDutpVisitLogList(dutpVisitLog);
        return getDataTable(list);
    }

    /**
     * 导出系统访问日志列表
     */
    @RequiresPermissions("system:visitLog:export")
    @Log(title = "导出系统访问日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DutpVisitLog dutpVisitLog)
    {
        List<DutpVisitLog> list = dutpVisitLogService.selectDutpVisitLogList(dutpVisitLog);
        ExcelUtil<DutpVisitLog> util = new ExcelUtil<DutpVisitLog>(DutpVisitLog.class);
        util.exportExcel(response, list, "系统访问日志数据");
    }

    /**
     * 获取系统访问日志详细信息
     */
    @RequiresPermissions("system:visitLog:query")
    @GetMapping(value = "/{logId}")
    public AjaxResult getInfo(@PathVariable("logId") Long logId)
    {
        return success(dutpVisitLogService.selectDutpVisitLogByLogId(logId));
    }

    /**
     * 新增系统访问日志
     */
    @RequiresPermissions("system:visitLog:add")
    @PostMapping
    public AjaxResult add(@RequestBody DutpVisitLog dutpVisitLog)
    {
        return toAjax(dutpVisitLogService.insertDutpVisitLog(dutpVisitLog));
    }

    /**
     * 修改系统访问日志
     */
    @RequiresPermissions("system:visitLog:edit")
    @Log(title = "修改系统访问日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DutpVisitLog dutpVisitLog)
    {
        return toAjax(dutpVisitLogService.updateDutpVisitLog(dutpVisitLog));
    }

    /**
     * 删除系统访问日志
     */
    @RequiresPermissions("system:visitLog:remove")
    @Log(title = "删除系统访问日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{logIds}")
    public AjaxResult remove(@PathVariable Long[] logIds)
    {
        return toAjax(dutpVisitLogService.deleteDutpVisitLogByLogIds(Arrays.asList(logIds)));
    }
}

package cn.dutp.system.mapper;

import cn.dutp.system.api.domain.DutpOperLog;
import cn.dutp.system.api.domain.SysOperLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 操作日志 数据层
 * 
 * <AUTHOR>
 */
public interface SysOperLogMapper
{
    /**
     * 新增操作日志
     * 
     * @param operLog 操作日志对象
     */
    public int insertOperlog(SysOperLog operLog);

    /**
     * 查询系统操作日志集合
     * 
     * @param operLog 操作日志对象
     * @return 操作日志集合
     */
    public List<SysOperLog> selectOperLogList(SysOperLog operLog);

    /**
     * 查询系统操作日志集合,根据操作人id所在学校schoolid过滤
     *
     * @param operLog 操作日志对象
     * @return 操作日志集合
     */
    public List<SysOperLog> selectOperLogListWithOperatorId(SysOperLog operLog);

    /**
     * 批量删除系统操作日志
     * 
     * @param operIds 需要删除的操作日志ID
     * @return 结果
     */
    public int deleteOperLogByIds(Long[] operIds);

    /**
     * 查询操作日志详细
     * 
     * @param operId 操作ID
     * @return 操作日志对象
     */
    public SysOperLog selectOperLogById(Long operId);

    /**
     * 清空操作日志
     */
    public void cleanOperLog();

    /**
     * 插入前端用户操作日志
     * @param dutpOperLog
     * @return
     */
    int insertDutpOperlog(DutpOperLog dutpOperLog);

    /**
     * 根据操作人ID查询操作日志记录
     * @param operatorId 操作人ID
     * @return 日志记录
     */
    List<SysOperLog> selectOperLogListByOperatorId(@Param("operatorId") Long operatorId);

    /**
     * 根据操作人Name查询操作日志记录
     * @param operatorName 操作人ID
     * @return 日志记录
     */
    List<SysOperLog> selectOperLogListByOperatorName(@Param("operatorName") String operatorName);
}

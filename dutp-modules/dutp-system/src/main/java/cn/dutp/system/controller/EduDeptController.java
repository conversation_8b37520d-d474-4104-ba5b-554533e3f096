package cn.dutp.system.controller;

import cn.dutp.common.core.constant.UserConstants;
import cn.dutp.common.core.utils.StringUtils;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.log.enums.OperatorType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.system.api.domain.SysDept;
import cn.dutp.system.api.model.LoginUser;
import cn.dutp.system.service.ISysDeptService;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 机构信息
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/eduDept")
public class EduDeptController extends BaseController
{
    @Autowired
    private ISysDeptService deptService;

    /**
     * 获取机构列表
     */
    @RequiresPermissions("edu:dept:list")
    @GetMapping("/list")
    public AjaxResult list(SysDept dept)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long schoolId = loginUser.getSysUser().getSchoolId();
        dept.setSchoolId(schoolId);
        List<SysDept> depts = deptService.selectDeptList(dept);
        return success(depts);
    }

    /**
     * 查询机构列表（排除节点）
     */
    @RequiresPermissions("edu:dept:list")
    @GetMapping("/list/exclude/{deptId}")
    public AjaxResult excludeChild(@PathVariable(value = "deptId", required = false) Long deptId)
    {
        List<SysDept> depts = deptService.selectDeptList(new SysDept());
        depts.removeIf(d -> d.getDeptId().intValue() == deptId || ArrayUtils.contains(StringUtils.split(d.getAncestors(), ","), deptId + ""));
        return success(depts);
    }

    /**
     * 根据机构编号获取详细信息
     */
    @GetMapping(value = "/{deptId}")
    public AjaxResult getInfo(@PathVariable Long deptId)
    {
        deptService.checkDeptDataScope(deptId);
        return success(deptService.selectDeptById(deptId));
    }

    /**
     * 新增机构
     */
    @RequiresPermissions("edu:dept:add")
    @Log(title = "新增机构", businessType = BusinessType.INSERT, operatorType = OperatorType.EDU)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysDept dept)
    {
        if (!deptService.checkDeptNameUnique(dept))
        {
            return error("新增机构'" + dept.getDeptName() + "'失败，机构名称已存在");
        }
        dept.setCreateBy(SecurityUtils.getUsername());
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long schoolId = loginUser.getSysUser().getSchoolId();
        dept.setSchoolId(schoolId);
        return toAjax(deptService.insertDept(dept));
    }

    /**
     * 修改机构
     */
    @RequiresPermissions("edu:dept:edit")
    @Log(title = "编辑机构", businessType = BusinessType.UPDATE, operatorType = OperatorType.EDU)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysDept dept)
    {

        Long deptId = dept.getDeptId();
        deptService.checkDeptDataScope(deptId);
        if (!deptService.checkDeptNameUnique(dept))
        {
            return error("修改机构'" + dept.getDeptName() + "'失败，机构名称已存在");
        }
        else if (dept.getParentId().equals(deptId))
        {
            return error("修改机构'" + dept.getDeptName() + "'失败，上级机构不能是自己");
        }
        else if (StringUtils.equals(UserConstants.DEPT_DISABLE, dept.getStatus()) && deptService.selectNormalChildrenDeptById(deptId) > 0)
        {
            return error("该机构包含未停用的子机构！");
        }
        dept.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(deptService.updateDept(dept));
    }

    /**
     * 删除机构
     */
    @RequiresPermissions("edu:dept:remove")
    @Log(title = "删除机构", businessType = BusinessType.DELETE, operatorType = OperatorType.EDU)
    @DeleteMapping("/{deptId}")
    public AjaxResult remove(@PathVariable Long deptId)
    {
        if (deptService.hasChildByDeptId(deptId))
        {
            return warn("存在下级机构,不允许删除");
        }
        if (deptService.checkDeptExistUser(deptId))
        {
            return warn("机构存在用户,不允许删除");
        }
        deptService.checkDeptDataScope(deptId);
        return toAjax(deptService.deleteDeptById(deptId));
    }

}

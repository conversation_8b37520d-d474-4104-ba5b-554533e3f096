package cn.dutp.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.Date;

/**
 * DUTP-BASE-019用户设备对象 dutp_user_device
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DutpUserDeviceVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long deviceId;

    /**
     * 1-PC,2-ANDROID,3-IOS,4其他
     */
    private Integer deviceType;

    /**
     * 设备唯一ID，PC设备的MAC地址，移动端的deviceId等
     */
    private String deviceUniqueId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 最后登录时间
     */
    // @JsonFormat(pattern = "yyyy-MM-dd")
    private Date lastLoginDate;

    /**
     * 用户ID dutp_user里userid
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("deviceId", getDeviceId())
                .append("deviceType", getDeviceType())
                .append("deviceUniqueId", getDeviceUniqueId())
                .append("deviceName", getDeviceName())
                .append("lastLoginDate", getLastLoginDate())
                .append("userId", getUserId())
                .append("delFlag", getDelFlag())
                .toString();
    }
}

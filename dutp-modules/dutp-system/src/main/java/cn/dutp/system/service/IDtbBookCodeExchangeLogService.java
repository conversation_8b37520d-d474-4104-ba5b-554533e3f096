package cn.dutp.system.service;

import java.util.List;

import cn.dutp.system.api.domain.DtbBookCodeExchangeLog;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 购书码兑换记录Service接口
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
public interface IDtbBookCodeExchangeLogService extends IService<DtbBookCodeExchangeLog>
{
    /**
     * 查询购书码兑换记录
     *
     * @param logId 购书码兑换记录主键
     * @return 购书码兑换记录
     */
    public DtbBookCodeExchangeLog selectDtbBookCodeExchangeLogByLogId(Long logId);

    /**
     * 查询购书码兑换记录列表
     *
     * @param dtbBookCodeExchangeLog 购书码兑换记录
     * @return 购书码兑换记录集合
     */
    public List<DtbBookCodeExchangeLog> selectDtbBookCodeExchangeLogList(DtbBookCodeExchangeLog dtbBookCodeExchangeLog);

    /**
     * 新增购书码兑换记录
     *
     * @param dtbBookCodeExchangeLog 购书码兑换记录
     * @return 结果
     */
    public boolean insertDtbBookCodeExchangeLog(DtbBookCodeExchangeLog dtbBookCodeExchangeLog);

    /**
     * 修改购书码兑换记录
     *
     * @param dtbBookCodeExchangeLog 购书码兑换记录
     * @return 结果
     */
    public boolean updateDtbBookCodeExchangeLog(DtbBookCodeExchangeLog dtbBookCodeExchangeLog);

    /**
     * 批量删除购书码兑换记录
     *
     * @param logIds 需要删除的购书码兑换记录主键集合
     * @return 结果
     */
    public boolean deleteDtbBookCodeExchangeLogByLogIds(List<Long> logIds);

}

package cn.dutp.system.mapper;

import cn.dutp.system.domain.SysMenu;
import cn.dutp.system.domain.vo.SysUserShortcutVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 菜单表 数据层
 *
 * <AUTHOR>
 */
@Repository
public interface SysMenuMapper {
    /**
     * 查询系统菜单列表
     *
     * @param menu 菜单信息
     * @return 菜单列表
     */
    public List<SysMenu> selectMenuList(SysMenu menu);

    /**
     * 根据用户所有权限
     *
     * @return 权限列表
     */
    public List<String> selectMenuPerms();

    /**
     * 根据用户查询系统菜单列表
     *
     * @param menu 菜单信息
     * @return 菜单列表
     */
    public List<SysMenu> selectMenuListByUserId(SysMenu menu);

    /**
     * 根据角色ID查询权限
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    public List<String> selectMenuPermsByRoleId(Long roleId);

    /**
     * 根据用户ID查询权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    public List<String> selectMenuPermsByUserId(Long userId);

    /**
     * 根据用户ID查询菜单
     *
     * @return 菜单列表
     */
    public List<SysMenu> selectMenuTreeAll(@Param("menuRole") Integer menuRole);

    /**
     * 根据用户ID查询菜单
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    public List<SysMenu> selectMenuTreeByUserId(Long userId);

    /**
     * 根据角色ID查询菜单树信息
     *
     * @param roleId            角色ID
     * @param menuCheckStrictly 菜单树选择项是否关联显示
     * @return 选中菜单列表
     */
    public List<Long> selectMenuListByRoleId(@Param("roleId") Long roleId, @Param("menuCheckStrictly") boolean menuCheckStrictly, @Param("menuRole") Integer menuRole);

    /**
     * 根据菜单ID查询信息
     *
     * @param menuId 菜单ID
     * @return 菜单信息
     */
    public SysMenu selectMenuById(Long menuId);

    /**
     * 是否存在菜单子节点
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    public int hasChildByMenuId(Long menuId);

    /**
     * 新增菜单信息
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public int insertMenu(SysMenu menu);

    /**
     * 修改菜单信息
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public int updateMenu(SysMenu menu);

    /**
     * 删除菜单管理信息
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    public int deleteMenuById(Long menuId);

    /**
     * 校验菜单名称是否唯一
     *
     * @param menuName 菜单名称
     * @param parentId 父菜单ID
     * @return 结果
     */
    public SysMenu checkMenuNameUnique(@Param("menuName") String menuName, @Param("parentId") Long parentId, @Param("menuRole") Integer menuRole);


    List<SysMenu> selectEduMenuListByUserId(SysMenu menu);
    /**
     * 根据用户id查询当前权限下系统菜单id列表
     *
     * @param userId 用户ID
     * @return 菜单id List
     */
    List<Long> findMenuListByUserId(@Param("userId") Long userId);

    /**
     * 根据用户当前权限下系统菜单id列表 查询一级快捷菜单列表
     * @param menuList 统菜单id列表
     * @return 快捷菜单列表
     */
    List<SysMenu> findParentShortcutListByUserId(@Param("menuList") List<Long> menuList, @Param("userId") Long userId);

    /**
     * 根据一级快捷菜单id，查询对应的二级菜单
     *
     * @param menuList 统菜单id列表
     * @return 快捷菜单列表
     */
    List<SysMenu> findChildShortcutListByParentId(@Param("menuList") List<Long> menuList, @Param("userId") Long userId, @Param("parentId") Long parentId);


    /**
     * 查询当前用户可设置为快捷菜单的菜单的一级菜单
     *
     * @param menuList 权限下系统菜单id列表
     * @return 可设置为快捷菜单的菜单的一级菜单
     */
    List<SysUserShortcutVo> findParentMenuListByUserId(@Param("menuList") List<Long> menuList);

    /**
     * 查询一级菜单下可设置为快捷菜单的菜单的子菜单
     *
     * @param parentId 一级菜单
     * @param menuList 权限下系统菜单id列表
     * @return 可设置为快捷菜单的菜单的子菜单
     */
    List<SysUserShortcutVo> findChildrenMenuListByUserId(@Param("parentId") Long parentId, @Param("menuList") List<Long> menuList);
}

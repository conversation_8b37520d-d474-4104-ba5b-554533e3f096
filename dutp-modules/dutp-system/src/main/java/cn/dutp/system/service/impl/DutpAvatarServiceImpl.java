package cn.dutp.system.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.system.mapper.DutpAvatarMapper;
import cn.dutp.system.domain.DutpAvatar;
import cn.dutp.system.service.IDutpAvatarService;

/**
 * 头像Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Service
public class DutpAvatarServiceImpl extends ServiceImpl<DutpAvatarMapper, DutpAvatar> implements IDutpAvatarService
{
    @Autowired
    private DutpAvatarMapper dutpAvatarMapper;

    /**
     * 查询头像
     *
     * @param avatarId 头像主键
     * @return 头像
     */
    @Override
    public DutpAvatar selectDutpAvatarByAvatarId(Long avatarId)
    {
        return this.getById(avatarId);
    }

    /**
     * 查询头像列表
     *
     * @param dutpAvatar 头像
     * @return 头像
     */
    @Override
    public List<DutpAvatar> selectDutpAvatarList(DutpAvatar dutpAvatar)
    {
        LambdaQueryWrapper<DutpAvatar> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dutpAvatar.getAvatarUrl())) {
                lambdaQueryWrapper.eq(DutpAvatar::getAvatarUrl
                ,dutpAvatar.getAvatarUrl());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增头像
     *
     * @param dutpAvatar 头像
     * @return 结果
     */
    @Override
    public boolean insertDutpAvatar(DutpAvatar dutpAvatar)
    {
        return this.save(dutpAvatar);
    }

    /**
     * 修改头像
     *
     * @param dutpAvatar 头像
     * @return 结果
     */
    @Override
    public boolean updateDutpAvatar(DutpAvatar dutpAvatar)
    {
        return this.updateById(dutpAvatar);
    }

    /**
     * 批量删除头像
     *
     * @param avatarIds 需要删除的头像主键
     * @return 结果
     */
    @Override
    public boolean deleteDutpAvatarByAvatarIds(List<Long> avatarIds)
    {
        return this.removeByIds(avatarIds);
    }

}

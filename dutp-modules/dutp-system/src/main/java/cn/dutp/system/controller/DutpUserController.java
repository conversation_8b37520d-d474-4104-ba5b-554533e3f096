package cn.dutp.system.controller;

import cn.dutp.api.common.constant.DutpConstant;
import cn.dutp.api.common.utils.RSAUtils;
import cn.dutp.common.core.domain.R;
import cn.dutp.common.core.utils.StringUtils;
import cn.dutp.common.core.utils.bean.BeanUtils;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.InnerAuth;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.common.sms.utils.AliyunSmsUtil;
import cn.dutp.system.api.domain.DutpUser;
import cn.dutp.system.api.domain.DutpUserWithCode;
import cn.dutp.system.api.domain.SysUser;
import cn.dutp.system.api.model.LoginDutpUser;
import cn.dutp.system.api.model.LoginUser;
import cn.dutp.system.service.IDutpUserService;
import cn.dutp.system.service.ISysConfigService;
import cn.dutp.system.service.ISysPermissionService;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * DUTP-BASE-001用户【教师，学生，无身份者】Controller
 *
 * <AUTHOR>
 * @date 2024-10-21
 */
@RestController
@RequestMapping("/dutpUser")
public class DutpUserController extends BaseController
{
    @Autowired
    private IDutpUserService dutpUserService;
    @Autowired
    private ISysPermissionService permissionService;
    @Autowired
    private ISysConfigService configService;
    @Autowired
    private RSAUtils rsaUtils;
    /**
     * 获取当前用户信息
     */
    @InnerAuth
    @GetMapping("/info/{username}")
    public R<LoginDutpUser> info(@PathVariable("username") String username)
    {
        DutpUser dutpUser = dutpUserService.selectDutpUserByUserName(username);
        if (StringUtils.isNull(dutpUser))
        {
            return R.fail("用户名或密码错误");
        }
        SysUser sysUser = new SysUser();
        BeanUtils.copyProperties(dutpUser, sysUser);
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(sysUser);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(sysUser);
        LoginDutpUser dutpUserVo = new LoginDutpUser();
        dutpUserVo.setDutpUser(dutpUser);
        dutpUserVo.setRoles(roles);
        dutpUserVo.setPermissions(permissions);
        return R.ok(dutpUserVo);
    }
    /**
     * 发送验证码
     */
    @PostMapping("/sendAliyun")
    public R<Object> sendAliyun(@RequestBody Map<String, String> request) {
//        String bookIdStr = rsaUtils.decrypt(request.get("phone"));
        String code = RandomUtil.randomNumbers(4);
        // 发送验证码
        AliyunSmsUtil.sendSMSCode(request.get("phone"),"code",code,"SMS_314725755", 5);
        return R.ok();
    }
    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo()
    {
        DutpUser user = dutpUserService.selectUserById(SecurityUtils.getUserId());
        if(ObjectUtil.isNotNull(user)){
            SysUser sysUser = new SysUser();
            BeanUtils.copyProperties(user, sysUser);
            // 角色集合
            Set<String> roles = permissionService.getRolePermission(sysUser);
            // 权限集合
            Set<String> permissions = permissionService.getMenuPermission(sysUser);
            AjaxResult ajax = AjaxResult.success();
            ajax.put("user", user);
            ajax.put("roles", roles);
            ajax.put("permissions", permissions);
            return ajax;
        }
        return AjaxResult.success();
    }
    /**
     * 获取当前用户信息
     */
    @InnerAuth
    @GetMapping("/infoByTel/{userTel}")
    public R<LoginUser> infoByTel(@PathVariable("userTel") String phoneNumber)
    {
        DutpUser user = dutpUserService.selectUserByPhoneNumber(phoneNumber);
        LoginUser sysUserVo = new LoginUser();
        SysUser sysUser = new SysUser();
        if(ObjectUtil.isNotNull(user)){
            BeanUtils.copyProperties(user, sysUser);
            if (ObjectUtil.isNull(sysUser))
            {
                return R.fail("此电话还未注册用户！");
            }
            // 角色集合
            Set<String> roles = permissionService.getRolePermission(sysUser);
            // 权限集合
            Set<String> permissions = permissionService.getMenuPermission(sysUser);
            sysUserVo.setSysUser(sysUser);
            sysUserVo.setRoles(roles);
            sysUserVo.setPermissions(permissions);
            return R.ok(sysUserVo);
        }else{

            return R.fail("此电话还未注册用户！");
        }
    }
    /**
     * 注册用户信息
     */
    @InnerAuth
    @PostMapping("/register")
    public R<Boolean> register(@RequestBody DutpUser dutpUser)
    {
        String username = dutpUser.getUserName();
        if (!("true".equals(configService.selectConfigByKey("sys.account.registerUser"))))
        {
            return R.fail("当前系统没有开启注册功能！");
        }
        if (!dutpUserService.checkUserNameUnique(dutpUser))
        {
            return R.fail("保存用户'" + username + "'失败，注册账号已存在");
        }
        return R.ok(dutpUserService.registerUser(dutpUser));
    }

    /**
     * 忘记密码
     */
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PostMapping("/forgotPassword")
    public AjaxResult forgotPassword(@RequestBody DutpUserWithCode user)
    {
        dutpUserService.checkUserAllowed(user);
        return toAjax(dutpUserService.forgetPwd(user));
    }

    /*
     * 获取用户列表 用户下拉
     */
    @GetMapping("/listNotPage")
    public AjaxResult listNotPage(DutpUser user)
    {
        List<DutpUser> list = dutpUserService.listNotPage(user);
        return success(list);
    }

    /**
     * 根据教材id和学校id查询教材绑定的用户列表
     * @param user
     * @return
     */
    @PostMapping("/listUserByBookIdAndSchoolId")
    public AjaxResult listUserByBookIdAndSchoolId(@RequestBody DutpUser user) {
        return success(dutpUserService.listUserByBookIdAndSchoolId(user));
    }
    /**
     * 学生教师端校验新用户
     */
    @GetMapping("/newUserVerification")
    public boolean newUserVerification()
    {
        DutpUser user = dutpUserService.selectUserById(SecurityUtils.getUserId());
        return SecurityUtils.matchesPassword(DutpConstant.INITIAL_PASSWORD, user.getPassword());
    }

    /**
     * 根据学院查询学院下用户（用于下拉）
     */
    @GetMapping("/getUserBySchoolId")
    public AjaxResult getUserBySchoolId(DutpUser user) {
        return success(dutpUserService.getUserBySchoolId(user));
    }

}

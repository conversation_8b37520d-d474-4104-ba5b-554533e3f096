package cn.dutp.system.service.impl;

import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.system.api.domain.DtbBookCodeExchangeLog;
import cn.dutp.system.api.model.LoginUser;
import cn.dutp.system.domain.DataCenterForm;
import cn.dutp.system.domain.vo.*;
import cn.dutp.system.mapper.DataCenterMapper;
import cn.dutp.system.mapper.DtbBookCodeExchangeLogMapper;
import cn.dutp.system.service.IDataCenterService;
import cn.dutp.system.service.IDtbBookCodeExchangeLogService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 购书码兑换记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@Service
public class DataCenterServiceImpl implements IDataCenterService
{
   @Autowired
   private DataCenterMapper dataCenterMapper;
    @Override
    public BookDataVo getBookData(DataCenterForm dataCenterForm) {
        BookDataVo dataVo = dataCenterMapper.getBookData(dataCenterForm);
        return dataVo;
    }

    @Override
    public List<BookStepDataVo> getBookStepData(DataCenterForm dataCenterForm) {
        List<BookStepDataVo> result = dataCenterMapper.getBookStepData(dataCenterForm);
        return result;
    }

    @Override
    public List<BookRankingVo> getBookRankingData(DataCenterForm dataCenterForm) {
        List<BookRankingVo> result = dataCenterMapper.getBookRankingData(dataCenterForm);
        return result;
    }

    @Override
    public List<OrderDataVo> getOrderData(DataCenterForm dataCenterForm) {
        List<OrderDataVo> result = new ArrayList<>();
        LocalDate localToday = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        if ("day".equals(dataCenterForm.getOrderDateType())) {
            dataCenterForm.setOrderDate(localToday.format(formatter));
            OrderDataVo orderDataVoToday = dataCenterMapper.getOrderData(dataCenterForm);
            result.add(orderDataVoToday);
            LocalDate localYesterday = localToday.plusDays(-1);
            dataCenterForm.setOrderDate(localYesterday.format(formatter));
            OrderDataVo orderDataVoYesterday = dataCenterMapper.getOrderData(dataCenterForm);
            result.add(orderDataVoYesterday);
           // 周统计
        } else if ("week".equals(dataCenterForm.getOrderDateType())) {
            localToday = localToday.plusDays(1);
            dataCenterForm.setOrderDate(null);
            LocalDate localWeekday = localToday.plusDays(-7);
            dataCenterForm.setStartOrderDate(localWeekday.format(formatter));
            dataCenterForm.setEndOrderDate(localToday.format(formatter));
            OrderDataVo orderDataVoWeek = dataCenterMapper.getOrderData(dataCenterForm);
            result.add(orderDataVoWeek);
            LocalDate localLastWeekday = localToday.plusDays(-14);
            dataCenterForm.setStartOrderDate(localWeekday.format(formatter));
            dataCenterForm.setEndOrderDate(localLastWeekday.format(formatter));
            OrderDataVo orderDataVoLastWeek = dataCenterMapper.getOrderData(dataCenterForm);
            result.add(orderDataVoLastWeek);
        } else {
            dataCenterForm.setOrderDate(null);
            LocalDate localMonth = localToday.plusDays(-30);
            dataCenterForm.setStartOrderDate(localMonth.format(formatter));
            dataCenterForm.setEndOrderDate(localToday.format(formatter));
            OrderDataVo orderDataVoWeek = dataCenterMapper.getOrderData(dataCenterForm);
            result.add(orderDataVoWeek);
            LocalDate localLastMonth = localToday.plusDays(-60);
            dataCenterForm.setStartOrderDate(localMonth.format(formatter));
            dataCenterForm.setEndOrderDate(localLastMonth.format(formatter));
            OrderDataVo orderDataVoLastWeek = dataCenterMapper.getOrderData(dataCenterForm);
            result.add(orderDataVoLastWeek);
        }
        return result;
    }

    @Override
    public List<StepDataVo> getSubjectBookData(DataCenterForm dataCenterForm) {
        return dataCenterMapper.getSubjectBookData(dataCenterForm);
    }

    @Override
    public HashMap<String,Object> getOrderDataForChart(DataCenterForm dataCenterForm) {
        HashMap<String,Object> dataMap = new HashMap<>();
        List<OrderDataVo> orderDataVos;
        List<OrderDataVo> refundOrderDataVos;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        if ("day".equals(dataCenterForm.getOrderDateType())) {
            orderDataVos = dataCenterMapper.getOrderDateDataForChart(dataCenterForm);
            refundOrderDataVos = dataCenterMapper.getOrderRefundDataForChart(dataCenterForm);
            dataMap.put("orderData", orderDataVos);
            dataMap.put("refundOrderData", refundOrderDataVos);
            int totalRefundOrder = dataCenterMapper.getRefundTotalOrderQuantity(dataCenterForm);
            dataMap.put("totalRefundOrder", totalRefundOrder);
            LocalDate startDate = LocalDate.parse(dataCenterForm.getStartOrderDate());
            LocalDate endDate = LocalDate.parse(dataCenterForm.getEndOrderDate());
            List<String> orderXAxis = getDateRange(startDate, endDate, dataCenterForm.getOrderDateType());
            dataMap.put("orderXAxis",orderXAxis);
            String lastStartDate = startDate.plusDays(-30).format(formatter);
            String lastEndDate = endDate.plusDays(-30).format(formatter);
            dataCenterForm.setStartOrderDate(lastStartDate);
            dataCenterForm.setEndOrderDate(lastEndDate);
            orderDataVos = dataCenterMapper.getOrderDateDataForChart(dataCenterForm);
            refundOrderDataVos = dataCenterMapper.getOrderRefundDataForChart(dataCenterForm);
            dataMap.put("lastOrderData", orderDataVos);
            dataMap.put("lastRefundOrderData", refundOrderDataVos);
            totalRefundOrder = dataCenterMapper.getRefundTotalOrderQuantity(dataCenterForm);
            dataMap.put("lastTotalRefundOrder", totalRefundOrder);
        } else if ("month".equals(dataCenterForm.getOrderDateType())) {
            formatter = DateTimeFormatter.ofPattern("yyyy-MM");
            orderDataVos = dataCenterMapper.getOrderMonthDataForChart(dataCenterForm);
            refundOrderDataVos = dataCenterMapper.getOrderMonthRefundDataForChart(dataCenterForm);
            dataMap.put("orderData", orderDataVos);
            dataMap.put("refundOrderData", refundOrderDataVos);
            int totalRefundOrder = dataCenterMapper.getRefundTotalOrderQuantity(dataCenterForm);
            dataMap.put("totalRefundOrder", totalRefundOrder);
            LocalDate startMonth = LocalDate.parse(dataCenterForm.getStartOrderMonth());
            LocalDate endMonth = LocalDate.parse(dataCenterForm.getEndOrderMonth());
            List<String> orderXAxis = getDateRange(startMonth, endMonth, dataCenterForm.getOrderDateType());
            dataMap.put("orderXAxis",orderXAxis);
            String lastStartMonth = startMonth.plusDays(-30).format(formatter);
            String lastEndMonth = endMonth.plusDays(-30).format(formatter);
            dataCenterForm.setStartOrderMonth(lastStartMonth);
            dataCenterForm.setEndOrderMonth(lastEndMonth);
            orderDataVos = dataCenterMapper.getOrderMonthDataForChart(dataCenterForm);
            refundOrderDataVos = dataCenterMapper.getOrderMonthRefundDataForChart(dataCenterForm);
            dataMap.put("lastOrderData", orderDataVos);
            dataMap.put("lastRefundOrderData", refundOrderDataVos);
            totalRefundOrder = dataCenterMapper.getRefundTotalOrderQuantity(dataCenterForm);
            dataMap.put("lastTotalRefundOrder", totalRefundOrder);
        }
        return dataMap;
    }
    public static List<String> getDateRange(LocalDate startDate, LocalDate endDate, String dateType) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        if ("month".equals(dateType)) {
            formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        }
        List<String> dateList = new ArrayList<>();
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            dateList.add(currentDate.format(formatter));
            if ("month".equals(dateType)) {
                currentDate = currentDate.plusMonths(1);
            } else {
                currentDate = currentDate.plusDays(1);
            }
        }
        return dateList;
    }
}

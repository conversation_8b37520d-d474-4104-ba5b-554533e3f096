package cn.dutp.system.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * DUTP-DTB_012订单表
 *
 * @TableName dtb_book_order
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BookOrderRefundManagementVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 完成率
     */
    private Integer refundCompletionRate;
    /**
     * 总订单处理完成数
     */
    private Integer orderRefundTotal;
    /**
     * 总订单待办数
     */
    private Integer orderRefundToBeDoneTotal;

    /**
     * 售后待处理——教务订单数量
     */
    private Integer academicRefundToBeDoneNumber;

    /**
     * 售后待处理——零售订单数量
     */
    private Integer retailRefundToBeDoneNumber;
    /**
     * 我处理过的 教务订单成功退款订单数
     */
    private Integer academicOrderRefundNumber;
    /**
     * 我处理过的 成功退款商品数
     */
    private Integer academicRefundGoodsNumber;
    /**
     * 我处理过的 成功退款金额
     */
    private Integer academicRefundAmount;
    /**
     * 我处理过的 驳回订单数量
     */
    private Integer academicRefundRejectNumber;

    /**
     * 零售订单 成功退款订单数
     */
    private Integer retailOrderRefundNumber;
    /**
     * 成功退款商品数
     */
    private Integer retailRefundGoodsNumber;
    /**
     * 成功退款金额
     */
    private Integer retailRefundAmount;
    /**
     * 驳回订单数量
     */
    private Integer retailRefundRejectNumber;
    /**
     * 我处理过的
     */
    private Integer myHandleCount;

}
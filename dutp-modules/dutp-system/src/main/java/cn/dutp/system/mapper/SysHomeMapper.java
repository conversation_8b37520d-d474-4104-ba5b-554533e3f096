package cn.dutp.system.mapper;

import cn.dutp.system.domain.vo.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【dtb_book(DUTP-DTB_002数字教材)】的数据库操作Mapper
* @createDate 2025-01-16 17:07:31
* @Entity cn.dutp.system.domain.DtbBook
*/
@Repository
public interface SysHomeMapper extends BaseMapper<BookManagementVo> {
    /**
     * 我审核的教材
     *
     * @param userId 用户id
     * @return 审核数据
     */
    List<ResVo> myReviewTextbook(@Param("userId") Long userId,@Param("isAdmin") Boolean isAdmin);

    /**
     * 查询处理过的教材数量
     *
     * @return 处理过的教材数量
     */
    List<ResVo> textbookProcessing();

    /**
     * 查询待处理的教材，以及对应分类数量
     *
     * @return 待处理的教材
     */
    BookManagementVo textbooksToBeDoneNumber();

    /**
     * 我审核的订单
     *
     * @param userId 用户id
     * @return 审核的订单数据
     */
    BookOrderManagementMyReviewVo myReviewBookOrder(@Param("userId") Long userId);

    /**
     * 查询订单的处理完成数和待办数
     *
     * @return 处理完成数和待办数
     */
    BookOrderManagementVo bookOrderManagement(@Param("userId") Long userId);

    /**
     * 查询订单支付待确认数据，教务采购和其他采购两个维度
     *
     * @return 订单支付待确认数据
     */
    BookOrderPaymentVo bookOrderPayment();

    /**
     * 根据用户id，查询我审核的教务订单类型的 成功退款订单数，成功退款商品数，成功退款金额
     *
     * @param auditUserId 用户id
     * @return 成功退款订单数，成功退款商品数，成功退款金额
     */
    BookOrderRefundManagementMyReviewVo myReviewAcademicBookRefundOrder(@Param("auditUserId") Long auditUserId);

    /**
     * 根据用户id查询教务订单类型我驳回的售后订单数量
     *
     * @param auditUserId 用户id
     * @return 驳回的售后订单数量
     */
    BookOrderRefundManagementMyReviewVo academicRefundRejectOrder(@Param("auditUserId") Long auditUserId);

    /**
     * 根据用户id，查询我审核的零售订单类型的 成功退款订单数，成功退款商品数，成功退款金额
     *
     * @param auditUserId id用户
     * @return 成功退款订单数，成功退款商品数，成功退款金额
     */
    BookOrderRefundManagementMyReviewVo myReviewRetailBookRefundOrder(@Param("auditUserId") Long auditUserId);

    /**
     * 根据用户id查询零售订单类型我驳回的售后订单数量
     *
     * @param auditUserId 用户id
     * @return 驳回的售后订单数量
     */
    BookOrderRefundManagementMyReviewVo retailRefundRejectOrder(@Param("auditUserId") Long auditUserId);

    /**
     * 查询待处理的售后订单---待办数
     *
     * @return 待办数
     */
    BookOrderRefundManagementVo pendingRefundOrder();

    /**
     * 查询处理完成的售后订单---处理数
     *
     * @return 处理数
     */
    BookOrderRefundManagementVo processedCompletedRefundOrder(@Param("userId") Long userId);

    /**
     * 查询教务类型的订单的发票待上传数量和处理完成的数量
     *
     * @return 教务类型的订单的发票待上传数量和处理完成的数量
     */
    InvoiceApplyManagementVo selectAcademicInvoice(@Param("userId") Long userId);

    /**
     * 查询零售类型的订单的发票待上传数量和处理完成的数量
     *
     * @return 售类型的订单的发票待上传数量和处理完成的数量
     */
    InvoiceApplyManagementVo selectRetailInvoice();

    /**
     * 我审核的教务类型的成功上传发票和作废发票数量
     *
     * @param dealUserId 用户id
     * @return 上传发票和作废发票数量
     */
    InvoiceApplyManagementMyReviewVo selectmyReviewAcademicInvoice(@Param("dealUserId") Long dealUserId);

    /**
     * 我审核的零售类型的成功上传发票和作废发票数量
     *
     * @param dealUserId 用户id
     * @return 上传发票和作废发票数量
     */
    InvoiceApplyManagementMyReviewVo selectmyReviewRetailInvoice(@Param("dealUserId") Long dealUserId);

    /**
     * 查询教师申请的试用教材待审核数量
     *
     * @return 教师申请的试用教材待审核数量
     */
    DtbUserTrialApplyManagementVo selectTrialApply(@Param("auditUserId") Long auditUserId);

    /**
     * 根据用户id 查询我审核的教师申请的试用教材数量
     *
     * @param auditUserId 用户id
     * @return 我审核的教师申请的试用教材数量
     */
    DtbUserTrialApplyManagementVo selectMyReviewTrialApply(@Param("auditUserId") Long auditUserId);

    /**
     * 根据用户id 查询我处理过的用户反馈数量
     *
     * @param processUserId 用户id
     * @return 我处理过的用户反馈数量
     */
    DutpUserWorkOrderManagementVo selectMyReviewUserWorkOrderNumber(Long processUserId);

    /**
     * 待处理的用户反馈数
     *
     * @return 用户反馈数
     */
    DutpUserWorkOrderManagementVo selectUserWorkOrderNumber();

    /**
     * 客服回复——会话待开启数量
     *
     * @return 话待开启数量
     */
    DutpCustomerChatManagementVo selectCustomerChatNumber();

    /**
     * 发行库存预警：dtb_book_purchase_code状态state<8的教材。
     *
     * @return 教材库存信息
     */
    List<DtbBookPurchaseCodeManagementVo> selectBookPurchaseCode();
}





package cn.dutp.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import cn.dutp.system.api.domain.SysUser;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * 用户表 数据层
 * 
 * <AUTHOR>
 */
@Repository
public interface SysUserMapper
{
    /**
     * 根据条件分页查询用户列表
     * 
     * @param sysUser 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectUserList(SysUser sysUser);

    /**
     * 根据条件分页查询已配用户角色列表
     * 
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectAllocatedList(SysUser user);

    /**
     * 根据条件分页查询未分配用户角色列表
     * 
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectUnallocatedList(SysUser user);

    /**
     * 通过用户名查询用户
     * 
     * @param userName 用户名
     * @return 用户对象信息
     */
    public SysUser selectUserByUserName(String userName);

    /**
     * 通过用户电话查询用户
     *
     * @param phoneNumber 用户电话
     * @return 用户对象信息
     */
    public SysUser selectUserByUserPhone(String phoneNumber);

    /**
     * 通过用户ID查询用户
     * 
     * @param userId 用户ID
     * @return 用户对象信息
     */
    public SysUser selectUserById(Long userId);

    /**
     * 新增用户信息
     * 
     * @param user 用户信息
     * @return 结果
     */
    public int insertUser(SysUser user);

    /**
     * 修改用户信息
     * 
     * @param user 用户信息
     * @return 结果
     */
    public int updateUser(SysUser user);

    /**
     * 修改用户头像
     * 
     * @param userName 用户名
     * @param avatar 头像地址
     * @return 结果
     */
    public int updateUserAvatar(@Param("userName") String userName, @Param("avatar") String avatar);

    /**
     * 重置用户密码
     * 
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    public int resetUserPwd(@Param("userName") String userName, @Param("password") String password);

    /**
     * 通过用户ID删除用户
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteUserById(Long userId);

    /**
     * 批量删除用户信息
     * 
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    public int deleteUserByIds(Long[] userIds);

    /**
     * 校验用户名称是否唯一
     * 
     * @param userName 用户名称
     * @return 结果
     */
    public SysUser checkUserNameUnique(String userName);

    /**
     * 校验手机号码是否唯一
     *
     * @param phonenumber 手机号码
     * @return 结果
     */
    public SysUser checkPhoneUnique(String phonenumber);

    /**
     * 校验email是否唯一
     *
     * @param email 用户邮箱
     * @return 结果
     */
    public SysUser checkEmailUnique(String email);

    @Select("select school_id FROM dutp_school where school_name =#{schoolName}")
    Long selectIdBySchoolName(String houseName);

    @Select("SELECT house_id FROM dutp_publishing_house where house_name = #{houseName}")
    Long selectIdByHouseName(String houseName);

    SysUser selectEduUserByUserName(String username);

    SysUser selectEduUserByPhoneNumber(String phoneNumber);
    List<SysUser> listNotPage();

    List<SysUser> listUserByDeptId(@Param("deptId") Long deptId);

    @Select("SELECT password FROM sys_user WHERE user_id = #{userId}")
    String queryPassword(Long userId);

    @Select("SELECT\n" +
            "\tur.role_id,\n" +
            "\tu.* \n" +
            "FROM\n" +
            "\tsys_user AS u\n" +
            "\tINNER JOIN sys_user_role AS ur ON ur.user_id = u.user_id \n" +
            "WHERE\n" +
            "\t\n" +
            "\t u.STATUS = 0")
    List<SysUser> filterUserList();

    /**
     * 根据角色查询用户列表
     *
     * @param roleId 角色id
     * @return 结果
     */
    @Select("SELECT\n" +
            "            DISTINCT\n" +
            "            ur.user_id\n" +
            "        FROM\n" +
            "            sys_user u\n" +
            "                INNER JOIN sys_user_role ur ON ur.user_id = u.user_id\n" +
            "        WHERE\n" +
            "            u.del_flag = '0'\n" +
            "          AND u.STATUS = 0\n" +
            "          AND ur.role_id = #{roleId}")
    List<SysUser> getUserListByRoleId(Long roleId);

    /**
     * 根据菜单查询用户列表
     *
     * @param menuId 菜单id
     * @return 结果
     */
    List<SysUser> listUserByMenuId(Long menuId);
}

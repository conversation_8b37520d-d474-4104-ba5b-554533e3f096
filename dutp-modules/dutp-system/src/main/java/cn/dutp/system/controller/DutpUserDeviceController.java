package cn.dutp.system.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.system.domain.DutpUserDevice;
import cn.dutp.system.service.IDutpUserDeviceService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * DUTP-BASE-019用户设备Controller
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@RestController
@RequestMapping("/dutpUserDevice")
public class DutpUserDeviceController extends BaseController {
    @Autowired
    private IDutpUserDeviceService dutpUserDeviceService;

    /**
     * 查询DUTP-BASE-019用户设备列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DutpUserDevice dutpUserDevice) {
        startPage();
        List<DutpUserDevice> list = dutpUserDeviceService.selectDutpUserDeviceList(dutpUserDevice);
        return getDataTable(list);
    }

    /**
     * 导出DUTP-BASE-019用户设备列表
     */
    @RequiresPermissions("system:dutpUserDevice:export")
    @Log(title = "导出DUTP-BASE-019用户设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DutpUserDevice dutpUserDevice) {
        List<DutpUserDevice> list = dutpUserDeviceService.selectDutpUserDeviceList(dutpUserDevice);
        ExcelUtil<DutpUserDevice> util = new ExcelUtil<DutpUserDevice>(DutpUserDevice.class);
        util.exportExcel(response, list, "DUTP-BASE-019用户设备数据");
    }

    /**
     * 获取DUTP-BASE-019用户设备详细信息
     */
    @RequiresPermissions("system:dutpUserDevice:query")
    @GetMapping(value = "/{deviceId}")
    public AjaxResult getInfo(@PathVariable("deviceId") Long deviceId) {
        return success(dutpUserDeviceService.selectDutpUserDeviceByDeviceId(deviceId));
    }

    /**
     * 新增DUTP-BASE-019用户设备
     */
    // @RequiresPermissions("system:dutpUserDevice:add")
    @Log(title = "新增DUTP-BASE-019用户设备", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DutpUserDevice dutpUserDevice) {
        // return toAjax(dutpUserDeviceService.insertDutpUserDevice(dutpUserDevice));
        return AjaxResult.success(dutpUserDeviceService.insertDutpUserDevice(dutpUserDevice));
    }

    /**
     * 修改DUTP-BASE-019用户设备
     */
    @RequiresPermissions("system:dutpUserDevice:edit")
    @Log(title = "修改DUTP-BASE-019用户设备", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DutpUserDevice dutpUserDevice) {
        return toAjax(dutpUserDeviceService.updateDutpUserDevice(dutpUserDevice));
    }

    /**
     * 删除DUTP-BASE-019用户设备
     */
    @RequiresPermissions("system:dutpUserDevice:remove")
    @Log(title = "删除DUTP-BASE-019用户设备", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deviceIds}")
    public AjaxResult remove(@PathVariable Long[] deviceIds) {
        return toAjax(dutpUserDeviceService.deleteDutpUserDeviceByDeviceIds(Arrays.asList(deviceIds)));
    }
}

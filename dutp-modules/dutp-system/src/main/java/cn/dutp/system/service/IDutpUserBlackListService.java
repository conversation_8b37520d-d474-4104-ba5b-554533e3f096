package cn.dutp.system.service;

import java.util.List;
import cn.dutp.system.domain.DutpUserBlackList;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 用户黑名单Service接口
 *
 * <AUTHOR>
 * @date 2024-12-16
 */
public interface IDutpUserBlackListService extends IService<DutpUserBlackList>
{
    /**
     * 查询用户黑名单
     *
     * @param blackListId 用户黑名单主键
     * @return 用户黑名单
     */
    public DutpUserBlackList selectDutpUserBlackListByBlackListId(Long blackListId);

    /**
     * 获取禁止兑换黑名单详细信息
     *
     * @param userId 用户id
     * @return 用户黑名单
     */
    public DutpUserBlackList getProhibitionOfExchange(Long userId);

    /**
     * 查询用户黑名单列表
     *
     * @param dutpUserBlackList 用户黑名单
     * @return 用户黑名单集合
     */
    public List<DutpUserBlackList> selectDutpUserBlackListList(DutpUserBlackList dutpUserBlackList);

    /**
     * 新增用户黑名单
     *
     * @param dutpUserBlackList 用户黑名单
     * @return 结果
     */
    public boolean insertDutpUserBlackList(DutpUserBlackList dutpUserBlackList);

    /**
     * 新增禁止兑换黑名单详细信息
     *
     * @param dutpUserBlackList 用户黑名单
     * @return 结果
     */
    public boolean addProhibitionOfExchange(DutpUserBlackList dutpUserBlackList);

    /**
     * 修改用户黑名单
     *
     * @param dutpUserBlackList 用户黑名单
     * @return 结果
     */
    public boolean updateDutpUserBlackList(DutpUserBlackList dutpUserBlackList);

    /**
     * 批量删除用户黑名单
     *
     * @param blackListIds 需要删除的用户黑名单主键集合
     * @return 结果
     */
    public boolean deleteDutpUserBlackListByBlackListIds(List<Long> blackListIds);

}

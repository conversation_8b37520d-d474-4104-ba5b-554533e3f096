package cn.dutp.system.service;

import cn.dutp.system.domain.DataCenterForm;
import cn.dutp.system.domain.DutpVisitLog;
import cn.dutp.system.domain.vo.*;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.HashMap;
import java.util.List;

/**
 * 系统访问日志Service接口
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
public interface IDataCenterService
{
    BookDataVo getBookData(DataCenterForm dataCenterForm);

    List<BookStepDataVo> getBookStepData(DataCenterForm dataCenterForm);

    List<BookRankingVo> getBookRankingData(DataCenterForm dataCenterForm);

    List<OrderDataVo> getOrderData(DataCenterForm dataCenterForm);

    List<StepDataVo> getSubjectBookData(DataCenterForm dataCenterForm);

    HashMap<String,Object> getOrderDataForChart(DataCenterForm dataCenterForm);
}

package cn.dutp.system.domain;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * 移动端版本对象 dutp_app_version
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
@Data
@TableName("dutp_app_version")
public class DutpAppVersion extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 发布记录的唯一标识符
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long releaseId;

    /**
     * 移动端APP的名称
     */
    @Excel(name = "移动端APP的名称")
    private String appName;

    /**
     * 版本号
     */
    @Excel(name = "版本号")
    private String versionNumber;

    /**
     * 发布日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发布日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date releaseDate;

    /**
     * 发布说明
     */
    @Excel(name = "发布说明")
    private String releaseNotes;

    /**
     * 发布标题
     */
    @Excel(name = "发布标题")
    private String releaseTitle;

    /**
     * 下载链接
     */
    @Excel(name = "下载链接")
    private String downloadLink;

    /**
     * 发布平台('iOS', 'Android', 'Web', 'Other')
     */
    @Excel(name = "发布平台('iOS', 'Android', 'Web', 'Other')")
    private String platform;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remark;

    /**
     * 是否强制更新0不强制1强制
     */
    @Excel(name = "是否强制更新0不强制1强制")
    private Integer isForce;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("releaseId", getReleaseId())
                .append("appName", getAppName())
                .append("versionNumber", getVersionNumber())
                .append("releaseDate", getReleaseDate())
                .append("releaseNotes", getReleaseNotes())
                .append("releaseTitle", getReleaseTitle())
                .append("downloadLink", getDownloadLink())
                .append("platform", getPlatform())
                .append("isForce", getIsForce())
                .append("remark", getRemark())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

package cn.dutp.system.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * DUTP-DTB_012订单表
 *
 * @TableName dtb_book_order
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DtbUserTrialApplyManagementVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 待审核的试用教材总数
     */
    private Integer trialApplyTotal;
    /**
     * 我处理过的试用总数
     */
    private Integer myReviewTrialApplyTotal;


}
package cn.dutp.system.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.system.domain.DutpAvatar;
import cn.dutp.system.service.IDutpAvatarService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 头像Controller
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@RestController
@RequestMapping("/avatar")
public class DutpAvatarController extends BaseController
{
    @Autowired
    private IDutpAvatarService dutpAvatarService;

    /**
     * 查询头像列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DutpAvatar dutpAvatar)
    {
        startPage();
        List<DutpAvatar> list = dutpAvatarService.selectDutpAvatarList(dutpAvatar);
        return getDataTable(list);
    }

    /**
     * 导出头像列表
     */
    @RequiresPermissions("system:avatar:export")
    @Log(title = "导出头像", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DutpAvatar dutpAvatar)
    {
        List<DutpAvatar> list = dutpAvatarService.selectDutpAvatarList(dutpAvatar);
        ExcelUtil<DutpAvatar> util = new ExcelUtil<DutpAvatar>(DutpAvatar.class);
        util.exportExcel(response, list, "头像数据");
    }

    /**
     * 获取头像详细信息
     */
    @RequiresPermissions("system:avatar:query")
    @GetMapping(value = "/{avatarId}")
    public AjaxResult getInfo(@PathVariable("avatarId") Long avatarId)
    {
        return success(dutpAvatarService.selectDutpAvatarByAvatarId(avatarId));
    }

    /**
     * 新增头像
     */
    @RequiresPermissions("system:avatar:add")
    @Log(title = "新增头像", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DutpAvatar dutpAvatar)
    {
        return toAjax(dutpAvatarService.insertDutpAvatar(dutpAvatar));
    }

    /**
     * 修改头像
     */
    @RequiresPermissions("system:avatar:edit")
    @Log(title = "修改头像", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DutpAvatar dutpAvatar)
    {
        return toAjax(dutpAvatarService.updateDutpAvatar(dutpAvatar));
    }

    /**
     * 删除头像
     */
    @RequiresPermissions("system:avatar:remove")
    @Log(title = "删除头像", businessType = BusinessType.DELETE)
    @DeleteMapping("/{avatarIds}")
    public AjaxResult remove(@PathVariable Long[] avatarIds)
    {
        return toAjax(dutpAvatarService.deleteDutpAvatarByAvatarIds(Arrays.asList(avatarIds)));
    }
}

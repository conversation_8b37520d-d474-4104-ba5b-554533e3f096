package cn.dutp.system.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.validation.Validator;

import cn.dutp.common.core.enums.SysUserTypeEnum;
import cn.dutp.common.redis.service.RedisService;
import cn.dutp.common.sms.utils.AliyunSmsUtil;
import cn.dutp.system.api.domain.SysUserWithCode;
import cn.dutp.system.domain.DutpAiUserConfig;
import cn.dutp.system.domain.SysPost;
import cn.dutp.system.domain.SysUserPost;
import cn.dutp.system.domain.SysUserRole;
import cn.dutp.system.mapper.*;
import cn.dutp.system.service.ISysConfigService;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import cn.dutp.common.core.constant.UserConstants;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.utils.SpringUtils;
import cn.dutp.common.core.utils.StringUtils;
import cn.dutp.common.core.utils.bean.BeanValidators;
import cn.dutp.common.datascope.annotation.DataScope;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.system.api.domain.SysRole;
import cn.dutp.system.api.domain.SysUser;
import cn.dutp.system.service.ISysDeptService;
import cn.dutp.system.service.ISysUserService;

/**
 * 用户 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysUserServiceImpl implements ISysUserService {
    private static final Logger log = LoggerFactory.getLogger(SysUserServiceImpl.class);

    private static final RedisService redisService = SpringUtils.getBean(RedisService.class);

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private SysDeptMapper deptMapper;

    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SysPostMapper postMapper;

    @Autowired
    private SysUserRoleMapper userRoleMapper;

    @Autowired
    private SysUserPostMapper userPostMapper;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    protected Validator validator;

    @Autowired
    private DutpAiUserConfigMapper dutpAiUserConfigMapper;

    @Value("${spring.experimentCount}")
    private Integer experimentCount;

    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectUserList(SysUser user) {
        List<SysUser> users = userMapper.selectUserList(user);
        users.forEach(item -> {
            Long roleId = userRoleMapper.getRoleIdByUserId(item.getUserId());
            if (ObjectUtil.isNotEmpty(roleId)){
                if (roleId != 0l) {
                    item.setRoleId(roleId);
                }
            }
        });
        return users;
    }

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectAllocatedList(SysUser user) {
        return userMapper.selectAllocatedList(user);
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectUnallocatedList(SysUser user) {
        return userMapper.selectUnallocatedList(user);
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByUserName(String userName) {
        return userMapper.selectUserByUserName(userName);
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserById(Long userId) {
        return userMapper.selectUserById(userId);
    }

    /**
     * 查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(String userName) {
        List<SysRole> list = roleMapper.selectRolesByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysRole::getRoleName).collect(Collectors.joining(","));
    }

    /**
     * 查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserPostGroup(String userName) {
        List<SysPost> list = postMapper.selectPostsByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysPost::getPostName).collect(Collectors.joining(","));
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean checkUserNameUnique(SysUser user) {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkUserNameUnique(user.getUserName());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public boolean checkPhoneUnique(SysUser user) {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkPhoneUnique(user.getPhonenumber());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public boolean checkEmailUnique(SysUser user) {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkEmailUnique(user.getEmail());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    @Override
    public void checkUserAllowed(SysUser user) {
        if (StringUtils.isNotNull(user.getUserId()) && user.isAdmin()) {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    @Override
    public void checkUserDataScope(Long userId) {
        if (!SysUser.isAdmin(SecurityUtils.getUserId())) {
            SysUser user = new SysUser();
            user.setUserId(userId);
            List<SysUser> users = SpringUtils.getAopProxy(this).selectUserList(user);
            if (StringUtils.isEmpty(users)) {
                throw new ServiceException("没有权限访问用户数据！");
            }
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertUser(SysUser user) {

        user = transUserType(user);
        // 新增用户信息
        int rows = userMapper.insertUser(user);
        // 新增用户岗位关联
        insertUserPost(user);
        // 新增用户与角色管理
        insertUserRole(user);
        //新增试用次数
        saveDutpAiUserConfig(user.getUserId());
        return rows;
    }

    public void saveDutpAiUserConfig(Long userId){
        DutpAiUserConfig config = new DutpAiUserConfig();
        config.setUserType(1);
        config.setUserId(userId);
        config.setAiExperimentCount(experimentCount);
        dutpAiUserConfigMapper.insert(config);
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertEduUser(SysUser user) {
        // 新增用户信息
        int rows = userMapper.insertUser(user);
        // 新增用户岗位关联
        insertUserPost(user);
        // 新增用户与角色管理
        insertUserRole(user);
        return rows;
    }

    @Override
    public List<SysUser> filterUserList() {
        return userMapper.filterUserList();
    }

    @Override
    public List<SysUser> getUserListByRoleId(Long roleId) {
        return userMapper.getUserListByRoleId(roleId);
    }

    @Override
    public List<SysUser> listUserByMenuId(Long menuId) {
        return userMapper.listUserByMenuId(menuId);
    }

    /**
     * 转换用户类型
     *
     * @param user
     * @return
     */
    private SysUser transUserType(SysUser user) {
        Long[] roleIds = user.getRoleIds();
        if (ObjectUtil.isNotEmpty(roleIds)) {
            Long roleId = roleIds[0];
            // 教务
            if (roleId.longValue() == 5) {
                user.setUserType("01");
            } else {
                user.setUserType("00");
            }
        } else {
            user.setUserType("00");
        }
        return user;
    }

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean registerUser(SysUser user) {
        return userMapper.insertUser(user) > 0;
    }

    /**
     * 修改保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateUser(SysUser user) {
        if (user.getRoleId() != 5){
            user.setSchoolId(null);
            user.setSchoolName(null);
        }
        user = transUserType(user);
        Long userId = user.getUserId();
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 新增用户与角色管理
        insertUserRole(user);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPostByUserId(userId);
        // 新增用户与岗位管理
        insertUserPost(user);

        return userMapper.updateUser(user);
    }

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertUserAuth(Long userId, Long[] roleIds) {
        userRoleMapper.deleteUserRoleByUserId(userId);
        insertUserRole(userId, roleIds);
    }

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserStatus(SysUser user) {
        return userMapper.updateUser(user);
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean updateUserProfile(SysUser user) {
        return userMapper.updateUser(user) > 0;
    }

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar) {
        return userMapper.updateUserAvatar(userName, avatar) > 0;
    }

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int resetPwd(SysUser user) {
        return userMapper.updateUser(user);
    }


    /**
     * 获取登录用短信验证码
     *
     * @param phoneNumber 手机号
     */
    @Override
    public void getLoginSmsCode(String phoneNumber) {


        SysUser sysUser = userMapper.selectUserByUserPhone(phoneNumber);
        if (sysUser == null) {
            throw new ServiceException("用户不存在！");
        }

        // redis记一下这个号码调用时间
        String key = "sysUser:loginSms:" + phoneNumber;
        if (redisService.hasKey(key)) {
            throw new ServiceException("请勿频繁发送短信！");
        }


        // todo 此次因为只有一个模板，暂时写成一个模板，实际上应该和忘记密码的短信模板区分
        String code = RandomUtil.randomNumbers(4);
        AliyunSmsUtil.sendSMSCode(phoneNumber, "code", code, "SMS_314725755", 5);

        //记录发送时间
        redisService.setCacheObject(key, 1, 40L, TimeUnit.SECONDS);
    }


    /**
     * 获取短信验证码
     *
     * @param phoneNumber 手机号
     */
    @Override
    public void getSmsCode(String phoneNumber) {


        SysUser sysUser = userMapper.selectUserByUserPhone(phoneNumber);
        if (sysUser == null) {
            throw new ServiceException("用户不存在！");
        }

        // redis记一下这个号码调用时间
        String key = "sysUser:sms:" + phoneNumber;
        if (redisService.hasKey(key)) {
            throw new ServiceException("请勿频繁发送短信！");
        }

        String code = RandomUtil.randomNumbers(4);
        AliyunSmsUtil.sendSMSCode(phoneNumber, "code", code, "SMS_314725755", 5);

        //记录发送时间
        redisService.setCacheObject(key, 1, 40L, TimeUnit.SECONDS);
    }

    @Override
    public SysUser selectUserByPhoneNumber(String phoneNumber) {
        return userMapper.selectUserByUserPhone(phoneNumber);
    }

    @Override
    public List<SysUser> listNotPage() {
        return userMapper.listNotPage();
    }

    @Override
    public Boolean checkMatches(SysUser user) {
        String password = userMapper.queryPassword(SecurityUtils.getUserId());
        if (ObjectUtil.isEmpty(password)) {
            return false;
        }

        if (SecurityUtils.matchesPassword(user.getPassword(), password)) {
            return true;
        }

        return false;
    }

    /**
     * 教务用户
     *
     * @param username
     * @return
     */
    @Override
    public SysUser selectEduUserByUserName(String username) {
        return userMapper.selectEduUserByUserName(username);
    }

    @Override
    public SysUser selectEduUserByPhoneNumber(String phoneNumber) {
        return userMapper.selectEduUserByPhoneNumber(phoneNumber);
    }

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int forgetPwd(SysUserWithCode user) {

        SysUser sysUser = userMapper.selectUserByUserPhone(user.getPhonenumber());
        if (sysUser == null) {
            throw new ServiceException("用户不存在！");
        }

        Integer checkCode = AliyunSmsUtil.checkCode(user.getPhonenumber(), user.getCode(), "SMS_314725755");
        if (checkCode != 200) {
            throw new ServiceException("验证码输入错误！");
        }

        return userMapper.resetUserPwd(sysUser.getUserName(), user.getPassword());
    }

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(String userName, String password) {
        return userMapper.resetUserPwd(userName, password);
    }

    /**
     * 新增用户角色信息
     *
     * @param user 用户对象
     */
    public void insertUserRole(SysUser user) {
        this.insertUserRole(user.getUserId(), user.getRoleIds());
    }

    /**
     * 新增用户岗位信息
     *
     * @param user 用户对象
     */
    public void insertUserPost(SysUser user) {
        Long[] posts = user.getPostIds();
        if (StringUtils.isNotEmpty(posts)) {
            // 新增用户与岗位管理
            List<SysUserPost> list = new ArrayList<SysUserPost>();
            for (Long postId : posts) {
                SysUserPost up = new SysUserPost();
                up.setUserId(user.getUserId());
                up.setPostId(postId);
                list.add(up);
            }
            userPostMapper.batchUserPost(list);
        }
    }

    /**
     * 新增用户角色信息
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    public void insertUserRole(Long userId, Long[] roleIds) {
        if (StringUtils.isNotEmpty(roleIds)) {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<SysUserRole>();
            for (Long roleId : roleIds) {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                list.add(ur);
            }
            userRoleMapper.batchUserRole(list);
        }
    }


    public void insertEduUserRole(Long[] userIds, Long roleId) {

    }


    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserById(Long userId) {
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 删除用户与岗位表
        userPostMapper.deleteUserPostByUserId(userId);
        return userMapper.deleteUserById(userId);
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserByIds(Long[] userIds) {
        for (Long userId : userIds) {
            checkUserAllowed(new SysUser(userId));
            checkUserDataScope(userId);
        }
        // 删除用户与角色关联
        userRoleMapper.deleteUserRole(userIds);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPost(userIds);
        return userMapper.deleteUserByIds(userIds);
    }

    /**
     * 导入用户数据
     *
     * @param userList        用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    @Override
    public String importUser(List<SysUser> userList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(userList) || userList.size() == 0) {
            throw new ServiceException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (SysUser user : userList) {

            try {
                // 验证是否存在这个用户
                SysUser u = userMapper.selectUserByUserName(user.getUserName());
//                SysUser up = userMapper.selectUserByUserPhone(user.getPhonenumber());
                if (StringUtils.isEmpty(user.getUserName())) {
                    throw new ServiceException("用户名称不能为空");
                }
                if (StringUtils.isEmpty(user.getNickName())) {
                    throw new ServiceException("用户昵称不能为空");
                }
                if (StringUtils.isEmpty(user.getRoleName())) {
                    throw new ServiceException("角色不能为空");
                }
                if (StringUtils.isNull(u) ) {
                    BeanValidators.validateWithException(validator, user);
                    deptService.checkDeptDataScope(user.getDeptId());
                    String password = configService.selectConfigByKey("sys.user.initPassword");
                    user.setPassword(SecurityUtils.encryptPassword(password));
                    user.setCreateBy(operName);
                    String schoolName = user.getSchoolName();
                    String houseName = user.getHouseName();
                    //查看学校名称
                    Long schoolId = userMapper.selectIdBySchoolName(schoolName);
                    if (ObjectUtil.isEmpty(schoolId) && ObjectUtil.isNotEmpty(schoolName)) {
                        throw new ServiceException("当前导入学校名称" + user.getSchoolName() + "不存在");
                    }
                    //查看出版社是否存在
                    Long houseId = userMapper.selectIdByHouseName(houseName);
                    if (ObjectUtil.isEmpty(houseId) && ObjectUtil.isNotEmpty(user.getHouseName())) {
                        throw new ServiceException("当前导入出版社名称:" + user.getHouseName() + "不存在");
                    }
                    //查看角色是否存在
                    Long roleId = roleMapper.selectRoleByRoleName(user.getRoleName());
                    if (ObjectUtil.isEmpty(roleId)) {
                        throw new ServiceException("当前导入角色名称：" + user.getRoleName() + "不存在"  );
                    }
                    //查看部门是否存在
                    Long deptId = deptMapper.selectDeptByDeptName(user.getDeptName());
                    if (ObjectUtil.isEmpty(deptId) && ObjectUtil.isNotEmpty(user.getDeptName())) {
                        throw new ServiceException("当前导入部门名称：" + user.getDeptName() + "不存在"  );
                    }
                    //查看岗位是否存在
                    Long postId = postMapper.selectPostByPostName(user.getPostName());
                    if (ObjectUtil.isEmpty(postId) && ObjectUtil.isNotEmpty(user.getPostName())) {
                        throw new ServiceException("当前导入岗位名称：" + user.getPostName() + "不存在"  );
                    }
                    user.setDeptId(deptId);
                    user.setPassword(SecurityUtils.encryptPassword("123456"));
                    user.setStatus("0");
                    user.setSchoolId(schoolId);
                    user.setHouseId(houseId);
                    userMapper.insertUser(user);
                    //插入用户角色的中间表
                    Long userId = user.getUserId();
                    userRoleMapper.insert(userId,roleId);
                    //插入用户岗位中间表
                    if(ObjectUtil.isNotEmpty(postId)){
                        userPostMapper.insert(userId,postId);
                    }
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 导入成功");
                } else if (isUpdateSupport) {
                    if(ObjectUtil.isNotNull(u) && u.getUserType().equals(SysUserTypeEnum.SYS_USER_TYPE.getCode())) {
                        // 只更新用户名重复的
                        this.handleUserUpdate(user, u, operName);
                        successNum++;
                        successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 更新成功");
                    } else {
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、账号" + user.getUserName() + " 已存在");
                    }
//                    if(ObjectUtil.isNotEmpty(up) && up.getUserType().equals(SysUserTypeEnum.SYS_USER_TYPE.getCode())) {
//                        // 只更新用户名重复的
//                        this.handleUserUpdate(user, up, operName);
//                        successNum++;
//                        successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 更新成功");
//                    } else {
//                        failureNum++;
//                        failureMsg.append("<br/>" + failureNum + "、手机号" + user.getPhonenumber() + " 已存在");
//                    }
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账号/手机号 " + user.getUserName() + " 已存在");
                }
            }
            catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + user.getUserName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 教务，导入用户数据
     *
     * @param userList        用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importEduUserList(List<SysUser> userList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(userList) || userList.size() == 0) {
            throw new ServiceException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (SysUser user : userList) {

            try {
                // 验证是否存在这个用户（用户名，电话号，后台和教务在一起判断不允许重复)
                SysUser u = userMapper.selectUserByUserName(user.getUserName());
                SysUser up = userMapper.selectUserByUserPhone(user.getPhonenumber());
                if (StringUtils.isNull(u) && StringUtils.isNull(up)) {
                    BeanValidators.validateWithException(validator, user);
                    deptService.checkDeptDataScope(user.getDeptId());
                    String password = configService.selectConfigByKey("sys.user.initPassword");
                    user.setPassword(SecurityUtils.encryptPassword(password));
                    user.setCreateBy(operName);
                    user.setUserType(SysUserTypeEnum.EDU_USER_TYPE.getCode());
                    String schoolName = user.getSchoolName();
                    String houseName = user.getHouseName();
                    String deptName = user.getDeptName();
                    //
                    if(ObjectUtil.isNotEmpty(deptName)) {
                        Long deptId = deptMapper.selectIdByDeptName(deptName);
                        if (ObjectUtil.isNotEmpty(deptId)) {
                            user.setDeptId(deptId);
                        } else {
                            throw new ServiceException("部门名称不存在");
                        }
                    }

                    Long schoolId = 0L;
                    if(ObjectUtil.isNotEmpty(schoolName)) {
                        schoolId = userMapper.selectIdBySchoolName(schoolName);
                        if (ObjectUtil.isEmpty(schoolId) ) {
                            throw new ServiceException("学校名称不存在");
                        }
                    } else {
                        if (ObjectUtil.isNotEmpty(SecurityUtils.getLoginUser().getSchoolId())) {
                            schoolId = SecurityUtils.getLoginUser().getSchoolId();
                        }
                    }
                    user.setSchoolId(schoolId);
                    /* */
                    Long houseId = 0L;
                    if (ObjectUtil.isNotEmpty(houseName)) {
                        houseId = userMapper.selectIdByHouseName(houseName);
                        if (ObjectUtil.isEmpty(houseId)) {
                            throw new ServiceException("出版社名称不存在");
                        }
                    }
                    user.setHouseId(houseId);
                    userMapper.insertUser(user);
                    //
                    String roleName = user.getEduRoleName();
                    if (ObjectUtil.isNotEmpty(roleName)) {
                        SysRole role = new SysRole();
                        role.setRoleName(roleName);
                        role.setSchoolId(schoolId);
                        List<SysRole> roleList = roleMapper.selectRoleList(role);
                        if (!roleList.isEmpty() && roleList.size() > 0) {
                            List<SysUserRole> userRoleList = new ArrayList<SysUserRole>(1);
                            SysUserRole userRole = new SysUserRole();
                            userRole.setUserId(user.getUserId());
                            userRole.setRoleId(roleList.get(0).getRoleId());
                            userRoleList.add(userRole);
                            userRoleMapper.batchUserRole(userRoleList);
                        } else {
                            throw new ServiceException("角色名称不存在");
                        }
                    }
                    //
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 导入成功");
                } else if (isUpdateSupport) {
                    if(ObjectUtil.isNotEmpty(u) && u.getUserType().equals(SysUserTypeEnum.EDU_USER_TYPE.getCode())) {
                        // 只更新用户名重复的
                        this.handleUserUpdate(user, u, operName);
                        successNum++;
                        successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 更新成功");
                    } else {
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、账号 " + user.getUserName() + " 已存在");
                    }
                    if(ObjectUtil.isNotEmpty(up) && up.getUserType().equals(SysUserTypeEnum.EDU_USER_TYPE.getCode())) {
                        // 只更新用户名重复的
                        this.handleUserUpdate(user, up, operName);
                        successNum++;
                        successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 更新成功");
                    } else {
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、手机号" + user.getPhonenumber() + " 已存在");
                    }

                }
                else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账号 或 手机号" + user.getUserName() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + user.getUserName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }


    /**
     *  导入的user数据
     *  DB被更新的user数据
     * **/
    private void handleUserUpdate(SysUser inUser, SysUser updateUser, String operName) {
        BeanValidators.validateWithException(validator, inUser);
        checkUserAllowed(updateUser);
        checkUserDataScope(updateUser.getUserId());
        deptService.checkDeptDataScope(inUser.getDeptId());
        inUser.setUserId(updateUser.getUserId());
        inUser.setUpdateBy(operName);
        userMapper.updateUser(inUser);
    }

    /**
     * 根据部门id查询用户列表
     */
    @Override
    public List<SysUser> listUserByDeptId(Long deptId) {
        return userMapper.listUserByDeptId(deptId);
    }
}

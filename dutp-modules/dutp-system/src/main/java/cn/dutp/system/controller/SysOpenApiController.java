package cn.dutp.system.controller;

import cn.dutp.common.core.utils.ip.IpUtils;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.log.enums.OperatorType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.system.api.model.LoginUser;
import cn.dutp.system.domain.DutpAppVersion;
import cn.dutp.system.domain.DutpUserDevice;
import cn.dutp.system.domain.DutpVisitLog;
import cn.dutp.system.service.IDutpAppVersionService;
import cn.dutp.system.service.IDutpUserDeviceService;
import cn.dutp.system.service.IDutpVisitLogService;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 系统访问日志Controller
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@RestController
@RequestMapping("/openApi")
@Slf4j
public class SysOpenApiController extends BaseController
{
    @Autowired
    private IDutpVisitLogService dutpVisitLogService;

    @Autowired
    private IDutpUserDeviceService dutpUserDeviceService;

    @Autowired
    private IDutpAppVersionService dutpAppVersionService;


    /**
     * 新增系统访问日志
     */
    @PostMapping("/addVisitLog")
    public AjaxResult addVisitLog(HttpServletRequest request, @RequestBody DutpVisitLog dutpVisitLog)
    {
        String ipAddress = IpUtils.getIpAddr(request);
        log.error("获取IP地址" + ipAddress);
        String address = IpUtils.getIpPossessionByFile(ipAddress);
        dutpVisitLog.setIpAddress(ipAddress);
        dutpVisitLog.setAddress(address);
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (ObjectUtil.isNotNull(loginUser)) {
            dutpVisitLog.setUserId(loginUser.getUserid());
            dutpVisitLog.setSchoolId(loginUser.getSchoolId());
            dutpVisitLog.setUserType(loginUser.getHomeUserType());
        } else {
            dutpVisitLog.setUserType(0);
        }
        return toAjax(dutpVisitLogService.insertDutpVisitLog(dutpVisitLog));
    }

    /**
     * 新增用户设备登录信息
     */
    @PostMapping("/addDutpUserDevice")
    public AjaxResult add(@RequestBody DutpUserDevice dutpUserDevice)
    {
        long userId = SecurityUtils.getLoginUser().getUserid();
        if (userId != 0) {
            dutpUserDevice.setUserId(userId);
        }
        dutpUserDevice.setCreateTime(new Date());
        dutpUserDevice.setLastLoginDate(new Date());
        return AjaxResult.success(dutpUserDeviceService.insertDutpUserDevice(dutpUserDevice));
        // return toAjax(dutpUserDeviceService.insertDutpUserDevice(dutpUserDevice));
    }

    /**
     * 修改用户设备登录信息 delete状态
     */
    @PutMapping("/updateDutpUserDeviceDelFlag")
    public AjaxResult updateDutpUserDeviceDelFlag(@RequestBody DutpUserDevice dutpUserDevice) {
        // long userId = SecurityUtils.getUserId();
        long userId = SecurityUtils.getLoginUser().getUserid();
        if (userId != 0) {
            dutpUserDevice.setUserId(userId);
        }

        return toAjax(dutpUserDeviceService.updateDutpUserDeviceDelFlag(dutpUserDevice));
    }
    /**
     *  根据用户id查询用户设备信息列表
     */
    @GetMapping("/getDutpUserDeviceList")
    public AjaxResult getDutpUserDeviceList(DutpUserDevice dutpUserDevice) {
        // return AjaxResult.success(dutpUserDeviceService.selectDutpUserDeviceList(dutpUserDevice));
        LoginUser logUser = SecurityUtils.getLoginUser();
        if (!ObjectUtil.isNotEmpty(logUser)) {
            return AjaxResult.success(); // 未登录用户，无设备登陆信息
        } else {
            Long userId = logUser.getUserid();
            if (userId != 0) {
                dutpUserDevice.setUserId(userId);
            }
            return AjaxResult.success(dutpUserDeviceService.getDutpUserDeviceList(dutpUserDevice));
        }

    }

    /**
     *  移动端获取最新版本信息
     */
    @GetMapping("/getLastAppVersion")
    public AjaxResult getLastAppVersion(DutpAppVersion dutpAppVersion) {
        return AjaxResult.success(dutpAppVersionService.getLastAppVersion(dutpAppVersion));
    }

    /**
     *  移动端获取最新版本信息
     */
    public AjaxResult getLastAppVersion() {
        return AjaxResult.success();
    }

    /**
     *  移动端获取当前用户设备列表
     */
    @GetMapping("/getDeviceList")
    public AjaxResult getDeviceList() {
        return AjaxResult.success(dutpUserDeviceService.getDeviceList());
    }

    /**
     *  移动端移除设备
     */
    @GetMapping("/removeDevice")
    @Log(title = "移动端移除设备", businessType = BusinessType.DELETE)
    public AjaxResult removeDevice(DutpUserDevice dutpUserDevice) {
        return AjaxResult.success(dutpUserDeviceService.removeDevice(dutpUserDevice));
    }

    /**
     *  根据用户id查询用户设备信息列表
     */
    @GetMapping("/getDutpUserDeviceNum")
    public AjaxResult getDutpUserDeviceNum(DutpUserDevice dutpUserDevice) {
        return toAjax(dutpUserDeviceService.getDutpUserDeviceNum(dutpUserDevice));
    }
}

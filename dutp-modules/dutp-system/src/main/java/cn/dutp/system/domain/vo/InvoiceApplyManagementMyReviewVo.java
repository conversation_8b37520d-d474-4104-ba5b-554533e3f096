package cn.dutp.system.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * DUTP-DTB_012订单表
 *
 * @TableName dtb_book_order
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InvoiceApplyManagementMyReviewVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 成功上传——教务发票
     */
    private Integer academicMyReviewInvoiceUploadNumber;
    /**
     * 作废——教务发票
     */
    private Integer academicMyReviewCancelNumber;

    /**
     * 成功上传——零售发票
     */
    private Integer retailMyReviewInvoiceUploadNumber;

    /**
     * 作废——零售发票
     */
    private Integer retailMyReviewCancelNumber;


}
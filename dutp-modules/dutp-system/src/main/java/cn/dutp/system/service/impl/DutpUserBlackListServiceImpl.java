package cn.dutp.system.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.system.mapper.DutpUserBlackListMapper;
import cn.dutp.system.domain.DutpUserBlackList;
import cn.dutp.system.service.IDutpUserBlackListService;

/**
 * 用户黑名单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-16
 */
@Service
public class DutpUserBlackListServiceImpl extends ServiceImpl<DutpUserBlackListMapper, DutpUserBlackList> implements IDutpUserBlackListService
{
    @Autowired
    private DutpUserBlackListMapper dutpUserBlackListMapper;

    /**
     * 查询用户黑名单
     *
     * @param blackListId 用户黑名单主键
     * @return 用户黑名单
     */
    @Override
    public DutpUserBlackList selectDutpUserBlackListByBlackListId(Long blackListId)
    {
        return this.getById(blackListId);
    }

    /**
     * 获取禁止兑换黑名单详细信息
     *
     * @param userId 用户ID
     * @return 用户黑名单
     */
    @Override
    public DutpUserBlackList getProhibitionOfExchange(Long userId)
    {
        return baseMapper.getProhibitionOfExchange(userId);
    }

    /**
     * 查询用户黑名单列表
     *
     * @param dutpUserBlackList 用户黑名单
     * @return 用户黑名单
     */
    @Override
    public List<DutpUserBlackList> selectDutpUserBlackListList(DutpUserBlackList dutpUserBlackList)
    {
        LambdaQueryWrapper<DutpUserBlackList> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(ObjectUtil.isNotEmpty(dutpUserBlackList.getUserId())) {
            lambdaQueryWrapper.eq(DutpUserBlackList::getUserId
                    ,dutpUserBlackList.getUserId());
        }
        if(ObjectUtil.isNotEmpty(dutpUserBlackList.getFrozenType())) {
            lambdaQueryWrapper.eq(DutpUserBlackList::getFrozenType
                    ,dutpUserBlackList.getFrozenType());
        }
        if(ObjectUtil.isNotEmpty(dutpUserBlackList.getFrozenScope())) {
            lambdaQueryWrapper.eq(DutpUserBlackList::getFrozenScope
                    ,dutpUserBlackList.getFrozenScope());
        }
        if(ObjectUtil.isNotEmpty(dutpUserBlackList.getFrozenReason())) {
            lambdaQueryWrapper.eq(DutpUserBlackList::getFrozenReason
                    ,dutpUserBlackList.getFrozenReason());
        }
        if(ObjectUtil.isNotEmpty(dutpUserBlackList.getState())) {
            lambdaQueryWrapper.eq(DutpUserBlackList::getState
                    ,dutpUserBlackList.getState());
        }
        if(ObjectUtil.isNotEmpty(dutpUserBlackList.getEndDate())) {
            lambdaQueryWrapper.eq(DutpUserBlackList::getEndDate
                    ,dutpUserBlackList.getEndDate());
        }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增用户黑名单
     *
     * @param dutpUserBlackList 用户黑名单
     * @return 结果
     */
    @Override
    public boolean insertDutpUserBlackList(DutpUserBlackList dutpUserBlackList)
    {
        return this.save(dutpUserBlackList);
    }

    /**
     * 新增禁止兑换黑名单详细信息
     *
     * @param dutpUserBlackList 用户黑名单
     * @return 结果
     */
    @Override
    public boolean addProhibitionOfExchange(DutpUserBlackList dutpUserBlackList)
    {
        DutpUserBlackList  res =  baseMapper.getProhibitionOfExchange(dutpUserBlackList.getUserId());
        if(res != null){
            dutpUserBlackList.setBlackListId(res.getBlackListId());
            return this.updateById(dutpUserBlackList);
        }else{
            return this.save(dutpUserBlackList);
        }
    }


    /**
     * 修改用户黑名单
     *
     * @param dutpUserBlackList 用户黑名单
     * @return 结果
     */
    @Override
    public boolean updateDutpUserBlackList(DutpUserBlackList dutpUserBlackList)
    {
        return this.updateById(dutpUserBlackList);
    }

    /**
     * 批量删除用户黑名单
     *
     * @param blackListIds 需要删除的用户黑名单主键
     * @return 结果
     */
    @Override
    public boolean deleteDutpUserBlackListByBlackListIds(List<Long> blackListIds)
    {
        return this.removeByIds(blackListIds);
    }

}

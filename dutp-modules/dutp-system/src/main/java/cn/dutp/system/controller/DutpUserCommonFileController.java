package cn.dutp.system.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.common.log.enums.OperatorType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.system.domain.DutpUserCommonFile;
import cn.dutp.system.service.IDutpUserCommonFileService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@RestController
@RequestMapping("/file")
public class DutpUserCommonFileController extends BaseController
{
    @Autowired
    private IDutpUserCommonFileService dutpUserCommonFileService;

    /**
     * 查询【请填写功能名称】列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DutpUserCommonFile dutpUserCommonFile)
    {
        startPage();
        List<DutpUserCommonFile> list = dutpUserCommonFileService.selectDutpUserCommonFileList(dutpUserCommonFile);
        return getDataTable(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @RequiresPermissions("system:file:export")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DutpUserCommonFile dutpUserCommonFile)
    {
        List<DutpUserCommonFile> list = dutpUserCommonFileService.selectDutpUserCommonFileList(dutpUserCommonFile);
        ExcelUtil<DutpUserCommonFile> util = new ExcelUtil<DutpUserCommonFile>(DutpUserCommonFile.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @RequiresPermissions("system:file:query")
    @GetMapping(value = "/{fileId}")
    public AjaxResult getInfo(@PathVariable("fileId") Long fileId)
    {
        return success(dutpUserCommonFileService.selectDutpUserCommonFileByFileId(fileId));
    }

    /**
     * 学生教师端新增【请填写功能名称】
     */
    @Log(title = "学生教师端新增【请填写功能名称】", operatorType = OperatorType.READER, businessType = BusinessType.INSERT)
    @PostMapping("/addDutpUserCommonFile")
    public AjaxResult add(@RequestBody List<DutpUserCommonFile> dutpUserCommonFiles)
    {
        return success(dutpUserCommonFileService.insertDutpUserCommonFile(dutpUserCommonFiles));
    }

    /**
     * 修改【请填写功能名称】
     */
    @RequiresPermissions("system:file:edit")
    @Log(title = "修改【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DutpUserCommonFile dutpUserCommonFile)
    {
        return toAjax(dutpUserCommonFileService.updateDutpUserCommonFile(dutpUserCommonFile));
    }

    /**
     * 删除【请填写功能名称】
     */
    @RequiresPermissions("system:file:remove")
    @Log(title = "删除【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{fileIds}")
    public AjaxResult remove(@PathVariable Long[] fileIds)
    {
        return toAjax(dutpUserCommonFileService.deleteDutpUserCommonFileByFileIds(Arrays.asList(fileIds)));
    }

    /**
     * 学生教师端通过ID查询列表
     */
    @Log(title = "学生教师端通过ID查询列表",operatorType = OperatorType.READER)
    @GetMapping("/getListByIds/{fileIds}")
    public TableDataInfo getListByIds(@PathVariable Long[] fileIds)
    {
        List<DutpUserCommonFile> list = dutpUserCommonFileService.getListByIds(Arrays.asList(fileIds));
        return getDataTable(list);
    }

    /**
     * 学生教师端共通文件删除
     */
    @Log(title = "学生教师端通过业务ID删除", operatorType = OperatorType.READER, businessType = BusinessType.DELETE)
    @PostMapping("/deleteDutpUserCommonFile")
    public AjaxResult deleteDutpUserCommonFile(@RequestBody DutpUserCommonFile dutpUserCommonFile)
    {
        return success(dutpUserCommonFileService.deleteDutpUserCommonFile(dutpUserCommonFile));
    }
}

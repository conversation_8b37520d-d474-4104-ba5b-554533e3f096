package cn.dutp.system.service;

import java.util.HashMap;
import java.util.List;

import cn.dutp.system.api.domain.DtbBookCodeExchangeLog;
import cn.dutp.system.domain.DataCenterForm;
import cn.dutp.system.domain.DutpVisitLog;
import cn.dutp.system.domain.vo.BookDataVo;
import cn.dutp.system.domain.vo.ChartVo;
import cn.dutp.system.domain.vo.VisitDataVo;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 系统访问日志Service接口
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
public interface IDutpVisitLogService extends IService<DutpVisitLog>
{
    /**
     * 查询系统访问日志
     *
     * @param logId 系统访问日志主键
     * @return 系统访问日志
     */
    public DutpVisitLog selectDutpVisitLogByLogId(Long logId);

    /**
     * 查询系统访问日志列表
     *
     * @param dutpVisitLog 系统访问日志
     * @return 系统访问日志集合
     */
    public List<DutpVisitLog> selectDutpVisitLogList(DutpVisitLog dutpVisitLog);

    /**
     * 新增系统访问日志
     *
     * @param dutpVisitLog 系统访问日志
     * @return 结果
     */
    public boolean insertDutpVisitLog(DutpVisitLog dutpVisitLog);

    /**
     * 修改系统访问日志
     *
     * @param dutpVisitLog 系统访问日志
     * @return 结果
     */
    public boolean updateDutpVisitLog(DutpVisitLog dutpVisitLog);

    /**
     * 批量删除系统访问日志
     *
     * @param logIds 需要删除的系统访问日志主键集合
     * @return 结果
     */
    public boolean deleteDutpVisitLogByLogIds(List<Long> logIds);

    /**
     * 获取数据统计
     * @param dataCenterForm
     * @return
     */
    HashMap<String, Integer> getVisitDetailData(DataCenterForm dataCenterForm);

    List<VisitDataVo> getVisitDetailTable(DataCenterForm dataCenterForm);

    ChartVo getPageVisitData(DataCenterForm dataCenterForm);
}

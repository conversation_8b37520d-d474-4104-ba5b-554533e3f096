package cn.dutp.system.mapper;

import cn.dutp.system.domain.SysUserShortcut;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @description 针对表【dtb_book_refund_order(售后退款订单)】的数据库操作Mapper
 * @createDate 2025-01-20 11:11:53
 * @Entity cn.dutp.system.domain.DtbBookRefundOrder
 */
@Repository
public interface ISysUserShortcutMapper extends BaseMapper<SysUserShortcut> {
    void deleteByUserId(@Param("userId") Long userId);

}





package cn.dutp.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 系统访问日志对象 dutp_visit_log
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@Data
@TableName("dutp_visit_log")
public class DutpVisitLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** $column.columnComment */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long logId;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /** 访问地址 */
    private String address;

    /** 访问IP */
    private String ipAddress;

    private String router;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolId;

    private Integer userType;

    @TableField(exist = false)
    private String startDate;

    @TableField(exist = false)
    private String endDate;

    private String logDate;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("logId", getLogId())
            .append("userId", getUserId())
            .append("address", getAddress())
            .append("ipAddress", getIpAddress())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
        .toString();
        }
    }

package cn.dutp.system.mapper;

import cn.dutp.system.api.domain.DutpUser;
import cn.dutp.system.api.domain.SysUser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户表 数据层
 *
 * <AUTHOR>
 */
public interface DutpUserMapper extends BaseMapper<DutpUser>
{
    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    public DutpUser selectDutpUserByUserName(String userName);
    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    public DutpUser selectUserById(Long userId);
    /**
     * 通过用户电话查询用户
     *
     * @param phoneNumber 用户电话
     * @return 用户对象信息
     */
    public DutpUser selectUserByUserPhone(String phoneNumber);
    /**
     * 校验用户名称是否唯一
     *
     * @param userName 用户名称
     * @return 结果
     */
    public DutpUser checkUserNameUnique(String userName);
    /**
     * 新增用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public int insertDutpUser(DutpUser user);
    /**
     * 重置用户密码
     *
     * @param userId 用户id
     * @param password 密码
     * @return 结果
     */
    public int resetUserPwd(@Param("userId") Long userId, @Param("password") String password);

    /**
     * 根据教材id查询教材绑定的用户列表
     * @param user
     * @return
     */
    public List<DutpUser> listUserByBookIdAndSchoolId(DutpUser user);
}

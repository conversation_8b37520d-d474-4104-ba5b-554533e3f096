package cn.dutp.system.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * DUTP-DTB_012订单表
 *
 * @TableName dtb_book_order
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BookOrderManagementMyReviewVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 我审核过得教材总数
     */
    private Integer myReviewOrderNumber;
    /**
     * 教务采购数量
     */
    private Integer academicProcurementOrderNumber;
    /**
     * 其他教材数量
     */
    private Integer otherProcurementOrderNumber;


}
package cn.dutp.system.service;

import java.util.List;
import cn.dutp.system.domain.DutpAppVersion;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 移动端版本Service接口
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
public interface IDutpAppVersionService extends IService<DutpAppVersion>
{
    /**
     * 查询移动端版本
     *
     * @param releaseId 移动端版本主键
     * @return 移动端版本
     */
    public DutpAppVersion selectDutpAppVersionByReleaseId(Long releaseId);

    /**
     * 查询移动端版本列表
     *
     * @param dutpAppVersion 移动端版本
     * @return 移动端版本集合
     */
    public List<DutpAppVersion> selectDutpAppVersionList(DutpAppVersion dutpAppVersion);

    /**
     * 查询最新版本号信息
     *
     * @param dutpAppVersion
     * @return 版本号信息
     */
    public DutpAppVersion getLastAppVersion(DutpAppVersion dutpAppVersion);

    /**
     * 新增移动端版本
     *
     * @param dutpAppVersion 移动端版本
     * @return 结果
     */
    public boolean insertDutpAppVersion(DutpAppVersion dutpAppVersion);

    /**
     * 修改移动端版本
     *
     * @param dutpAppVersion 移动端版本
     * @return 结果
     */
    public boolean updateDutpAppVersion(DutpAppVersion dutpAppVersion);

    /**
     * 批量删除移动端版本
     *
     * @param releaseIds 需要删除的移动端版本主键集合
     * @return 结果
     */
    public boolean deleteDutpAppVersionByReleaseIds(List<Long> releaseIds);

}

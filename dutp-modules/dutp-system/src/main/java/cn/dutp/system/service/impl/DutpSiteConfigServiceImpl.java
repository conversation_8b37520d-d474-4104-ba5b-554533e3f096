package cn.dutp.system.service.impl;

import java.util.List;

import cn.dutp.domain.DutpSiteConfig;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.system.mapper.DutpSiteConfigMapper;
import cn.dutp.system.service.IDutpSiteConfigService;

/**
 * 平台整体设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@Service
public class DutpSiteConfigServiceImpl extends ServiceImpl<DutpSiteConfigMapper, DutpSiteConfig> implements IDutpSiteConfigService
{
    @Autowired
    private DutpSiteConfigMapper dutpSiteConfigMapper;

    /**
     * 查询平台整体设置
     *
     * @param configId 平台整体设置主键
     * @return 平台整体设置
     */
    @Override
    public DutpSiteConfig selectDutpSiteConfigByConfigId(Long configId)
    {
        return this.getById(configId);
    }

    /**
     * 查询平台整体设置列表
     *
     * @param dutpSiteConfig 平台整体设置
     * @return 平台整体设置
     */
    @Override
    public List<DutpSiteConfig> selectDutpSiteConfigList(DutpSiteConfig dutpSiteConfig)
    {
        LambdaQueryWrapper<DutpSiteConfig> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dutpSiteConfig.getTeacherCopyCountLimit())) {
                lambdaQueryWrapper.eq(DutpSiteConfig::getTeacherCopyCountLimit
                ,dutpSiteConfig.getTeacherCopyCountLimit());
            }
                if(ObjectUtil.isNotEmpty(dutpSiteConfig.getTeacherCopyWordLimit())) {
                lambdaQueryWrapper.eq(DutpSiteConfig::getTeacherCopyWordLimit
                ,dutpSiteConfig.getTeacherCopyWordLimit());
            }
                if(ObjectUtil.isNotEmpty(dutpSiteConfig.getStudentCopyCountLimit())) {
                lambdaQueryWrapper.eq(DutpSiteConfig::getStudentCopyCountLimit
                ,dutpSiteConfig.getStudentCopyCountLimit());
            }
                if(ObjectUtil.isNotEmpty(dutpSiteConfig.getStudentCopyWordLimit())) {
                lambdaQueryWrapper.eq(DutpSiteConfig::getStudentCopyWordLimit
                ,dutpSiteConfig.getStudentCopyWordLimit());
            }
                if(ObjectUtil.isNotEmpty(dutpSiteConfig.getReaderCopyCountLimit())) {
                lambdaQueryWrapper.eq(DutpSiteConfig::getReaderCopyCountLimit
                ,dutpSiteConfig.getReaderCopyCountLimit());
            }
                if(ObjectUtil.isNotEmpty(dutpSiteConfig.getReaderCopyWordLimit())) {
                lambdaQueryWrapper.eq(DutpSiteConfig::getReaderCopyWordLimit
                ,dutpSiteConfig.getReaderCopyWordLimit());
            }
                if(ObjectUtil.isNotEmpty(dutpSiteConfig.getExchangeLimit())) {
                lambdaQueryWrapper.eq(DutpSiteConfig::getExchangeLimit
                ,dutpSiteConfig.getExchangeLimit());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增平台整体设置
     *
     * @param dutpSiteConfig 平台整体设置
     * @return 结果
     */
    @Override
    public boolean insertDutpSiteConfig(DutpSiteConfig dutpSiteConfig)
    {
        return this.save(dutpSiteConfig);
    }

    /**
     * 修改平台整体设置
     *
     * @param dutpSiteConfig 平台整体设置
     * @return 结果
     */
    @Override
    public boolean updateDutpSiteConfig(DutpSiteConfig dutpSiteConfig)
    {
        return this.updateById(dutpSiteConfig);
    }

    /**
     * 批量删除平台整体设置
     *
     * @param configIds 需要删除的平台整体设置主键
     * @return 结果
     */
    @Override
    public boolean deleteDutpSiteConfigByConfigIds(List<Long> configIds)
    {
        return this.removeByIds(configIds);
    }

}

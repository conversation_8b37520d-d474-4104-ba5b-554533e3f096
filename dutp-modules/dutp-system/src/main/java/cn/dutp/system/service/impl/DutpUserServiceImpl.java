package cn.dutp.system.service.impl;

import cn.dutp.common.core.constant.UserConstants;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.utils.StringUtils;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.common.sms.utils.AliyunSmsUtil;
import cn.dutp.system.api.domain.DutpUser;
import cn.dutp.system.api.domain.DutpUserWithCode;
import cn.dutp.system.domain.DutpAiUserConfig;
import cn.dutp.system.mapper.DutpAiUserConfigMapper;
import cn.dutp.system.mapper.DutpUserMapper;
import cn.dutp.system.service.IDutpUserService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * DUTP-BASE-001用户【教师，学生，无身份者】Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-21
 */
@Service
public class DutpUserServiceImpl implements IDutpUserService
{
    @Autowired
    private DutpUserMapper userMapper;

    @Autowired
    private DutpAiUserConfigMapper dutpAiUserConfigMapper;

    @Value("${spring.experimentCount}")
    private Integer experimentCount;
    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public DutpUser selectDutpUserByUserName(String userName)
    {
        return userMapper.selectDutpUserByUserName(userName);
    }
    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public DutpUser selectUserById(Long userId)
    {
        return userMapper.selectUserById(userId);
    }
    /**
     * 通过用户手机号码查询用户
     *
     * @param phoneNumber 手机号码
     * @return 用户对象信息
     */
    @Override
    public DutpUser selectUserByPhoneNumber(String phoneNumber) {
        return userMapper.selectUserByUserPhone(phoneNumber);
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean checkUserNameUnique(DutpUser user)
    {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        DutpUser info = userMapper.checkUserNameUnique(user.getUserName());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }
    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean registerUser(DutpUser user)
    {
        boolean flag = userMapper.insert(user) > 0;
        if (flag) {
            // 新增默认试用次数
            DutpAiUserConfig config = new DutpAiUserConfig();
            config.setUserType(0);
            config.setUserId(user.getUserId());
            config.setAiExperimentCount(experimentCount);
            dutpAiUserConfigMapper.insert(config);
        }
        return flag;
    }
    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    @Override
    public void checkUserAllowed(DutpUser user)
    {
        if (StringUtils.isNotNull(user.getUserId()) && user.isAdmin())
        {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int forgetPwd(DutpUserWithCode user) {

        DutpUser dutpUser = userMapper.selectUserByUserPhone(user.getPhonenumber());
        if (dutpUser==null){
            throw new ServiceException("用户不存在！");
        }

        Integer checkCode = AliyunSmsUtil.checkCode(user.getPhonenumber(), user.getCode(),"SMS_314725755");
        if (checkCode!=200){
            throw new ServiceException("验证码输入错误！",7001);
        }
        return userMapper.resetUserPwd(dutpUser.getUserId(), user.getPassword());
    }

    @Override
    public List<DutpUser> listNotPage(DutpUser user) {
        QueryWrapper<DutpUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DutpUser::getDelFlag,0)
                .eq(DutpUser::getStatus,0);
        if (StringUtils.isNotBlank(user.getUserType())) {
            queryWrapper.lambda().eq(DutpUser::getUserType, user.getUserType());
        }
        if (StringUtils.isNotBlank(user.getPhonenumber())) {
            queryWrapper.lambda().like(DutpUser::getPhonenumber, user.getPhonenumber());
        }
        if (StringUtils.isNotBlank(user.getUserName())) {
            queryWrapper.lambda().like(DutpUser::getUserName, user.getUserName());
        }
        if (StringUtils.isNotBlank(user.getNickName())) {
            queryWrapper.lambda().like(DutpUser::getNickName, user.getNickName());
        }
        return userMapper.selectList(queryWrapper);
    }

    /**
     * 根据教材id查询教材绑定的用户列表
     * @param user
     * @return
     */
    @Override
    public List<DutpUser> listUserByBookIdAndSchoolId(DutpUser user) {
        return userMapper.listUserByBookIdAndSchoolId(user);
    }
}

package cn.dutp.system.controller;

import cn.dutp.common.core.constant.CacheConstants;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.redis.service.RedisService;
import cn.dutp.system.api.model.LoginUser;
import cn.dutp.system.domain.DataCenterForm;
import cn.dutp.system.domain.vo.*;
import cn.dutp.system.service.IDataCenterService;
import cn.dutp.system.service.IDutpVisitLogService;
import cn.hutool.core.util.ObjectUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 在线用户监控
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dataCenter")
public class DataCenterController extends BaseController
{
    @Autowired
    private RedisService redisService;
    @Autowired
    private IDutpVisitLogService visitLogService;
    @Autowired
    private IDataCenterService dataCenterService;
    /**
     * 用户类型饼状图
     * @return
     */
    @GetMapping("/userTypePie")
    public AjaxResult userTypePie(DataCenterForm dataCenterForm)
    {
        HashMap<String,Integer> dataMap = new HashMap<>();
        Collection<String> keys = redisService.keys(CacheConstants.LOGIN_TOKEN_KEY + "*");
        int readerQuantity = 0;
        int stuQuantity = 0;
        int teaQuantity = 0;
        for (String key : keys)
        {
            LoginUser user = redisService.getCacheObject(key);
            if (ObjectUtil.isNotNull(user.getUserType()) && user.getUserType() == 3) {
                // 有参数
                if (dataCenterForm.getSchoolId().longValue() != 0) {
                    if (ObjectUtil.isNotNull(user.getSchoolId())
                            && user.getSchoolId().longValue()==dataCenterForm.getSchoolId().longValue()
                            && ObjectUtil.isNotNull(user.getHomeUserType())
                            && user.getHomeUserType().intValue()==0) {
                        readerQuantity ++;
                    }
                    if (ObjectUtil.isNotNull(user.getSchoolId())
                            && user.getSchoolId().longValue()==dataCenterForm.getSchoolId().longValue()
                            && ObjectUtil.isNotNull(user.getHomeUserType())
                            && user.getHomeUserType().intValue()==1) {
                        stuQuantity ++;
                    }
                    if (ObjectUtil.isNotNull(user.getSchoolId())
                            && user.getSchoolId().longValue()==dataCenterForm.getSchoolId().longValue()
                            && ObjectUtil.isNotNull(user.getHomeUserType())
                            && user.getHomeUserType().intValue()==2) {
                        teaQuantity ++;
                    }
                } else {
                    if (ObjectUtil.isNotNull(user.getHomeUserType())
                            && user.getHomeUserType().intValue()==0) {
                        readerQuantity ++;
                    }
                    if (ObjectUtil.isNotNull(user.getHomeUserType())
                            && user.getHomeUserType().intValue()==1) {
                        stuQuantity ++;
                    }
                    if (ObjectUtil.isNotNull(user.getHomeUserType())
                            && user.getHomeUserType().intValue()==2) {
                        teaQuantity ++;
                    }
                }
            }
        }
        dataMap.put("readerQuantity", readerQuantity);
        dataMap.put("stuQuantity", stuQuantity);
        dataMap.put("teaQuantity", teaQuantity);
        return AjaxResult.success(dataMap);
    }

    /**
     * 访问图表
     * @param dataCenterForm
     * @return
     */
    @GetMapping("/pageVisit")
    public AjaxResult pageVisit(DataCenterForm dataCenterForm) {
        ChartVo chartVo = visitLogService.getPageVisitData(dataCenterForm);

        return AjaxResult.success(chartVo);
    }

    /**
     * 系统访问数据详情
     * @param dataCenterForm
     * @return
     */
    @GetMapping("/visitDetail")
    public AjaxResult visitDetail(DataCenterForm dataCenterForm) {
        HashMap<String, Integer> dataMap = visitLogService.getVisitDetailData(dataCenterForm);
        return AjaxResult.success(dataMap);
    }

    /**
     * 访问数据列表
     * @param dataCenterForm
     * @return
     */
    @GetMapping("/visitDetailTable")
    public TableDataInfo visitDetailTable(DataCenterForm dataCenterForm) {
        startPage();
        List<VisitDataVo> list = visitLogService.getVisitDetailTable(dataCenterForm);
        return getDataTable(list);

    }

    /**
     *
     * @param response
     * @param dataCenterForm
     */
    @PostMapping("/exportVisitDetailTable")
    public void exportVisitDetailTable(HttpServletResponse response, DataCenterForm dataCenterForm) {
        List<VisitDataVo> list = visitLogService.getVisitDetailTable(dataCenterForm);
        ExcelUtil<VisitDataVo> util = new ExcelUtil<>(VisitDataVo.class);
        util.exportExcel(response, list, "移动端版本数据");
    }

    /**
     * 教材整体数据
     * @param dataCenterForm
     * @return
     */
    @GetMapping("/bookData")
    public AjaxResult bookData(DataCenterForm dataCenterForm) {
        BookDataVo bookDataVo = dataCenterService.getBookData(dataCenterForm);
        return AjaxResult.success(bookDataVo);
    }

    /**
     * 审核步骤数据
     * @param dataCenterForm
     * @return
     */
    @GetMapping("/stepData")
    public AjaxResult stepData(DataCenterForm dataCenterForm) {
        List<BookStepDataVo> bookStepDataVos = dataCenterService.getBookStepData(dataCenterForm);
        return AjaxResult.success(bookStepDataVos);
    }

    /**
     * 排名数据
     * @param dataCenterForm
     * @return
     */
    @GetMapping("/rankingData")
    public AjaxResult rankingData(DataCenterForm dataCenterForm) {
        List<BookRankingVo> studentRanking = dataCenterService.getBookRankingData(dataCenterForm);
        return AjaxResult.success(studentRanking);
    }

    /**
     *
     * @param dataCenterForm
     * @return
     */
    @GetMapping("/orderData")
    public AjaxResult orderData(DataCenterForm dataCenterForm) {
        List<OrderDataVo> orderDataVos = dataCenterService.getOrderData(dataCenterForm);
        return AjaxResult.success(orderDataVos);
    }

    /**
     * 教材弹窗
     * @param dataCenterForm
     * @return
     */
    @GetMapping("/subjectBookData")
    public AjaxResult subjectBookData(DataCenterForm dataCenterForm) {
        List<StepDataVo> orderDataVos = dataCenterService.getSubjectBookData(dataCenterForm);
        return AjaxResult.success(orderDataVos);
    }

    /**
     * 教材弹窗
     * @param dataCenterForm
     * @return
     */
    @GetMapping("/bookOrderData")
    public AjaxResult bookOrderData(DataCenterForm dataCenterForm) {
        HashMap<String,Object> orderDatas = dataCenterService.getOrderDataForChart(dataCenterForm);
        return AjaxResult.success(orderDatas);
    }
}

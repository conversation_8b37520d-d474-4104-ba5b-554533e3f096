<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.system.mapper.DutpUserMapper">

	<resultMap type="cn.dutp.system.api.domain.DutpUser" id="DutpUserResult">
		<id     property="userId"       column="user_id"      />
		<result property="userName"     column="user_name"    />
		<result property="userType"     column="user_type"    />
		<result property="nickName"     column="nick_name"    />
		<result property="realName"     column="real_name"    />
		<result property="email"        column="email"        />
		<result property="phonenumber"  column="phonenumber"  />
		<result property="sex"          column="sex"          />
		<result property="avatar"       column="avatar"       />
		<result property="schoolId"       column="school_id"  />
		<result property="password"     column="password"     />
		<result property="status"       column="status"       />
		<result property="delFlag"      column="del_flag"     />
		<result property="loginIp"      column="login_ip"     />
		<result property="loginDate"    column="login_date"   />
		<result property="createBy"     column="create_by"    />
		<result property="createTime"   column="create_time"  />
		<result property="updateBy"     column="update_by"    />
		<result property="updateTime"   column="update_time"  />
		<result property="remark"       column="remark"       />
		<result property="schoolName"       column="school_name"  />
		<result property="academyId"       column="academy_id"  />
		<result property="academyName"       column="academy_name"  />
		<collection  property="roles"   javaType="java.util.List"  resultMap="RoleResult" />
	</resultMap>

	<resultMap id="RoleResult" type="SysRole">
		<id     property="roleId"       column="role_id"        />
		<result property="roleName"     column="role_name"      />
		<result property="roleKey"      column="role_key"       />
		<result property="roleSort"     column="role_sort"      />
		<result property="dataScope"    column="data_scope"     />
		<result property="status"       column="role_status"    />
	</resultMap>

	<sql id="selectDutpUserVo">
		select u.user_id, u.user_name, u.real_name, u.nick_name, u.user_type, u.email, u.avatar, u.phonenumber, u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark,
			   r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status, u.school_id, s.school_name AS school_name, u.academy_id, a.school_name AS academy_name
		from dutp_user u
				 left join dutp_school s on u.school_id = s.school_id
				 left join dutp_school a on u.academy_id = a.school_id
				 left join sys_user_role ur on u.user_id = ur.user_id
				 left join sys_role r on r.role_id = ur.role_id
	</sql>

	<select id="selectDutpUserList" parameterType="DutpUser" resultMap="DutpUserResult">
		<include refid="selectDutpUserVo"/>
		<where>
			<if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
			<if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
			<if test="userType != null  and userType != ''"> and user_type = #{userType}</if>
			<if test="email != null  and email != ''"> and email = #{email}</if>
			<if test="phonenumber != null  and phonenumber != ''"> and phonenumber = #{phonenumber}</if>
			<if test="sex != null  and sex != ''"> and sex = #{sex}</if>
			<if test="avatar != null  and avatar != ''"> and avatar = #{avatar}</if>
			<if test="password != null  and password != ''"> and password = #{password}</if>
			<if test="status != null  and status != ''"> and status = #{status}</if>
			<if test="loginIp != null  and loginIp != ''"> and login_ip = #{loginIp}</if>
			<if test="loginDate != null "> and login_date = #{loginDate}</if>
		</where>
	</select>

	<select id="selectDutpUserByUserId" parameterType="Long" resultMap="DutpUserResult">
		<include refid="selectDutpUserVo"/>
		where user_id = #{userId}
	</select>
	<select id="selectDutpUserByUserName" parameterType="String" resultMap="DutpUserResult">
		<include refid="selectDutpUserVo"/>
		where u.user_name = #{userName} and u.del_flag = '0'
	</select>
	<select id="selectUserById" parameterType="Long" resultMap="DutpUserResult">
		<include refid="selectDutpUserVo"/>
		where u.user_id = #{userId}
	</select>
	<select id="selectUserByUserPhone"  resultMap="DutpUserResult">
		<include refid="selectDutpUserVo"/>
		where u.phonenumber = #{phoneNumber}
		AND  u.del_flag = 0
	</select>
	<select id="checkUserNameUnique" parameterType="String" resultMap="DutpUserResult">
		select user_id, user_name from dutp_user where user_name = #{userName} and del_flag = '0' limit 1
	</select>
	<insert id="insertDutpUser" parameterType="cn.dutp.system.api.domain.DutpUser" useGeneratedKeys="true" keyProperty="userId">
		insert into dutp_user
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="userName != null and userName != ''">user_name,</if>
			<if test="nickName != null and nickName != ''">nick_name,</if>
			<if test="userType != null">user_type,</if>
			<if test="email != null">email,</if>
			<if test="phonenumber != null">phonenumber,</if>
			<if test="sex != null">sex,</if>
			<if test="avatar != null">avatar,</if>
			<if test="password != null">password,</if>
			<if test="status != null">status,</if>
			<if test="delFlag != null">del_flag,</if>
			<if test="loginIp != null">login_ip,</if>
			<if test="loginDate != null">login_date,</if>
			<if test="createBy != null">create_by,</if>
			<if test="createTime != null">create_time,</if>
			<if test="updateBy != null">update_by,</if>
			<if test="updateTime != null">update_time,</if>
			<if test="remark != null">remark,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="userName != null and userName != ''">#{userName},</if>
			<if test="nickName != null and nickName != ''">#{nickName},</if>
			<if test="userType != null">#{userType},</if>
			<if test="email != null">#{email},</if>
			<if test="phonenumber != null">#{phonenumber},</if>
			<if test="sex != null">#{sex},</if>
			<if test="avatar != null">#{avatar},</if>
			<if test="password != null">#{password},</if>
			<if test="status != null">#{status},</if>
			<if test="delFlag != null">#{delFlag},</if>
			<if test="loginIp != null">#{loginIp},</if>
			<if test="loginDate != null">#{loginDate},</if>
			<if test="createBy != null">#{createBy},</if>
			<if test="createTime != null">#{createTime},</if>
			<if test="updateBy != null">#{updateBy},</if>
			<if test="updateTime != null">#{updateTime},</if>
			<if test="remark != null">#{remark},</if>
		</trim>
	</insert>
	<update id="resetUserPwd" parameterType="cn.dutp.system.api.domain.DutpUser">
		update dutp_user set password = #{password} where user_id = #{userId}
	</update>
	<update id="updateDutpUser" parameterType="DutpUser">
		update dutp_user
		<trim prefix="SET" suffixOverrides=",">
			<if test="userName != null and userName != ''">user_name = #{userName},</if>
			<if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
			<if test="userType != null">user_type = #{userType},</if>
			<if test="email != null">email = #{email},</if>
			<if test="phonenumber != null">phonenumber = #{phonenumber},</if>
			<if test="sex != null">sex = #{sex},</if>
			<if test="avatar != null">avatar = #{avatar},</if>
			<if test="password != null">password = #{password},</if>
			<if test="status != null">status = #{status},</if>
			<if test="delFlag != null">del_flag = #{delFlag},</if>
			<if test="loginIp != null">login_ip = #{loginIp},</if>
			<if test="loginDate != null">login_date = #{loginDate},</if>
			<if test="createBy != null">create_by = #{createBy},</if>
			<if test="createTime != null">create_time = #{createTime},</if>
			<if test="updateBy != null">update_by = #{updateBy},</if>
			<if test="updateTime != null">update_time = #{updateTime},</if>
			<if test="remark != null">remark = #{remark},</if>
		</trim>
		where user_id = #{userId}
	</update>

	<delete id="deleteDutpUserByUserId" parameterType="Long">
		delete from dutp_user where user_id = #{userId}
	</delete>

	<delete id="deleteDutpUserByUserIds" parameterType="String">
		delete from dutp_user where user_id in
		<foreach item="userId" collection="array" open="(" separator="," close=")">
			#{userId}
		</foreach>
	</delete>

	<select id="listUserByBookIdAndSchoolId" parameterType="Long" resultType="DutpUser">
		select
		    dub.user_id,
			dub.book_id,
			du.school_id,
			dub.code_id,
			dpc.code,
			du.nick_name,
			du.user_name,
			du.phonenumber,
			du.user_type,
			du.user_no,
			ds.school_name as academyName,
			ds1.school_name as majorlName
		from
			dtb_user_book dub
		left join
			dutp_user du on du.user_id = dub.user_id and du.del_flag = 0 and du.status = 0
		left join
			dutp_school ds on ds.school_id = du.academy_id and ds.data_type = 1 and ds.del_flag = 0
		left join
			dutp_school ds1 on ds1.school_id = du.speciality_id and ds.data_type = 2 and ds.del_flag = 0
		left join
			dtb_book_purchase_code dpc on dpc.code_id = dub.code_id and dpc.code_type = 2 and dpc.del_flag = 0
		where
			dub.add_way = 2 and dub.book_id = #{bookId} and dub.del_flag = 0 and du.school_id = #{schoolId}
			<if test="userNo != null and userNo != ''">and du.user_name like concat('%', #{userName}, '%')</if>
			<if test="userNo != null and userNo != ''">and du.user_no like concat('%', #{userNo}, '%')</if>
	</select>
</mapper>
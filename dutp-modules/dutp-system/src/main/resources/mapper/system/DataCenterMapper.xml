<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.system.mapper.DataCenterMapper">
    <select id="getBookData" resultType="cn.dutp.system.domain.vo.BookDataVo">
    SELECT
        (
            SELECT
                count(1)
            FROM
                dtb_book
            WHERE
                del_flag = 0
        ) allBookQuantity,
        (
            SELECT
                COUNT(DISTINCT b.book_id)
            FROM
                dtb_book b
            <if test="schoolId !=null and schoolId !=''">
                INNER JOIN dtb_book_school bs ON bs.book_id = b.book_id
            </if>
            WHERE
                b.del_flag = 0
            AND b.book_organize = 1
            <if test="schoolId !=null and schoolId !=''">
                AND bs.school_id = #{schoolId}
            </if>
        ) totalBookQuantity,
        (
            SELECT
                COUNT(DISTINCT b.book_id)
            FROM
                dtb_book b
            <if test="schoolId !=null and schoolId !=''">
                INNER JOIN dtb_book_school bs ON bs.book_id = b.book_id
            </if>
            WHERE
                b.del_flag = 0
            AND b.book_organize = 2
            <if test="schoolId !=null and schoolId !=''">
                AND bs.school_id = #{schoolId}
            </if>
        ) totalSchoolQuantity,
        (
            SELECT
                COUNT(DISTINCT b.book_id)
            FROM
                dtb_book b
            INNER JOIN dtb_user_book ub ON ub.book_id = b.book_id
            INNER JOIN dutp_user u ON ub.user_id = u.user_id
            WHERE 1=1
            AND u.del_flag = 0
            AND b.del_flag = 0
            AND ub.del_flag = 0
            AND u.user_type = 1
            <if test="schoolId !=null and schoolId !=''">
                AND u.school_id = #{schoolId}
            </if>
        ) studentBookQuantity,
        (
            SELECT
                COUNT(DISTINCT b.book_id)
            FROM
                dtb_book b
            INNER JOIN dtb_user_book ub ON ub.book_id = b.book_id
            INNER JOIN dutp_user u ON ub.user_id = u.user_id
            WHERE 1=1
            AND u.del_flag = 0
            AND ub.del_flag = 0
            AND b.del_flag = 0
            <if test="schoolId !=null and schoolId !=''">
                AND u.school_id = #{schoolId}
            </if>
            AND u.user_type = 2
        ) teacherBookQuantity
    </select>
    <select id="getBookStepData" resultType="cn.dutp.system.domain.vo.BookStepDataVo">
        SELECT
            COUNT(DISTINCT b.book_id) book_quantity,
            b.book_organize,
            s.step_id,
            s.step_name
        FROM
            dtb_book b
        INNER JOIN dtb_book_publish_step s ON b.current_step_id = s.step_id
        <if test="schoolId !=null and schoolId !=''">
            INNER JOIN dtb_book_school bs on bs.book_id = b.book_id
        </if>
        WHERE
            b.del_flag = 0
        <if test="schoolId !=null and schoolId !=''">
            AND bs.school_id = #{schoolId}
        </if>
        GROUP BY
            s.step_id,
            b.book_organize,
            s.step_name
    </select>
    <select id="getBookRankingData" resultType="cn.dutp.system.domain.vo.BookRankingVo">
        SELECT
            b.book_name,
            COUNT(DISTINCT u.user_id) userQuantity
        FROM
            dtb_user_book ub
        INNER JOIN dtb_book b ON b.book_id = ub.book_id
        INNER JOIN dutp_user u ON ub.user_id = u.user_id
        WHERE
            b.del_flag = 0
            AND u.del_flag = 0
            AND ub.del_flag = 0
            <if test="schoolId !=null and schoolId !=''">
                AND u.school_id = #{schoolId}
            </if>
            <if test="userType !=null and userType !=''">
                AND u.user_type = #{userType}
            </if>
        GROUP BY
            b.book_name
        ORDER BY
            COUNT(DISTINCT u.user_id) DESC
        <if test="userType == 1">
            limit #{studentRankingLimit}
        </if>
        <if test="userType == 2">
            limit #{teacherRankingLimit}
        </if>
    </select>
    <select id="getOrderData" resultType="cn.dutp.system.domain.vo.OrderDataVo">
        SELECT
            IFNULL(COUNT(order_id),0) order_quantity,
            IFNULL(SUM(pay_amount),0) money_amount,
            IFNULL(COUNT(DISTINCT book_id),0) book_quantity
        FROM
            dtb_book_order
        WHERE 1=1
        <if test="orderDate!='' and orderDate!=null">
            AND DATE_FORMAT(create_time, '%Y-%m-%d') = #{orderDate}
        </if>
        <if test="startOrderDate!='' and startOrderDate!=null">
            AND create_time >= #{startOrderDate}
        </if>
        AND payment_status = 'A'
        <if test="endOrderDate !=null and endOrderDate !=''">
            AND create_time &lt;= #{endOrderDate}
        </if>

    </select>
    <select id="getSubjectBookData" resultType="cn.dutp.system.domain.vo.StepDataVo">
        SELECT
            p.step_id,
            p.step_name,
            IFNULL(COUNT(DISTINCT b.book_id), 0) book_quantity,
            COUNT(DISTINCT CASE WHEN b.shelf_state = 1 THEN b.book_id END) shelf_quantity
        FROM
            dtb_book_publish_step p
        LEFT JOIN (
            SELECT
                b.book_id,
                b.current_step_id,
                b.shelf_state
            FROM
                dtb_book b
                <if test="schoolId !=null">
                    INNER JOIN dtb_book_school s on b.book_id = s.book_id
                </if>
            WHERE
                b.del_flag = 0
            AND b.book_organize = #{bookOrganize}
            <if test="topSubjectId != null">
                AND b.top_subject_id = #{topSubjectId}
            </if>
            <if test="secondSubjectId != null">
                AND b.second_subject_id = #{secondSubjectId}
            </if>
            <if test="thirdSubjectId != null">
                AND b.third_subject_id = #{thirdSubjectId}
            </if>
            <if test="forthSubjectId != null">
                AND b.forth_subject_id = #{forthSubjectId}
            </if>
            <if test="schoolId != null">
                AND s.school_id = #{schoolId}
            </if>
        ) b ON p.step_id = b.current_step_id
        WHERE
            1 = 1
            <if test="bookOrganize == 2">
                AND p.school_flag = 1
            </if>
        GROUP BY
            p.step_id,
            p.step_name
    </select>
    <select id="getOrderDateDataForChart" resultType="cn.dutp.system.domain.vo.OrderDataVo">
        SELECT
        date_format(ord.pay_time, '%Y-%m-%d') order_date,
        IFNULL(COUNT(DISTINCT ord.order_id),0) order_quantity,
        IFNULL(SUM(item.price_order_item),0) money_amount,
        IFNULL(SUM(item.book_quantity),0) book_quantity
        FROM
        dtb_book_order ord INNER JOIN dtb_book_order_item item on ord.order_id = item.order_id
        WHERE 1=1
        <if test="startOrderDate!='' and startOrderDate!=null">
            AND date_format(ord.pay_time,'%Y-%m-%d') >= #{startOrderDate}
        </if>
        AND ord.payment_status = 'A'
        <if test="endOrderDate !=null and endOrderDate !=''">
            AND date_format(ord.pay_time,'%Y-%m-%d') &lt;= #{endOrderDate}
        </if>
        <if test="schoolId!='' and schoolId!=null">
            AND ord.school_id = #{schoolId}
        </if>
        group by order_date
    </select>
    <select id="getOrderRefundDataForChart" resultType="cn.dutp.system.domain.vo.OrderDataVo">
        SELECT
            date_format(ord.create_time, '%Y-%m-%d') order_date,
            IFNULL(
                COUNT(DISTINCT ord.refund_order_id),
                0
            ) order_quantity,
            IFNULL(SUM(item.refund_amount), 0) money_amount,
            IFNULL(
                SUM(item.refund_quantity),
                0
            ) book_quantity

        FROM
            dtb_book_refund_order ord
        INNER JOIN dtb_book_refund_order_item item ON ord.refund_order_id = item.refund_order_id
        WHERE
            1 = 1
            <if test="startOrderDate!='' and startOrderDate!=null">
                AND date_format(ord.create_time,'%Y-%m-%d') >= #{startOrderDate}
            </if>
            <if test="endOrderDate !=null and endOrderDate !=''">
                AND date_format(ord.create_time,'%Y-%m-%d') &lt;= #{endOrderDate}
            </if>
            <if test="schoolId!='' and schoolId!=null">
                AND ord.school_id = #{schoolId}
            </if>
        GROUP BY
            order_date
    </select>
    <select id="getOrderMonthDataForChart" resultType="cn.dutp.system.domain.vo.OrderDataVo">
        SELECT
        date_format(ord.pay_time, '%Y-%m') order_month,
        IFNULL(COUNT(DISTINCT ord.order_id),0) order_quantity,
        IFNULL(SUM(item.price_order_item),0) money_amount,
        IFNULL(SUM(item.book_quantity),0) book_quantity
        FROM
        dtb_book_order ord INNER JOIN dtb_book_order_item item on ord.order_id = item.order_id
        WHERE 1=1
        <if test="startOrderMonth!='' and startOrderMonth!=null">
            AND date_format(ord.pay_time,'%Y-%m-%d') >= #{startOrderMonth}
        </if>
        AND ord.payment_status = 'A'
        <if test="endOrderMonth !=null and endOrderMonth !=''">
            AND date_format(ord.pay_time,'%Y-%m-%d') &lt;= #{endOrderMonth}
        </if>
        <if test="schoolId!='' and schoolId!=null">
            AND ord.school_id = #{schoolId}
        </if>
        group by order_month
    </select>
    <select id="getOrderMonthRefundDataForChart" resultType="cn.dutp.system.domain.vo.OrderDataVo">
        SELECT
        date_format(ord.create_time, '%Y-%m') order_month,
        IFNULL(
        COUNT(DISTINCT ord.refund_order_id),
        0
        ) order_quantity,
        IFNULL(SUM(item.refund_amount), 0) money_amount,
        IFNULL(
        SUM(item.refund_quantity),
        0
        ) book_quantity,
        IFNULL((SELECT COUNT(1) FROM dtb_book_refund_order WHERE 1=1
            <if test="startOrderMonth!='' and startOrderMonth!=null">
                AND date_format(create_time,'%Y-%m-%d') >= #{startOrderMonth}
            </if>
            <if test="endOrderMonth !=null and endOrderMonth !=''">
                AND date_format(create_time,'%Y-%m-%d') &lt;= #{endOrderMonth}
            </if>
            <if test="schoolId!='' and schoolId!=null">
                AND school_id = #{schoolId}
            </if>
        ),0) refund_total_quantity
        FROM
        dtb_book_refund_order ord
        INNER JOIN dtb_book_refund_order_item item ON ord.refund_order_id = item.refund_order_id
        WHERE
        1 = 1
        <if test="startOrderMonth!='' and startOrderMonth!=null">
            AND date_format(ord.create_time,'%Y-%m-%d') >= #{startOrderMonth}
        </if>
        AND ord.refund_status = 1
        <if test="endOrderMonth !=null and endOrderMonth !=''">
            AND date_format(ord.create_time,'%Y-%m-%d') &lt;= #{endOrderMonth}
        </if>
        <if test="schoolId!='' and schoolId!=null">
            AND ord.school_id = #{schoolId}
        </if>
        GROUP BY
            order_month
    </select>
    <select id="getRefundTotalOrderQuantity" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM dtb_book_refund_order WHERE 1=1
        <if test="startOrderMonth!='' and startOrderMonth!=null">
            AND date_format(create_time,'%Y-%m-%d') >= #{startOrderMonth}
        </if>
        <if test="endOrderMonth !=null and endOrderMonth !=''">
            AND date_format(create_time,'%Y-%m-%d') &lt;= #{endOrderMonth}
        </if>
        <if test="startOrderDate!='' and startOrderDate!=null">
            AND date_format(create_time,'%Y-%m-%d') >= #{startOrderDate}
        </if>
        <if test="endOrderDate !=null and endOrderDate !=''">
            AND date_format(create_time,'%Y-%m-%d') &lt;= #{endOrderDate}
        </if>
        <if test="schoolId!='' and schoolId!=null">
            AND school_id = #{schoolId}
        </if>

    </select>
</mapper>
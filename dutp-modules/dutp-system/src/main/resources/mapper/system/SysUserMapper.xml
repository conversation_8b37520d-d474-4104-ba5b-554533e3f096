<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.system.mapper.SysUserMapper">

    <resultMap type="SysUser" id="SysUserResult">
        <id     property="userId"       column="user_id"      />
        <result property="deptId"       column="dept_id"      />
		<result property="schoolId"     column="school_id"    />
		<result property="houseId"      column="house_id"     />
		<result property="entryTime"      column="entry_time"     />
		<result property="employmentStatus"      column="employment_status"     />
		<result property="schoolName"   column="school_name"  />
		<result property="houseName"    column="house_name"   />
        <result property="userName"     column="user_name"    />
        <result property="nickName"     column="nick_name"    />
        <result property="email"        column="email"        />
		<result property="userType"     column="user_type"    />
        <result property="phonenumber"  column="phonenumber"  />
        <result property="sex"          column="sex"          />
        <result property="avatar"       column="avatar"       />
        <result property="password"     column="password"     />
        <result property="status"       column="status"       />
        <result property="delFlag"      column="del_flag"     />
        <result property="loginIp"      column="login_ip"     />
        <result property="loginDate"    column="login_date"   />
        <result property="createBy"     column="create_by"    />
        <result property="createTime"   column="create_time"  />
        <result property="updateBy"     column="update_by"    />
        <result property="updateTime"   column="update_time"  />
        <result property="remark"       column="remark"       />
		<result property="deptName"     column="dept_name"   />
		<result property="postName"     column="post_name"   />
		<result property="roleName"     column="role_name"   />
        <association property="dept"    javaType="SysDept"         resultMap="deptResult" />
        <collection  property="roles"   javaType="java.util.List"  resultMap="RoleResult" />
    </resultMap>
	
    <resultMap id="deptResult" type="SysDept">
        <id     property="deptId"    column="dept_id"     />
        <result property="parentId"  column="parent_id"   />
        <result property="deptName"  column="dept_name"   />
        <result property="ancestors" column="ancestors"   />
        <result property="orderNum"  column="order_num"   />
        <result property="leader"    column="leader"      />
        <result property="status"    column="dept_status" />
    </resultMap>
	
    <resultMap id="RoleResult" type="SysRole">
        <id     property="roleId"       column="role_id"        />
        <result property="roleName"     column="role_name"      />
        <result property="roleKey"      column="role_key"       />
        <result property="roleSort"     column="role_sort"      />
        <result property="dataScope"    column="data_scope"     />
        <result property="status"       column="role_status"    />
    </resultMap>
	
	<sql id="selectUserVo">
		SELECT
			u.user_id,
			u.dept_id,
			u.school_id,
			u.house_id,
			u.entry_time,
			u.employment_status,
			s.school_name,
			ph.house_name,
			ph.house_id,
			u.user_name,
			u.nick_name,
			u.user_type,
			u.email,
			u.avatar,
			u.phonenumber,
			u.PASSWORD,
			u.sex,
			u.STATUS,
			u.del_flag,
			u.login_ip,
			u.login_date,
			u.create_by,
			u.create_time,
			u.remark,
			d.dept_id,
			d.parent_id,
			d.ancestors,
			d.dept_name,
			d.order_num,
			d.leader,
			d.STATUS AS dept_status,
			r.role_id,
			r.role_name,
			r.role_key,
			r.role_sort,
			r.data_scope,
			r.STATUS AS role_status
		FROM
			sys_user u
				LEFT JOIN sys_dept d ON u.dept_id = d.dept_id
				LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id
				LEFT JOIN sys_role r ON r.role_id = ur.role_id
				LEFT JOIN dutp_school s ON u.school_id = s.school_id
				LEFT JOIN dutp_publishing_house ph ON u.house_id = ph.house_id
    </sql>
    
     <select id="selectUserList" parameterType="SysUser" resultMap="SysUserResult">
		 SELECT
		 u.user_id,
		 u.dept_id,
		 u.school_id,
		 u.house_id,
		 u.entry_time,
		 u.employment_status,
		 u.nick_name,
		 u.user_name,
		 u.email,
		 u.avatar,
		 u.phonenumber,
		 u.sex,
		 u.status,
		 u.del_flag,
		 u.login_ip,
		 u.login_date,
		 u.create_by,
		 u.create_time,
		 u.remark,
		 d.dept_name,
		 d.leader,
		 u.user_type,
		 s.school_name,
		 ph.house_name,
		 GROUP_CONCAT(DISTINCT p.post_name SEPARATOR ', ') AS post_name,
		 GROUP_CONCAT(DISTINCT r.role_name SEPARATOR ', ') AS role_name
		 FROM
		 sys_user u
		 LEFT JOIN sys_dept d ON u.dept_id = d.dept_id
		 LEFT JOIN dutp_school s ON u.school_id = s.school_id
		 LEFT JOIN dutp_publishing_house ph ON u.house_id = ph.house_id
		 LEFT JOIN sys_user_post up ON up.user_id = u.user_id
		 LEFT JOIN sys_post p ON p.post_id = up.post_id
		 LEFT JOIN sys_user_role ur ON ur.user_id = u.user_id
		 LEFT JOIN sys_role r ON r.role_id = ur.role_id
		 WHERE
		 u.del_flag = '0'
		 AND u.user_id != 1
		<if test="userId != null and userId != 0">
			AND u.user_id = #{userId}
		</if>
		<if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		 <if test="nickName != null and nickName != ''">
			 AND u.nick_name like concat('%', #{nickName}, '%')
		 </if>
		<if test="status != null and status != ''">
			AND u.status = #{status}
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<if test="schoolId != null and schoolId != ''">
			AND u.school_id = #{schoolId}
		</if>
		 <if test="employmentStatus != null">
			 AND u.employment_status = #{employmentStatus}
		 </if>
		 <if test="entryTime != null">
		    AND date_format(u.entry_time,'%Y%m%d') &lt;=  date_format(#{entryTime},'%Y%m%d')
		 </if>
		<if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
			AND date_format(u.create_time,'%Y%m%d') &gt;= date_format(#{params.beginTime},'%Y%m%d')
		</if>
		<if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
			AND date_format(u.create_time,'%Y%m%d') &lt;= date_format(#{params.endTime},'%Y%m%d')
		</if>
		<if test="deptId != null and deptId != 0">
			AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors) ))
		</if>
		 GROUP BY
		 u.user_id,
		 u.dept_id,
		 u.school_id,
		 u.house_id,
		 u.entry_time,
		 u.employment_status,
		 u.nick_name,
		 u.user_name,
		 u.email,
		 u.avatar,
		 u.phonenumber,
		 u.sex,
		 u.status,
		 u.del_flag,
		 u.login_ip,
		 u.login_date,
		 u.create_by,
		 u.create_time,
		 u.remark,
		 d.dept_name,
		 d.leader,
		 u.user_type,
		 s.school_name,
		 ph.house_name
		 ORDER BY
		 u.user_id
	</select>
	
	<select id="selectAllocatedList" parameterType="SysUser" resultMap="SysUserResult">
	    select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
	    from sys_user u
			 left join sys_dept d on u.dept_id = d.dept_id
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and r.role_id = #{roleId}
	    <if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
	</select>
	
	<select id="selectUnallocatedList" parameterType="SysUser" resultMap="SysUserResult">
	    select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
	    from sys_user u
			 left join sys_dept d on u.dept_id = d.dept_id
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and (r.role_id != #{roleId} or r.role_id IS NULL)
	    and u.user_id not in (select u.user_id from sys_user u inner join sys_user_role ur on u.user_id = ur.user_id and ur.role_id = #{roleId})
	    <if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="schoolId != null">
			AND u.school_id = #{schoolId}
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
	</select>
	
	<select id="selectUserByUserName" parameterType="String" resultMap="SysUserResult">
	    <include refid="selectUserVo"/>
		where u.user_name = #{userName} and u.del_flag = '0' and user_type = '00'
	</select>
	<select id="selectEduUserByUserName" parameterType="String" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.user_name = #{userName} and u.del_flag = '0' and user_type = '01'
	</select>
	<select id="selectUserById" parameterType="Long" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.user_id = #{userId}
	</select>

	<select id="selectUserByUserPhone" resultType="cn.dutp.system.api.domain.SysUser">
		<include refid="selectUserVo"/>
		where u.phonenumber = #{phonenumber}
		and u.del_flag = 0
	</select>
	<select id="selectEduUserByPhoneNumber" resultType="cn.dutp.system.api.domain.SysUser">
		<include refid="selectUserVo"/>
		where u.phonenumber = #{phonenumber} and u.user_type='01'
	</select>
	<select id="checkUserNameUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, user_name from sys_user where user_name = #{userName} and del_flag = '0' limit 1
	</select>
	
	<select id="checkPhoneUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, phonenumber from sys_user where phonenumber = #{phonenumber} and del_flag = '0' limit 1
	</select>
	
	<select id="checkEmailUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, email from sys_user where email = #{email} and del_flag = '0' limit 1
	</select>

	<select id="listNotPage" resultType="cn.dutp.system.api.domain.SysUser">
		SELECT
			u.user_id,
			u.nick_name,
			u.phonenumber
		FROM
			sys_user u
				INNER JOIN sys_user_role ur on ur.user_id = u.user_id
		WHERE
			u.del_flag = '0'
		  and (ur.role_id = 3 or ur.role_id = 4)
		  AND u.STATUS = 0
		GROUP BY u.user_id
	</select>

	<select id="listUserByDeptId" resultType="cn.dutp.system.api.domain.SysUser" parameterType="cn.dutp.system.api.domain.SysUser">
		SELECT
			u.user_id,
			u.nick_name,
			u.phonenumber
		FROM
			sys_user u
		WHERE
			u.del_flag = '0'
			  and u.STATUS = 0
			  and u.dept_id = #{deptId}

	</select>

	<select id="listUserByMenuId" resultType="cn.dutp.system.api.domain.SysUser" parameterType="Long">
		select
		    u.user_id,
		    u.dept_id,
		    u.user_name,
		    u.nick_name,
		    u.user_type,
		    u.email,
		    u.phonenumber,
		    u.sex,
		    u.avatar
		from
			sys_user u
		left join
			sys_user_role sur on sur.user_id = u.user_id
		left join
			sys_role sr on sr.role_id = sur.role_id and sr.status = 0 and sr.del_flag = 0
		left join
			sys_role_menu srm on srm.role_id = sr.role_id
		where
		    u.status = 0 and (u.employment_status = 1 or u.employment_status is NULL) and u.user_type = '00'
		  	and u.del_flag = 0 and srm.menu_id = #{menuId}
	</select>

	<insert id="insertUser" parameterType="SysUser" useGeneratedKeys="true" keyProperty="userId">
 		insert into sys_user(
 			<if test="userId != null and userId != 0">user_id,</if>
 			<if test="deptId != null and deptId != 0">dept_id,</if>
			<if test="schoolId != null and schoolId != 0">school_id,</if>
			<if test="houseId != null and houseId != 0">house_id,</if>
			<if test="entryTime != null">entry_time,</if>
			<if test="employmentStatus != null">employment_status,</if>
 			<if test="userName != null and userName != ''">user_name,</if>
 			<if test="nickName != null and nickName != ''">nick_name,</if>
 			<if test="email != null and email != ''">email,</if>
 			<if test="avatar != null and avatar != ''">avatar,</if>
 			<if test="phonenumber != null and phonenumber != ''">phonenumber,</if>
 			<if test="sex != null and sex != ''">sex,</if>
 			<if test="password != null and password != ''">password,</if>
 			<if test="status != null and status != ''">status,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			<if test="remark != null and remark != ''">remark,</if>
			<if test="userType != null and userType != ''">user_type,</if>
 			create_time
 		)values(
 			<if test="userId != null and userId != ''">#{userId},</if>
 			<if test="deptId != null and deptId != ''">#{deptId},</if>
			<if test="schoolId != null and schoolId != 0">#{schoolId},</if>
			<if test="houseId != null and houseId != 0">#{houseId},</if>
			<if test="entryTime != null">#{entryTime},</if>
			<if test="employmentStatus != null">#{employmentStatus},</if>
 			<if test="userName != null and userName != ''">#{userName},</if>
 			<if test="nickName != null and nickName != ''">#{nickName},</if>
 			<if test="email != null and email != ''">#{email},</if>
 			<if test="avatar != null and avatar != ''">#{avatar},</if>
 			<if test="phonenumber != null and phonenumber != ''">#{phonenumber},</if>
 			<if test="sex != null and sex != ''">#{sex},</if>
 			<if test="password != null and password != ''">#{password},</if>
 			<if test="status != null and status != ''">#{status},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			<if test="remark != null and remark != ''">#{remark},</if>
			<if test="userType != null and userType != ''">#{userType},</if>
 			sysdate()
 		)
	</insert>
	
	<update id="updateUser" parameterType="SysUser">
 		update sys_user
 		<set>
 			<if test="deptId != null and deptId != 0">dept_id = #{deptId},</if>
			<if test="houseId != null and houseId != 0">house_id = #{houseId},</if>
			<if test=" schoolId != 0 and schoolId != null">school_id = #{schoolId},</if>
 			<if test="userName != null and userName != ''">user_name = #{userName},</if>
 			<if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
 			<if test="email != null ">email = #{email},</if>
 			<if test="phonenumber != null ">phonenumber = #{phonenumber},</if>
 			<if test="sex != null and sex != ''">sex = #{sex},</if>
 			<if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
 			<if test="password != null and password != ''">password = #{password},</if>
 			<if test="status != null and status != ''">status = #{status},</if>
 			<if test="loginIp != null and loginIp != ''">login_ip = #{loginIp},</if>
 			<if test="loginDate != null">login_date = #{loginDate},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
			<if test="userType != null and userType != ''">user_type = #{userType},</if>
 			<if test="remark != null">remark = #{remark},</if>
 			<if test="entryTime != null">entry_time = #{entryTime},</if>
 			<if test="employmentStatus != null">employment_status = #{employmentStatus},</if>
 			update_time = sysdate()
 		</set>
 		where user_id = #{userId}
	</update>
	
	<update id="updateUserStatus" parameterType="SysUser">
 		update sys_user set status = #{status} where user_id = #{userId}
	</update>
	
	<update id="updateUserAvatar" parameterType="SysUser">
 		update sys_user set avatar = #{avatar} where user_name = #{userName}
	</update>
	
	<update id="resetUserPwd" parameterType="SysUser">
 		update sys_user set password = #{password} where user_name = #{userName}
	</update>
	
	<delete id="deleteUserById" parameterType="Long">
 		update sys_user set del_flag = '2' where user_id = #{userId}
 	</delete>
 	
 	<delete id="deleteUserByIds" parameterType="Long">
 		update sys_user set del_flag = '2' where user_id in
 		<foreach collection="array" item="userId" open="(" separator="," close=")">
 			#{userId}
        </foreach> 
 	</delete>


	
</mapper> 
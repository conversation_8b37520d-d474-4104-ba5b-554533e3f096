<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.system.mapper.DutpUserBlackListMapper">

    <resultMap type="DutpUserBlackList" id="DutpUserBlackListResult">
        <result property="blackListId"    column="black_list_id"    />
        <result property="userId"    column="user_id"    />
        <result property="frozenType"    column="frozen_type"    />
        <result property="frozenScope"    column="frozen_scope"    />
        <result property="frozenReason"    column="frozen_reason"    />
        <result property="state"    column="state"    />
        <result property="endDate"    column="end_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDutpUserBlackListVo">
        select black_list_id, user_id, frozen_type, frozen_scope, frozen_reason, state, end_date, create_by, create_time, update_by, update_time from dutp_user_black_list
    </sql>

    <select id="selectDutpUserBlackListList" parameterType="DutpUserBlackList" resultMap="DutpUserBlackListResult">
        <include refid="selectDutpUserBlackListVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="frozenType != null "> and frozen_type = #{frozenType}</if>
            <if test="frozenScope != null "> and frozen_scope = #{frozenScope}</if>
            <if test="frozenReason != null  and frozenReason != ''"> and frozen_reason = #{frozenReason}</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="endDate != null "> and end_date = #{endDate}</if>
        </where>
    </select>

    <select id="selectDutpUserBlackListByBlackListId" parameterType="Long" resultMap="DutpUserBlackListResult">
        <include refid="selectDutpUserBlackListVo"/>
        where black_list_id = #{blackListId}
    </select>

    <insert id="insertDutpUserBlackList" parameterType="DutpUserBlackList" useGeneratedKeys="true" keyProperty="blackListId">
        insert into dutp_user_black_list
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="frozenType != null">frozen_type,</if>
            <if test="frozenScope != null">frozen_scope,</if>
            <if test="frozenReason != null">frozen_reason,</if>
            <if test="state != null">state,</if>
            <if test="endDate != null">end_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="frozenType != null">#{frozenType},</if>
            <if test="frozenScope != null">#{frozenScope},</if>
            <if test="frozenReason != null">#{frozenReason},</if>
            <if test="state != null">#{state},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateDutpUserBlackList" parameterType="DutpUserBlackList">
        update dutp_user_black_list
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="frozenType != null">frozen_type = #{frozenType},</if>
            <if test="frozenScope != null">frozen_scope = #{frozenScope},</if>
            <if test="frozenReason != null">frozen_reason = #{frozenReason},</if>
            <if test="state != null">state = #{state},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where black_list_id = #{blackListId}
    </update>

    <delete id="deleteDutpUserBlackListByBlackListId" parameterType="Long">
        delete from dutp_user_black_list where black_list_id = #{blackListId}
    </delete>

    <delete id="deleteDutpUserBlackListByBlackListIds" parameterType="String">
        delete from dutp_user_black_list where black_list_id in
        <foreach item="blackListId" collection="array" open="(" separator="," close=")">
            #{blackListId}
        </foreach>
    </delete>
    <select id="getProhibitionOfExchange" parameterType="Long" resultMap="DutpUserBlackListResult">
        <include refid="selectDutpUserBlackListVo"/>
        <where>
            frozen_type = 3
            and frozen_scope = 3
            and state = 1
            and end_date >= NOW()
            <if test="userId != null "> and user_id = #{userId}</if>
        </where>
    </select>
</mapper>
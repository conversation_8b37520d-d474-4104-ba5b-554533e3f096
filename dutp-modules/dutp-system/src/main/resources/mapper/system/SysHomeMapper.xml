<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.system.mapper.SysHomeMapper">
    <!--我审核的教材-->
    <select id="myReviewTextbook" resultType="cn.dutp.system.domain.vo.ResVo">
        WITH RankedProcesses AS (
            SELECT
                book.book_organize AS 'bookOrganize',
                    ROW_NUMBER() OVER (PARTITION BY book.book_id ORDER BY bpp.create_time DESC) AS rn
            FROM
                dtb_book book
                    INNER JOIN dtb_book_version bv ON book.last_version_id = bv.version_id
                    INNER JOIN dtb_book_publish_step bps ON book.current_step_id = bps.step_id
                    INNER JOIN dtb_book_publish_process bpp ON book.book_id = bpp.book_id AND book.current_step_id = bpp.step_id
                    INNER JOIN dtb_book_publish_process_audit_user bpau ON bpp.process_id = bpau.process_id
            WHERE
             bpau.user_id = #{userId} or bpau.handle_user_id = #{userId}
             and book.del_flag = '0'
             and bpp.state = 1
        )
        SELECT bookOrganize AS bookOrganize
        FROM RankedProcesses
        WHERE rn = 1
    </select>

    <!--查询处理过的教材数量-->
    <select id="textbookProcessing" resultType="cn.dutp.system.domain.vo.ResVo">
        WITH RankedProcesses AS (
            SELECT
                book.book_organize AS 'bookOrganize',
                    bpp.state AS 'state',
                    ROW_NUMBER() OVER (PARTITION BY book.book_id ORDER BY bpp.create_time DESC) AS rn
            FROM
                dtb_book book
                    INNER JOIN dtb_book_version bv ON book.last_version_id = bv.version_id
                    INNER JOIN dtb_book_publish_step bps ON book.current_step_id = bps.step_id
                    INNER JOIN dtb_book_publish_process bpp ON book.book_id = bpp.book_id AND book.current_step_id = bpp.step_id
                    INNER JOIN dtb_book_publish_process_audit_user bpau ON bpp.process_id = bpau.process_id
            WHERE
                book.del_flag = '0'
        )
        SELECT bookOrganize AS bookOrganize,
               state AS state
        FROM RankedProcesses
        WHERE rn = 1
    </select>

    <!--查询待处理的教材，以及对应分类数量-->
    <select id="textbooksToBeDoneNumber" resultType="cn.dutp.system.domain.vo.BookManagementVo">
        SELECT count(dbpp.process_id) AS textbookProcessingNumber
        FROM dtb_book db
                 LEFT JOIN
             dtb_book_publish_process dbpp ON
                 db.book_id = dbpp.book_id
        WHERE dbpp.state != '0'
          AND db.del_flag = '0';
    </select>

    <!--我审核的订单-->
    <select id="myReviewBookOrder" resultType="cn.dutp.system.domain.vo.BookOrderManagementMyReviewVo">
        SELECT COUNT(*)                                             AS myReviewOrderNumber,
               SUM(CASE WHEN dbo.order_type = 2 THEN 1 ELSE 0 END)  AS academicProcurementOrderNumber,
               SUM(CASE WHEN dbo.order_type != 2 THEN 1 ELSE 0 END) AS otherProcurementOrderNumber
        FROM dtb_book_order dbo
                 LEFT JOIN dtb_book_order_audit_user dboal ON
            dbo.order_id = dboal.order_id
        where dbo.deleted = '1'
          AND dboal.user_id = #{userId};
    </select>

    <!--查询订单的处理完成数和待办数-->
    <select id="bookOrderManagement" resultType="cn.dutp.system.domain.vo.BookOrderManagementVo">
        SELECT
            SUM(CASE WHEN audit.audit_status = 0 THEN 1 ELSE 0 END) AS orderToBeDoneTotal,
            SUM(CASE WHEN audit.audit_status = 1 || audit.audit_status = 2 THEN 1 ELSE 0 END) AS orderProcessingTotal,
            SUM(CASE WHEN dborder.order_type = 4 &amp;&amp; audit.audit_status = 0 THEN 1 ELSE 0 END) AS otherPaymentOrderNumber,
            SUM(CASE WHEN dborder.order_type IN (2, 3, 5) &amp;&amp; audit.audit_status = 0 THEN 1 ELSE 0 END) AS educationalPaymentOrderNumber,
            SUM(CASE WHEN dborder.order_type = 4 &amp;&amp; audit.audit_status = 0 &amp;&amp; audit.user_id = #{userId} THEN 1 ELSE 0 END) AS otherProcurementOrderNumber,
            SUM(
                    CASE

                        WHEN dborder.order_type IN (2, 3, 5) &amp;&amp; audit.audit_status = 0 &amp;&amp; audit.user_id  = #{userId} THEN
                            1 ELSE 0
                        END) AS academicProcurementOrderNumber,
            SUM(
                    CASE

                        WHEN (dborder.order_type IN (2, 3, 5) || dborder.order_type = 4) &amp;&amp; audit.user_id  = #{userId} &amp;&amp; audit.audit_status = 0 THEN
                            1 ELSE 0
                        END) AS myReviewOrderNumber
        FROM
            dtb_book_order_audit_user audit
                LEFT JOIN dtb_book_order dborder ON dborder.order_id = audit.order_id
                LEFT JOIN dutp_sale_area area ON dborder.area_id = area.area_id
                LEFT JOIN dtb_book_merchant merchant ON merchant.merchant_id = dborder.merchant_id
                LEFT JOIN sys_user suser ON dborder.operator_id = suser.user_id
                LEFT JOIN dutp_school school ON dborder.school_id = school.school_id
        WHERE
            dborder.deleted = '1'
    </select>
    <!--查询订单支付待确认数据，教务采购和其他采购两个维度-->
    <select id="bookOrderPayment" resultType="cn.dutp.system.domain.vo.BookOrderPaymentVo">
        SELECT SUM(CASE WHEN order_type = 2 THEN 1 ELSE 0 END)  AS educationalPaymentOrderNumber,
               SUM(CASE WHEN order_type != 2 THEN 1 ELSE 0 END) AS otherPaymentOrderNumber
        FROM dtb_book_order
        WHERE order_status = 'pending'
          AND deleted = '1';
    </select>

    <!--根据用户id，查询我审核的教务订单类型的 成功退款订单数，成功退款商品数，成功退款金额-->
    <select id="myReviewAcademicBookRefundOrder"
            resultType="cn.dutp.system.domain.vo.BookOrderRefundManagementMyReviewVo">
        SELECT COUNT(dbro.refund_order_id) AS academicOrderRefundNumber,
               SUM(dbroi.refund_quantity)  AS academicRefundGoodsNumber,
               SUM(dbroi.refund_amount)    AS academicRefundAmount
        FROM dtb_book_refund_order dbro
                 LEFT JOIN
             dtb_book_order dbo ON
                 dbo.order_id = dbro.order_id
                 LEFT JOIN dtb_book_refund_order_item dbroi
                           ON dbro.refund_order_id = dbroi.refund_order_id
        WHERE dbro.audit_user_id = #{auditUserId}
          AND dbro.refund_status = '1'
          AND dbo.order_type = '2'
          AND dbo.deleted = '1';
    </select>
    <!--根据用户id查询教务订单类型我驳回的售后订单数量-->
    <select id="academicRefundRejectOrder" resultType="cn.dutp.system.domain.vo.BookOrderRefundManagementMyReviewVo">
        SELECT COUNT(dbro.refund_order_id) AS academicRefundRejectNumber
        FROM dtb_book_refund_order dbro
                 LEFT JOIN
             dtb_book_order dbo ON
                 dbo.order_id = dbro.order_id
                 LEFT JOIN dtb_book_refund_order_item dbroi
                           ON dbro.refund_order_id = dbroi.refund_order_id
        WHERE dbro.audit_user_id = #{auditUserId}
          AND dbro.refund_status = '2'
          AND dbo.order_type = '2'
          AND dbo.deleted = '1'
    </select>
    <!--根据用户id，查询我审核的零售订单类型的 成功退款订单数，成功退款商品数，成功退款金额-->
    <select id="myReviewRetailBookRefundOrder"
            resultType="cn.dutp.system.domain.vo.BookOrderRefundManagementMyReviewVo">
        SELECT COUNT(dbro.refund_order_id) AS retailOrderRefundNumber,
               SUM(dbroi.refund_quantity)  AS retailRefundGoodsNumber,
               SUM(dbroi.refund_amount)    AS retailRefundAmount
        FROM dtb_book_refund_order dbro
                 LEFT JOIN
             dtb_book_order dbo ON
                 dbo.order_id = dbro.order_id
                 LEFT JOIN dtb_book_refund_order_item dbroi
                           ON dbro.refund_order_id = dbroi.refund_order_id
        WHERE dbro.audit_user_id = #{auditUserId}
          AND dbro.refund_status = '1'
          AND dbo.order_type = '1'
          AND dbo.deleted = '1';
    </select>
    <!--根据用户id查询零售订单类型我驳回的售后订单数量-->
    <select id="retailRefundRejectOrder" resultType="cn.dutp.system.domain.vo.BookOrderRefundManagementMyReviewVo">
        SELECT COUNT(dbro.refund_order_id) AS retailRefundRejectNumber
        FROM dtb_book_refund_order dbro
                 LEFT JOIN
             dtb_book_order dbo ON
                 dbo.order_id = dbro.order_id
                 LEFT JOIN dtb_book_refund_order_item dbroi
                           ON dbro.refund_order_id = dbroi.refund_order_id
        WHERE dbro.audit_user_id = #{auditUserId}
          AND dbro.refund_status = '2'
          AND dbo.order_type = '1'
          AND dbo.deleted = '1'
    </select>
    <!--查询待处理的售后订单-待办数-->
    <select id="pendingRefundOrder" resultType="cn.dutp.system.domain.vo.BookOrderRefundManagementVo">
        SELECT SUM(CASE WHEN dbo.order_type = 1 THEN 1 ELSE 0 END) AS retailRefundToBeDoneNumber,
               SUM(CASE WHEN order_type = 2 THEN 1 ELSE 0 END)     AS academicRefundToBeDoneNumber
        FROM dtb_book_refund_order dbro
                 LEFT JOIN
             dtb_book_order dbo
             ON dbo.order_id = dbro.order_id
        WHERE dbro.refund_status = '0'
          AND dbo.deleted = '1';
    </select>
    <!--查询处理完成的售后订单-处理数-->
    <select id="processedCompletedRefundOrder" resultType="cn.dutp.system.domain.vo.BookOrderRefundManagementVo">
        SELECT
            IFNULL(SUM(CASE WHEN dbro.refund_status = 0 THEN 1 ELSE 0 END),0) AS orderRefundToBeDoneTotal,
            IFNULL(SUM(CASE WHEN dbro.refund_status != 0 THEN 1 ELSE 0 END),0) AS orderRefundTotal,
            IFNULL(SUM(CASE WHEN dbo.order_type IN (2, 3, 5) &amp;&amp; dbro.refund_status = 0 THEN 1 ELSE 0 END),0) AS academicRefundToBeDoneNumber,
            IFNULL(SUM(CASE WHEN dbo.order_type = 1 &amp;&amp; dbro.refund_status = 0 THEN 1 ELSE 0 END),0) AS retailRefundToBeDoneNumber,
            IFNULL(SUM(
                    CASE

                        WHEN dbo.order_type IN (2, 3, 5) &amp;&amp; dbro.audit_user_id = #{userId}  &amp;&amp; dbro.refund_order_status = 2 THEN
                            1 ELSE 0
                        END),0) AS academicOrderRefundNumber,
            IFNULL(SUM(
                    CASE

                        WHEN dbo.order_type IN (2, 3, 5) &amp;&amp; dbro.audit_user_id = #{userId}  &amp;&amp; dbro.refund_order_status = 2 THEN
                            dbroi.refund_quantity ELSE 0
                        END),0) AS academicRefundGoodsNumber,
            IFNULL(SUM(
                    CASE

                        WHEN dbo.order_type IN (2, 3, 5) &amp;&amp; dbro.audit_user_id = #{userId}  &amp;&amp; dbro.refund_status != 0 THEN
                            dbroi.refund_amount ELSE 0
                        END),0) AS academicRefundAmount,
            IFNULL(SUM(
                    CASE

                        WHEN dbo.order_type IN (2, 3, 5) &amp;&amp; dbro.audit_user_id = #{userId}  &amp;&amp; dbro.refund_status = 2 THEN
                            1 ELSE 0
                        END),0) AS academicRefundRejectNumber,
            IFNULL(SUM(CASE WHEN dbo.order_type = 1 &amp;&amp; dbro.audit_user_id = #{userId}  &amp;&amp; dbro.refund_order_status = 2 THEN 1 ELSE 0 END),0) AS retailOrderRefundNumber,
            IFNULL(SUM(CASE WHEN dbo.order_type = 1 &amp;&amp; dbro.audit_user_id = #{userId}  &amp;&amp; dbro.refund_order_status = 2 THEN dbroi.refund_quantity ELSE 0 END),0) AS retailRefundGoodsNumber,
            IFNULL(SUM(CASE WHEN dbo.order_type = 1 &amp;&amp; dbro.audit_user_id = #{userId}  &amp;&amp; dbro.refund_status != 0 THEN dbroi.refund_amount ELSE 0 END),0) AS retailRefundAmount,
            IFNULL(SUM(CASE WHEN dbo.order_type = 1 &amp;&amp; dbro.audit_user_id = #{userId}  &amp;&amp; dbro.refund_status = 2 THEN 1 ELSE 0 END),0) AS retailRefundRejectNumber,
            IFNULL(SUM(CASE WHEN dbro.audit_user_id = #{userId}  &amp;&amp; dbro.refund_status != 0 THEN 1 ELSE 0 END),0) AS myHandleCount
        FROM
            dtb_book_refund_order dbro
                LEFT JOIN dtb_book_refund_order_item dbroi ON dbro.refund_order_id = dbroi.refund_order_id
                LEFT JOIN dtb_book_order dbo ON dbo.order_id = dbro.order_id
    </select>

    <!--查询教务类型的订单的发票待上传数量和处理完成的数量-->
    <select id="selectAcademicInvoice" resultType="cn.dutp.system.domain.vo.InvoiceApplyManagementVo">
        SELECT
            IFNULL(SUM(CASE WHEN duia.apply_status != 1 THEN 1 ELSE 0 END),0) AS invoiceTotal,
            IFNULL(SUM(CASE WHEN duia.apply_status = 1 THEN 1 ELSE 0 END),0) AS invoiceToBeDoneTotal,
            IFNULL(SUM(CASE WHEN dbo.order_type IN (2, 3, 5) &amp;&amp; duia.apply_status = 1 THEN 1 ELSE 0 END),0) AS academicinvoiceToBeDoneNumber,
            IFNULL(SUM(CASE WHEN dbo.order_type = 1 &amp;&amp; duia.apply_status = 1 THEN 1 ELSE 0 END),0) AS retailInvoiceToBeDoneNumber,
            IFNULL(SUM(CASE WHEN duia.deal_user_id = #{userId}  &amp;&amp; duia.apply_status != 1 THEN 1 ELSE 0 END),0) AS myReviewInvoiceTotalNum,
            IFNULL(SUM(
                    CASE

                        WHEN dbo.order_type IN (2, 3, 5) &amp;&amp; duia.deal_user_id = #{userId}  &amp;&amp; duia.apply_status = 2 THEN
                            1 ELSE 0
                        END),0) AS academicMyReviewInvoiceUploadNumber,
            IFNULL(SUM(
                    CASE

                        WHEN dbo.order_type IN (2, 3, 5) &amp;&amp; duia.deal_user_id = #{userId}  &amp;&amp; duia.apply_status = 3 THEN
                            1 ELSE 0
                        END),0) AS academicMyReviewCancelNumber,
            IFNULL(SUM(CASE WHEN dbo.order_type = 1 &amp;&amp; duia.deal_user_id = #{userId}  &amp;&amp; duia.apply_status = 2 THEN 1 ELSE 0 END),0) AS retailMyReviewInvoiceUploadNumber,
            IFNULL(SUM(CASE WHEN dbo.order_type = 1 &amp;&amp; duia.deal_user_id = #{userId}  &amp;&amp; duia.apply_status = 3 THEN 1 ELSE 0 END),0) AS retailMyReviewCancelNumber
        FROM
            dtb_user_invoice_apply duia
                LEFT JOIN dtb_book_order dbo ON duia.order_id = dbo.order_id
    </select>
    <!--查询零售类型的订单的发票待上传数量和处理完成的数量-->
    <select id="selectRetailInvoice" resultType="cn.dutp.system.domain.vo.InvoiceApplyManagementVo">
        SELECT SUM(CASE WHEN duia.apply_status = 1 THEN 1 ELSE 0 END) AS retailInvoiceToBeDoneNumber,
               SUM(CASE WHEN duia.apply_status = 2 THEN 1 ELSE 0 END) AS retailInvoiceFinishNumber
        FROM dtb_user_invoice_apply duia
                 LEFT JOIN dtb_book_order dbo
                           ON duia.order_id = dbo.order_id
        WHERE dbo.order_type = '1'
          AND dbo.deleted = '1';
    </select>

    <!--我审核的教务类型的成功上传发票和作废发票数量-->
    <select id="selectmyReviewAcademicInvoice" resultType="cn.dutp.system.domain.vo.InvoiceApplyManagementMyReviewVo">
        SELECT SUM(CASE WHEN duia.apply_status = 1 THEN 1 ELSE 0 END) AS academicMyReviewInvoiceUploadNumber,
               SUM(CASE WHEN duia.apply_status = 2 THEN 1 ELSE 0 END) AS academicMyReviewCancelNumber
        FROM dtb_user_invoice_apply duia
                 LEFT JOIN dtb_book_order dbo
                           ON duia.order_id = dbo.order_id
        WHERE duia.deal_user_id = #{dealUserId}
          and dbo.order_type = '2'
          AND dbo.deleted = '1';
    </select>
    <!--我审核的零售类型的成功上传发票和作废发票数量-->
    <select id="selectmyReviewRetailInvoice" resultType="cn.dutp.system.domain.vo.InvoiceApplyManagementMyReviewVo">
        SELECT SUM(CASE WHEN duia.apply_status = 1 THEN 1 ELSE 0 END) AS retailMyReviewInvoiceUploadNumber,
               SUM(CASE WHEN duia.apply_status = 2 THEN 1 ELSE 0 END) AS retailMyReviewCancelNumber
        FROM dtb_user_invoice_apply duia
                 LEFT JOIN dtb_book_order dbo
                           ON duia.order_id = dbo.order_id
        WHERE duia.deal_user_id = #{dealUserId}
          and dbo.order_type = '1'
          AND dbo.deleted = '1';
    </select>

    <!--查询教师申请的试用教材待审核数量-->
    <select id="selectTrialApply" resultType="cn.dutp.system.domain.vo.DtbUserTrialApplyManagementVo">
        SELECT
            SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) AS trialApplyTotal,
            SUM(CASE WHEN audit_user_id = #{auditUserId} &amp;&amp; status != 0 THEN 1 ELSE 0 END) AS myReviewTrialApplyTotal
        FROM
            dtb_user_trial_apply
    </select>

    <!--根据用户id 查询我审核的教师申请的试用教材数量-->
    <select id="selectMyReviewTrialApply" resultType="cn.dutp.system.domain.vo.DtbUserTrialApplyManagementVo">
        SELECT COUNT(*) AS myReviewTrialApplyTotal
        FROM dtb_user_trial_apply
        WHERE audit_user_id = #{auditUserId}
          AND status IN ('2', '1');
    </select>

    <!--根据用户id 查询我处理过的用户反馈数量-->
    <select id="selectMyReviewUserWorkOrderNumber" resultType="cn.dutp.system.domain.vo.DutpUserWorkOrderManagementVo">
        SELECT
            SUM(CASE WHEN d.STATUS = 'pending' THEN 1 ELSE 0 END) AS customerChatNumber,
            SUM(CASE WHEN d.STATUS = 'inProgress' AND d.process_user_id = #{processUserId} THEN 1 ELSE 0 END) AS userWorkOrderNumber,
            SUM(CASE WHEN d.STATUS = 'resolved' AND d.process_user_id = #{processUserId} THEN 1 ELSE 0 END) AS myReviewUserWorkOrderNumber
        FROM
            dutp_user_work_order d
        WHERE
            d.del_flag = 0
    </select>

    <!--待处理的用户反馈数-->
    <select id="selectUserWorkOrderNumber" resultType="cn.dutp.system.domain.vo.DutpUserWorkOrderManagementVo">
        SELECT COUNT(*) AS userWorkOrderNumber
        FROM dutp_user_work_order
        WHERE status = 'pending'
          AND del_flag = '0'
    </select>

    <!--客服回复——会话待开启数量-->
    <select id="selectCustomerChatNumber" resultType="cn.dutp.system.domain.vo.DutpCustomerChatManagementVo">
        SELECT count(chat_id) AS customerChatNumber
        FROM dutp_customer_chat
        WHERE chat_status = '1'
          AND del_flag = '0'
    </select>

    <!--发行库存预警：dtb_book_purchase_code状态state<8的教材。-->
    <select id="selectBookPurchaseCode" resultType="cn.dutp.system.domain.vo.DtbBookPurchaseCodeManagementVo">
        WITH BookInventory AS (SELECT book_id, COUNT(0) AS bookPurchaseNumber FROM dtb_book_purchase_code WHERE code_type = 1 AND del_flag = 0 AND state = 1 GROUP BY book_id) SELECT
        dtb.book_name,
        COALESCE(bi.bookPurchaseNumber, 0) AS bookPurchaseNumber
        FROM
        dtb_book dtb
        LEFT JOIN BookInventory bi ON dtb.book_id = bi.book_id
        WHERE
        dtb.del_flag = 0
        AND dtb.master_flag != 3
        AND ((dtb.shelf_state IN (1, 2) AND dtb.publish_status = 2) OR (dtb.shelf_state = 4))
        AND dtb.book_organize = 1
        AND COALESCE(bi.bookPurchaseNumber, 0)  &lt; 5;
    </select>


</mapper>

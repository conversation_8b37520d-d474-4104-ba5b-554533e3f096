<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.system.mapper.DutpAvatarMapper">
    
    <resultMap type="DutpAvatar" id="DutpAvatarResult">
        <result property="avatarId"    column="avatar_id"    />
        <result property="avatarUrl"    column="avatar_url"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDutpAvatarVo">
        select avatar_id, avatar_url, del_flag, create_by, create_time, update_by, update_time from dutp_avatar
    </sql>

    <select id="selectDutpAvatarList" parameterType="DutpAvatar" resultMap="DutpAvatarResult">
        <include refid="selectDutpAvatarVo"/>
        <where>  
            <if test="avatarUrl != null  and avatarUrl != ''"> and avatar_url = #{avatarUrl}</if>
        </where>
    </select>
    
    <select id="selectDutpAvatarByAvatarId" parameterType="Long" resultMap="DutpAvatarResult">
        <include refid="selectDutpAvatarVo"/>
        where avatar_id = #{avatarId}
    </select>

    <insert id="insertDutpAvatar" parameterType="DutpAvatar" useGeneratedKeys="true" keyProperty="avatarId">
        insert into dutp_avatar
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="avatarUrl != null">avatar_url,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="avatarUrl != null">#{avatarUrl},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDutpAvatar" parameterType="DutpAvatar">
        update dutp_avatar
        <trim prefix="SET" suffixOverrides=",">
            <if test="avatarUrl != null">avatar_url = #{avatarUrl},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where avatar_id = #{avatarId}
    </update>

    <delete id="deleteDutpAvatarByAvatarId" parameterType="Long">
        delete from dutp_avatar where avatar_id = #{avatarId}
    </delete>

    <delete id="deleteDutpAvatarByAvatarIds" parameterType="String">
        delete from dutp_avatar where avatar_id in 
        <foreach item="avatarId" collection="array" open="(" separator="," close=")">
            #{avatarId}
        </foreach>
    </delete>
</mapper>
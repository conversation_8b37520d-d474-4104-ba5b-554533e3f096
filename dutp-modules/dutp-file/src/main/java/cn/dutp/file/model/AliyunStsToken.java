package cn.dutp.file.model;

import lombok.Data;

import java.io.Serializable;

@Data
public class AliyunStsToken implements Serializable {

    /**
     * 过期时间
     */
    private String expiration;

    /**
     * 临时访问密钥AccessKeyId
     */
    private String accessKeyId;

    /**
     * 临时访问密钥AccessKeySecret
     */
    private String accessKeySecret;

    /**
     * 临时安全令牌SecurityToken
     */
    private String securityToken;

    /**
     * 存储空间名称
     */
    private String bucket;

    /**
     * 存储空间域名
     */
    private String ossEndpoint;
}

package cn.dutp.file.controller;

import cn.dutp.common.core.domain.R;
import cn.dutp.common.core.utils.file.FileUtils;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.redis.service.RedisService;
import cn.dutp.file.config.AliOssConfig;
import cn.dutp.file.model.AliyunStsToken;
import cn.dutp.file.service.ISysFileService;
import cn.dutp.system.api.domain.SysFile;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.auth.sts.AssumeRoleRequest;
import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.concurrent.TimeUnit;

import static cn.dutp.common.core.web.domain.AjaxResult.error;
import static cn.dutp.common.core.web.domain.AjaxResult.success;

/**
 * 文件请求处理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class SysFileController {

    @Autowired
    @Qualifier("ossSysFileServiceImpl")
    private ISysFileService sysFileService;

    @Autowired
    private AliOssConfig aliOssConfig;

    @Autowired
    private RedisService redisService;

    final static private String STS_TOKEN_KEY = "aliyun:stsToken";

    /**
     * 文件上传请求
     */
    @PostMapping("upload")
    public R<SysFile> upload(MultipartFile file) {
        try {
            log.error("接收到请求==={}", file.getOriginalFilename());
            // 上传并返回访问地址
            String url = sysFileService.uploadFile(file);
            SysFile sysFile = new SysFile();
            sysFile.setName(FileUtils.getName(url));
            sysFile.setUrl(url);
            log.error("请求完毕==={}", url);
            return R.ok(sysFile);
        } catch (Exception e) {
            log.error("上传文件失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 获取阿里云配置
     *
     * @return
     */
    @GetMapping("config")
    public R<AliOssConfig> aliConfig() {
        AliOssConfig config = aliOssConfig;
        return R.ok(config);
    }

    /**
     * 获取sts临时token
     *
     * @return
     */
    @GetMapping("/getStsToken")
    public AjaxResult getStsToken() {
        // 判断是否已经存在stsToken
        AliyunStsToken aliyunStsToken = redisService.getCacheObject(STS_TOKEN_KEY);
        if (aliyunStsToken != null) {
            return success(aliyunStsToken);
        }
        return generateStsToken();
    }

    /**
     * 生成stsToken
     *
     * @return
     */
    @NotNull
    private AjaxResult generateStsToken() {
        // STS服务接入点aliOssConfig.getOssEndPoint()
        String endpoint = aliOssConfig.getStsEndPoint();
        String ossEndpoint = aliOssConfig.getOssEndPoint();
        String accessKeyId = aliOssConfig.getAccessKeyId();
        String accessKeySecret = aliOssConfig.getAccessKeySecret();
        String roleArn = aliOssConfig.getRoleArn();
        // regionId表示RAM的地域ID
        String regionId = aliOssConfig.getRegionId();
        String bucket = aliOssConfig.getBucket();
        // 自定义角色会话名称，用来区分不同的令牌
        String roleSessionName = "uploadFile";

        // 临时访问凭证的有效时间，单位为秒。最小值为900，最大值以当前角色设定的最大会话时间为准。当前角色最大会话时间取值范围为3600秒~43200秒，默认值为3600秒。
        // 在上传大文件或者其他较耗时的使用场景中，建议合理设置临时访问凭证的有效时间，确保在完成目标任务前无需反复调用STS服务以获取临时访问凭证。
        Long durationSeconds = aliOssConfig.getUrlExpirationMinute() * 60L;

        try {

            DefaultProfile.addEndpoint(regionId, "Sts", endpoint);
            // 构造default profile。
            IClientProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
            // 构造client。
            DefaultAcsClient client = new DefaultAcsClient(profile);
            final AssumeRoleRequest request = new AssumeRoleRequest();
            request.setSysMethod(MethodType.POST);
            request.setRoleArn(roleArn);
            request.setRoleSessionName(roleSessionName);
            request.setDurationSeconds(durationSeconds);

            final AssumeRoleResponse response = client.getAcsResponse(request);
            AssumeRoleResponse.Credentials credentials = response.getCredentials();
            AliyunStsToken aliyunStsToken = new AliyunStsToken();
            aliyunStsToken.setExpiration(credentials.getExpiration());
            aliyunStsToken.setAccessKeyId(credentials.getAccessKeyId());
            aliyunStsToken.setAccessKeySecret(credentials.getAccessKeySecret());
            aliyunStsToken.setSecurityToken(credentials.getSecurityToken());
            aliyunStsToken.setBucket(bucket);
            aliyunStsToken.setOssEndpoint(ossEndpoint);
            // 放入redis
            redisService.setCacheObject(STS_TOKEN_KEY, aliyunStsToken, (durationSeconds - 5) <= 0 ? durationSeconds : (durationSeconds - 5), TimeUnit.SECONDS);
            log.info("stsToken: {}", aliyunStsToken);
            return success(aliyunStsToken);
        } catch (ClientException e) {
            log.error("获取stsToken失败：{}, {}", e.getMessage(), e.getStackTrace());
            return error("获取stsToken失败");
        }
    }
}
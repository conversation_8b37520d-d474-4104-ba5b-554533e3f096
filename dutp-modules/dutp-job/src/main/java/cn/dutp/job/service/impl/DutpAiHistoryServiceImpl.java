package cn.dutp.job.service.impl;

import cn.dutp.domain.DutpAiHistory;
import cn.dutp.job.mapper.DutpAiHistoryMapper;
import cn.dutp.job.service.IDutpAiHistoryService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * ai请求记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Service
public class DutpAiHistoryServiceImpl extends ServiceImpl<DutpAiHistoryMapper, DutpAiHistory> implements IDutpAiHistoryService
{
    @Autowired
    private DutpAiHistoryMapper dutpAiHistoryMapper;

    /**
     * 查询ai请求记录
     *
     * @param promptHistoryId ai请求记录主键
     * @return ai请求记录
     */
    @Override
    public DutpAiHistory selectDutpAiHistoryByPromptHistoryId(Long promptHistoryId)
    {
        return this.getById(promptHistoryId);
    }

    /**
     * 查询ai请求记录列表
     *
     * @param dutpAiHistory ai请求记录
     * @return ai请求记录
     */
    @Override
    public List<DutpAiHistory> selectDutpAiHistoryList(DutpAiHistory dutpAiHistory)
    {
        LambdaQueryWrapper<DutpAiHistory> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dutpAiHistory.getPromptAbility())) {
                lambdaQueryWrapper.eq(DutpAiHistory::getPromptAbility
                ,dutpAiHistory.getPromptAbility());
            }
                if(ObjectUtil.isNotEmpty(dutpAiHistory.getQuestion())) {
                lambdaQueryWrapper.eq(DutpAiHistory::getQuestion
                ,dutpAiHistory.getQuestion());
            }
                if(ObjectUtil.isNotEmpty(dutpAiHistory.getAnswer())) {
                lambdaQueryWrapper.eq(DutpAiHistory::getAnswer
                ,dutpAiHistory.getAnswer());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增ai请求记录
     *
     * @param dutpAiHistory ai请求记录
     * @return 结果
     */
    @Override
    public boolean insertDutpAiHistory(DutpAiHistory dutpAiHistory)
    {
        return this.save(dutpAiHistory);
    }

    /**
     * 修改ai请求记录
     *
     * @param dutpAiHistory ai请求记录
     * @return 结果
     */
    @Override
    public boolean updateDutpAiHistory(DutpAiHistory dutpAiHistory)
    {
        return this.updateById(dutpAiHistory);
    }

    /**
     * 批量删除ai请求记录
     *
     * @param promptHistoryIds 需要删除的ai请求记录主键
     * @return 结果
     */
    @Override
    public boolean deleteDutpAiHistoryByPromptHistoryIds(List<Long> promptHistoryIds)
    {
        return this.removeByIds(promptHistoryIds);
    }

}

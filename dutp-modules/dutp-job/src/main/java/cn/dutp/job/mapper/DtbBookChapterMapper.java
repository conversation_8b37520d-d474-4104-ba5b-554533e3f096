package cn.dutp.job.mapper;

import cn.dutp.job.domain.DtbBookChapter;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 数字教材章节目录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-30
 */
@Repository
public interface DtbBookChapterMapper extends BaseMapper<DtbBookChapter> {

    List<DtbBookChapter> dtbBookChapterMapper(DtbBookChapter dtbBookChapter);

}

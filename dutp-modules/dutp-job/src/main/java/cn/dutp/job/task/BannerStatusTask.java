package cn.dutp.job.task;

import cn.dutp.job.mapper.BannerStatusMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;



/**
 * banner状态每日维护
 * <AUTHOR>
 *
 */
@Slf4j
@Component("bannerStatusTask")
public class BannerStatusTask {

    @Autowired
    BannerStatusMapper bannerStatusMapper;

    public void bannerStatusTask() {
        bannerStatusMapper.updateBannerStatus(new Date());
    }

}

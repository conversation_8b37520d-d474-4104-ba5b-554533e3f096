package cn.dutp.job;

import org.dromara.easyes.starter.register.EsMapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import cn.dutp.common.security.annotation.EnableCustomConfig;
import cn.dutp.common.security.annotation.EnableDutpFeignClients;

/**
 * 定时任务
 * 
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableDutpFeignClients
@SpringBootApplication
@EsMapperScan("cn.dutp.**.esmapper")
public class DutpJobApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(DutpJobApplication.class, args);
        System.out.println("定时任务模块启动成功");
    }
}

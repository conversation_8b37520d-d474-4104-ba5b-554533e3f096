package cn.dutp.job.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * DUTP-DTB_022笔记/标注对象 dtb_user_book_note
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@TableName("dtb_user_book_note")
public class DtbUserBookNote extends BaseEntity {
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long noteId;

    @Excel(name = "笔记记录人")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    @Excel(name = "教材ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    @Excel(name = "章节ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    @Excel(name = "页码")
    private Long pageNumber;

    @Excel(name = "笔记内容")
    private String nodeContent;

    private String fromWordId;

    private String endWordId;

    private String delFlag;

    private Integer shareFlag;

    private String bookContent;

    /**
     * 章节名称
     */
    @TableField(exist = false)
    private String chapterName;

}

package cn.dutp.job.task;


import cn.dutp.common.mongo.service.MongoService;
import cn.dutp.job.domain.DtbBookChapter;
import cn.dutp.job.domain.DtbBookChapterContent;
import cn.dutp.job.domain.DtbBookTestPaper;
import cn.dutp.job.domain.DutpTask;
import cn.dutp.job.mapper.DtbBookChapterMapper;
import cn.dutp.job.mapper.DtbBookTestPaperMapper;
import cn.dutp.job.mapper.DutpTaskMapper;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

import static cn.dutp.job.domain.constant.BookChapterContentEnum.BOOK_CHAPTER_CONTENT;


/**
 * 统计章节试题和作业任务
 */
@Component("statisticalChapterPaperTask")
@Slf4j
public class StatisticalChapterPaperTask {

    @Autowired
    private DtbBookChapterMapper chapterMapper;

    @Autowired
    private MongoService mongoService;

    @Autowired
    private DutpTaskMapper taskMapper;

    @Autowired
    private DtbBookTestPaperMapper bookTestPaperMapper;

    /**
     * 每天0点30秒执行
     */
    public void statisticalChapterData() {
        log.info("统计章节试题和作业任务开始执行");
        List<DutpTask> taskList = taskMapper.selectList(new LambdaQueryWrapper<DutpTask>()
                .select(DutpTask::getDataId, DutpTask::getTaskId)
                .ne(DutpTask::getTaskState, 2)
                .eq(DutpTask::getTaskType, 9));
        if (ObjectUtil.isNotEmpty(taskList)) {
            for (DutpTask task : taskList) {
                try {
                    Long bookId = task.getDataId();
                    if (ObjectUtil.isEmpty(bookId)) {
                        continue;
                    }

                    task.setTaskState(1);
                    task.setStartTime(new Date());
                    taskMapper.updateById(task);
                    DtbBookChapter dtbBookChapter = new DtbBookChapter();
                    dtbBookChapter.setBookId(bookId);
                    List<DtbBookChapter> chapterList = chapterMapper.dtbBookChapterMapper(dtbBookChapter);
                    if (ObjectUtil.isEmpty(chapterList)) {
                        continue;
                    }

                    for (DtbBookChapter chapter : chapterList) {
                        // 查询章节内容
                        Long chapterId = chapter.getChapterId();
                        Query query = new Query(Criteria.where("chapterId").is(chapterId));
                        DtbBookChapterContent chapterContent = mongoService.findOne(BOOK_CHAPTER_CONTENT, query, DtbBookChapterContent.class);
                        if (ObjectUtil.isEmpty(chapterContent)) {
                            continue;
                        }
                        String contentJson = chapterContent.getContent();
                        if (ObjectUtil.isEmpty(contentJson)) {
                            continue;
                        }

                        JSONObject jsonObject = JSONUtil.parseObj(contentJson);
                        JSONArray pageList = jsonObject.getJSONArray("content");

                        if (ObjectUtil.isEmpty(pageList)) {
                            continue;
                        }
                        for (Object page : pageList) {
                            JSONObject pageNode = (JSONObject) page;
                            JSONObject pageAttrs = pageNode.getJSONObject("attrs");
                            Integer pageNumber = pageAttrs.getInt("pageNumber");
                            if (ObjectUtil.isEmpty(pageNumber)) {
                                pageNumber = 1;
                            }
                            JSONArray contentList = pageNode.getJSONArray("content");
                            if (ObjectUtil.isEmpty(contentList)) {
                                continue;
                            }
                            for (Object parentNode : contentList) {
                                JSONObject node = (JSONObject) parentNode;
                                String type = node.getStr("type");
                                if ("papers".equals(type)) {
                                    JSONObject nodeAttrs = node.getJSONObject("attrs");
                                    String domId = nodeAttrs.getStr("id");
                                    JSONObject paperInfo = nodeAttrs.getJSONObject("paperInfo");
                                    Long paperId = paperInfo.getLong("paperId");
                                    DtbBookTestPaper dtbBookTestPaper = new DtbBookTestPaper();
                                    dtbBookTestPaper.setBookId(bookId);
                                    dtbBookTestPaper.setDomId(domId);
                                    dtbBookTestPaper.setPaperId(paperId);
                                    dtbBookTestPaper.setChapterId(chapterId);
                                    dtbBookTestPaper.setPageNumber(pageNumber);
                                    bookTestPaperMapper.insert(dtbBookTestPaper);
                                }
                            }
                        }
                    }

                    task.setTaskState(2);
                } catch (Exception e) {
                    task.setTaskState(3);
                    log.info("统计章节试题和作业任务执行失败：{}", e);
                }
                task.setEndTime(new Date());
                taskMapper.updateById(task);

            }
        }
        log.info("统计章节试题和作业任务执行结束");
    }
}

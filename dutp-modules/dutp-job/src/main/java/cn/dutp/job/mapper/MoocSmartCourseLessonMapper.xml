<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.job.mapper.MoocSmartCourseLessonMapper">
    
    <resultMap type="MoocSmartCourseLesson" id="MoocSmartCourseLessonResult">
        <result property="lessonId"    column="lesson_id"    />
        <result property="classId"    column="class_id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="title"    column="title"    />
        <result property="scheduledStartDatetime"    column="scheduled_start_datetime"    />
        <result property="scheduledEndDatetime"    column="scheduled_end_datetime"    />
        <result property="startDatetime"    column="start_datetime"    />
        <result property="endDatetime"    column="end_datetime"    />
        <result property="teacherCheckDatetime"    column="teacher_check_datetime"    />
        <result property="status"    column="status"    />
        <result property="createdBy"    column="created_by"    />
        <result property="updatedBy"    column="updated_by"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMoocSmartCourseLessonVo">
        select lesson_id, class_id, teacher_id, title, scheduled_start_datetime, scheduled_end_datetime, start_datetime, end_datetime, teacher_check_datetime, status, created_by, updated_by, del_flag, create_time, update_time from mooc_smart_course_lesson
    </sql>

    <select id="selectMoocSmartCourseLessonList" parameterType="MoocSmartCourseLesson" resultMap="MoocSmartCourseLessonResult">
        <include refid="selectMoocSmartCourseLessonVo"/>
        <where>  
            <if test="classId != null "> and class_id = #{classId}</if>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="scheduledStartDatetime != null "> and scheduled_start_datetime = #{scheduledStartDatetime}</if>
            <if test="scheduledEndDatetime != null "> and scheduled_end_datetime = #{scheduledEndDatetime}</if>
            <if test="startDatetime != null "> and start_datetime = #{startDatetime}</if>
            <if test="endDatetime != null "> and end_datetime = #{endDatetime}</if>
            <if test="teacherCheckDatetime != null "> and teacher_check_datetime = #{teacherCheckDatetime}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="createdBy != null "> and created_by = #{createdBy}</if>
            <if test="updatedBy != null "> and updated_by = #{updatedBy}</if>
        </where>
    </select>
    
    <select id="selectMoocSmartCourseLessonByLessonId" parameterType="Long" resultMap="MoocSmartCourseLessonResult">
        <include refid="selectMoocSmartCourseLessonVo"/>
        where lesson_id = #{lessonId}
    </select>

    <insert id="insertMoocSmartCourseLesson" parameterType="MoocSmartCourseLesson" useGeneratedKeys="true" keyProperty="lessonId">
        insert into mooc_smart_course_lesson
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="classId != null">class_id,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="scheduledStartDatetime != null">scheduled_start_datetime,</if>
            <if test="scheduledEndDatetime != null">scheduled_end_datetime,</if>
            <if test="startDatetime != null">start_datetime,</if>
            <if test="endDatetime != null">end_datetime,</if>
            <if test="teacherCheckDatetime != null">teacher_check_datetime,</if>
            <if test="status != null">status,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="classId != null">#{classId},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="scheduledStartDatetime != null">#{scheduledStartDatetime},</if>
            <if test="scheduledEndDatetime != null">#{scheduledEndDatetime},</if>
            <if test="startDatetime != null">#{startDatetime},</if>
            <if test="endDatetime != null">#{endDatetime},</if>
            <if test="teacherCheckDatetime != null">#{teacherCheckDatetime},</if>
            <if test="status != null">#{status},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMoocSmartCourseLesson" parameterType="MoocSmartCourseLesson">
        update mooc_smart_course_lesson
        <trim prefix="SET" suffixOverrides=",">
            <if test="classId != null">class_id = #{classId},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="scheduledStartDatetime != null">scheduled_start_datetime = #{scheduledStartDatetime},</if>
            <if test="scheduledEndDatetime != null">scheduled_end_datetime = #{scheduledEndDatetime},</if>
            <if test="startDatetime != null">start_datetime = #{startDatetime},</if>
            <if test="endDatetime != null">end_datetime = #{endDatetime},</if>
            <if test="teacherCheckDatetime != null">teacher_check_datetime = #{teacherCheckDatetime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where lesson_id = #{lessonId}
    </update>

    <delete id="deleteMoocSmartCourseLessonByLessonId" parameterType="Long">
        delete from mooc_smart_course_lesson where lesson_id = #{lessonId}
    </delete>

    <delete id="deleteMoocSmartCourseLessonByLessonIds" parameterType="String">
        delete from mooc_smart_course_lesson where lesson_id in 
        <foreach item="lessonId" collection="array" open="(" separator="," close=")">
            #{lessonId}
        </foreach>
    </delete>
    <select id="getInfo" parameterType="Long" resultType="cn.dutp.job.domain.MoocSmartCourseLesson">
        SELECT
            l.lesson_id,
            l.class_id,
            l.teacher_id,
            l.title,
            l.scheduled_start_datetime,
            l.scheduled_end_datetime,
            l.start_datetime,
            l.end_datetime,
            l.teacher_check_datetime,
            l.`status`,
            l.examine,
            l.student_join,
            c.class_code,
            s.course_id
        FROM
            mooc_smart_course_lesson AS l
                LEFT JOIN
            mooc_smart_course_class AS c
            ON
                l.class_id = c.class_id
                LEFT JOIN
            mooc_smart_course AS s
            ON
                c.course_id = s.course_id
        WHERE
            l.del_flag = '0'
            AND l.lesson_id = #{lessonId}
    </select>
</mapper>
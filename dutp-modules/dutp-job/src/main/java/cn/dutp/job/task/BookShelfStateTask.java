package cn.dutp.job.task;

import cn.dutp.domain.DtbBook;
import cn.dutp.job.mapper.BookMapper;
import cn.dutp.job.esmapper.EsBooksMapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 库存0书籍自动下架
 * <AUTHOR>
 */
@Slf4j
@Component("bookShelfStateTask")
public class BookShelfStateTask {

    @Autowired
    BookMapper bookMapper;

    @Autowired(required=true)
    private EsBooksMapper esBooksMapper;

    public void bookUnShelfState() {
        List<DtbBook> bookList = bookMapper.selectByInventory();
        if (!CollectionUtils.isEmpty(bookList)) {
            List<Long> bookIds = bookList.stream().map(DtbBook::getBookId).collect(Collectors.toList());
            UpdateWrapper<DtbBook> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().in(DtbBook::getBookId, bookIds)
                    .eq(DtbBook::getShelfState, 1)
                    .ne(DtbBook::getMasterFlag,3)
                    .eq(DtbBook::getDelFlag,0)
                    .set(DtbBook::getShelfState, 2)
                    .set(DtbBook::getUpdateTime, new Date())
                    .set(DtbBook::getUpdateBy, "系统任务");
            bookMapper.update(null, updateWrapper);
            updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().in(DtbBook::getMasterBookId, bookIds)
                    .eq(DtbBook::getShelfState, 1)
                    .eq(DtbBook::getMasterFlag,3)
                    .eq(DtbBook::getDelFlag,0)
                    .set(DtbBook::getShelfState, 2)
                    .set(DtbBook::getUpdateTime, new Date())
                    .set(DtbBook::getUpdateBy, "系统任务");
            bookMapper.update(null, updateWrapper);

            // 处理es
            bookList.forEach(book -> {
                esBooksMapper.deleteById(book.getBookId());
            });
        }
    }



    // 库存为0自动下架

}

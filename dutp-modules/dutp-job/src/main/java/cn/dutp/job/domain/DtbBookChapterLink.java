package cn.dutp.job.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 外部链接扫描对象 dtb_book_chapter_link
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Data
@TableName("dtb_book_chapter_link")
public class DtbBookChapterLink implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 链接Id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long linkId;

    /**
     * url
     */
    private String url;

    /**
     * 章节ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    /**
     * 教材ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 页码
     */
    private Integer pageNumber;

    /**
     * 0未处理 1已处理
     */
    private Integer state;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}

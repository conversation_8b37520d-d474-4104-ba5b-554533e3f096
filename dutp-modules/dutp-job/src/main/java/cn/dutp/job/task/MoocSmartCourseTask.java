package cn.dutp.job.task;

import cn.dutp.job.domain.MoocSmartCourseLesson;
import cn.dutp.job.mapper.MoocSmartCourseLessonMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 每日自动关课
 * <AUTHOR>
 */
@Slf4j
@Component("moocSmartCourseTask")
public class MoocSmartCourseTask {

    @Autowired
    MoocSmartCourseLessonMapper moocSmartCourseLessonMapper;

    public void automaticallyCloseClassesTask() {
        LambdaQueryWrapper<MoocSmartCourseLesson> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .isNotNull(MoocSmartCourseLesson::getStartDatetime)
                .apply("DATE(start_datetime) = CURDATE()");
        MoocSmartCourseLesson moocSmartCourseLesson = new MoocSmartCourseLesson();
        moocSmartCourseLesson.setStatus(2);
        moocSmartCourseLesson.setEndDatetime(new Date());
        moocSmartCourseLessonMapper.update(moocSmartCourseLesson, lambdaQueryWrapper);

    }

}

package cn.dutp.job.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 教材管理对象 dutp_task
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@Data
@TableName("dutp_task")
public class DutpTask extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long taskId;

    /**
     * 任务类型，1.数据导出2.教材导入3教材复制4章节导入5教材导出6更新题注7修正8统计章节资源9统计章节试题和作业 10章节导出
     */
    @Excel(name = "任务类型，1.数据导出2.教材导入3教材复制4章节导入5教材导出6更新题注7修正8统计章节资源9统计章节试题和作业 10章节导出")
    private Integer taskType;

    /**
     * 任务内容
     */
    @Excel(name = "任务内容")
    private String taskContent;

    /**
     * 数据的ID，可能是教材ID,章节ID等。
     */
    @Excel(name = "数据的ID，可能是教材ID,章节ID等。")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long dataId;

    /**
     * 归属人
     */
    @Excel(name = "归属人")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 执行进度0~100
     */
    @Excel(name = "执行进度0~100")
    private Integer taskRate;

    /**
     * 任务开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "任务开始时间")
    private Date startTime;

    /**
     * 任务结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "任务结束时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 任务状态,0未开始 1进行中 2成功 3失败
     */
    @Excel(name = "任务状态")
    private Integer taskState;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    @TableField(exist = false)
    private String bookNo;

    @TableField(exist = false)
    private String bookName;

    @TableField(exist = false)
    private String userName;

    @TableField(exist = false)
    private String userPhone;

    private String remark;

    /**
     * 导出后生成的url
     */
    private String url;
}

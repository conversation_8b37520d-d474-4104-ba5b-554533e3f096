<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.job.mapper.DtbBookChapterMapper">
    <select id="dtbBookChapterMapper" resultType="cn.dutp.job.domain.DtbBookChapter">
        SELECT
            c.chapter_id,
            c.chapter_name,
            c.sort
        FROM
            dtb_book_chapter c
                INNER JOIN dtb_book b ON c.book_id = b.book_id
                AND b.current_version_id = c.version_id
        WHERE
            c.book_id = #{bookId}
          AND c.del_flag = '0'
        ORDER BY
            sort asc
    </select>
</mapper>
package cn.dutp.job.service.impl;


import cn.dutp.job.domain.DtbBookChapterLink;
import cn.dutp.job.mapper.DtbBookChapterLinkMapper;
import cn.dutp.job.service.IDtbBookChapterLinkService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 外部链接扫描Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Service
public class DtbBookChapterLinkServiceImpl extends ServiceImpl<DtbBookChapterLinkMapper, DtbBookChapterLink> implements IDtbBookChapterLinkService {
    @Autowired
    private DtbBookChapterLinkMapper dtbBookChapterLinkMapper;

}

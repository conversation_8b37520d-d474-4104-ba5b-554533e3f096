package cn.dutp.job.mapper;


import cn.dutp.job.domain.DtbBookChapterData;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

/**
 * 章节数据统计Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-10
 */
@Repository
public interface DtbBookChapterDataMapper extends BaseMapper<DtbBookChapterData> {

    DtbBookChapterData queryChapterDataByChapterId(Long chapterId);

}

package cn.dutp.job.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 互动课堂的单次课堂对象 mooc_smart_course_lesson
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName("mooc_smart_course_lesson")
public class MoocSmartCourseLesson extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 单次课堂ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long lessonId;

    /**
     * 所属班级ID
     */
    @Excel(name = "所属班级ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long classId;

    /** 课程ID (mooc_smart_course) */
    @Excel(name = "课程ID")
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long courseId;

    /**
     * 授课教师用户ID
     */
    @Excel(name = "授课教师用户ID ")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long teacherId;

    /**
     * 课堂标题 按时间生成
     */
    @Excel(name = "课堂标题 按时间生成")
    private String title;

    /**
     * 计划开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "计划开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date scheduledStartDatetime;

    /**
     * 计划结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "计划结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date scheduledEndDatetime;

    /**
     * 教师签到时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "教师签到时间 ", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startDatetime;

    /**
     * 实际结束时间 (教师点击下课时记录)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "实际结束时间 (教师点击下课时记录)", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endDatetime;

    /**
     * 教师签到时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "教师签到时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date teacherCheckDatetime;

    /**
     * 课堂状态 (0: 未开始, 1: 进行中, 2: 已结束, 3: 已取消)
     */
    @Excel(name = "课堂状态 (0: 未开始, 1: 进行中, 2: 已结束, 3: 已取消)")
    private Integer status;

    /**
     * 是否开启审核(0:是，1否)
     */
    @Excel(name = "是否开启审核")
    private Integer examine;

    /**
     * 学生加入方式 (0: 禁止加入, 1: 邀请码进班)
     */
    @Excel(name = "学生加入方式")
    private Integer studentJoin;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 班级编号
     */
    @TableField(exist = false)
    private String classCode;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("lessonId", getLessonId())
                .append("classId", getClassId())
                .append("teacherId", getTeacherId())
                .append("title", getTitle())
                .append("scheduledStartDatetime", getScheduledStartDatetime())
                .append("scheduledEndDatetime", getScheduledEndDatetime())
                .append("startDatetime", getStartDatetime())
                .append("endDatetime", getEndDatetime())
                .append("teacherCheckDatetime", getTeacherCheckDatetime())
                .append("status", getStatus())
                .append("examine", getExamine())
                .append("studentJoin", getStudentJoin())
                .append("delFlag", getDelFlag())
                .toString();
    }
}

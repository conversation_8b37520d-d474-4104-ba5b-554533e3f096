package cn.dutp.job.task;

import cn.dutp.job.mapper.BannerStatusMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;


/**
 * 教材数据统计
 * <AUTHOR>
 * 统计原则：1.章节为单位统计。
 *         2.教材发行两天后不再执行统计任务。
 *         3.根据json数据更新素材数量。
 */
@Slf4j
@Component("bookDataTask")
public class BookDataTask {

}

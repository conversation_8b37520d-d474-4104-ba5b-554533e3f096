package cn.dutp.job.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 教材资源与章节对应关系对象 dtb_book_chapter_resource
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@TableName("dtb_book_chapter_resource")
public class DtbBookChapterResource extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterResourceId;

    /**
     * 章节ID
     */
    @Excel(name = "章节ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    /**
     * 教材ID
     */
    @Excel(name = "教材ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 文件名
     */
    @Excel(name = "文件名")
    private String fileName;

    /**
     * 文件大小
     */
    @Excel(name = "文件大小")
    private Long fileSize;

    /**
     * 文件地址
     */
    @Excel(name = "文件地址")
    private String fileUrl;

    /**
     * 文件类型1=图片，2=音频，3=视频，4=虚拟仿真，5=AR/VR，6=3D模型，7习题（弃用），8课件
     */
    private Integer fileType;

    @Excel(name = "页码")
    private Integer pageNumber;

    @TableField(exist = false)
    private String chapterName;
}

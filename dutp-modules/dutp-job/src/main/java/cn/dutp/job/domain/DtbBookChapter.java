package cn.dutp.job.domain;

import cn.dutp.book.domain.DtbBookChapterCatalog;
import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.serialize.LongListToStringSerializer;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;

/**
 * 数字教材章节目录对象 dtb_book_chapter
 *
 * <AUTHOR>
 * @date 2024-11-30
 */
@Data
@TableName("dtb_book_chapter")
@Slf4j
public class DtbBookChapter extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 章节ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    /**
     * 章节名称
     */
    @Excel(name = "章节名称")
    private String chapterName;

    /**
     * 章节保存的mongodb中的objectId【弃用】
     */
    @Excel(name = "章节保存的mongodb中的objectId【弃用】")
    private String chapterObjectId;

    /**
     * 教材ID
     */
    @Excel(name = "教材ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Integer sort;

    /**
     * 版本ID
     */
    @Excel(name = "版本ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long versionId;

    /**
     * 是否免费1付费2免费
     */
    @Excel(name = "是否免费1付费2免费")
    private Integer free;

    /**
     * 状态 1已提交 2已通过 3已驳回
     */
    @Excel(name = "状态 1已提交 2已通过 3已驳回")
    private Integer chapterStatus;

    /**
     * 申请撤回0不申请1申请
     */
    @Excel(name = "申请撤回0不申请1申请")
    private Integer backApply;

    /**
     * 0正常1冻结【批量更新时用】...
     */
    @Excel(name = "0正常1冻结【批量更新时用】...")
    private Integer frozen;

    /**
     * 0空闲1编辑中
     */
    @Excel(name = "0空闲1编辑中")
    private Integer state;

    /**
     * 完成度
     */
    @Excel(name = "完成度")
    private BigDecimal completeRate;

    /**
     * 模板id
     */
    @Excel(name = "模板id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long templateId;

    /**
     * 删除用户ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long delUserId;

    /**
     * 总页数
     */
    private Integer chapterTotalPage;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 教材名称
     */
    @TableField(exist = false)
    private String bookName;

    /**
     * 教材编号
     */
    @TableField(exist = false)
    private String bookNo;

    /**
     * 编辑者
     */
    @JsonSerialize(using = LongListToStringSerializer.class)
    @TableField(exist = false)
    private List<Long> editorIds;

    /**
     * 查看者
     */
    @JsonSerialize(using = LongListToStringSerializer.class)
    @TableField(exist = false)
    private List<Long> viewerIds;

    @TableField(exist = false)
    private List<DtbBookChapterCatalog> children;

    /**
     * 导出章节ID
     */
    @TableField(exist = false)
    @JsonSerialize(using = LongListToStringSerializer.class)
    private List<Long> chapterIdList;
}

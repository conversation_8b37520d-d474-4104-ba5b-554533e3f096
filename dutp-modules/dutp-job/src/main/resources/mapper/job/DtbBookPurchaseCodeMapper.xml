<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.job.mapper.BookPurchaseCodeMapper">
    <update id="updatePurchaseCodeState">
        <![CDATA[
        UPDATE
            dtb_book_purchase_code
        SET
            state = 4,
            update_by = '系统任务',
            update_time = NOW()
        WHERE
            state = 3 and expiry_date <= DATE(NOW())
        ]]>
    </update>

    <select id="getPurchaseCodeSendMsg" resultType="cn.dutp.domain.DtbBookPurchaseCode">
        <![CDATA[
        SELECT
            b.book_name as bookName,
            bpc.user_id as userId,
            bpc.book_id as bookId
        FROM
            dtb_book_purchase_code bpc
        inner join dtb_book b on bpc.book_id = b.book_id
        WHERE
            state = 3 and expiry_date = DATE_ADD(DATE(NOW()), INTERVAL 4 DAY)
        ]]>
    </select>
</mapper>
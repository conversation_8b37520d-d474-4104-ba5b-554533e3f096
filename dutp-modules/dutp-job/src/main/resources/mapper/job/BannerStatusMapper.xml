<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.job.mapper.BannerStatusMapper">


	<update id="updateBannerStatus">
		UPDATE dutp_banner
		SET status = CASE
		WHEN status = 2 AND DATE_ADD(end_date, INTERVAL 1 DAY) &lt; #{nowDate} THEN 3 -- 已发布改为已下线的
		WHEN status = 1 AND start_date &lt; #{nowDate} THEN 2 -- 未开始且到时间改为已发布
		ELSE status END
		WHERE status IN (1, 2)
	</update>
</mapper>
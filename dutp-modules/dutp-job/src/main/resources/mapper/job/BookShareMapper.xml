<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.job.mapper.BookShareMapper">
    <update id="updateShareLinkedStatus">
        <![CDATA[
        UPDATE
            dtb_book_share
        SET
            status =
                CASE WHEN status = 1 AND start_date <= DATE(NOW()) AND (end_date IS NULL OR end_date >= DATE(NOW())) THEN 2
                     WHEN status = 2 AND end_date < DATE(NOW()) THEN 3
            ELSE status
        END
        WHERE
            (status = 1 AND start_date <= DATE(NOW()) AND (end_date IS NULL OR end_date >= DATE(NOW())))
            OR (status = 2 AND end_date < DATE(NOW()))
        ]]>
    </update>

</mapper>
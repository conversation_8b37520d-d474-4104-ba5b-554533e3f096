<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.dutp</groupId>
        <artifactId>dutp-modules</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>dutp-job</artifactId>

    <description>
        dutp-modules-job定时任务
    </description>

    <dependencies>
    	
    	<!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        
        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        
        <!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>
        
        <!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        
        <!-- Quartz -->
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.mchange</groupId>
                    <artifactId>c3p0</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        
        <!-- Mysql Connector -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        
        <!-- dutp Common Log -->
        <dependency>
            <groupId>cn.dutp</groupId>
            <artifactId>dutp-common-log</artifactId>
        </dependency>
        <!-- dutp 消息服务 -->
        <dependency>
            <groupId>cn.dutp</groupId>
            <artifactId>dutp-api-message</artifactId>
            <version>${dutp.version}</version>
        </dependency>
        
        <!-- dutp Common Swagger -->
        <dependency>
            <groupId>cn.dutp</groupId>
            <artifactId>dutp-common-swagger</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.dutp</groupId>
            <artifactId>dutp-common-ai</artifactId>
            <version>${dutp.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.dutp</groupId>
            <artifactId>dutp-api-common</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>cn.dutp</groupId>
            <artifactId>dutp-api-system</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- dutp 消息服务 -->
        <dependency>
            <groupId>cn.dutp</groupId>
            <artifactId>dutp-api-message</artifactId>
            <version>${dutp.version}</version>
        </dependency>


        <dependency>
            <groupId>cn.dutp</groupId>
            <artifactId>dutp-common-mongo</artifactId>
        </dependency>
        
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.yml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.yml</include>
                    <include>**/*.txt</include>
                </includes>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>
   
</project>
package cn.dutp.message.service;

import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.message.domain.DutpUserMessage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 用户消息Service接口
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
public interface IDutpUserMessageService extends IService<DutpUserMessage> {
    /**
     * 查询用户消息
     *
     * @param messageId 用户消息主键
     * @return 用户消息
     */
    public DutpUserMessage selectDutpUserMessageByMessageId(Long messageId);

    /**
     * 查询用户消息列表
     *
     * @param dutpUserMessage 用户消息
     * @return 用户消息集合
     */
    public List<DutpUserMessage> selectDutpUserMessageList(DutpUserMessage dutpUserMessage);

    /**
     * 新增用户消息
     *
     * @param dutpUserMessage 用户消息
     * @return 结果
     */
    public boolean insertDutpUserMessage(DutpUserMessage dutpUserMessage);

    /**
     * 修改用户消息
     *
     * @param dutpUserMessage 用户消息
     * @return 结果
     */
    public boolean updateDutpUserMessage(DutpUserMessage dutpUserMessage);

    /**
     * 批量删除用户消息
     *
     * @param messageIds 需要删除的用户消息主键集合
     * @return 结果
     */
    public boolean deleteDutpUserMessageByMessageIds(List<Long> messageIds);

    List<DutpUserMessage> pushBookList(DutpUserMessage dutpUserMessage);

    String pushBook(DutpUserMessage dutpUserMessage);

    Integer messageUnreadQuantity(Long userId);

    AjaxResult readMessage(Long messageId);

    /**
     * 学生教师端端修改消息已读状态
     *
     * @param dutpUserMessageList 用户消息List
     * @return 结果
     */
    public boolean editBatch(List<DutpUserMessage> dutpUserMessageList);

    int allRead(DutpUserMessage dutpUserMessage);

    Integer messageUnreadQuantityByUserType();

    List<DutpUserMessage> selectDutpUserMessageListNoPage(DutpUserMessage dutpUserMessage);
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.message.mapper.DutpContributionEmailMapper">
    
    <resultMap type="DutpContributionEmail" id="DutpContributionEmailResult">
        <result property="emailId"    column="email_id"    />
        <result property="emailAdress"    column="email_adress"    />
        <result property="specialityName"    column="speciality_name"    />
        <result property="sort"    column="sort"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDutpContributionEmailVo">
        select email_id, email_adress, speciality_name, sort, del_flag, create_by, create_time, update_by, update_time from dutp_contribution_email
    </sql>

    <select id="selectDutpContributionEmailList" parameterType="DutpContributionEmail" resultMap="DutpContributionEmailResult">
        <include refid="selectDutpContributionEmailVo"/>
        <where>  
            <if test="emailAdress != null  and emailAdress != ''"> and email_adress = #{emailAdress}</if>
            <if test="specialityName != null  and specialityName != ''"> and speciality_name like concat('%', #{specialityName}, '%')</if>
            <if test="sort != null "> and sort = #{sort}</if>
        </where>
    </select>
    
    <select id="selectDutpContributionEmailByEmailId" parameterType="Long" resultMap="DutpContributionEmailResult">
        <include refid="selectDutpContributionEmailVo"/>
        where email_id = #{emailId}
    </select>

    <insert id="insertDutpContributionEmail" parameterType="DutpContributionEmail" useGeneratedKeys="true" keyProperty="emailId">
        insert into dutp_contribution_email
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="emailAdress != null">email_adress,</if>
            <if test="specialityName != null">speciality_name,</if>
            <if test="sort != null">sort,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="emailAdress != null">#{emailAdress},</if>
            <if test="specialityName != null">#{specialityName},</if>
            <if test="sort != null">#{sort},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDutpContributionEmail" parameterType="DutpContributionEmail">
        update dutp_contribution_email
        <trim prefix="SET" suffixOverrides=",">
            <if test="emailAdress != null">email_adress = #{emailAdress},</if>
            <if test="specialityName != null">speciality_name = #{specialityName},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where email_id = #{emailId}
    </update>

    <delete id="deleteDutpContributionEmailByEmailId" parameterType="Long">
        delete from dutp_contribution_email where email_id = #{emailId}
    </delete>

    <delete id="deleteDutpContributionEmailByEmailIds" parameterType="String">
        delete from dutp_contribution_email where email_id in 
        <foreach item="emailId" collection="array" open="(" separator="," close=")">
            #{emailId}
        </foreach>
    </delete>
</mapper>
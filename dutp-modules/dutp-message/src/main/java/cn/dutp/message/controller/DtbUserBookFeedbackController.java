package cn.dutp.message.controller;

import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.message.domain.DtbUserBookFeedback;
import cn.dutp.message.service.IDtbUserBookFeedbackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

/**
 * 读者反馈/纠错Controller
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
@RestController
@RequestMapping("/feedback")
public class DtbUserBookFeedbackController extends BaseController {
    @Autowired
    private IDtbUserBookFeedbackService dtbUserBookFeedbackService;

    /**
     * 查询读者反馈/纠错列表
     */
    @GetMapping("/list")
    public AjaxResult list(DtbUserBookFeedback dtbUserBookFeedback) {
        return success(dtbUserBookFeedbackService.selectDtbUserBookFeedbackList(dtbUserBookFeedback));
    }

    /**
     * 获取读者反馈/纠错详细信息
     * 查询读者反馈列表
     */
    @GetMapping("/listForAdmin")
    public TableDataInfo listForAdmin(DtbUserBookFeedback dtbUserBookFeedback) {
        startPage();
        return getDataTable(dtbUserBookFeedbackService.listForAdmin(dtbUserBookFeedback));
    }

    /**
     * 获取读者反馈详细信息
     */
    @RequiresPermissions("book:book:feedback")
    @GetMapping(value = "/admin/{feedBackId}")
    public AjaxResult adminQueryInfo(@PathVariable("feedBackId") Long feedBackId) {
        return success(dtbUserBookFeedbackService.adminQueryInfo(feedBackId));
    }

    /**
     * 获取DUTP-DTB_016读者反馈/纠错详细信息
     */
    @RequiresPermissions("message:feedback:query")
    @GetMapping(value = "/{feedBackId}")
    public AjaxResult getInfo(@PathVariable("feedBackId") Long feedBackId) {
        return success(dtbUserBookFeedbackService.selectDtbUserBookFeedbackByFeedBackId(feedBackId));
    }

    /**
     * 新增读者反馈/纠错
     */
    @RequiresPermissions("message:feedback:add")
    @Log(title = "新增读者反馈", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbUserBookFeedback dtbUserBookFeedback) {
        return dtbUserBookFeedbackService.insertDtbUserBookFeedback(dtbUserBookFeedback);
    }

    /**
     * 修改读者反馈/纠错
     */
    @RequiresPermissions("message:feedback:edit")
    @Log(title = "修改读者反馈", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbUserBookFeedback dtbUserBookFeedback) {
        return toAjax(dtbUserBookFeedbackService.updateDtbUserBookFeedback(dtbUserBookFeedback));
    }

    /**
     * 删除读者反馈/纠错
     */
    @Log(title = "删除读者反馈", businessType = BusinessType.DELETE)
    @DeleteMapping("/{feedBackIds}")
    public AjaxResult remove(@PathVariable Long[] feedBackIds) {
        return toAjax(dtbUserBookFeedbackService.deleteDtbUserBookFeedbackByFeedBackIds(Arrays.asList(feedBackIds)));
    }

    @PostMapping("/saveFeedBack")
    public AjaxResult saveFeedBack(@RequestBody DtbUserBookFeedback dtbUserBookFeedback) {
        return dtbUserBookFeedbackService.insertDtbUserBookFeedback(dtbUserBookFeedback);
    }

    @GetMapping("/checkHasFeedback")
    public AjaxResult checkHasFeedback() {
        return AjaxResult.success("操作成功", dtbUserBookFeedbackService.checkHasFeedback());
    }

    /**
     * 处理读者反馈/纠错
     */
    @RequiresPermissions("message:feedback:edit")
    @Log(title = "处理读者反馈", businessType = BusinessType.UPDATE)
    @PutMapping("/handle")
    public AjaxResult handle(@RequestBody DtbUserBookFeedback dtbUserBookFeedback) {
        return toAjax(dtbUserBookFeedbackService.handleFeedback(dtbUserBookFeedback));
    }
}

package cn.dutp.message.service.impl;

import cn.dutp.common.core.constant.SecurityConstants;
import cn.dutp.common.core.domain.R;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBook;
import cn.dutp.domain.DtbBookGroup;
import cn.dutp.message.domain.DtbUserBookFeedback;
import cn.dutp.message.domain.DtbUserBookFeedbackImage;
import cn.dutp.message.domain.DutpUserMessage;
import cn.dutp.message.domain.vo.DtbUserBookFeedbackVo;
import cn.dutp.message.mapper.DtbUserBookFeedbackImageMapper;
import cn.dutp.message.mapper.DtbUserBookFeedbackMapper;
import cn.dutp.message.mapper.DutpUserMessageMapper;
import cn.dutp.message.service.IDtbUserBookFeedbackService;
import cn.dutp.message.service.IDutpUserMessageService;
import cn.dutp.system.api.RemoteUserService;
import cn.dutp.system.api.domain.SysUser;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;

/**
 * 读者反馈/纠错Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
@Service
public class DtbUserBookFeedbackServiceImpl extends ServiceImpl<DtbUserBookFeedbackMapper, DtbUserBookFeedback> implements IDtbUserBookFeedbackService {
    @Autowired
    private DtbUserBookFeedbackMapper dtbUserBookFeedbackMapper;
    @Autowired
    private DtbUserBookFeedbackImageMapper dtbUserBookFeedbackImageMapper;
    @Autowired
    private DutpUserMessageMapper messageMapper;

    @Autowired
    private IDutpUserMessageService messageService;

    @Autowired
    private RemoteUserService remoteUserService;


    /**
     * 查询读者反馈/纠错
     *
     * @param feedBackId 读者反馈/纠错主键
     * @return 读者反馈/纠错
     */
    @Override
    public DtbUserBookFeedback selectDtbUserBookFeedbackByFeedBackId(Long feedBackId) {
        return this.getById(feedBackId);
    }

    /**
     * 查询读者反馈/纠错列表
     *
     * @param dtbUserBookFeedback 读者反馈/纠错
     * @return 读者反馈/纠错
     */
    @Override
    public List<DtbUserBookFeedbackVo> selectDtbUserBookFeedbackList(DtbUserBookFeedback dtbUserBookFeedback) {
        // 获取用户Id
        dtbUserBookFeedback.setUserId(SecurityUtils.getUserId());
        List<DtbUserBookFeedbackVo> res = baseMapper.selectDtbUserBookFeedbackList(dtbUserBookFeedback);
        res.forEach(item -> {
            LambdaQueryWrapper<DtbUserBookFeedbackImage> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(DtbUserBookFeedbackImage::getFeedbackId, item.getFeedBackId());
            item.setImages(dtbUserBookFeedbackImageMapper.selectList(lambdaQueryWrapper));
        });
        return res;
    }

    /**
     * 新增读者反馈/纠错
     *
     * @param dtbUserBookFeedback 读者反馈/纠错
     * @return 结果
     */
    @Override
    public AjaxResult insertDtbUserBookFeedback(DtbUserBookFeedback dtbUserBookFeedback) {

        this.save(dtbUserBookFeedback);
        List<DtbUserBookFeedbackImage> images = dtbUserBookFeedback.getImages();
        for (DtbUserBookFeedbackImage image : images) {
            image.setFeedbackId(dtbUserBookFeedback.getFeedBackId());
            dtbUserBookFeedbackImageMapper.insert(image);
        }

        // 查询书籍信息
        DtbBook book = dtbUserBookFeedbackMapper.queryBook(dtbUserBookFeedback.getBookId());
        if (book != null) {
            // 查询书稿联系人和主编
            List<DtbBookGroup> groupList = this.baseMapper.selectBookGroup(dtbUserBookFeedback.getBookId());

            R<List<SysUser>> adminIdList = remoteUserService.listUserByRoleId(2L, SecurityConstants.FROM_SOURCE);
            if (R.FAIL == adminIdList.getCode()) {
                throw new ServiceException(adminIdList.getMsg());
            }

            // 发送消息给书稿联系人和主编
            List<DutpUserMessage> messageList = new ArrayList<>();
            groupList.stream()
                    .map(DtbBookGroup::getUserId) // 提取 userId
                    .distinct() // 去重
                    .forEach(userId -> {
                        DutpUserMessage dutpUserMessage = new DutpUserMessage();
                        dutpUserMessage.setContent("您好，您有一条新的读者反馈信息待处理。【教材名称：" + book.getBookName() + "；教材编号：" + book.getBookNo() + "】。");
                        dutpUserMessage.setTitle("读者反馈提醒");
                        dutpUserMessage.setBusinessId(book.getBookId());
                        dutpUserMessage.setFromUserId(SecurityUtils.getUserId());
                        dutpUserMessage.setToUserId(userId);
                        dutpUserMessage.setMessageType(1);
                        dutpUserMessage.setFromUserType(1);
                        dutpUserMessage.setToUserType(1);
                        messageList.add(dutpUserMessage);
                    });


            for (SysUser sysUser : adminIdList.getData()) {
                DutpUserMessage dutpUserMessage = new DutpUserMessage();
                dutpUserMessage.setContent("您好，您有一条新的读者反馈信息待处理。【教材名称：" + book.getBookName() + "；教材编号：" + book.getBookNo() + "】。");
                dutpUserMessage.setTitle("读者反馈提醒");
                dutpUserMessage.setBusinessId(book.getBookId());
                dutpUserMessage.setFromUserId(SecurityUtils.getUserId());
                dutpUserMessage.setToUserId(sysUser.getUserId());
                dutpUserMessage.setMessageType(1);
                dutpUserMessage.setFromUserType(1);
                dutpUserMessage.setToUserType(1);
                messageList.add(dutpUserMessage);
            }
            messageService.saveBatch(messageList);
        }


        return AjaxResult.success();
    }

    /**
     * 修改读者反馈/纠错
     *
     * @param dtbUserBookFeedback 读者反馈/纠错
     * @return 结果
     */
    @Override
    public boolean updateDtbUserBookFeedback(DtbUserBookFeedback dtbUserBookFeedback) {
        return this.updateById(dtbUserBookFeedback);
    }

    /**
     * 批量删除读者反馈/纠错
     *
     * @param feedBackIds 需要删除的读者反馈/纠错主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbUserBookFeedbackByFeedBackIds(List<Long> feedBackIds) {
        return this.removeByIds(feedBackIds);
    }

    @Override
    public List<DtbUserBookFeedback> listForAdmin(DtbUserBookFeedback dtbUserBookFeedback) {
        List<DtbUserBookFeedback> dtbUserBookFeedbackList = dtbUserBookFeedbackMapper.listForAdmin(dtbUserBookFeedback);
        for (DtbUserBookFeedback userBookFeedback : dtbUserBookFeedbackList) {
            userBookFeedback.setFaultType(convertToFaultText(userBookFeedback));
        }
        return dtbUserBookFeedbackList;
    }

    @Override
    public DtbUserBookFeedback adminQueryInfo(Long feedBackId) {
        DtbUserBookFeedback userBookFeedback = dtbUserBookFeedbackMapper.adminQueryInfo(feedBackId);
        userBookFeedback.setFaultType(convertToFaultText(userBookFeedback));
        LambdaQueryWrapper<DtbUserBookFeedbackImage> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DtbUserBookFeedbackImage::getFeedbackId, feedBackId);
        lambdaQueryWrapper.select(DtbUserBookFeedbackImage::getFileUrl);
        userBookFeedback.setImages(dtbUserBookFeedbackImageMapper.selectList(lambdaQueryWrapper));
        return userBookFeedback;
    }

    @Override
    public String checkHasFeedback() {
        Long userId = SecurityUtils.getUserId();
        List<DtbUserBookFeedback> userBookFeedbackList = dtbUserBookFeedbackMapper.checkHasFeedback(userId);
        StringJoiner message = new StringJoiner(",");
        if (ObjectUtil.isNotEmpty(userBookFeedbackList)) {
            for (DtbUserBookFeedback userBookFeedback : userBookFeedbackList) {
                message.add("【教材名称：" + userBookFeedback.getBookName() + "；教材编号：" + userBookFeedback.getBookNo() + "】");
            }
        }
        return message.toString();
    }

    @Override
    public int handleFeedback(DtbUserBookFeedback dtbUserBookFeedback) {
        boolean success = this.updateById(dtbUserBookFeedback);
        if (success) {
            DtbUserBookFeedback bookFeedback = this.getOne(new LambdaQueryWrapper<DtbUserBookFeedback>()
                    .select(DtbUserBookFeedback::getUserId, DtbUserBookFeedback::getBookId)
                    .eq(DtbUserBookFeedback::getFeedBackId, dtbUserBookFeedback.getFeedBackId()));
            String bookName = dtbUserBookFeedbackMapper.queryBookName(bookFeedback.getBookId());
            DutpUserMessage dutpUserMessage = new DutpUserMessage();
            dutpUserMessage.setContent(String.format("您好，您提交关于《%s》的纠错信息已被处理。", bookName));
            dutpUserMessage.setTitle("读者反馈提醒");
            dutpUserMessage.setFromUserId(SecurityUtils.getUserId());
            dutpUserMessage.setToUserId(bookFeedback.getUserId());
            dutpUserMessage.setBusinessId(bookFeedback.getBookId());
            dutpUserMessage.setMessageType(1);
            dutpUserMessage.setFromUserType(1);
            dutpUserMessage.setToUserType(2);
            messageMapper.insert(dutpUserMessage);
        }
        return success ? 1 : 0;
    }

    /**
     * 转化为错误文本
     *
     * @param userBookFeedback
     */
    private static String convertToFaultText(DtbUserBookFeedback userBookFeedback) {
        String faultType = userBookFeedback.getFaultType();
        String[] faultTypeArr = faultType.replace("，", ",").split(",");
        StringJoiner stringJoiner = new StringJoiner("，");
        // 纠错类型1,2,3  1错别字2逻辑错误3内容错误4图片错误5其他
        for (String s : faultTypeArr) {
            if (Integer.valueOf(s) == 1) {
                stringJoiner.add("错别字");
            } else if (Integer.valueOf(s) == 2) {
                stringJoiner.add("逻辑错误");
            } else if (Integer.valueOf(s) == 3) {
                stringJoiner.add("内容错误");
            } else if (Integer.valueOf(s) == 4) {
                stringJoiner.add("图片错误");
            } else if (Integer.valueOf(s) == 5) {
                stringJoiner.add("其他");
            }
        }
        return stringJoiner.toString();
    }

}

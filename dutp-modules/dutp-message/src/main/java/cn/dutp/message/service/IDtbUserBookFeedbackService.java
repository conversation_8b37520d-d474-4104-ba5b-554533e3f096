package cn.dutp.message.service;

import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.message.domain.DtbUserBookFeedback;
import cn.dutp.message.domain.vo.DtbUserBookFeedbackVo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 读者反馈/纠错Service接口
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
public interface IDtbUserBookFeedbackService extends IService<DtbUserBookFeedback> {
    /**
     * 查询读者反馈/纠错
     *
     * @param feedBackId 读者反馈/纠错主键
     * @return 读者反馈/纠错
     */
    public DtbUserBookFeedback selectDtbUserBookFeedbackByFeedBackId(Long feedBackId);

    /**
     * 查询读者反馈/纠错列表
     *
     * @param dtbUserBookFeedback 读者反馈/纠错
     * @return 读者反馈/纠错集合
     */
    public List<DtbUserBookFeedbackVo> selectDtbUserBookFeedbackList(DtbUserBookFeedback dtbUserBookFeedback);

    /**
     * 新增读者反馈/纠错
     *
     * @param dtbUserBookFeedback 读者反馈/纠错
     * @return 结果
     */
    public AjaxResult insertDtbUserBookFeedback(DtbUserBookFeedback dtbUserBookFeedback);

    /**
     * 修改读者反馈/纠错
     *
     * @param dtbUserBookFeedback 读者反馈/纠错
     * @return 结果
     */
    public boolean updateDtbUserBookFeedback(DtbUserBookFeedback dtbUserBookFeedback);

    /**
     * 批量删除读者反馈/纠错
     *
     * @param feedBackIds 需要删除的读者反馈/纠错主键集合
     * @return 结果
     */
    public boolean deleteDtbUserBookFeedbackByFeedBackIds(List<Long> feedBackIds);

    List<DtbUserBookFeedback> listForAdmin(DtbUserBookFeedback dtbUserBookFeedback);

    DtbUserBookFeedback adminQueryInfo(Long feedBackId);

    String checkHasFeedback();

    int handleFeedback(DtbUserBookFeedback dtbUserBookFeedback);
}

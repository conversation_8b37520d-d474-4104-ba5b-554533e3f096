package cn.dutp.message.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 投稿邮息对象 dutp_contribution_email
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
@Data
@TableName("dutp_contribution_email")
public class DutpContributionEmail extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** $column.columnComment */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long emailId;

    /** 邮箱地址 */
        @Excel(name = "邮箱地址")
    private String emailAdress;

    /** 专业名 */
        @Excel(name = "专业名")
    private String specialityName;

    /** 序号 */
        @Excel(name = "序号")
    private Integer sort;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("emailId", getEmailId())
            .append("emailAdress", getEmailAdress())
            .append("specialityName", getSpecialityName())
            .append("sort", getSort())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
        .toString();
        }
        }

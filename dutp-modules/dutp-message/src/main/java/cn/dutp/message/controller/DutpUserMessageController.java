package cn.dutp.message.controller;

import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.log.enums.OperatorType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.message.domain.DutpUserMessage;
import cn.dutp.message.service.IDutpUserMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 用户消息Controller
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@RestController
@RequestMapping("/message")
public class DutpUserMessageController extends BaseController {
    @Autowired
    private IDutpUserMessageService dutpUserMessageService;

    /**
     * 查询用户消息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DutpUserMessage dutpUserMessage) {
        startPage();
        List<DutpUserMessage> list = dutpUserMessageService.selectDutpUserMessageList(dutpUserMessage);
        return getDataTable(list);
    }

    @GetMapping("/myList")
    public TableDataInfo myList(DutpUserMessage dutpUserMessage) {
        startPage();
        dutpUserMessage.setToUserId(SecurityUtils.getUserId());
        List<DutpUserMessage> list = dutpUserMessageService.selectDutpUserMessageList(dutpUserMessage);
        return getDataTable(list);
    }

    @GetMapping("/myListNoPage")
    public AjaxResult myListNoPage(DutpUserMessage dutpUserMessage) {
        dutpUserMessage.setToUserId(SecurityUtils.getUserId());
        List<DutpUserMessage> list = dutpUserMessageService.selectDutpUserMessageListNoPage(dutpUserMessage);
        return success(list);
    }

    /**
     * 查询推送教材列表
     */
    @RequiresPermissions("book:book:pushBook")
    @GetMapping("/pushBookList")
    public TableDataInfo pushBookList(DutpUserMessage dutpUserMessage) {
        startPage();
        List<DutpUserMessage> list = dutpUserMessageService.pushBookList(dutpUserMessage);
        return getDataTable(list);
    }

    /**
     * 推送教材
     */
    @RequiresPermissions("book:book:pushBook")
    @Log(title = "推送教材", businessType = BusinessType.INSERT)
    @PostMapping("/pushBook")
    public AjaxResult pushBook(@RequestBody DutpUserMessage dutpUserMessage) {
        return AjaxResult.success("操作成功", dutpUserMessageService.pushBook(dutpUserMessage));
    }

    /**
     * 导出用户消息列表
     */
    @RequiresPermissions("message:message:export")
    @Log(title = "用户消息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DutpUserMessage dutpUserMessage) {
        List<DutpUserMessage> list = dutpUserMessageService.selectDutpUserMessageList(dutpUserMessage);
        ExcelUtil<DutpUserMessage> util = new ExcelUtil<DutpUserMessage>(DutpUserMessage.class);
        util.exportExcel(response, list, "用户消息数据");
    }

    /**
     * 获取用户消息详细信息
     */
    @RequiresPermissions("message:message:query")
    @GetMapping(value = "/{messageId}")
    public AjaxResult getInfo(@PathVariable("messageId") Long messageId) {
        return success(dutpUserMessageService.selectDutpUserMessageByMessageId(messageId));
    }

    /**
     * 新增用户消息
     */
    @RequiresPermissions("message:message:add")
    @Log(title = "新增用户消息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DutpUserMessage dutpUserMessage) {
        return toAjax(dutpUserMessageService.insertDutpUserMessage(dutpUserMessage));
    }

    /**
     * 新增用户消息
     */
    @PostMapping("/addMessage")
    public AjaxResult addMessage(@RequestBody DutpUserMessage dutpUserMessage) {
        return toAjax(dutpUserMessageService.insertDutpUserMessage(dutpUserMessage));
    }

    /**
     * 全部已读
     */
    @Log(title = "全部已读", businessType = BusinessType.UPDATE)
    @PutMapping("/allRead")
    public AjaxResult allRead(@RequestBody DutpUserMessage dutpUserMessage) {
        dutpUserMessage.setToUserId(SecurityUtils.getUserId());
        return toAjax(dutpUserMessageService.allRead(dutpUserMessage));
    }

    /**
     * 修改用户消息
     */
    @RequiresPermissions("message:message:edit")
    @Log(title = "修改用户消息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DutpUserMessage dutpUserMessage) {
        return toAjax(dutpUserMessageService.updateDutpUserMessage(dutpUserMessage));
    }

    /**
     * 删除用户消息
     */
    @RequiresPermissions("message:message:remove")
    @Log(title = "删除用户消息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{messageIds}")
    public AjaxResult remove(@PathVariable Long[] messageIds) {
        return toAjax(dutpUserMessageService.deleteDutpUserMessageByMessageIds(Arrays.asList(messageIds)));
    }

    @GetMapping("/messageUnreadQuantity")
    public AjaxResult messageUnreadQuantity() {
        // 根据登陆用户id获取消息列表
        Integer messageUnreadQuantity = dutpUserMessageService.messageUnreadQuantity(SecurityUtils.getUserId());
        return success(messageUnreadQuantity);
    }

    @GetMapping("/readMessage/{messageId}")
    @Log(title = "阅读消息", businessType = BusinessType.UPDATE)
    public AjaxResult readMessage(@PathVariable Long messageId) {
        return dutpUserMessageService.readMessage(messageId);
    }

    /**
     * 学生教师端查询消息
     */
    @Log(title = "学生教师端查询消息", operatorType = OperatorType.READER)
    @GetMapping("/getMessage")
    public TableDataInfo getMessage(DutpUserMessage dutpUserMessage) {
        startPage();
        dutpUserMessage.setToUserId(SecurityUtils.getUserId());
        List<DutpUserMessage> list = dutpUserMessageService.selectDutpUserMessageList(dutpUserMessage);
        TableDataInfo res = getDataTable(list);
        res.setOtherCount(dutpUserMessageService.messageUnreadQuantityByUserType());
        return res;
    }

    /**
     * 学生教师端修改消息已读状态
     */
    @Log(title = "学生教师端修改消息已读状态", businessType = BusinessType.UPDATE, operatorType = OperatorType.READER)
    @PutMapping("/editBatch")
    public AjaxResult editBatch(@RequestBody List<DutpUserMessage> dutpUserMessageList) {
        return toAjax(dutpUserMessageService.editBatch(dutpUserMessageList));
    }

    /**
     * 学生教师端删除用户消息
     */
    @Log(title = "学生教师端删除用户消息", businessType = BusinessType.DELETE, operatorType = OperatorType.READER)
    @PostMapping("/removeBatch")
    public AjaxResult removeBatch(@RequestBody Long[] messageIds) {
        return toAjax(dutpUserMessageService.deleteDutpUserMessageByMessageIds(Arrays.asList(messageIds)));
    }
}

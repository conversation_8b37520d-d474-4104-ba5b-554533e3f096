package cn.dutp.message.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.message.mapper.DutpContributionEmailMapper;
import cn.dutp.message.domain.DutpContributionEmail;
import cn.dutp.message.service.IDutpContributionEmailService;

/**
 * 投稿邮息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
@Service
public class DutpContributionEmailServiceImpl extends ServiceImpl<DutpContributionEmailMapper, DutpContributionEmail> implements IDutpContributionEmailService
{
    @Autowired
    private DutpContributionEmailMapper dutpContributionEmailMapper;

    /**
     * 查询投稿邮息
     *
     * @param emailId 投稿邮息主键
     * @return 投稿邮息
     */
    @Override
    public DutpContributionEmail selectDutpContributionEmailByEmailId(Long emailId)
    {
        return this.getById(emailId);
    }

    /**
     * 查询投稿邮息列表
     *
     * @param dutpContributionEmail 投稿邮息
     * @return 投稿邮息
     */
    @Override
    public List<DutpContributionEmail> selectDutpContributionEmailList(DutpContributionEmail dutpContributionEmail)
    {
        LambdaQueryWrapper<DutpContributionEmail> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dutpContributionEmail.getEmailAdress())) {
                lambdaQueryWrapper.eq(DutpContributionEmail::getEmailAdress
                ,dutpContributionEmail.getEmailAdress());
            }
                if(ObjectUtil.isNotEmpty(dutpContributionEmail.getSpecialityName())) {
                lambdaQueryWrapper.like(DutpContributionEmail::getSpecialityName
                ,dutpContributionEmail.getSpecialityName());
            }
                if(ObjectUtil.isNotEmpty(dutpContributionEmail.getSort())) {
                lambdaQueryWrapper.eq(DutpContributionEmail::getSort
                ,dutpContributionEmail.getSort());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增投稿邮息
     *
     * @param dutpContributionEmail 投稿邮息
     * @return 结果
     */
    @Override
    public boolean insertDutpContributionEmail(DutpContributionEmail dutpContributionEmail)
    {
        return this.save(dutpContributionEmail);
    }

    /**
     * 修改投稿邮息
     *
     * @param dutpContributionEmail 投稿邮息
     * @return 结果
     */
    @Override
    public boolean updateDutpContributionEmail(DutpContributionEmail dutpContributionEmail)
    {
        return this.updateById(dutpContributionEmail);
    }

    /**
     * 批量删除投稿邮息
     *
     * @param emailIds 需要删除的投稿邮息主键
     * @return 结果
     */
    @Override
    public boolean deleteDutpContributionEmailByEmailIds(List<Long> emailIds)
    {
        return this.removeByIds(emailIds);
    }

}

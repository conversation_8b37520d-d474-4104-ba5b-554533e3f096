<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.message.mapper.DtbUserBookFeedbackMapper">
    
    <resultMap type="cn.dutp.message.domain.vo.DtbUserBookFeedbackVo" id="DtbUserBookFeedbackResult">
        <result property="feedBackId"    column="feed_back_id"    />
        <result property="bookId"    column="book_id"    />
        <result property="bookName"    column="book_name"    />
        <result property="chapterId"    column="chapter_id"    />
        <result property="fromWordId"    column="from_word_id"    />
        <result property="endWordId"    column="end_word_id"    />
        <result property="faultText"    column="fault_text"    />
        <result property="comment"    column="comment"    />
        <result property="auditStatus"    column="audit_status"    />
        <result property="imageUrl"    column="image_url"    />
        <result property="faultType"    column="fault_type"    />
        <result property="userId"    column="user_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDtbUserBookFeedbackVo">
        SELECT
            dubf.feed_back_id,
            dubf.book_id,
            dubf.chapter_id,
            dubf.from_word_id,
            dubf.end_word_id,
            dubf.fault_text,
            dubf.COMMENT,
            dubf.CONTENT,
            dubf.audit_status,
            dubf.image_url,
            dubf.fault_type,
            dubf.user_id,
            dubf.create_by,
            dubf.create_time,
            dubf.update_by,
            dubf.update_time,
            CONCAT('《', db.book_name , '》', dbc.chapter_name) AS book_name
        FROM
            dtb_user_book_feedback dubf LEFT JOIN dtb_book db
                                                  ON dubf.book_id = db.book_id
                                        LEFT JOIN dtb_book_chapter dbc
                                                  ON dubf.chapter_id = dbc.chapter_id
    </sql>

    <select id="selectDtbUserBookFeedbackList" parameterType="cn.dutp.message.domain.DtbUserBookFeedback" resultMap="DtbUserBookFeedbackResult">
        <include refid="selectDtbUserBookFeedbackVo"/>
        <where>
            <if test="feedBackId != null "> and dubf.feed_back_id = #{feedBackId}</if>
            <if test="bookId != null "> and dubf.book_id = #{bookId}</if>
            <if test="chapterId != null "> and dubf.chapter_id = #{chapterId}</if>
            <if test="fromWordId != null "> and dubf.from_word_id = #{fromWordId}</if>
            <if test="endWordId != null "> and dubf.end_word_id = #{endWordId}</if>
            <if test="faultText != null  and faultText != ''"> and dubf.fault_text = #{faultText}</if>
            <if test="comment != null  and comment != ''"> and dubf.comment = #{comment}</if>
            <if test="auditStatus != null "> and dubf.audit_status = #{auditStatus}</if>
            <if test="imageUrl != null  and imageUrl != ''"> and dubf.image_url = #{imageUrl}</if>
            <if test="faultType != null  and faultType != ''"> and dubf.fault_type = #{faultType}</if>
            <if test="userId != null "> and dubf.user_id = #{userId}</if>
        </where>
        ORDER BY
         dubf.audit_status,dubf.create_time DESC
    </select>
    
    <select id="selectDtbUserBookFeedbackByFeedBackId" parameterType="Long" resultMap="DtbUserBookFeedbackResult">
        <include refid="selectDtbUserBookFeedbackVo"/>
        where dubf.feed_back_id = #{feedBackId}
    </select>
    <select id="listForAdmin" resultType="cn.dutp.message.domain.DtbUserBookFeedback">
        SELECT
            ubf.feed_back_id,
            ubf.fault_text,
            ubf.audit_status,
            ubf.fault_type,
            ubf.page_number,
            ubf.create_time,
            u.nick_name,
            u.phonenumber,
            s.school_name,
            CONCAT_WS( '-', c.chapter_name, cl.title ) AS chapter_location
        FROM
            dtb_user_book_feedback ubf
                INNER JOIN dutp_user u ON ubf.user_id = u.user_id
                LEFT JOIN dutp_school s ON u.school_id = s.school_id
                LEFT JOIN dtb_book_chapter c ON ubf.chapter_id = c.chapter_id
                LEFT JOIN dtb_book_chapter_catalog cl ON ubf.catalog_id = cl.catalog_id
        WHERE
            ubf.book_id = #{bookId}
        <if test="faultType != null and faultType != ''"> and ubf.fault_type like CONCAT('%', #{faultType}, '%')</if>
        <if test="auditStatus != null "> and ubf.audit_status = #{auditStatus}</if>
    </select>
    <select id="adminQueryInfo" resultType="cn.dutp.message.domain.DtbUserBookFeedback">
        SELECT
            ubf.feed_back_id,
            ubf.fault_text,
            ubf.audit_status,
            ubf.fault_type,
            ubf.page_number,
            ubf.create_time,
            ubf.comment,
            ubf.image_url,
            ubf.content,
            u.nick_name,
            u.phonenumber,
            s.school_name,
            b.book_name,
            CONCAT_WS( '-', c.chapter_name, cl.title ) AS chapter_location
        FROM
            dtb_user_book_feedback ubf
                INNER JOIN dutp_user u ON ubf.user_id = u.user_id
                INNER JOIN dtb_book b on b.book_id = ubf.book_id
                LEFT JOIN dutp_school s ON u.school_id = s.school_id
                LEFT JOIN dtb_book_chapter c ON ubf.chapter_id = c.chapter_id
                LEFT JOIN dtb_book_chapter_catalog cl ON ubf.catalog_id = cl.catalog_id
        WHERE
            ubf.feed_back_id = #{feedBackId}
    </select>
    <select id="checkHasFeedback" resultType="cn.dutp.message.domain.DtbUserBookFeedback">
        SELECT
            b.book_name,
            b.book_no
        FROM
            dtb_user_book_feedback ubf
                INNER JOIN dtb_book b ON b.book_id = ubf.book_id
                INNER JOIN dtb_book_group g ON b.book_id = g.book_id
                AND g.role_type IN ( 1, 2, 5, 6 )
                AND g.del_flag = 0
        WHERE
            g.user_id = #{userId}
          AND ubf.audit_status = 0
        GROUP BY
            b.book_id
    </select>

    <insert id="insertDtbUserBookFeedback" parameterType="cn.dutp.message.domain.DtbUserBookFeedback" useGeneratedKeys="true" keyProperty="feedBackId">
        insert into dtb_user_book_feedback
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bookId != null">book_id,</if>
            <if test="chapterId != null">chapter_id,</if>
            <if test="fromWordId != null">from_word_id,</if>
            <if test="endWordId != null">end_word_id,</if>
            <if test="faultText != null">fault_text,</if>
            <if test="comment != null">comment,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="imageUrl != null">image_url,</if>
            <if test="faultType != null">fault_type,</if>
            <if test="userId != null">user_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bookId != null">#{bookId},</if>
            <if test="chapterId != null">#{chapterId},</if>
            <if test="fromWordId != null">#{fromWordId},</if>
            <if test="endWordId != null">#{endWordId},</if>
            <if test="faultText != null">#{faultText},</if>
            <if test="comment != null">#{comment},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="faultType != null">#{faultType},</if>
            <if test="userId != null">#{userId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDtbUserBookFeedback" parameterType="cn.dutp.message.domain.DtbUserBookFeedback">
        update dtb_user_book_feedback
        <trim prefix="SET" suffixOverrides=",">
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="chapterId != null">chapter_id = #{chapterId},</if>
            <if test="fromWordId != null">from_word_id = #{fromWordId},</if>
            <if test="endWordId != null">end_word_id = #{endWordId},</if>
            <if test="faultText != null">fault_text = #{faultText},</if>
            <if test="comment != null">comment = #{comment},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="faultType != null">fault_type = #{faultType},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where feed_back_id = #{feedBackId}
    </update>

    <delete id="deleteDtbUserBookFeedbackByFeedBackId" parameterType="Long">
        delete from dtb_user_book_feedback where feed_back_id = #{feedBackId}
    </delete>

    <delete id="deleteDtbUserBookFeedbackByFeedBackIds" parameterType="String">
        delete from dtb_user_book_feedback where feed_back_id in 
        <foreach item="feedBackId" collection="array" open="(" separator="," close=")">
            #{feedBackId}
        </foreach>
    </delete>
</mapper>
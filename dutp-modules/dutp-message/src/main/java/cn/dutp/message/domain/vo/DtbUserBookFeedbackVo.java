package cn.dutp.message.domain.vo;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import cn.dutp.message.domain.DtbUserBookFeedbackImage;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 读者反馈/纠错对象 dtb_user_book_feedback
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
@Data
public class DtbUserBookFeedbackVo extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long feedBackId;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    /**
     * 书名
     */
    @Excel(name = "书名")
    private String bookName;

    /**
     * 开始文字ID
     */
    @Excel(name = "开始文字ID")
    private Long fromWordId;

    /**
     * 结束文字ID
     */
    @Excel(name = "结束文字ID")
    private Long endWordId;

    /**
     * 纠错文本
     */
    @Excel(name = "纠错文本")
    private String faultText;

    /**
     * 留言内容
     */
    @Excel(name = "留言内容")
    private String comment;

    /**
     * 审核状态0未处理1已处理
     */
    @Excel(name = "审核状态0未处理1已处理")
    private Integer auditStatus;

    /**
     * 截图地址
     */
    @Excel(name = "截图地址")
    private String imageUrl;

    /**
     * 纠错类型1,2,3  1错别字2逻辑错误3内容错误4图片错误5其他
     */
    @Excel(name = "纠错类型1,2,3  1错别字2逻辑错误3内容错误4图片错误5其他")
    private String faultType;

    /**
     * 留言用户dutp_user中user_id
     */
    @Excel(name = "留言用户dutp_user中user_id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 反馈内容
     */
    private String content;

    /**
     * 纠错截图
     */
    @TableField(exist = false)
    private List<DtbUserBookFeedbackImage> images;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("feedBackId", getFeedBackId())
                .append("bookId", getBookId())
                .append("bookName", getBookName())
                .append("chapterId", getChapterId())
                .append("fromWordId", getFromWordId())
                .append("endWordId", getEndWordId())
                .append("faultText", getFaultText())
                .append("comment", getComment())
                .append("auditStatus", getAuditStatus())
                .append("imageUrl", getImageUrl())
                .append("faultType", getFaultType())
                .append("userId", getUserId())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

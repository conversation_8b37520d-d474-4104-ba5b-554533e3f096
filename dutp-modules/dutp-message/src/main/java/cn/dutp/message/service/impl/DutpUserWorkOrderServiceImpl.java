package cn.dutp.message.service.impl;

import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DutpUserWorkOrder;
import cn.dutp.message.mapper.DutpUserWorkOrderMapper;
import cn.dutp.message.service.IDutpUserWorkOrderService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 反馈工单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@Service
public class DutpUserWorkOrderServiceImpl extends ServiceImpl<DutpUserWorkOrderMapper, DutpUserWorkOrder> implements IDutpUserWorkOrderService
{
    @Autowired
    private DutpUserWorkOrderMapper dutpUserWorkOrderMapper;

    /**
     * 查询反馈工单
     *
     * @param ticketId 反馈工单主键
     * @return 反馈工单
     */
    @Override
    public DutpUserWorkOrder selectDutpUserWorkOrderByTicketId(Long ticketId)
    {
        return this.getById(ticketId);
    }

    /**
     * 查询反馈工单列表
     *
     * @param dutpUserWorkOrder 反馈工单
     * @return 反馈工单
     */
    @Override
    public List<DutpUserWorkOrder> selectDutpUserWorkOrderList(DutpUserWorkOrder dutpUserWorkOrder)
    {
        // 获取用户Id
        dutpUserWorkOrder.setUserId(SecurityUtils.getUserId());
        LambdaQueryWrapper<DutpUserWorkOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(ObjectUtil.isNotEmpty(dutpUserWorkOrder.getTicketId())) {
            lambdaQueryWrapper.eq(DutpUserWorkOrder::getTicketId
                    ,dutpUserWorkOrder.getTicketId());
        }
        if(ObjectUtil.isNotEmpty(dutpUserWorkOrder.getUserId())) {
            lambdaQueryWrapper.eq(DutpUserWorkOrder::getUserId
                    ,dutpUserWorkOrder.getUserId());
        }
        if(ObjectUtil.isNotEmpty(dutpUserWorkOrder.getTitle())) {
            lambdaQueryWrapper.like(DutpUserWorkOrder::getTitle
                    ,dutpUserWorkOrder.getTitle());
        }
        if(ObjectUtil.isNotEmpty(dutpUserWorkOrder.getDescription())) {
            lambdaQueryWrapper.like(DutpUserWorkOrder::getDescription
                    ,dutpUserWorkOrder.getDescription());
        }
        if(ObjectUtil.isNotEmpty(dutpUserWorkOrder.getStatus())) {
            lambdaQueryWrapper.eq(DutpUserWorkOrder::getStatus
                    ,dutpUserWorkOrder.getStatus());
        }
        if(ObjectUtil.isNotEmpty(dutpUserWorkOrder.getPriority())) {
            lambdaQueryWrapper.eq(DutpUserWorkOrder::getPriority
                    ,dutpUserWorkOrder.getPriority());
        }
        if(ObjectUtil.isNotEmpty(dutpUserWorkOrder.getFeedbackType())) {
            lambdaQueryWrapper.eq(DutpUserWorkOrder::getFeedbackType
                    ,dutpUserWorkOrder.getFeedbackType());
        }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增反馈工单
     *
     * @param dutpUserWorkOrder 反馈工单
     * @return 结果
     */
    @Override
    public boolean insertDutpUserWorkOrder(DutpUserWorkOrder dutpUserWorkOrder)
    {
        dutpUserWorkOrder.setUserId(SecurityUtils.getUserId());
        return this.save(dutpUserWorkOrder);
    }

    /**
     * 修改反馈工单
     *
     * @param dutpUserWorkOrder 反馈工单
     * @return 结果
     */
    @Override
    public boolean updateDutpUserWorkOrder(DutpUserWorkOrder dutpUserWorkOrder)
    {
        return this.updateById(dutpUserWorkOrder);
    }

    /**
     * 批量删除反馈工单
     *
     * @param ticketIds 需要删除的反馈工单主键
     * @return 结果
     */
    @Override
    public boolean deleteDutpUserWorkOrderByTicketIds(List<Long> ticketIds)
    {
        return this.removeByIds(ticketIds);
    }

}

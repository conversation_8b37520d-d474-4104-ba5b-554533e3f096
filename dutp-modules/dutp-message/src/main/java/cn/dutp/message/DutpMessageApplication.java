package cn.dutp.message;

import cn.dutp.common.security.annotation.EnableCustomConfig;
import cn.dutp.common.security.annotation.EnableDutpFeignClients;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 代码生成
 * 
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableDutpFeignClients
@SpringBootApplication
public class DutpMessageApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(DutpMessageApplication.class, args);
        System.out.println("消息模块启动成功");
    }
}

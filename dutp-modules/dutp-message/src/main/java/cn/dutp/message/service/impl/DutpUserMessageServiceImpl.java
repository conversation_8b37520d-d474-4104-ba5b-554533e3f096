package cn.dutp.message.service.impl;

import cn.dutp.common.core.constant.HttpStatus;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBook;
import cn.dutp.message.domain.DutpUserMessage;
import cn.dutp.message.mapper.DutpUserMessageMapper;
import cn.dutp.message.service.IDutpUserMessageService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户消息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Service
@Slf4j
public class DutpUserMessageServiceImpl extends ServiceImpl<DutpUserMessageMapper, DutpUserMessage> implements IDutpUserMessageService {
    @Autowired
    private DutpUserMessageMapper dutpUserMessageMapper;

    /**
     * 查询用户消息
     *
     * @param messageId 用户消息主键
     * @return 用户消息
     */
    @Override
    public DutpUserMessage selectDutpUserMessageByMessageId(Long messageId) {
        return this.getById(messageId);
    }

    /**
     * 查询用户消息列表
     *
     * @param dutpUserMessage 用户消息
     * @return 用户消息
     */
    @Override
    public List<DutpUserMessage> selectDutpUserMessageList(DutpUserMessage dutpUserMessage) {
        LambdaQueryWrapper<DutpUserMessage> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotEmpty(dutpUserMessage.getContent())) {
            lambdaQueryWrapper.eq(DutpUserMessage::getContent
                    , dutpUserMessage.getContent());
        }
        if (ObjectUtil.isNotEmpty(dutpUserMessage.getTitle())) {
            lambdaQueryWrapper.like(DutpUserMessage::getTitle
                    , dutpUserMessage.getTitle());
        }

        if (ObjectUtil.isNotEmpty(dutpUserMessage.getToUserId())) {
            lambdaQueryWrapper.eq(DutpUserMessage::getToUserId
                    , dutpUserMessage.getToUserId());
        }
        if (ObjectUtil.isNotEmpty(dutpUserMessage.getMessageType())) {
            lambdaQueryWrapper.eq(DutpUserMessage::getMessageType
                    , dutpUserMessage.getMessageType());
        }
        if (ObjectUtil.isNotEmpty(dutpUserMessage.getFromUserType())) {
            lambdaQueryWrapper.eq(DutpUserMessage::getFromUserType
                    , dutpUserMessage.getFromUserType());
        }
        if (ObjectUtil.isNotEmpty(dutpUserMessage.getToUserType())) {
            lambdaQueryWrapper.eq(DutpUserMessage::getToUserType
                    , dutpUserMessage.getToUserType());
        }
        if (ObjectUtil.isNotEmpty(dutpUserMessage.getReadFlag())) {
            lambdaQueryWrapper.eq(DutpUserMessage::getReadFlag
                    , dutpUserMessage.getReadFlag());
        }
        lambdaQueryWrapper.orderByAsc(DutpUserMessage::getReadFlag)
                .orderByDesc(DutpUserMessage::getCreateTime)
                .orderByDesc(DutpUserMessage::getMessageId);
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增用户消息
     *
     * @param dutpUserMessage 用户消息
     * @return 结果
     */
    @Override
    public boolean insertDutpUserMessage(DutpUserMessage dutpUserMessage) {
        return this.save(dutpUserMessage);
    }

    /**
     * 修改用户消息
     *
     * @param dutpUserMessage 用户消息
     * @return 结果
     */
    @Override
    public boolean updateDutpUserMessage(DutpUserMessage dutpUserMessage) {
        return this.updateById(dutpUserMessage);
    }

    /**
     * 批量删除用户消息
     *
     * @param messageIds 需要删除的用户消息主键
     * @return 结果
     */
    @Override
    public boolean deleteDutpUserMessageByMessageIds(List<Long> messageIds) {
        return this.removeByIds(messageIds);
    }

    @Override
    public List<DutpUserMessage> pushBookList(DutpUserMessage dutpUserMessage) {
        return dutpUserMessageMapper.pushBookList(dutpUserMessage);
    }

    @Override
    public String pushBook(DutpUserMessage dutpUserMessage) {
        String userPhoneStrList = dutpUserMessage.getUserPhoneStrList();
        Long userId = SecurityUtils.getUserId();
        if (ObjectUtil.isEmpty(userPhoneStrList)) {
            log.error("推送账号为空, 当前操作人ID：{}", userId);
            throw new ServiceException("推送账号不能为空");
        }
        String[] userNameArr = userPhoneStrList.replace("，", ",").split(",");
        List<String> userNameList = Arrays.stream(userNameArr).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
        if (ObjectUtil.isEmpty(userNameList)) {
            log.error("推送账号为空, 当前操作人ID：{}", userId);
            throw new ServiceException("推送账号不能为空");
        }
        DtbBook book = dutpUserMessageMapper.queryBookByBookId(dutpUserMessage.getBookId());
        // 校验主副教材
        if (ObjectUtil.isEmpty(book)) {
            log.error("推送教材不存在, 当前操作人ID：{}", userId);
            throw new ServiceException("推送教材不存在");
        }
        if (book.getMasterFlag() == 3) {
            log.error("推送副教材, 当前操作人ID：{}", userId);
            throw new ServiceException("副教材不允许推送");
        }
        if (book.getBookOrganize() == 2) {
            log.error("推送教材类型错误, 当前操作人ID：{}", userId);
            throw new ServiceException("推送教材类型错误，校本教材不允许这样推送");
        }
        if (book.getPublishStatus() == 1) {
            log.error("推送教材状态错误, 当前操作人ID：{}", userId);
            throw new ServiceException("推送教材状态错误，未出版状态的公开教材不允许推送");
        }
        List<DutpUserMessage> dutpUserMessageList = new ArrayList<>();
        StringBuilder res = new StringBuilder();
        int f = 1;
        for (String phoneNumber : userNameList) {
            // 查询用户
            Long toUserId = dutpUserMessageMapper.queryUserIdByPhoneNumber(phoneNumber);
            if (ObjectUtil.isEmpty(toUserId)) {

                res.append("手机号：" + phoneNumber + " 用户不存在，推送失败；\n");
                f = 2;
                continue;
            }
            DutpUserMessage message = new DutpUserMessage();
            message.setContent("亲爱的用户，为您推送《" + book.getBookName() + "》书籍。");
            message.setTitle("教材推送提醒");
            message.setFromUserId(userId);
            message.setToUserId(toUserId);
            message.setBookId(book.getBookId());
            message.setBusinessId(book.getBookId());
            message.setMessageType(3);
            message.setFromUserType(1);
            message.setToUserType(2);
            dutpUserMessageList.add(message);
        }

        if (f == 2 && ObjectUtil.isEmpty(dutpUserMessageList)) {
            return res.toString();
        }

        boolean success = this.saveBatch(dutpUserMessageList);
        if (!success) {
            log.error("推送数据保存失败，{}", dutpUserMessageList);
            throw new ServiceException("推送失败");
        }

        if (f == 2) {
            return res.toString();
        }
        return "推送成功";

    }

    @Override
    public Integer messageUnreadQuantity(Long userId) {
        return dutpUserMessageMapper.messageUnreadQuantity(userId);
    }

    @Override
    public AjaxResult readMessage(Long messageId) {
        DutpUserMessage message = dutpUserMessageMapper.selectById(messageId);
        Long userId = SecurityUtils.getUserId();
        if (userId.longValue() == message.getToUserId().longValue()) {
            DutpUserMessage param = new DutpUserMessage();
            param.setMessageId(messageId);
            param.setReadFlag(1);
            dutpUserMessageMapper.updateById(param);
        } else {
            return AjaxResult.error(HttpStatus.FORBIDDEN, "无阅读消息权限");
        }
        return AjaxResult.success();
    }

    /**
     * 学生教师端端修改消息已读状态
     *
     * @param dutpUserMessageList 用户消息List
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean editBatch(List<DutpUserMessage> dutpUserMessageList) {
        if(ObjectUtil.isNotEmpty(dutpUserMessageList)){
            return this.updateBatchById(dutpUserMessageList);
        }
        return true;
    }

    @Override
    public int allRead(DutpUserMessage dutpUserMessage) {
        return dutpUserMessageMapper.allRead(dutpUserMessage);
    }

    @Override
    public Integer messageUnreadQuantityByUserType() {
        return dutpUserMessageMapper.messageUnreadQuantityByUserType(SecurityUtils.getUserId());
    }

    @Override
    public List<DutpUserMessage> selectDutpUserMessageListNoPage(DutpUserMessage dutpUserMessage) {
        return selectDutpUserMessageList(dutpUserMessage);
    }

    @Override
    public List<String> getSelTitle() {
        LambdaQueryWrapper<DutpUserMessage> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DutpUserMessage::getToUserId, SecurityUtils.getUserId());
        lambdaQueryWrapper.eq(DutpUserMessage::getToUserType, 2);

        List<DutpUserMessage> dutpUserMessages = this.list(lambdaQueryWrapper);
        if(ObjectUtil.isNotEmpty(dutpUserMessages)){
            return dutpUserMessages.stream()
                    .map(DutpUserMessage::getTitle)
                    .distinct()
                    .collect(Collectors.toList());
        }
        return null;
    }
}

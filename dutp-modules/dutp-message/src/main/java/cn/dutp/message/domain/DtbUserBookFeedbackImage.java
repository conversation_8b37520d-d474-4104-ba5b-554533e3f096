package cn.dutp.message.domain;

import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 纠错图片对象 dtb_user_book_feedback_image
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Data
@TableName("dtb_user_book_feedback_image")
public class DtbUserBookFeedbackImage extends BaseEntity {
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long feedbackImageId;

    private String fileName;

    private String fileUrl;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long feedbackId;

}

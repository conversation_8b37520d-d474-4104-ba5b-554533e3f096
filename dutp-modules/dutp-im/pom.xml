<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>dutp-modules</artifactId>
        <groupId>cn.dutp</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>dutp-im</artifactId>

    <dependencies>
        <dependency>
            <groupId>cn.dutp</groupId>
            <artifactId>dutp-common-core</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>

        <!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- Mysql Connector -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>

        <!-- dutp Common DataSource -->
        <dependency>
            <groupId>cn.dutp</groupId>
            <artifactId>dutp-common-datasource</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.dutp</groupId>
            <artifactId>dutp-common-rocketmq</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.dutp</groupId>
            <artifactId>dutp-common-websocket</artifactId>
        </dependency>

        <!-- dutp 消息服务 -->
        <dependency>
            <groupId>cn.dutp</groupId>
            <artifactId>dutp-api-message</artifactId>
            <version>${dutp.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.dutp</groupId>
            <artifactId>dutp-api-pojo</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>cn.dutp</groupId>
            <artifactId>dutp-api-common</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-stream</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.dutp</groupId>
            <artifactId>dutp-common-ai</artifactId>
            <version>${dutp.version}</version>
        </dependency>

        <!-- dutp Common DataScope -->
        <dependency>
            <groupId>cn.dutp</groupId>
            <artifactId>dutp-common-datascope</artifactId>
        </dependency>

        <!-- dutp Common Log -->
        <dependency>
            <groupId>cn.dutp</groupId>
            <artifactId>dutp-common-log</artifactId>
        </dependency>

        <!-- dutp Common Swagger -->
        <dependency>
            <groupId>cn.dutp</groupId>
            <artifactId>dutp-common-swagger</artifactId>
        </dependency>
    </dependencies>
    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.yml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.yml</include>
                    <include>**/*.txt</include>
                </includes>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

</project>
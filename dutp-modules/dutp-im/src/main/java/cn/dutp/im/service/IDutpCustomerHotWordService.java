package cn.dutp.im.service;

import cn.dutp.domain.DutpCustomerHotWord;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
/**
 * 客服咨询热词Service接口
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
public interface IDutpCustomerHotWordService extends IService<DutpCustomerHotWord>
{
    /**
     * 查询客服咨询热词
     *
     * @param wordId 客服咨询热词主键
     * @return 客服咨询热词
     */
    public DutpCustomerHotWord selectDutpCustomerHotWordByWordId(Long wordId);

    /**
     * 查询客服咨询热词列表
     *
     * @param dutpCustomerHotWord 客服咨询热词
     * @return 客服咨询热词集合
     */
    public List<DutpCustomerHotWord> selectDutpCustomerHotWordList(DutpCustomerHotWord dutpCustomerHotWord);

    /**
     * 新增客服咨询热词
     *
     * @param dutpCustomerHotWord 客服咨询热词
     * @return 结果
     */
    public boolean insertDutpCustomerHotWord(DutpCustomerHotWord dutpCustomerHotWord);

    /**
     * 修改客服咨询热词
     *
     * @param dutpCustomerHotWord 客服咨询热词
     * @return 结果
     */
    public boolean updateDutpCustomerHotWord(DutpCustomerHotWord dutpCustomerHotWord);

    /**
     * 批量删除客服咨询热词
     *
     * @param wordIds 需要删除的客服咨询热词主键集合
     * @return 结果
     */
    public boolean deleteDutpCustomerHotWordByWordIds(List<Long> wordIds);

}

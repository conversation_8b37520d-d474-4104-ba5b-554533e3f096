package cn.dutp.im.service;

import java.util.List;

import cn.dutp.im.domain.DutpCustomerQuickReply;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * DUTP-BASE-024客服快捷回复Service接口
 *
 * <AUTHOR>
 * @date 2024-12-27
 */
public interface IDutpCustomerQuickReplyService extends IService<DutpCustomerQuickReply>
{
    /**
     * 查询DUTP-BASE-024客服快捷回复
     *
     * @param replyId DUTP-BASE-024客服快捷回复主键
     * @return DUTP-BASE-024客服快捷回复
     */
    public DutpCustomerQuickReply selectDutpCustomerQuickReplyByReplyId(Long replyId);

    /**
     * 查询DUTP-BASE-024客服快捷回复列表
     *
     * @param dutpCustomerQuickReply DUTP-BASE-024客服快捷回复
     * @return DUTP-BASE-024客服快捷回复集合
     */
    public List<DutpCustomerQuickReply> selectDutpCustomerQuickReplyList(DutpCustomerQuickReply dutpCustomerQuickReply);

    /**
     * 新增DUTP-BASE-024客服快捷回复
     *
     * @param dutpCustomerQuickReply DUTP-BASE-024客服快捷回复
     * @return 结果
     */
    public boolean insertDutpCustomerQuickReply(DutpCustomerQuickReply dutpCustomerQuickReply);

    /**
     * 修改DUTP-BASE-024客服快捷回复
     *
     * @param dutpCustomerQuickReply DUTP-BASE-024客服快捷回复
     * @return 结果
     */
    public boolean updateDutpCustomerQuickReply(DutpCustomerQuickReply dutpCustomerQuickReply);

    /**
     * 批量删除DUTP-BASE-024客服快捷回复
     *
     * @param replyIds 需要删除的DUTP-BASE-024客服快捷回复主键集合
     * @return 结果
     */
    public boolean deleteDutpCustomerQuickReplyByReplyIds(List<Long> replyIds);

    DutpCustomerQuickReply getInfoByWord(String word);

    int editStatus(DutpCustomerQuickReply dutpCustomerQuickReply);

    List<DutpCustomerQuickReply> getListByUserId();
}

package cn.dutp.im.service.impl;

import java.util.List;

import cn.dutp.im.domain.DutpCustomerChat;
import cn.dutp.im.mapper.DutpCustomerChatMapper;
import cn.dutp.im.service.IDutpCustomerChatService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * DUTP-BASE-011客服对话明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
@Service
public class DutpCustomerChatServiceImpl extends ServiceImpl<DutpCustomerChatMapper, DutpCustomerChat> implements IDutpCustomerChatService
{
    @Autowired
    private DutpCustomerChatMapper dutpCustomerChatMapper;

    /**
     * 查询DUTP-BASE-011客服对话明细
     *
     * @param chatId DUTP-BASE-011客服对话明细主键
     * @return DUTP-BASE-011客服对话明细
     */
    @Override
    public DutpCustomerChat selectDutpCustomerChatByChatId(Long chatId)
    {
        return this.getById(chatId);
    }

    /**
     * 查询DUTP-BASE-011客服对话明细列表
     *
     * @param dutpCustomerChat DUTP-BASE-011客服对话明细
     * @return DUTP-BASE-011客服对话明细
     */
    @Override
    public List<DutpCustomerChat> selectDutpCustomerChatList(DutpCustomerChat dutpCustomerChat)
    {
        LambdaQueryWrapper<DutpCustomerChat> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dutpCustomerChat.getUserId())) {
                lambdaQueryWrapper.eq(DutpCustomerChat::getUserId
                ,dutpCustomerChat.getUserId());
            }
                if(ObjectUtil.isNotEmpty(dutpCustomerChat.getLastContent())) {
                lambdaQueryWrapper.eq(DutpCustomerChat::getLastContent
                ,dutpCustomerChat.getLastContent());
            }
                if(ObjectUtil.isNotEmpty(dutpCustomerChat.getLastTime())) {
                lambdaQueryWrapper.eq(DutpCustomerChat::getLastTime
                ,dutpCustomerChat.getLastTime());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增DUTP-BASE-011客服对话明细
     *
     * @param dutpCustomerChat DUTP-BASE-011客服对话明细
     * @return 结果
     */
    @Override
    public boolean insertDutpCustomerChat(DutpCustomerChat dutpCustomerChat)
    {
        return this.save(dutpCustomerChat);
    }

    /**
     * 修改DUTP-BASE-011客服对话明细
     *
     * @param dutpCustomerChat DUTP-BASE-011客服对话明细
     * @return 结果
     */
    @Override
    public boolean updateDutpCustomerChat(DutpCustomerChat dutpCustomerChat)
    {
        return this.updateById(dutpCustomerChat);
    }

    /**
     * 批量删除DUTP-BASE-011客服对话明细
     *
     * @param chatIds 需要删除的DUTP-BASE-011客服对话明细主键
     * @return 结果
     */
    @Override
    public boolean deleteDutpCustomerChatByChatIds(List<Long> chatIds)
    {
        return this.removeByIds(chatIds);
    }

}

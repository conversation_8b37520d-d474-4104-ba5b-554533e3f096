package cn.dutp.im.domain;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * DUTP-BASE-011客服对话明细对象 dutp_customer_chat
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
@Data
@TableName("dutp_customer_chat")
public class DutpCustomerChat extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 对话ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chatId;

    /**
     * 用户ID  dutp_user中userId
     */
    @Excel(name = "用户ID  dutp_user中userId")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 最后聊天记录
     */
    @Excel(name = "最后聊天记录")
    private String lastContent;

    /**
     * 1待开启服务2正在会话3会话关闭
     */
    @Excel(name = "1待开启服务2正在会话3会话关闭")
    private Integer chatStatus;

    /**
     * 最后的聊天记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最后的聊天记录时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lastTime;

    /**
     * 0未删除2已删除
     */
    private String delFlag;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("chatId", getChatId())
                .append("userId", getUserId())
                .append("lastContent", getLastContent())
                .append("lastTime", getLastTime())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("delFlag", getDelFlag())
                .append("chatStatus", getChatStatus())
                .toString();
    }
}

package cn.dutp.im.service.impl;

import java.util.List;

import cn.dutp.domain.DutpCustomerHotWord;
import cn.dutp.im.mapper.DutpCustomerHotWordMapper;
import cn.dutp.im.service.IDutpCustomerHotWordService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 客服咨询热词Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
@Service
public class DutpCustomerHotWordServiceImpl extends ServiceImpl<DutpCustomerHotWordMapper, DutpCustomerHotWord> implements IDutpCustomerHotWordService
{
    @Autowired
    private DutpCustomerHotWordMapper dutpCustomerHotWordMapper;

    /**
     * 查询客服咨询热词
     *
     * @param wordId 客服咨询热词主键
     * @return 客服咨询热词
     */
    @Override
    public DutpCustomerHotWord selectDutpCustomerHotWordByWordId(Long wordId)
    {
        return this.getById(wordId);
    }

    /**
     * 查询客服咨询热词列表
     *
     * @param dutpCustomerHotWord 客服咨询热词
     * @return 客服咨询热词
     */
    @Override
    public List<DutpCustomerHotWord> selectDutpCustomerHotWordList(DutpCustomerHotWord dutpCustomerHotWord)
    {
        LambdaQueryWrapper<DutpCustomerHotWord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dutpCustomerHotWord.getWord())) {
                lambdaQueryWrapper.like(DutpCustomerHotWord::getWord
                ,dutpCustomerHotWord.getWord());
            }
                if(ObjectUtil.isNotEmpty(dutpCustomerHotWord.getQuantity())) {
                lambdaQueryWrapper.eq(DutpCustomerHotWord::getQuantity
                ,dutpCustomerHotWord.getQuantity());
            }
                if(ObjectUtil.isNotEmpty(dutpCustomerHotWord.getInUse())) {
                lambdaQueryWrapper.eq(DutpCustomerHotWord::getInUse
                ,dutpCustomerHotWord.getInUse());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增客服咨询热词
     *
     * @param dutpCustomerHotWord 客服咨询热词
     * @return 结果
     */
    @Override
    public boolean insertDutpCustomerHotWord(DutpCustomerHotWord dutpCustomerHotWord)
    {
        return this.save(dutpCustomerHotWord);
    }

    /**
     * 修改客服咨询热词
     *
     * @param dutpCustomerHotWord 客服咨询热词
     * @return 结果
     */
    @Override
    public boolean updateDutpCustomerHotWord(DutpCustomerHotWord dutpCustomerHotWord)
    {
        return this.updateById(dutpCustomerHotWord);
    }

    /**
     * 批量删除客服咨询热词
     *
     * @param wordIds 需要删除的客服咨询热词主键
     * @return 结果
     */
    @Override
    public boolean deleteDutpCustomerHotWordByWordIds(List<Long> wordIds)
    {
        return this.removeByIds(wordIds);
    }

}

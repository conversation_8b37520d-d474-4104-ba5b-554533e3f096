package cn.dutp.im.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import cn.dutp.common.ai.common.ai.domain.ChatAiRequest;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DutpCustomerChatDetail;
import cn.dutp.im.domain.DutpCustomer;
import cn.dutp.im.domain.DutpCustomerChat;
import cn.dutp.im.domain.DutpCustomerQuickReply;
import cn.dutp.im.domain.vo.DutpCustomerChatDetailVO;
import cn.dutp.im.mapper.DutpCustomerChatDetailMapper;
import cn.dutp.im.mapper.DutpCustomerChatMapper;
import cn.dutp.im.mapper.DutpCustomerMapper;
import cn.dutp.im.mapper.DutpCustomerQuickReplyMapper;
import cn.dutp.im.service.IDutpCustomerChatDetailService;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sun.org.apache.bcel.internal.generic.NEW;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 客服聊天明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
@Service
public class DutpCustomerChatDetailServiceImpl extends ServiceImpl<DutpCustomerChatDetailMapper, DutpCustomerChatDetail> implements IDutpCustomerChatDetailService
{
    @Autowired
    private DutpCustomerChatDetailMapper dutpCustomerChatDetailMapper;

    @Autowired
    private DutpCustomerChatStatusServiceImpl dutpCustomerChatStatusService;

    @Autowired
    private DutpCustomerChatServiceImpl dutpCustomerChatService;

    @Autowired
    private DutpCustomerChatMapper dutpCustomerChatMapper;

    @Autowired
    private DutpCustomerMapper dutpcustomerMapper;

    @Autowired
    private DutpCustomerQuickReplyMapper dutpCustomerQuickReplyMapper;

    /**
     * 查询客服聊天明细
     *
     * @param detailId 客服聊天明细主键
     * @return 客服聊天明细
     */
    @Override
    public DutpCustomerChatDetail selectDutpCustomerChatDetailByDetailId(Long detailId)
    {
        return this.getById(detailId);
    }

    /**
     * 查询客服聊天明细列表
     *
     * @param dutpCustomerChatDetail 客服聊天明细
     * @return 客服聊天明细
     */
    @Override
    public List<DutpCustomerChatDetail> selectDutpCustomerChatDetailList(DutpCustomerChatDetail dutpCustomerChatDetail)
    {
        LambdaQueryWrapper<DutpCustomerChatDetail> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dutpCustomerChatDetail.getChatId())) {
                lambdaQueryWrapper.eq(DutpCustomerChatDetail::getChatId
                ,dutpCustomerChatDetail.getChatId());
            }
                if(ObjectUtil.isNotEmpty(dutpCustomerChatDetail.getUserType())) {
                lambdaQueryWrapper.eq(DutpCustomerChatDetail::getUserType
                ,dutpCustomerChatDetail.getUserType());
            }
                if(ObjectUtil.isNotEmpty(dutpCustomerChatDetail.getChatContent())) {
                lambdaQueryWrapper.eq(DutpCustomerChatDetail::getChatContent
                ,dutpCustomerChatDetail.getChatContent());
            }
                if(ObjectUtil.isNotEmpty(dutpCustomerChatDetail.getState())) {
                lambdaQueryWrapper.eq(DutpCustomerChatDetail::getState
                ,dutpCustomerChatDetail.getState());
            }
                if(ObjectUtil.isNotEmpty(dutpCustomerChatDetail.getContentType())) {
                lambdaQueryWrapper.eq(DutpCustomerChatDetail::getContentType
                ,dutpCustomerChatDetail.getContentType());
            }
                if(ObjectUtil.isNotEmpty(dutpCustomerChatDetail.getReadFlag())) {
                lambdaQueryWrapper.eq(DutpCustomerChatDetail::getReadFlag
                ,dutpCustomerChatDetail.getReadFlag());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增客服聊天明细
     *
     * @param dutpCustomerChatDetail 客服聊天明细
     * @return 结果
     */
    @Override
    public boolean insertDutpCustomerChatDetail(DutpCustomerChatDetail dutpCustomerChatDetail)
    {
        return this.save(dutpCustomerChatDetail);
    }

    /**
     * 修改客服聊天明细
     *
     * @param dutpCustomerChatDetail 客服聊天明细
     * @return 结果
     */
    @Override
    public boolean updateDutpCustomerChatDetail(DutpCustomerChatDetail dutpCustomerChatDetail)
    {
        return this.updateById(dutpCustomerChatDetail);
    }

    /**
     * 批量删除客服聊天明细
     *
     * @param detailIds 需要删除的客服聊天明细主键
     * @return 结果
     */
    @Override
    public boolean deleteDutpCustomerChatDetailByDetailIds(List<Long> detailIds)
    {
        return this.removeByIds(detailIds);
    }

    @Override
    @Transactional
    public DutpCustomerChatDetail sendMessage(DutpCustomerChatDetail dutpCustomerChatDetail) {
        Integer userType = dutpCustomerChatDetail.getUserType();
        Long userId = SecurityUtils.getUserId();
//        if (userType == 2) {
//            DutpCustomerChatStatus chatStatus = new DutpCustomerChatStatus();
//            chatStatus.setDelFlag("0");
//            chatStatus.setUserId(userId);
//            chatStatus.setChatStatus(dutpCustomerChatDetail.getChatStatus());
//            dutpCustomerChatStatusService.save(chatStatus);
//        }
        if (userType == 1) {
            // 是否为客服人员
            QueryWrapper<DutpCustomer> customerQueryWrapper = new QueryWrapper<>();
            customerQueryWrapper.lambda().eq(DutpCustomer::getUserId,SecurityUtils.getUserId())
                    .eq(DutpCustomer::getDelFlag,0);
            DutpCustomer customer = dutpcustomerMapper.selectOne(customerQueryWrapper);
            if (ObjectUtil.isEmpty(customer)) {
                throw new ServiceException("您不是客服人员，不能进行会话");
            }
        }
        QueryWrapper<DutpCustomerChat> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DutpCustomerChat::getUserId,userId);
        DutpCustomerChat chat = dutpCustomerChatMapper.selectOne(queryWrapper);
        if (userType == 2) {
            if (ObjectUtil.isNotEmpty(chat)) {
                // 更新最后对话时间
                chat.setLastTime(new Date());
                chat.setLastContent(dutpCustomerChatDetail.getChatContent().replace("\r", "").replace("\n", ""));
                if (chat.getChatStatus() == 3) {
                    chat.setChatStatus(1);
                }
                dutpCustomerChatService.updateById(chat);
            } else {
                chat = new DutpCustomerChat();
                chat.setUserId(userId);
                chat.setChatStatus(1);
                chat.setLastContent(dutpCustomerChatDetail.getChatContent().replace("\r", "").replace("\n", ""));
                chat.setLastTime(new Date());
                dutpCustomerChatService.save(chat);
            }
        }
        QueryWrapper<DutpCustomerChatDetail> detailWrapper = new QueryWrapper<>();
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date startOfDay = calendar.getTime();

        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        Date endOfDay = calendar.getTime();
        detailWrapper.lambda().eq(DutpCustomerChatDetail::getUserId,SecurityUtils.getUserId())
                .between(DutpCustomerChatDetail::getCreateTime,startOfDay,endOfDay);
        List<DutpCustomerChatDetail> list = dutpCustomerChatDetailMapper.selectList(detailWrapper);
        if (userType == 2 && CollectionUtils.isEmpty(list)) {
            DutpCustomerChatDetail statusDetail = new DutpCustomerChatDetail();
            statusDetail.setUserType(3);
            statusDetail.setChatContent("待开启服务");
            statusDetail.setContentType(dutpCustomerChatDetail.getContentType());
            statusDetail.setReadFlag(0);
            statusDetail.setUserId(userId);
            statusDetail.setChatId(chat.getChatId());
            this.save(statusDetail);
        }
        dutpCustomerChatDetail.setUserId(userId);
        dutpCustomerChatDetail.setReadFlag(0);
        if (userType == 2) {
            dutpCustomerChatDetail.setChatId(chat.getChatId());
        }
        this.save(dutpCustomerChatDetail);
        // 客户发送消息，判断是否有客服在线，判断机器人是否开启服务。是否匹配到快捷回复
        if (userType == 2) {
            // 判断是否有客服在线
            QueryWrapper<DutpCustomer> customerQueryWrapper = new QueryWrapper<>();
            customerQueryWrapper.lambda().eq(DutpCustomer::getDelFlag,0).ne(DutpCustomer::getUserId,0)
                    .eq(DutpCustomer::getStatus,1);
            List<DutpCustomer> customerList = dutpcustomerMapper.selectList(customerQueryWrapper);
            if (CollectionUtils.isEmpty(customerList)) {
                // 没有客服在线，判断机器人是否开启服务,否则等待在线客服进行处理
                customerQueryWrapper = new QueryWrapper<>();
                customerQueryWrapper.lambda().eq(DutpCustomer::getDelFlag,0).eq(DutpCustomer::getUserId,0)
                        .eq(DutpCustomer::getStatus,1);
                DutpCustomer robot = dutpcustomerMapper.selectOne(customerQueryWrapper);
                if (ObjectUtil.isNotEmpty(robot)) {
                    // 机器人开启服务，匹配快捷回复，否则不做处理
                    QueryWrapper<DutpCustomerQuickReply> replyQueryWrapper = new QueryWrapper<>();
                    replyQueryWrapper.lambda().eq(DutpCustomerQuickReply::getDelFlag,0).eq(DutpCustomerQuickReply::getStatus,1)
                            .eq(DutpCustomerQuickReply::getUserId,0);
                    List<DutpCustomerQuickReply> replyList = dutpCustomerQuickReplyMapper.selectList(replyQueryWrapper);
                    if (!CollectionUtils.isEmpty(replyList)) {
                        // 匹配快捷回复
                        matchKeywords(replyList,dutpCustomerChatDetail.getChatContent(),chat.getChatId());
//                        List<DutpCustomerQuickReply> matchWords = matchKeywords(replyList,dutpCustomerChatDetail.getChatContent(),chat.getChatId());
//                        if (!CollectionUtils.isEmpty(matchWords)) {
//                            //匹配到快捷回复，判断是否达到快捷回复次数，未达到回复次数，将第一条自动回复用户，否则不做操作
//                            QueryWrapper<DutpCustomerChatDetail> detailQueryWrapper = new QueryWrapper<>();
//                            detailQueryWrapper.lambda().eq(DutpCustomerChatDetail::getChatId, chat.getChatId())
//                                    .eq(DutpCustomerChatDetail::getChatContent, matchWords.get(0).getReplyContent());
//                            List<DutpCustomerChatDetail> detailList = dutpCustomerChatDetailMapper.selectList(detailQueryWrapper);
//                            if (CollectionUtils.isEmpty(detailList) || (detailList.size() < matchWords.get(0).getQuantity())) {
//                                // 未达到回复次数，自动回复，否则不做处理
//                                DutpCustomerChatDetail statusDetail = new DutpCustomerChatDetail();
//                                statusDetail.setUserType(1);
//                                statusDetail.setChatContent(matchWords.get(0).getReplyContent());
//                                statusDetail.setUserId(0L);
//                                statusDetail.setContentType(1);
//                                statusDetail.setReadFlag(0);
//                                statusDetail.setChatId(chat.getChatId());
//                                this.save(statusDetail);
//                            }
//                        }
                    }
                }
            }
        }
        return dutpCustomerChatDetail;
    }

    public void matchKeywords(List<DutpCustomerQuickReply> replyList, String chatContent,Long chatId){
        Map<String,DutpCustomerQuickReply> map = new HashMap<>();
        // 遍历对象列表，将 word 按逗号分隔，并将 id 和 content 存入 map
        for (DutpCustomerQuickReply obj : replyList) {
            String[] words = obj.getWord().split(",");
            for (String word : words) {
                map.put(word.trim(), obj);
            }
        }
        // 匹配输入的 word 如果在 map 中找到，返回对应的 content
        if (map.containsKey(chatContent)) {
            // 判断是否达到上限
            QueryWrapper<DutpCustomerChatDetail> detailQueryWrapper = new QueryWrapper<>();
            detailQueryWrapper.lambda().eq(DutpCustomerChatDetail::getChatId, chatId).eq(DutpCustomerChatDetail::getUserId,0)
                    .eq(DutpCustomerChatDetail::getUserType, 1)
                    .eq(DutpCustomerChatDetail::getChatContent, map.get(chatContent).getReplyContent());
            List<DutpCustomerChatDetail> detailList = dutpCustomerChatDetailMapper.selectList(detailQueryWrapper);
            if (CollectionUtils.isEmpty(detailList) || (detailList.size() < map.get(chatContent).getQuantity())) {
                // 未达到上限，回复客户
                // 未达到回复次数，自动回复，否则不做处理
                DutpCustomerChatDetail statusDetail = new DutpCustomerChatDetail();
                statusDetail.setUserType(1);
                statusDetail.setChatContent(map.get(chatContent).getReplyContent());
                statusDetail.setUserId(0L);
                statusDetail.setContentType(1);
                statusDetail.setReadFlag(0);
                statusDetail.setChatId(chatId);
                this.save(statusDetail);
            }
        }
    }

    @Override
    public List<DutpCustomerChatDetailVO> getMessage(DutpCustomerChatDetail dutpCustomerChatDetail) {
        List<DutpCustomerChatDetailVO> res = new ArrayList();
        if (dutpCustomerChatDetail.getUserType() == 1) {
            // 客服信息
            res = dutpCustomerChatDetailMapper.getCustomerMessage(dutpCustomerChatDetail);
        } else if (dutpCustomerChatDetail.getUserType() == 2) {
            // 用户信息 学生教师端
            res = dutpCustomerChatDetailMapper.getUserMessage(dutpCustomerChatDetail,SecurityUtils.getUserId());
        }
        return res;
    }

    @Override
    public List<DutpCustomerChatDetailVO> getLastMessage(DutpCustomerChatDetail dutpCustomerChatDetail) {
        // 查看当前人是否为客服
        List<DutpCustomerChatDetailVO> res = dutpCustomerChatDetailMapper.getLastMessage(dutpCustomerChatDetail);
        if (dutpCustomerChatDetail.getSortValue() != null) {
            List<DutpCustomerChatDetailVO> list = res.stream()
                    .sorted((p1, p2) -> {
                        if (p1.getChatStatus() == dutpCustomerChatDetail.getSortValue()) return -1;
                        return 0;
                    })
                    .collect(Collectors.toList());
            return list;
        }
        return res;
    }

    @Override
    public void changeStatus(DutpCustomerChatDetail dutpCustomerChatDetail) {
        // 是否为客服人员
        QueryWrapper<DutpCustomer> customerQueryWrapper = new QueryWrapper<>();
        customerQueryWrapper.lambda().eq(DutpCustomer::getUserId,SecurityUtils.getUserId())
                .eq(DutpCustomer::getDelFlag,0);
        DutpCustomer customer = dutpcustomerMapper.selectOne(customerQueryWrapper);
        if (ObjectUtil.isEmpty(customer)) {
            throw new ServiceException("您不是客服人员，不能进行会话");
        }
        QueryWrapper<DutpCustomerChat> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DutpCustomerChat::getChatId,dutpCustomerChatDetail.getChatId());
        DutpCustomerChat chat = dutpCustomerChatMapper.selectOne(queryWrapper);
        if (chat.getChatStatus() == dutpCustomerChatDetail.getChatStatus()) {
            if (dutpCustomerChatDetail.getChatStatus() == 2) {
                throw new ServiceException("该客户正在对话中，无法开启会话");
            }
            if (dutpCustomerChatDetail.getChatStatus() == 3) {
                throw new ServiceException("该客户会话已关闭，不能关闭会话");
            }
        }
        // 变更最后聊天状态
        chat.setChatStatus(dutpCustomerChatDetail.getChatStatus());
        chat.setLastTime(new Date());
        dutpCustomerChatMapper.updateById(chat);
        // 插入详情表
        DutpCustomerChatDetail chatDetail = new DutpCustomerChatDetail();
        chatDetail.setUserId(SecurityUtils.getUserId());
        chatDetail.setUserType(3);
        chatDetail.setChatId(dutpCustomerChatDetail.getChatId());
        if (dutpCustomerChatDetail.getChatStatus() == 2) {
            chatDetail.setChatContent("正在会话");
        } else if (dutpCustomerChatDetail.getChatStatus() == 3) {
            chatDetail.setChatContent("会话关闭");
        }
        this.save(chatDetail);
    }

    @Override
    public Boolean changeReadStatus(DutpCustomerChatDetail dutpCustomerChatDetail) {
        if (dutpCustomerChatDetail.getUserType() == 2) {
            // 客户已读客服消息
            QueryWrapper<DutpCustomerChat> chatQueryWrapper = new QueryWrapper<>();
            chatQueryWrapper.lambda().eq(DutpCustomerChat::getUserId, SecurityUtils.getUserId())
                    .eq(DutpCustomerChat::getDelFlag, 0);
            DutpCustomerChat chat = dutpCustomerChatMapper.selectOne(chatQueryWrapper);
            if (ObjectUtil.isNotEmpty(chat)) {
                UpdateWrapper<DutpCustomerChatDetail> queryWrapper = new UpdateWrapper<>();
                queryWrapper.lambda().eq(DutpCustomerChatDetail::getUserType, 1)
                        .eq(DutpCustomerChatDetail::getChatId, chat.getChatId())
                        .set(DutpCustomerChatDetail::getUpdateBy, SecurityUtils.getUsername())
                        .set(DutpCustomerChatDetail::getUpdateTime, new Date())
                        .set(DutpCustomerChatDetail::getReadFlag, 1);
                dutpCustomerChatDetailMapper.update(null,queryWrapper);
                return true;
            }
        } else {
            // 客服已读客户消息
            QueryWrapper<DutpCustomerChat> chatQueryWrapper = new QueryWrapper<>();
            chatQueryWrapper.lambda().eq(DutpCustomerChat::getChatId,dutpCustomerChatDetail.getChatId())
                    .eq(DutpCustomerChat::getDelFlag,0);
            DutpCustomerChat chat = dutpCustomerChatMapper.selectOne(chatQueryWrapper);
            if (chat.getChatStatus() == 2) {
                if (dutpCustomerChatDetail.getUserType() == 1) {
                    QueryWrapper<DutpCustomer> customerQueryWrapper = new QueryWrapper<>();
                    customerQueryWrapper.lambda().eq(DutpCustomer::getUserId,SecurityUtils.getUserId());
                    DutpCustomer customer = dutpcustomerMapper.selectOne(customerQueryWrapper);

                    if (ObjectUtil.isNotEmpty(customer) && customer.getStatus() == 1) {
                        if (customer.getUserId().equals(SecurityUtils.getUserId())) {
                            // 当前登录人正在为该客户服务，修改消息状态
                            DutpCustomerChatDetail detail = new DutpCustomerChatDetail();
                            detail.setUserId(SecurityUtils.getUserId());
                            detail.setReadFlag(1);
                            detail.setUserType(2);
                            detail.setUserId(dutpCustomerChatDetail.getUserId());
                            detail.setChatId(dutpCustomerChatDetail.getChatId());
                            detail.setUpdateTime(new Date());
                            detail.setUpdateBy(SecurityUtils.getUserId().toString());
                            dutpCustomerChatDetailMapper.updateByChatId(detail);
                            return true;
                        } else {
                            return false;
                        }
                    } else {
                        return false;
                    }
                }
            } else {
                return false;
            }
        }
        return false;
    }

    @Override
    public DutpCustomer checkCustomer(DutpCustomer dutpCustomer) {
        QueryWrapper<DutpCustomer> customerQueryWrapper = new QueryWrapper<>();
        customerQueryWrapper.lambda().eq(DutpCustomer::getUserId,SecurityUtils.getUserId());
        DutpCustomer customer = dutpcustomerMapper.selectOne(customerQueryWrapper);
        if (ObjectUtil.isNotEmpty(customer)) {
            if (ObjectUtil.isNotEmpty(dutpCustomer) && dutpCustomer.getChatId() != null) {
                // 获取最新一条会话状态
                QueryWrapper<DutpCustomerChatDetail> detailQueryWrapper = new QueryWrapper<>();
                detailQueryWrapper.lambda().eq(DutpCustomerChatDetail::getChatId, dutpCustomer.getChatId())
                        .eq(DutpCustomerChatDetail::getUserType, 3)
                        .orderByDesc(DutpCustomerChatDetail::getCreateTime)
                        .last("limit 1");
                DutpCustomerChatDetail detail = this.getOne(detailQueryWrapper);
                if (ObjectUtil.isNotEmpty(detail)) {
                    if (!detail.getChatContent().equals("正在会话")) {
                        //非正在会话状态，即非正在服务该客户
                        customer.setIsService(2);
                    } else {
                        if (customer.getUserId().equals(SecurityUtils.getUserId())) {
                            // 正在会话状态，userid与当前客户一致，表示该客户正在被当前客服服务
                            customer.setIsService(1);
                        } else {
                            customer.setIsService(1);
                        }
                    }
                } else {
                    // 没有客服或只有机器人客服应答，非当前人服务对象
                    customer.setIsService(2);
                }

            }
            return customer;
        } else {
            return null;
        }
    }

    @Override
    @Transactional
    public void checkUserChat(DutpCustomerChatDetail detail) {
        // 获取最后聊天记录
        QueryWrapper<DutpCustomerChat> userQueryWrapper = new QueryWrapper<>();
        userQueryWrapper.lambda().eq(DutpCustomerChat::getUserId,SecurityUtils.getUserId());
        DutpCustomerChat chat = dutpCustomerChatMapper.selectOne(userQueryWrapper);
        if (ObjectUtil.isNotEmpty(chat)) {
            DutpCustomerChatDetail dutpCustomerChatDetail = new DutpCustomerChatDetail();
            dutpCustomerChatDetail.setUserId(SecurityUtils.getUserId());
            dutpCustomerChatDetail.setChatId(chat.getChatId());
            QueryWrapper<DutpCustomer> customerQueryWrapper = new QueryWrapper<>();
            customerQueryWrapper.lambda().eq(DutpCustomer::getDelFlag,0).ne(DutpCustomer::getUserId,0)
                    .eq(DutpCustomer::getStatus,1);
            List<DutpCustomer> customerList = dutpcustomerMapper.selectList(customerQueryWrapper);
            if (CollectionUtils.isEmpty(customerList)) {
                // 没有客服在线并且机器人开启状态，机器人上线，修改消息为已读
                customerQueryWrapper = new QueryWrapper<>();
                customerQueryWrapper.lambda().eq(DutpCustomer::getUserId,0);
                DutpCustomer customer = dutpcustomerMapper.selectOne(customerQueryWrapper);
                 if (!ObjectUtil.isEmpty(customer)) {
                    //机器人开启，修改消息为已读
                    QueryWrapper<DutpCustomerChatDetail> userWrapper = new QueryWrapper<>();
                    userWrapper.lambda().eq(DutpCustomerChatDetail::getChatId,dutpCustomerChatDetail.getChatId())
                            .eq(DutpCustomerChatDetail::getUserType,2).eq(DutpCustomerChatDetail::getReadFlag,0)
                            .eq(DutpCustomerChatDetail::getUserId,dutpCustomerChatDetail.getUserId());
                    List<DutpCustomerChatDetail> detailList = dutpCustomerChatDetailMapper.selectList(userWrapper);
                    if (!CollectionUtils.isEmpty(detailList)) {
                        List<Long> ids = detailList.stream().map(DutpCustomerChatDetail::getDetailId).collect(Collectors.toList());
                        UpdateWrapper<DutpCustomerChatDetail> updateWrapper = new UpdateWrapper<>();
                        updateWrapper.lambda().in(DutpCustomerChatDetail::getDetailId,ids)
                                .set(DutpCustomerChatDetail::getReadFlag,1)
                                .set(DutpCustomerChatDetail::getUpdateTime,new Date())
                                .set(DutpCustomerChatDetail::getUpdateBy,"客服机器人");
                        dutpCustomerChatDetailMapper.update(null,updateWrapper);
                    }
                }
            } else {
                // 客户未发送过消息，不做处理
            }
        }
    }

    @Override
    public Boolean checkCustomerByUser(DutpCustomerChatDetail dutpCustomerChatDetail) {
        DutpCustomerChatDetail lastDetail = dutpCustomerChatDetailMapper.selectById(dutpCustomerChatDetail.getUserId());
        if (SecurityUtils.getUserId().equals(lastDetail.getChatUserId())) {
            return true;
        } else {
            return false;
        }
    }
}

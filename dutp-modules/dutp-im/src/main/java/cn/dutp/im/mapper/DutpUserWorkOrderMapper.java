package cn.dutp.im.mapper;

import cn.dutp.domain.DutpUserWorkOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 反馈工单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@Repository
public interface DutpUserWorkOrderMapper extends BaseMapper<DutpUserWorkOrder> {

    List<DutpUserWorkOrder> getCustomerlist(DutpUserWorkOrder dutpUserWorkOrder);

    DutpUserWorkOrder getCustomerInfo(Long ticketId);

    List<DutpUserWorkOrder> selectDutpUserWorkOrderList(DutpUserWorkOrder dutpUserWorkOrder);
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
 <mapper namespace="cn.dutp.im.mapper.DutpCustomerChatDetailMapper">
    
    <resultMap type="DutpCustomerChatDetail" id="DutpCustomerChatDetailResult">
        <result property="detailId"    column="detail_id"    />
        <result property="chatId"    column="chat_id"    />
        <result property="userType"    column="user_type"    />
        <result property="userId"    column="user_id"    />
        <result property="chatContent"    column="chat_content"    />
        <result property="state"    column="state"    />
        <result property="contentType"    column="content_type"    />
        <result property="readFlag"    column="read_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDutpCustomerChatDetailVo">
        select detail_id, chat_id, user_type,user_id, chat_content, state, content_type, read_flag, create_by, create_time, update_by, update_time from dutp_customer_chat_detail
    </sql>

    <select id="selectDutpCustomerChatDetailList" parameterType="DutpCustomerChatDetail" resultMap="DutpCustomerChatDetailResult">
        <include refid="selectDutpCustomerChatDetailVo"/>
        <where>  
            <if test="chatId != null "> and chat_id = #{chatId}</if>
            <if test="userType != null "> and user_type = #{userType}</if>
            <if test="chatContent != null  and chatContent != ''"> and chat_content = #{chatContent}</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="contentType != null "> and content_type = #{contentType}</if>
            <if test="readFlag != null "> and read_flag = #{readFlag}</if>
        </where>
    </select>
    
    <select id="selectDutpCustomerChatDetailByDetailId" parameterType="Long" resultMap="DutpCustomerChatDetailResult">
        <include refid="selectDutpCustomerChatDetailVo"/>
        where detail_id = #{detailId}
    </select>

    <insert id="insertDutpCustomerChatDetail" parameterType="DutpCustomerChatDetail" useGeneratedKeys="true" keyProperty="detailId">
        insert into dutp_customer_chat_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="chatId != null">chat_id,</if>
            <if test="userType != null">user_type,</if>
            <if test="chatContent != null">chat_content,</if>
            <if test="state != null">state,</if>
            <if test="contentType != null">content_type,</if>
            <if test="readFlag != null">read_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="chatId != null">#{chatId},</if>
            <if test="userType != null">#{userType},</if>
            <if test="chatContent != null">#{chatContent},</if>
            <if test="state != null">#{state},</if>
            <if test="contentType != null">#{contentType},</if>
            <if test="readFlag != null">#{readFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDutpCustomerChatDetail" parameterType="DutpCustomerChatDetail">
        update dutp_customer_chat_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="chatId != null">chat_id = #{chatId},</if>
            <if test="userType != null">user_type = #{userType},</if>
            <if test="chatContent != null">chat_content = #{chatContent},</if>
            <if test="state != null">state = #{state},</if>
            <if test="contentType != null">content_type = #{contentType},</if>
            <if test="readFlag != null">read_flag = #{readFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where detail_id = #{detailId}
    </update>

    <delete id="deleteDutpCustomerChatDetailByDetailId" parameterType="Long">
        delete from dutp_customer_chat_detail where detail_id = #{detailId}
    </delete>

    <delete id="deleteDutpCustomerChatDetailByDetailIds" parameterType="String">
        delete from dutp_customer_chat_detail where detail_id in 
        <foreach item="detailId" collection="array" open="(" separator="," close=")">
            #{detailId}
        </foreach>
    </delete>
    <select id="getUserMessage" resultType="cn.dutp.im.domain.vo.DutpCustomerChatDetailVO">
        select
            dcd.detail_id,
            dcd.chat_content,
            dcd.create_time,
            dcr.nick_name as nickName,
            dcd.content_type,
            dcc.user_id,
            dcd.user_type,
            dcd.read_flag
        from
            dutp_customer_chat_detail dcd
        inner join
            dutp_customer_chat dcc on dcd.chat_id = dcc.chat_id
        left join
            dutp_customer dcr on dcr.user_id = dcd.user_id
        <where>
            dcc.del_flag = 0 and dcc.user_id = #{userId} and dcd.user_type != 3
        </where>
        order by dcd.detail_id asc
    </select>
    <select id="getCustomerMessage" resultType="cn.dutp.im.domain.vo.DutpCustomerChatDetailVO" parameterType="cn.dutp.domain.DutpCustomerChatDetail">
        select
            dcd.detail_id,
            dcd.chat_content,
            dcd.content_type,
            dcd.create_time,
            dcd.read_flag,
            dcd.user_id,
            su.nick_name as nickCustomerName,
            du.nick_name as nickUserName,
            su.avatar as nickCustomerUrl,
            du.avatar as nickUserUrl,
            dcd.user_type
        from
            dutp_customer_chat_detail dcd
        inner join
            dutp_customer_chat dcc on dcd.chat_id = dcc.chat_id
        left join
            sys_user su on su.user_id = dcd.user_id
        left join
            dutp_user du on du.user_id = dcc.user_id
        <where>
            dcc.del_flag = 0 and dcd.chat_id = #{chatId}
        </where>
        group by
            dcd.detail_id, dcd.chat_content, dcd.create_time, dcc.user_id, dcd.user_type
        order by
            dcd.create_time asc
    </select>
    <select id="getLastMessage" parameterType="cn.dutp.domain.DutpCustomerChatDetail" resultType="cn.dutp.im.domain.vo.DutpCustomerChatDetailVO">
        select
            dcc.user_id,
            dcc.last_content,
            dcc.last_time,
            dcc.chat_id,
            du.nick_name,
            du.avatar,
            (select count(0) from dutp_customer_chat_detail where user_id = dcc.user_id and read_flag = 0 and user_type != 3) as unReadCount,
            dcc.chat_status,
            (select count(0) from dutp_customer_chat_detail where user_type = 1 and user_id = 0 and chat_id = dcc.chat_id) as robotCount
        from
            dutp_customer_chat dcc
        left join
            dutp_user du on du.user_id = dcc.user_id
        <where>
            dcc.del_flag = 0
            <if test="chatStatus != null">and dcc.chat_status = #{chatStatus}</if>
            order by dcc.create_time desc
        </where>
    </select>
    <update id="updateByChatId" parameterType="cn.dutp.domain.DutpCustomerChatDetail">
        update
            dutp_customer_chat_detail
        set
            read_flag = #{readFlag}
        where
            user_type = #{userType} and chat_id = #{chatId} and user_id = #{userId}
    </update>
</mapper>
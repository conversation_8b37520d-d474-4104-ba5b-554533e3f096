package cn.dutp.im.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * DUTP-BASE-024客服快捷回复对象 dutp_customer_quick_reply
 *
 * <AUTHOR>
 * @date 2024-12-27
 */
@Data
@TableName("dutp_customer_quick_reply")
public class DutpCustomerQuickReply extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long replyId;

    /**
     * 回复内容
     */
    @Excel(name = "回复内容")
    private String replyContent;

    /**
     * 关键词
     */
    @Excel(name = "关键词")
    private String word;

    /**
     * 被设置的关键词
     */
    @TableField(exist = false)
    private String originalWord;

    /**
     * 设置的其他关键词
     */
    @TableField(exist = false)
    private String wordStr;

    /**
     * 自动回复次数
     */
    @Excel(name = "自动回复次数")
    private Integer quantity;

    /**
     * 归属用户0系统通用
     */
    @Excel(name = "归属用户0系统通用")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 状态1启用2停用
     */
    @Excel(name = "状态1启用2停用")
    private Long status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 0系统新增1用户新增
     */
    @TableField(exist = false)
    private Integer isCustomer;

    /**
     * 已回复次数
     */
    @TableField(exist = false)
    private Integer quantityed;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("replyId", getReplyId())
                .append("replyContent", getReplyContent())
                .append("word", getWord())
                .append("quantity", getQuantity())
                .append("userId", getUserId())
                .append("status", getStatus())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

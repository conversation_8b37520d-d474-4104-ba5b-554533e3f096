package cn.dutp.im.controller;

import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.domain.DutpUserWorkOrder;
import cn.dutp.im.service.IDutpUserWorkOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 反馈工单Controller
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@RestController
@RequestMapping("/userorder")
public class DutpUserWorkOrderController extends BaseController {
    @Autowired
    private IDutpUserWorkOrderService dutpUserWorkOrderService;

    /**
     * 查询反馈工单列表
     */
    @RequiresPermissions("im:userorder:list")
    @GetMapping("/list")
    public TableDataInfo list(DutpUserWorkOrder dutpUserWorkOrder) {
        startPage();
        List<DutpUserWorkOrder> list = dutpUserWorkOrderService.selectDutpUserWorkOrderList(dutpUserWorkOrder);
        return getDataTable(list);
    }

    /**
     * 查询反馈工单列表
     */
    @GetMapping("/getCustomerlist")
    public TableDataInfo getCustomerlist(DutpUserWorkOrder dutpUserWorkOrder) {
        startPage();
        List<DutpUserWorkOrder> list = dutpUserWorkOrderService.getCustomerlist(dutpUserWorkOrder);
        return getDataTable(list);
    }

    /**
     * 导出反馈工单列表
     */
    @RequiresPermissions("im:userorder:export")
    @Log(title = "反馈工单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DutpUserWorkOrder dutpUserWorkOrder) {
        List<DutpUserWorkOrder> list = dutpUserWorkOrderService.selectDutpUserWorkOrderList(dutpUserWorkOrder);
        ExcelUtil<DutpUserWorkOrder> util = new ExcelUtil<DutpUserWorkOrder>(DutpUserWorkOrder.class);
        util.exportExcel(response, list, "反馈工单数据");
    }

    /**
     * 获取反馈工单详细信息
     */
    @RequiresPermissions("im:userorder:query")
    @GetMapping(value = "/{ticketId}")
    public AjaxResult getInfo(@PathVariable("ticketId") Long ticketId) {
        return success(dutpUserWorkOrderService.selectDutpUserWorkOrderByTicketId(ticketId));
    }

    /**
     * 客服获取反馈工单详细信息
     */
    @RequiresPermissions("im:userorder:query")
    @GetMapping(value = "/getCustomerInfo")
    public AjaxResult getCustomerInfo(@RequestParam Long ticketId) {
        return success(dutpUserWorkOrderService.getCustomerInfo(ticketId));
    }

    /**
     * 新增反馈工单
     */
    @RequiresPermissions("im:userorder:add")
    @Log(title = "反馈工单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DutpUserWorkOrder dutpUserWorkOrder) {
        return toAjax(dutpUserWorkOrderService.insertDutpUserWorkOrder(dutpUserWorkOrder));
    }

    /**
     * 修改反馈工单
     */
    @RequiresPermissions("im:userorder:edit")
    @Log(title = "反馈工单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DutpUserWorkOrder dutpUserWorkOrder) {
        return toAjax(dutpUserWorkOrderService.updateDutpUserWorkOrder(dutpUserWorkOrder));
    }

    /**
     * 删除反馈工单
     */
    @RequiresPermissions("im:userorder:remove")
    @Log(title = "反馈工单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ticketIds}")
    public AjaxResult remove(@PathVariable Long[] ticketIds) {
        return toAjax(dutpUserWorkOrderService.deleteDutpUserWorkOrderByTicketIds(Arrays.asList(ticketIds)));
    }
}

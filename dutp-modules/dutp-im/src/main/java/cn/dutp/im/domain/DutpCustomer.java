package cn.dutp.im.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * DUTP-BASE-010客服对象 dutp_customer
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
@Data
@TableName("dutp_customer")
public class DutpCustomer extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 客服唯一标识符
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long repId;

    /**
     * 用户ID
     */
    @Excel(name = "用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 客服昵称
     */
    @Excel(name = "客服昵称")
    private String nickName;

    /**
     * 工作状态1上线2下线
     */
    @Excel(name = "工作状态1上线2下线")
    private Integer status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /** 备注 */
    private String remark;

    /** 席位名称 */
    private String seatName;

    /** 头像 */
    @TableField(exist = false)
    private String avatar;

    /**
     * 今日会话
     */
    @TableField(exist = false)
    private Integer talkedCount;

    /**
     * 正在会话
     */
    @TableField(exist = false)
    private Integer talkingCount;

    /**
     * 会话id
     */
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chatId;

    /**
     * 是否正在为当前用户服务 1正在服务2非正在服务对象
     */
    @TableField(exist = false)
    private Integer isService;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("repId", getRepId())
                .append("userId", getUserId())
                .append("nickName", getNickName())
                .append("status", getStatus())
                .append("remark", getRemark())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("seatName", getSeatName())
                .append("avatar", getAvatar())
                .append("talkedCount", getSeatName())
                .append("talkingCount", getTalkingCount())
                .toString();
    }
}

package cn.dutp.im.mapper;

import cn.dutp.domain.DutpCustomerChatDetail;
import cn.dutp.im.domain.vo.DutpCustomerChatDetailVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 客服聊天明细Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
@Repository
public interface DutpCustomerChatDetailMapper extends BaseMapper<DutpCustomerChatDetail>
{

    List<DutpCustomerChatDetailVO> getCustomerMessage(DutpCustomerChatDetail dutpCustomerChatDetail);

    List<DutpCustomerChatDetailVO> getUserMessage(@Param("dutpCustomerChatDetail") DutpCustomerChatDetail dutpCustomerChatDetail,@Param("userId") Long userId);

    List<DutpCustomerChatDetailVO> getLastMessage(DutpCustomerChatDetail dutpCustomerChatDetail);

    void updateByChatId(DutpCustomerChatDetail detail);
}

package cn.dutp.im.controller;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.domain.DutpCustomerChatDetail;
import cn.dutp.im.domain.DutpCustomer;
import cn.dutp.im.service.IDutpCustomerChatDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 客服聊天明细Controller
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
@RestController
@RequestMapping("/detail")
public class DutpCustomerChatDetailController extends BaseController
{
    @Autowired
    private IDutpCustomerChatDetailService dutpCustomerChatDetailService;

    /**
     * 查询客服聊天明细列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DutpCustomerChatDetail dutpCustomerChatDetail)
    {
        startPage();
        List<DutpCustomerChatDetail> list = dutpCustomerChatDetailService.selectDutpCustomerChatDetailList(dutpCustomerChatDetail);
        return getDataTable(list);
    }

    /**
     * 导出客服聊天明细列表
     */
    @Log(title = "客服聊天明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DutpCustomerChatDetail dutpCustomerChatDetail)
    {
        List<DutpCustomerChatDetail> list = dutpCustomerChatDetailService.selectDutpCustomerChatDetailList(dutpCustomerChatDetail);
        ExcelUtil<DutpCustomerChatDetail> util = new ExcelUtil<DutpCustomerChatDetail>(DutpCustomerChatDetail.class);
        util.exportExcel(response, list, "客服聊天明细数据");
    }

    /**
     * 获取客服聊天明细详细信息
     */
    @RequiresPermissions("im:detail:detail")
    @GetMapping(value = "/{detailId}")
    public AjaxResult getInfo(@PathVariable("detailId") Long detailId)
    {
        return success(dutpCustomerChatDetailService.selectDutpCustomerChatDetailByDetailId(detailId));
    }

    /**
     * 新增客服聊天明细
     */
    @RequiresPermissions("system:detail:add")
    @Log(title = "新增客服聊天明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DutpCustomerChatDetail dutpCustomerChatDetail)
    {
        return toAjax(dutpCustomerChatDetailService.insertDutpCustomerChatDetail(dutpCustomerChatDetail));
    }

    /**
     * 修改客服聊天明细
     */
    @RequiresPermissions("system:detail:edit")
    @Log(title = "修改客服聊天明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DutpCustomerChatDetail dutpCustomerChatDetail)
    {
        return toAjax(dutpCustomerChatDetailService.updateDutpCustomerChatDetail(dutpCustomerChatDetail));
    }

    /**
     * 删除客服聊天明细
     */
    @RequiresPermissions("system:detail:remove")
    @Log(title = "删除客服聊天明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{detailIds}")
    public AjaxResult remove(@PathVariable Long[] detailIds)
    {
        return toAjax(dutpCustomerChatDetailService.deleteDutpCustomerChatDetailByDetailIds(Arrays.asList(detailIds)));
    }

    @PostMapping("/send")
    @Log(title = "发送消息", businessType = BusinessType.INSERT)
    public AjaxResult sendMessage(@RequestBody DutpCustomerChatDetail dutpCustomerChatDetail){
        return AjaxResult.success(dutpCustomerChatDetailService.sendMessage(dutpCustomerChatDetail));
    }

    @PostMapping("/getMessage")
    @Log(title = "获取消息")
    public AjaxResult getMessage(@RequestBody DutpCustomerChatDetail dutpCustomerChatDetail){
        return AjaxResult.success(dutpCustomerChatDetailService.getMessage(dutpCustomerChatDetail));
    }

    @PostMapping("/getLastMessage")
    @Log(title = "获取历史最后消息")
    public AjaxResult getLastMessage(@RequestBody DutpCustomerChatDetail dutpCustomerChatDetail){
        return AjaxResult.success(dutpCustomerChatDetailService.getLastMessage(dutpCustomerChatDetail));
    }

    @PostMapping("/changeStatus")
    @RequiresPermissions("im:detail:service")
    @Log(title = "开始/结束服务", businessType = BusinessType.INSERT)
    public AjaxResult changeStatus(@RequestBody DutpCustomerChatDetail dutpCustomerChatDetail){
        dutpCustomerChatDetailService.changeStatus(dutpCustomerChatDetail);
        return AjaxResult.success();
    }

    @PostMapping("/changeReadStatus")
    @Log(title = "已读未读更新", businessType = BusinessType.UPDATE)
    public AjaxResult changeReadStatus(@RequestBody DutpCustomerChatDetail dutpCustomerChatDetail){
        return AjaxResult.success(dutpCustomerChatDetailService.changeReadStatus(dutpCustomerChatDetail));
    }

    @PostMapping("/checkCustomer")
    @Log(title = "是否有客服权限,是否正在为当前客户服务")
    public AjaxResult checkCustomer(@RequestBody DutpCustomer dutpCustomer){
        return AjaxResult.success(dutpCustomerChatDetailService.checkCustomer(dutpCustomer));
    }

    @PostMapping("/checkCurrentCustomer")
    @Log(title = "是否正为当前客户服务")
    public AjaxResult checkCurrentCustomer(@RequestBody DutpCustomerChatDetail dutpCustomerChatDetail){
        return AjaxResult.success(dutpCustomerChatDetailService.checkCustomerByUser(dutpCustomerChatDetail));
    }

    @PostMapping("/checkUserChat")
    @Log(title = "初始化客户聊天")
    public AjaxResult checkUserChat(@RequestBody DutpCustomerChatDetail detail){
        dutpCustomerChatDetailService.checkUserChat(detail);
        return AjaxResult.success();
    }
}

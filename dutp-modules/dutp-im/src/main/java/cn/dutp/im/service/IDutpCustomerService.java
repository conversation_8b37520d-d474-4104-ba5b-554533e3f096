package cn.dutp.im.service;

import cn.dutp.im.domain.DutpCustomer;
import cn.dutp.system.api.domain.SysUser;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * DUTP-BASE-010客服Service接口
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
public interface IDutpCustomerService extends IService<DutpCustomer>
{
    /**
     * 查询DUTP-BASE-010客服
     *
     * @param repId DUTP-BASE-010客服主键
     * @return DUTP-BASE-010客服
     */
    public DutpCustomer selectDutpCustomerByRepId(Long repId);

    /**
     * 查询DUTP-BASE-010客服列表
     *
     * @param dutpCustomer DUTP-BASE-010客服
     * @return DUTP-BASE-010客服集合
     */
    public Map<String,Object> selectDutpCustomerList(DutpCustomer dutpCustomer);

    /**
     * 新增DUTP-BASE-010客服
     *
     * @param dutpCustomer DUTP-BASE-010客服
     * @return 结果
     */
    public boolean insertDutpCustomer(DutpCustomer dutpCustomer);

    /**
     * 修改DUTP-BASE-010客服
     *
     * @param dutpCustomer DUTP-BASE-010客服
     * @return 结果
     */
    public boolean updateDutpCustomer(DutpCustomer dutpCustomer);

    /**
     * 批量删除DUTP-BASE-010客服
     *
     * @param repIds 需要删除的DUTP-BASE-010客服主键集合
     * @return 结果
     */
    public boolean deleteDutpCustomerByRepIds(List<Long> repIds);

    /**
     * 查询客服人员列表
     */
    List<SysUser> selectUserList(SysUser sysUser);

    int updateDutpCustomerStatus(DutpCustomer dutpCustomer);
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.im.mapper.DutpCustomerHotWordMapper">
    
    <resultMap type="DutpCustomerHotWord" id="DutpCustomerHotWordResult">
        <result property="wordId"    column="word_id"    />
        <result property="word"    column="word"    />
        <result property="quantity"    column="quantity"    />
        <result property="inUse"    column="in_use"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDutpCustomerHotWordVo">
        select word_id, word, quantity, in_use, del_flag, create_by, create_time, update_by, update_time from dutp_customer_hot_word
    </sql>

    <select id="selectDutpCustomerHotWordList" parameterType="DutpCustomerHotWord" resultMap="DutpCustomerHotWordResult">
        <include refid="selectDutpCustomerHotWordVo"/>
        <where>  
            <if test="word != null  and word != ''"> and word = #{word}</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
            <if test="inUse != null "> and in_use = #{inUse}</if>
        </where>
    </select>
    
    <select id="selectDutpCustomerHotWordByWordId" parameterType="Long" resultMap="DutpCustomerHotWordResult">
        <include refid="selectDutpCustomerHotWordVo"/>
        where word_id = #{wordId}
    </select>

    <insert id="insertDutpCustomerHotWord" parameterType="DutpCustomerHotWord" useGeneratedKeys="true" keyProperty="wordId">
        insert into dutp_customer_hot_word
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="word != null">word,</if>
            <if test="quantity != null">quantity,</if>
            <if test="inUse != null">in_use,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="word != null">#{word},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="inUse != null">#{inUse},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDutpCustomerHotWord" parameterType="DutpCustomerHotWord">
        update dutp_customer_hot_word
        <trim prefix="SET" suffixOverrides=",">
            <if test="word != null">word = #{word},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="inUse != null">in_use = #{inUse},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where word_id = #{wordId}
    </update>

    <delete id="deleteDutpCustomerHotWordByWordId" parameterType="Long">
        delete from dutp_customer_hot_word where word_id = #{wordId}
    </delete>

    <delete id="deleteDutpCustomerHotWordByWordIds" parameterType="String">
        delete from dutp_customer_hot_word where word_id in 
        <foreach item="wordId" collection="array" open="(" separator="," close=")">
            #{wordId}
        </foreach>
    </delete>

    <update id="updateByWord" parameterType="DutpCustomerHotWord">
        update dutp_customer_hot_word set in_use = #{inUse} where word = #{word}
    </update>
</mapper>
package cn.dutp.im.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DutpCustomerHotWord;
import cn.dutp.im.domain.DutpCustomerQuickReply;
import cn.dutp.im.mapper.DutpCustomerHotWordMapper;
import cn.dutp.im.mapper.DutpCustomerQuickReplyMapper;
import cn.dutp.im.service.IDutpCustomerQuickReplyService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * DUTP-BASE-024客服快捷回复Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-27
 */
@Service
public class DutpCustomerQuickReplyServiceImpl extends ServiceImpl<DutpCustomerQuickReplyMapper, DutpCustomerQuickReply> implements IDutpCustomerQuickReplyService
{
    @Autowired
    private DutpCustomerQuickReplyMapper dutpCustomerQuickReplyMapper;

    @Autowired
    private DutpCustomerHotWordMapper dutpCustomerHotWordMapper;

    /**
     * 查询DUTP-BASE-024客服快捷回复
     *
     * @param replyId DUTP-BASE-024客服快捷回复主键
     * @return DUTP-BASE-024客服快捷回复
     */
    @Override
    public DutpCustomerQuickReply selectDutpCustomerQuickReplyByReplyId(Long replyId)
    {
        return this.getById(replyId);
    }

    /**
     * 查询DUTP-BASE-024客服快捷回复列表
     *
     * @param dutpCustomerQuickReply DUTP-BASE-024客服快捷回复
     * @return DUTP-BASE-024客服快捷回复
     */
    @Override
    public List<DutpCustomerQuickReply> selectDutpCustomerQuickReplyList(DutpCustomerQuickReply dutpCustomerQuickReply)
    {

        LambdaQueryWrapper<DutpCustomerQuickReply> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (dutpCustomerQuickReply.getUserId() == null) {
            lambdaQueryWrapper.eq(DutpCustomerQuickReply::getUserId
                    ,SecurityUtils.getUserId());
        } else {
            lambdaQueryWrapper.eq(DutpCustomerQuickReply::getUserId
                    ,0);
        }
                if(ObjectUtil.isNotEmpty(dutpCustomerQuickReply.getReplyContent())) {
                lambdaQueryWrapper.like(DutpCustomerQuickReply::getReplyContent
                ,dutpCustomerQuickReply.getReplyContent());
            }
                if(ObjectUtil.isNotEmpty(dutpCustomerQuickReply.getWord())) {
                lambdaQueryWrapper.like(DutpCustomerQuickReply::getWord
                ,dutpCustomerQuickReply.getWord());
            }
                if(ObjectUtil.isNotEmpty(dutpCustomerQuickReply.getQuantity())) {
                lambdaQueryWrapper.eq(DutpCustomerQuickReply::getQuantity
                ,dutpCustomerQuickReply.getQuantity());
            }
                if(ObjectUtil.isNotEmpty(dutpCustomerQuickReply.getUserId())) {
                lambdaQueryWrapper.eq(DutpCustomerQuickReply::getUserId
                ,0);
            }
                if(ObjectUtil.isNotEmpty(dutpCustomerQuickReply.getStatus())) {
                lambdaQueryWrapper.eq(DutpCustomerQuickReply::getStatus
                ,dutpCustomerQuickReply.getStatus());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增DUTP-BASE-024客服快捷回复
     *
     * @param dutpCustomerQuickReply DUTP-BASE-024客服快捷回复
     * @return 结果
     */
    @Override
    @Transactional
    public boolean insertDutpCustomerQuickReply(DutpCustomerQuickReply dutpCustomerQuickReply)
    {
        if (StringUtils.isNotBlank(dutpCustomerQuickReply.getReplyContent()) && dutpCustomerQuickReply.getReplyContent().length() >200) {
            throw new ServiceException("回复语最多200个字");
        }
        if (dutpCustomerQuickReply.getUserId() != 0) {
            return this.save(dutpCustomerQuickReply);
        }
        String duplicates = extractDuplicates(dutpCustomerQuickReply.getWord().trim());
        if (StringUtils.isNotBlank(duplicates)) {
            throw new ServiceException("关键字" + duplicates + "重复，请重新输入");
        }
        if (checkByWord(dutpCustomerQuickReply.getWord(), dutpCustomerQuickReply.getOriginalWord(),dutpCustomerQuickReply.getWordStr(),dutpCustomerQuickReply.getUserId())) {
            DutpCustomerHotWord hotWord = new DutpCustomerHotWord(null, null, null, null, "0");
            hotWord.setInUse(1);
            hotWord.setWord(dutpCustomerQuickReply.getWord().trim());
            hotWord.setWord(dutpCustomerQuickReply.getOriginalWord());
            dutpCustomerHotWordMapper.updateByWord(hotWord);
            return this.save(dutpCustomerQuickReply);
        }
        return false;
    }

    private String extractDuplicates(String input) {
        String[] elements = input.split(",");
        HashMap<String, Integer> countMap = new HashMap<>();
        for (String element : elements) {
            countMap.put(element.trim(), countMap.getOrDefault(element.trim(), 0) + 1);
        }
        HashSet<String> duplicates = new HashSet<>();
        for (String key : countMap.keySet()) {
            if (countMap.get(key) > 1) {
                duplicates.add(key);
            }
        }
        return String.join(",", duplicates);
    }

    private Boolean checkByWord(String word,String originalWord,String wordStr,Long userId) {
        List<String> wordList = Arrays.asList(word.split(","));
        if (userId != 0) {
            userId = SecurityUtils.getUserId();
        }
        List<DutpCustomerQuickReply> dutpCustomerQuickReplyList = dutpCustomerQuickReplyMapper.findByWordList(wordList,userId);
        List<DutpCustomerQuickReply> list = new ArrayList<>();
        if (StringUtils.isNotEmpty(wordStr)) {
            list = dutpCustomerQuickReplyMapper.findByWord(wordStr);
        }
        if (CollectionUtils.isEmpty(dutpCustomerQuickReplyList) && CollectionUtils.isEmpty(list)) {
            return true;
        } else {
            List<String> nameList = Arrays.asList(word.split(","));
            String resMessage = "";
            if (!CollectionUtils.isEmpty(dutpCustomerQuickReplyList)) {
                // 获取重复的元素
                List<String> matchedWords = nameList.stream()
                        .filter(wo -> dutpCustomerQuickReplyList.stream()
                                .flatMap(item -> Arrays.stream(item.getWord().split(","))) // 将 wo 拆分成个别词
                                .anyMatch(existingWord -> existingWord.trim().equals(wo))) // 检查是否存在
                        .collect(Collectors.toList()); // 收集结果
                resMessage = String.join(", ", matchedWords);
            }
            if (!CollectionUtils.isEmpty(list)) {
                String err = list.stream()
                        .filter(e -> (!e.getWord().equals(originalWord)))
                        .map(DutpCustomerQuickReply::getWord)
                        .collect(Collectors.joining(","));
                resMessage = StringUtils.isNotEmpty(resMessage) ? (resMessage + ',' + err) : err;
            }
            throw new ServiceException("关键字" + resMessage + "已存在，请重新输入");
        }
    }

    /**
     * 修改DUTP-BASE-024客服快捷回复
     *
     * @param dutpCustomerQuickReply DUTP-BASE-024客服快捷回复
     * @return 结果
     */
    @Override
    @Transactional
    public boolean updateDutpCustomerQuickReply(DutpCustomerQuickReply dutpCustomerQuickReply)
    {
        if (dutpCustomerQuickReply.getUserId() == 0) {
            List<String> wordList = Arrays.asList(dutpCustomerQuickReply.getWord().split(","));
            String result = findDuplicatesAndJoin(wordList);
            if (StringUtils.isNotBlank(result)) {
                throw new ServiceException("关键字" + result + "已存在，请重新输入");
            }
            QueryWrapper<DutpCustomerQuickReply> queryWrapper = new QueryWrapper();
            queryWrapper.lambda().eq(DutpCustomerQuickReply::getDelFlag,0)
                    .eq(DutpCustomerQuickReply::getUserId,dutpCustomerQuickReply.getUserId() == 0 ? 0 :SecurityUtils.getUserId())
                    .ne(DutpCustomerQuickReply::getReplyId,dutpCustomerQuickReply.getReplyId());
            List<DutpCustomerQuickReply> list = dutpCustomerQuickReplyMapper.selectList(queryWrapper);
            if (!CollectionUtils.isEmpty(list)) {
                String checkStr = checkWord(dutpCustomerQuickReply.getWord(), list);
                if (StringUtils.isNotBlank(checkStr)) {
                    throw new ServiceException("关键字" + checkStr + "已存在，请重新输入");
                } else {
                    return this.updateById(dutpCustomerQuickReply);
                }
            }
        } else {
            return this.updateById(dutpCustomerQuickReply);
        }
        return false;
    }

    public static String findDuplicatesAndJoin(List<String> list) {
        Map<String, Integer> frequencyMap = new HashMap<>();
        List<String> duplicates = new ArrayList<>();

        // 统计字符串出现的频率
        for (String item : list) {
            frequencyMap.put(item, frequencyMap.getOrDefault(item, 0) + 1);
        }

        // 找到出现超过一次的字符串
        for (Map.Entry<String, Integer> entry : frequencyMap.entrySet()) {
            if (entry.getValue() > 1) {
                duplicates.add(entry.getKey());
            }
        }
        // 用逗号拼接重复的字符串
        return String.join(", ", duplicates);
    }

    private String checkWord(String inputString, List<DutpCustomerQuickReply> list) {
        Set<String> inputSet = new HashSet<>();
        String[] inputWords = inputString.split(",");
        // 将输入字符串的单词存入一个Set
        for (String word : inputWords) {
            inputSet.add(word);
        }
        Set<String> duplicates = new HashSet<>(); // 用来存储重复的字符串

        // 遍历对象列表，检查每个对象的word是否包含输入字符串中的单词
        for (DutpCustomerQuickReply obj : list) {
            String[] wordObjectWords = obj.getWord().split(",");
            for (String word : wordObjectWords) {
                // 如果输入字符串中包含这个单词，并且它还没有被记录为重复
                if (inputSet.contains(word)) {
                    duplicates.add(word); // 添加重复的单词
                }
            }
        }

        // 拼接所有重复的字符串，并返回
        return String.join(",", duplicates);
    }

    /**
     * 批量删除DUTP-BASE-024客服快捷回复
     *
     * @param replyIds 需要删除的DUTP-BASE-024客服快捷回复主键
     * @return 结果
     */
    @Override
    @Transactional
    public boolean deleteDutpCustomerQuickReplyByReplyIds(List<Long> replyIds)
    {
        return this.removeByIds(replyIds);
    }

    @Override
    public DutpCustomerQuickReply getInfoByWord(String word) {
        DutpCustomerQuickReply res = dutpCustomerQuickReplyMapper.getInfoByWord(word);
        String[] parts = res.getWord().split(",", 2);
        res.setWord(parts[0]);
        res.setWordStr(parts.length > 1 ? parts[1] : "");
        return res;
    }

    @Override
    @Transactional
    public int editStatus(DutpCustomerQuickReply dutpCustomerQuickReply) {
        UpdateWrapper<DutpCustomerQuickReply> queryWrapper = new UpdateWrapper();
        queryWrapper.lambda().eq(DutpCustomerQuickReply::getDelFlag,0)
                .eq(DutpCustomerQuickReply::getReplyId,dutpCustomerQuickReply.getReplyId())
                .set(DutpCustomerQuickReply::getStatus,dutpCustomerQuickReply.getStatus())
                .set(DutpCustomerQuickReply::getUpdateTime,new Date())
                .set(DutpCustomerQuickReply::getUpdateBy, SecurityUtils.getUsername());
        return dutpCustomerQuickReplyMapper.update(null,queryWrapper);
    }

    @Override
    public List<DutpCustomerQuickReply> getListByUserId() {
        LambdaQueryWrapper<DutpCustomerQuickReply> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DutpCustomerQuickReply::getUserId
                ,SecurityUtils.getUserId());
        lambdaQueryWrapper.eq(DutpCustomerQuickReply::getStatus
                ,1);
        return this.list(lambdaQueryWrapper);
    }

}

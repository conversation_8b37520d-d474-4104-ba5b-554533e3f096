package cn.dutp.im.mapper;

import cn.dutp.domain.DutpCustomerChatDetail;
import cn.dutp.im.domain.DutpCustomerQuickReply;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * DUTP-BASE-024客服快捷回复Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
@Repository
public interface DutpCustomerQuickReplyMapper extends BaseMapper<DutpCustomerQuickReply>
{

    List<DutpCustomerQuickReply> findByWordList(@Param("wordList") List<String> wordList,@Param("userId") Long userId);

    List<DutpCustomerQuickReply> findByWord(@Param("wordStr") String wordStr);

    @Select("SELECT * FROM dutp_customer_quick_reply WHERE FIND_IN_SET(#{word}, word)")
    DutpCustomerQuickReply getInfoByWord(String word);

    List<DutpCustomerQuickReply> findByChatDetail(@Param("dutpCustomerChatDetail") DutpCustomerChatDetail dutpCustomerChatDetail,@Param("content") String content);
}

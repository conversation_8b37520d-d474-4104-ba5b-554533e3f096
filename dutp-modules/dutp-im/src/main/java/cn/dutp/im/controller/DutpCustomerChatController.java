package cn.dutp.im.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.im.domain.DutpCustomer;
import cn.dutp.im.domain.DutpCustomerChat;
import cn.dutp.im.service.IDutpCustomerChatService;
import cn.dutp.im.service.IDutpCustomerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * DUTP-BASE-011客服对话明细Controller
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
@RestController
@RequestMapping("/chat")
public class DutpCustomerChatController extends BaseController
{
    @Autowired
    private IDutpCustomerChatService dutpCustomerChatService;

    @Autowired
    private IDutpCustomerService dutpCustomerService;

    /**
     * 查询DUTP-BASE-011客服对话明细列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DutpCustomerChat dutpCustomerChat)
    {
        startPage();
        List<DutpCustomerChat> list = dutpCustomerChatService.selectDutpCustomerChatList(dutpCustomerChat);
        return getDataTable(list);
    }

    /**
     * 导出DUTP-BASE-011客服对话明细列表
     */
    @RequiresPermissions("im:chat:export")
    @Log(title = "客服对话明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DutpCustomerChat dutpCustomerChat)
    {
        List<DutpCustomerChat> list = dutpCustomerChatService.selectDutpCustomerChatList(dutpCustomerChat);
        ExcelUtil<DutpCustomerChat> util = new ExcelUtil<DutpCustomerChat>(DutpCustomerChat.class);
        util.exportExcel(response, list, "DUTP-BASE-011客服对话明细数据");
    }

    /**
     * 获取DUTP-BASE-011客服对话明细详细信息
     */
    @RequiresPermissions("im:chat:query")
    @GetMapping(value = "/{chatId}")
    public AjaxResult getInfo(@PathVariable("chatId") Long chatId)
    {
        return success(dutpCustomerChatService.selectDutpCustomerChatByChatId(chatId));
    }

    /**
     * 新增DUTP-BASE-011客服对话明细
     */
    @RequiresPermissions("im:chat:add")
    @Log(title = "新增客服对话明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DutpCustomerChat dutpCustomerChat)
    {
        return toAjax(dutpCustomerChatService.insertDutpCustomerChat(dutpCustomerChat));
    }

    /**
     * 修改DUTP-BASE-011客服对话明细
     */
    @RequiresPermissions("im:chat:edit")
    @Log(title = "修改客服对话明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DutpCustomerChat dutpCustomerChat)
    {
        return toAjax(dutpCustomerChatService.updateDutpCustomerChat(dutpCustomerChat));
    }

    /**
     * 删除DUTP-BASE-011客服对话明细
     */
    @RequiresPermissions("im:chat:remove")
    @Log(title = "删除客服对话明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{chatIds}")
    public AjaxResult remove(@PathVariable Long[] chatIds)
    {
        return toAjax(dutpCustomerChatService.deleteDutpCustomerChatByChatIds(Arrays.asList(chatIds)));
    }

    /**
     * 客服上线/下线
     */
    @Log(title = "修改客服上线下线", businessType = BusinessType.UPDATE)
    @PostMapping("/updateCustomerStatus")
    public AjaxResult updateCustomerStatus(@RequestBody DutpCustomer dutpCustomer)
    {
        Long userId = SecurityUtils.getUserId();
        if (userId != 1){
            dutpCustomer.setUserId(userId);
            return toAjax(dutpCustomerService.updateDutpCustomerStatus(dutpCustomer));
        }
        return null;
    }
}

package cn.dutp.im.domain.vo;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 客服聊天明细对象 dutp_customer_chat_detail
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
@Data
public class DutpCustomerChatDetailVO extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long detailId;

    /**
     * 对话ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chatId;

    /**
     * 用户名id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 1客服2用户3状态
     */
    private Integer userType;

    /**
     * 对话内容
     */
    private String chatContent;

    /**
     * 0待审核1审核通过2审核不通过
     */
    private Integer state;

    /**
     * 内容类型1文本2图片3文件
     */
    private Integer contentType;

    /**
     * 0未读1已读
     */
    private Integer readFlag;

    /**
     * 1待开启服务2正在会话3会话关闭
     */
    private Integer chatStatus;

    /**
     * 未读条数
     */
    private Integer unReadCount;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 最后聊天记录
     */
    @Excel(name = "最后聊天记录")
    private String lastContent;

    /**
     * 客服名称
     */
    @Excel(name = "客服名称")
    private String nickCustomerName;

    /**
     * 用户名称
     */
    @Excel(name = "用户名称")
    private String nickUserName;

    /**
     * 客服头像
     */
    @Excel(name = "客服头像")
    private String nickCustomerUrl;

    /**
     * 用户头像
     */
    @Excel(name = "用户头像")
    private String nickUserUrl;

    /**
     * 最后的聊天记录时间
     */
    @JsonFormat(pattern = "HH:mm:ss")
    private Date lastTime;

    /**
     * 是否有开始结束服务权限 1有2没有
     */
    private Integer isService;

    /**
     * 机器人自动回复次数
     */
    private Integer robotCount;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("detailId", getDetailId())
                .append("chatId", getChatId())
                .append("userType", getUserType())
                .append("chatContent", getChatContent())
                .append("state", getState())
                .append("contentType", getContentType())
                .append("readFlag", getReadFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("chatStatus", getChatStatus())
                .append("unReadCount", getUnReadCount())
                .append("nickName", getNickName())
                .append("lastContent", getLastContent())
                .append("avatar", getAvatar())
                .append("lastTime", getLastTime())
                .append("nickCustomerName", getNickCustomerName())
                .append("nickUserName", getNickUserName())
                .append("nickCustomerUrl", getNickCustomerUrl())
                .append("nickUserUrl", getNickUserUrl())
                .toString();
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.qrcode.mapper.DutpDiskQrcodeScanLogMapper">
    
    <resultMap type="DutpDiskQrcodeScanLog" id="DutpDiskQrcodeScanLogResult">
        <result property="logId"    column="log_id"    />
        <result property="qrcodeId"    column="qrcode_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDutpDiskQrcodeScanLogVo">
        select log_id, qrcode_id, create_by, create_time, update_by, update_time from dutp_disk_qrcode_scan_log
    </sql>

    <select id="selectDutpDiskQrcodeScanLogList" parameterType="DutpDiskQrcodeScanLog" resultMap="DutpDiskQrcodeScanLogResult">
        <include refid="selectDutpDiskQrcodeScanLogVo"/>
        <where>  
            <if test="qrcodeId != null "> and qrcode_id = #{qrcodeId}</if>
        </where>
    </select>
    
    <select id="selectDutpDiskQrcodeScanLogByLogId" parameterType="Long" resultMap="DutpDiskQrcodeScanLogResult">
        <include refid="selectDutpDiskQrcodeScanLogVo"/>
        where log_id = #{logId}
    </select>

    <insert id="insertDutpDiskQrcodeScanLog" parameterType="DutpDiskQrcodeScanLog" useGeneratedKeys="true" keyProperty="logId">
        insert into dutp_disk_qrcode_scan_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="qrcodeId != null">qrcode_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="qrcodeId != null">#{qrcodeId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDutpDiskQrcodeScanLog" parameterType="DutpDiskQrcodeScanLog">
        update dutp_disk_qrcode_scan_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="qrcodeId != null">qrcode_id = #{qrcodeId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where log_id = #{logId}
    </update>

    <delete id="deleteDutpDiskQrcodeScanLogByLogId" parameterType="Long">
        delete from dutp_disk_qrcode_scan_log where log_id = #{logId}
    </delete>

    <delete id="deleteDutpDiskQrcodeScanLogByLogIds" parameterType="String">
        delete from dutp_disk_qrcode_scan_log where log_id in 
        <foreach item="logId" collection="array" open="(" separator="," close=")">
            #{logId}
        </foreach>
    </delete>
</mapper>
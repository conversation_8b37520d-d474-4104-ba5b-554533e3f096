package cn.dutp.qrcode.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.qrcode.domain.DutpDiskQrcodeScanLog;
import cn.dutp.qrcode.service.IDutpDiskQrcodeScanLogService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 二维码扫描记录Controller
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@RestController
@RequestMapping("/scanLog")
public class DutpDiskQrcodeScanLogController extends BaseController
{
    @Autowired
    private IDutpDiskQrcodeScanLogService dutpDiskQrcodeScanLogService;

/**
 * 查询二维码扫描记录列表
 */
@GetMapping("/list")
    public TableDataInfo list(DutpDiskQrcodeScanLog dutpDiskQrcodeScanLog)
    {
        startPage();
        List<DutpDiskQrcodeScanLog> list = dutpDiskQrcodeScanLogService.selectDutpDiskQrcodeScanLogList(dutpDiskQrcodeScanLog);
        return getDataTable(list);
    }

    /**
     * 导出二维码扫描记录列表
     */
    @RequiresPermissions("qrcode:scanLog:export")
    @Log(title = "二维码扫描记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DutpDiskQrcodeScanLog dutpDiskQrcodeScanLog)
    {
        List<DutpDiskQrcodeScanLog> list = dutpDiskQrcodeScanLogService.selectDutpDiskQrcodeScanLogList(dutpDiskQrcodeScanLog);
        ExcelUtil<DutpDiskQrcodeScanLog> util = new ExcelUtil<DutpDiskQrcodeScanLog>(DutpDiskQrcodeScanLog.class);
        util.exportExcel(response, list, "二维码扫描记录数据");
    }

    /**
     * 获取二维码扫描记录详细信息
     */
    @RequiresPermissions("qrcode:scanLog:query")
    @GetMapping(value = "/{logId}")
    public AjaxResult getInfo(@PathVariable("logId") Long logId)
    {
        return success(dutpDiskQrcodeScanLogService.selectDutpDiskQrcodeScanLogByLogId(logId));
    }

    /**
     * 新增二维码扫描记录
     */
    @RequiresPermissions("qrcode:scanLog:add")
    @Log(title = "二维码扫描记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DutpDiskQrcodeScanLog dutpDiskQrcodeScanLog)
    {
        return dutpDiskQrcodeScanLogService.insertDutpDiskQrcodeScanLog(dutpDiskQrcodeScanLog);
    }

    /**
     * 修改二维码扫描记录
     */
    @RequiresPermissions("qrcode:scanLog:edit")
    @Log(title = "二维码扫描记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DutpDiskQrcodeScanLog dutpDiskQrcodeScanLog)
    {
        return toAjax(dutpDiskQrcodeScanLogService.updateDutpDiskQrcodeScanLog(dutpDiskQrcodeScanLog));
    }

    /**
     * 删除二维码扫描记录
     */
    @RequiresPermissions("qrcode:scanLog:remove")
    @Log(title = "二维码扫描记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{logIds}")
    public AjaxResult remove(@PathVariable Long[] logIds)
    {
        return toAjax(dutpDiskQrcodeScanLogService.deleteDutpDiskQrcodeScanLogByLogIds(Arrays.asList(logIds)));
    }
}

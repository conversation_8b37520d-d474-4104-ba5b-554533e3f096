package cn.dutp.qrcode.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 用户的二维码模板对象 dutp_disk_user_template
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@TableName("dutp_disk_user_template")
public class DutpDiskUserTemplate extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userTemplateId;

    /**
     * 图片地址
     */
    @Excel(name = "图片地址")
    private String imageUrl;

    /**
     * 用户ID
     */
    @Excel(name = "用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long templateId;

    /**
     * 1是默认模板
     */
    @Excel(name = "1是默认模板")
    private Integer isDefault;

    /**
     * 模板名称
     */
    @Excel(name = "模板名称")
    private String templateName;

    /**
     * 组件地址
     */
    @Excel(name = "组件地址")
    private String componentPath;

    /**
     * 默认值JSON
     */
    @Excel(name = "默认值JSON")
    private String defaultData;

    /**
     * 删除标志（0代表存在 1在回收站 2代表删除）
     */
    private String delFlag;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("userTemplateId", getUserTemplateId())
                .append("imageUrl", getImageUrl())
                .append("userId", getUserId())
                .append("templateId", getTemplateId())
                .append("isDefault", getIsDefault())
                .append("templateName", getTemplateName())
                .append("componentPath", getComponentPath())
                .append("defaultData", getDefaultData())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

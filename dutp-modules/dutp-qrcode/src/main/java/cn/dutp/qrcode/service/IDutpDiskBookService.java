package cn.dutp.qrcode.service;

import java.util.List;
import java.util.Map;

import cn.dutp.qrcode.domain.DutpDiskBook;
import cn.dutp.qrcode.domain.dto.DutpDiskRecycleDto;
import cn.dutp.qrcode.domain.dto.DutpDiskStatisticsDto;
import cn.dutp.qrcode.domain.vo.DutpDiskBookVO;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 智典云盘书籍Service接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
public interface IDutpDiskBookService extends IService<DutpDiskBook>
{
    /**
     * 查询智典云盘书籍
     *
     * @param bookId 智典云盘书籍主键
     * @return 智典云盘书籍
     */
    public DutpDiskBook selectDutpDiskBookByBookId(Long bookId);

    /**
     * 查询智典云盘书籍列表
     *
     * @param dutpDiskBook 智典云盘书籍
     * @return 智典云盘书籍集合
     */
    public List<DutpDiskBook> selectDutpDiskBookList(DutpDiskBook dutpDiskBook);

    /**
     * 新增智典云盘书籍
     *
     * @param dutpDiskBook 智典云盘书籍
     * @return 结果
     */
    public boolean insertDutpDiskBook(DutpDiskBook dutpDiskBook);

    /**
     * 修改智典云盘书籍
     *
     * @param dutpDiskBook 智典云盘书籍
     * @return 结果
     */
    public boolean updateDutpDiskBook(DutpDiskBook dutpDiskBook);

    /**
     * 批量删除智典云盘书籍
     *
     * @param bookIds 需要删除的智典云盘书籍主键集合
     * @return 结果
     */
    public boolean deleteDutpDiskBookByBookIds(List<Long> bookIds);

    /**
     * 查询智典云盘回收站列表
     *
     * @param dto 智典云盘书籍
     * @return 智典云盘书籍集合
     */
    List<DutpDiskBook> getRecycleList(DutpDiskRecycleDto dto);

    Integer restore(DutpDiskRecycleDto dto);

    List<DutpDiskBookVO> getStatisticsList(DutpDiskStatisticsDto dto);

    Map<String,Integer> getStatisticsSummary();

    List<DutpDiskBookVO> getStatisticsDetailList(DutpDiskStatisticsDto dto);
}

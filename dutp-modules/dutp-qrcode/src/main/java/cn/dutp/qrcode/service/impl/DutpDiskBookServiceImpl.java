package cn.dutp.qrcode.service.impl;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.redis.service.RedisService;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.qrcode.domain.DutpDiskQrcode;
import cn.dutp.qrcode.domain.DutpDiskQrcodeScanLog;
import cn.dutp.qrcode.domain.dto.DutpDiskRecycleDto;
import cn.dutp.qrcode.domain.dto.DutpDiskStatisticsDto;
import cn.dutp.qrcode.domain.vo.DutpDiskBookVO;
import cn.dutp.qrcode.mapper.DutpDiskQrcodeMapper;
import cn.dutp.qrcode.mapper.DutpDiskQrcodeScanLogMapper;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import cn.dutp.qrcode.mapper.DutpDiskBookMapper;
import cn.dutp.qrcode.domain.DutpDiskBook;
import cn.dutp.qrcode.service.IDutpDiskBookService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import cn.dutp.qrcode.domain.vo.DutpDiskBookClickVo;

/**
 * 智典云盘书籍Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Service
public class DutpDiskBookServiceImpl extends ServiceImpl<DutpDiskBookMapper, DutpDiskBook> implements IDutpDiskBookService
{
    @Autowired
    private DutpDiskBookMapper dutpDiskBookMapper;

    @Autowired
    private DutpDiskQrcodeMapper dutpDiskQrcodeMapper;
    @Autowired
    private DutpDiskQrcodeScanLogMapper dutpDiskQrcodeScanLogMapper;

    @Autowired
    private RedisService redisService;

    private static final String QRCODE_SCAN_COUNT_KEY =  "qrcode_scan_count";

    /**
     * 查询智典云盘书籍
     *
     * @param bookId 智典云盘书籍主键
     * @return 智典云盘书籍
     */
    @Override
    public DutpDiskBook selectDutpDiskBookByBookId(Long bookId)
    {
        return this.getById(bookId);
    }

    /**
     * 查询智典云盘书籍列表
     *
     * @param dutpDiskBook 智典云盘书籍
     * @return 智典云盘书籍
     */
    @Override
    public List<DutpDiskBook> selectDutpDiskBookList(DutpDiskBook dutpDiskBook)
    {
        List<DutpDiskBook> list = dutpDiskBookMapper.selectDutpDiskBookList(dutpDiskBook);

        //拉取这些书的二维码扫描数，置入对象中
        if (!list.isEmpty()) {
            getTotalCountMap(list);
        }

        return list;
    }


    private void getTotalCountMap(List<DutpDiskBook> list) {
        // 先从缓存获取
        Map<Long, DutpDiskBookClickVo> cacheMap = redisService.getCacheObject(QRCODE_SCAN_COUNT_KEY);
        
        // 记录未从缓存获取到的bookId
        List<Long> remainingBookIds = new ArrayList<>();
        
        // 处理缓存中有的数据
        if (cacheMap != null && !cacheMap.isEmpty()) {
            list.forEach(book -> {
                DutpDiskBookClickVo clickVo = cacheMap.get(book.getBookId());
                if (clickVo != null) {
                    book.setQrcodeClickVo(clickVo);
                } else {
                    remainingBookIds.add(book.getBookId()); 
                }
            });
        } else {
            remainingBookIds.addAll(list.stream()
                .map(DutpDiskBook::getBookId)
                .collect(Collectors.toList()));
        }

        // 如果所有数据都从缓存获取到了，直接返回
        if (remainingBookIds.isEmpty()) {
            return;
        }
        
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfToday = now.toLocalDate().atStartOfDay();
        LocalDateTime startOfWeek = now.with(DayOfWeek.MONDAY).toLocalDate().atStartOfDay();
        
        // 查询剩余书籍的今日、本周和总扫描数
        Map<Long, Integer> todayCountMap = dutpDiskQrcodeScanLogMapper.selectList(
            new LambdaQueryWrapper<DutpDiskQrcodeScanLog>()
                .in(DutpDiskQrcodeScanLog::getBookId, remainingBookIds)
                .ge(DutpDiskQrcodeScanLog::getCreateTime, startOfToday)
        ).stream().collect(
            Collectors.groupingBy(
                DutpDiskQrcodeScanLog::getBookId,
                Collectors.collectingAndThen(Collectors.counting(), Long::intValue)
            )
        );
        
        Map<Long, Integer> weekCountMap = dutpDiskQrcodeScanLogMapper.selectList(
            new LambdaQueryWrapper<DutpDiskQrcodeScanLog>()
                .in(DutpDiskQrcodeScanLog::getBookId, remainingBookIds)
                .ge(DutpDiskQrcodeScanLog::getCreateTime, startOfWeek)
        ).stream().collect(
            Collectors.groupingBy(
                DutpDiskQrcodeScanLog::getBookId,
                Collectors.collectingAndThen(Collectors.counting(), Long::intValue)
            )
        );
        
        Map<Long, Integer> totalCountMap = dutpDiskQrcodeScanLogMapper.selectList(
            new LambdaQueryWrapper<DutpDiskQrcodeScanLog>()
                .in(DutpDiskQrcodeScanLog::getBookId, remainingBookIds)
        ).stream().collect(
            Collectors.groupingBy(
                DutpDiskQrcodeScanLog::getBookId,
                Collectors.collectingAndThen(Collectors.counting(), Long::intValue)
            )
        );
        
        // 将数据库查询的扫描数设置到对应的书籍对象中
        list.forEach(book -> {
            if (remainingBookIds.contains(book.getBookId())) {
                DutpDiskBookClickVo clickVo = new DutpDiskBookClickVo();
                clickVo.setToday(todayCountMap.getOrDefault(book.getBookId(), 0));
                clickVo.setWeek(weekCountMap.getOrDefault(book.getBookId(), 0));
                clickVo.setTotal(totalCountMap.getOrDefault(book.getBookId(), 0));
                book.setQrcodeClickVo(clickVo);
            }
        });
    }

    /**
     * 新增智典云盘书籍
     *
     * @param dutpDiskBook 智典云盘书籍
     * @return 结果
     */
    @Override
    public boolean insertDutpDiskBook(DutpDiskBook dutpDiskBook)
    {
        return this.save(dutpDiskBook);
    }

    /**
     * 修改智典云盘书籍
     *
     * @param dutpDiskBook 智典云盘书籍
     * @return 结果
     */
    @Override
    public boolean updateDutpDiskBook(DutpDiskBook dutpDiskBook)
    {
        return this.updateById(dutpDiskBook);
    }

    /**
     * 批量删除智典云盘书籍
     *
     * @param bookIds 需要删除的智典云盘书籍主键
     * @return 结果
     */
    @Override
    public boolean deleteDutpDiskBookByBookIds(List<Long> bookIds)
    {
        boolean removed = this.removeByIds(bookIds);
        if (removed){
            //如果书被删除了，要把下面的码都设为删除
            dutpDiskQrcodeMapper.update(null, new LambdaUpdateWrapper<DutpDiskQrcode>()
                    .set(DutpDiskQrcode::getDelFlag, "2").set(DutpDiskQrcode::getUpdateTime, new Date()).set(DutpDiskQrcode::getUpdateBy, SecurityUtils.getUsername())
                    .in(DutpDiskQrcode::getBookId, bookIds));
        }

        return removed;
    }

    @Override
    public List<DutpDiskBook> getRecycleList(DutpDiskRecycleDto dto) {
        List<DutpDiskBook> res = new ArrayList<>();
        if ("basic".equals(dto.getActiveName())) {
            return dutpDiskBookMapper.getBookRecycleList(dto);
        }
        if ("columnInfo".equals(dto.getActiveName())) {
            res = dutpDiskBookMapper.getQrcodeRecycleList(dto);
        }
        return res;
    }

    @Override
    @Transactional
    public Integer restore(DutpDiskRecycleDto dto) {
        if ("basic".equals(dto.getActiveName())) {
            return dutpDiskBookMapper.restoreBook(dto.getRestoreList());
        }
        if ("columnInfo".equals(dto.getActiveName())) {
            List<DutpDiskBook> bookList = dutpDiskBookMapper.getBookRecycleListByIds(dto.getRestoreList());
            if (!CollectionUtils.isEmpty(bookList)) {
                String errMsg = bookList.stream()
                        .map(DutpDiskBook::getBookName)
                        .collect(Collectors.joining(","));
                throw new ServiceException("教材【" + errMsg + "】信息尚未恢复，请先恢复教材，再进行恢复二维码操作");
            } else {
                return dutpDiskBookMapper.restoreQrcode(dto.getRestoreList());
            }
        }
        return null;
    }

    @Override
    public List<DutpDiskBookVO> getStatisticsList(DutpDiskStatisticsDto dto) {
        if (dto.getSelectType() == 1) {
            return dutpDiskBookMapper.getStatisticsBookList(dto);
        } else if (dto.getSelectType() == 2) {
            return dutpDiskBookMapper.getStatisticsQrCodeList(dto);
        }
        return null;
    }

    @Override
    public Map<String, Integer> getStatisticsSummary() {
        Map<String,Integer> res = new HashMap<>();
        // 教材总量
        LambdaQueryWrapper<DutpDiskBook> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DutpDiskBook::getDelFlag,0);
        Integer bookCount = dutpDiskBookMapper.selectCount(queryWrapper);
        res.put("bookCount",bookCount);
        // 二维码总量
        LambdaQueryWrapper<DutpDiskQrcode> codeWrapper = new LambdaQueryWrapper<>();
        codeWrapper.eq(DutpDiskQrcode::getDelFlag,0).eq(DutpDiskQrcode::getQrcodeType,1);
        List<DutpDiskQrcode> qrcodeList = dutpDiskQrcodeMapper.selectList(codeWrapper);
        res.put("qrcodeCount",qrcodeList.size());
        codeWrapper.eq(DutpDiskQrcode::getState,1);
        List<DutpDiskQrcode> emptyList = dutpDiskQrcodeMapper.selectList(codeWrapper);
        // 二维码累计扫描量
        // 二维码空码数量
        if (CollectionUtils.isEmpty(qrcodeList)) {
            res.put("qrcodeHistory",0);
            res.put("emptyQrcode",0);
        } else {
            List<Long> ids = qrcodeList.stream().map(DutpDiskQrcode::getQrcodeId).collect(Collectors.toList());
            LambdaQueryWrapper<DutpDiskQrcodeScanLog> historyWrapper = new LambdaQueryWrapper<>();
            historyWrapper.in(DutpDiskQrcodeScanLog::getQrcodeId,ids);
            Integer qrcodeHistory = dutpDiskQrcodeScanLogMapper.selectCount(historyWrapper);
            res.put("qrcodeHistory",qrcodeHistory);
            if (CollectionUtils.isEmpty(emptyList)) {
                res.put("emptyQrcode",0);
            } else {
                List<Long> idlist = emptyList.stream().map(DutpDiskQrcode::getQrcodeId).collect(Collectors.toList());
                LambdaQueryWrapper<DutpDiskQrcodeScanLog> historyemptyWrapper = new LambdaQueryWrapper<>();
                historyemptyWrapper.in(DutpDiskQrcodeScanLog::getQrcodeId,idlist);
                Integer emptyCount = dutpDiskQrcodeScanLogMapper.selectCount(historyWrapper);
                res.put("emptyQrcode",emptyCount);
            }
        }
        return res;
    }

    @Override
    public List<DutpDiskBookVO> getStatisticsDetailList(DutpDiskStatisticsDto dto) {
        return dutpDiskQrcodeMapper.getStatisticsDetailList(dto);
    }

}

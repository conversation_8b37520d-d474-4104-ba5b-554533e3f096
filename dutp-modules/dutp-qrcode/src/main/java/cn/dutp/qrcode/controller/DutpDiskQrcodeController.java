package cn.dutp.qrcode.controller;

import java.util.List;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.Arrays;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.qrcode.domain.DutpDiskQrcode;
import cn.dutp.qrcode.service.IDutpDiskQrcodeService;
import cn.hutool.core.io.resource.InputStreamResource;
import lombok.extern.slf4j.Slf4j;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

import com.aliyun.oss.common.utils.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;

/**
 * 智典云盘资源Controller
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Slf4j
@RestController
@RequestMapping("/qrcode")
public class DutpDiskQrcodeController extends BaseController
{
    @Autowired
    private IDutpDiskQrcodeService dutpDiskQrcodeService;
    

/**
 * 查询智典云盘资源列表
 */
@GetMapping("/list")
    public TableDataInfo list(DutpDiskQrcode dutpDiskQrcode)
    {
        startPage();
        List<DutpDiskQrcode> list = dutpDiskQrcodeService.selectDutpDiskQrcodeList(dutpDiskQrcode);
        return getDataTable(list);
    }


    /**
     * 查询智典云盘资源列表(检索窗口使用)
     */
    @GetMapping("/search/list") 
    public TableDataInfo searchList(DutpDiskQrcode dutpDiskQrcode)
    {

        List<DutpDiskQrcode> list = dutpDiskQrcodeService.selectDutpDiskQrcodeSearchList(dutpDiskQrcode);
        return getDataTable(list);
    }

    /**
     * 导出智典云盘资源列表
     */
    @RequiresPermissions("qrcode:qrcode:export")
    @Log(title = "智典云盘资源", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DutpDiskQrcode dutpDiskQrcode)
    {
        List<DutpDiskQrcode> list = dutpDiskQrcodeService.selectDutpDiskQrcodeList(dutpDiskQrcode);
        ExcelUtil<DutpDiskQrcode> util = new ExcelUtil< DutpDiskQrcode>(DutpDiskQrcode.class);
        util.exportExcel(response, list, "智典云盘资源数据");
    }

    /**
     * 获取智典云盘资源详细信息
     */
    @RequiresPermissions("qrcode:qrcode:query")
    @GetMapping(value = "/{qrcodeId}")
    public AjaxResult getInfo(@PathVariable("qrcodeId") Long qrcodeId)
    {
        return success(dutpDiskQrcodeService.selectDutpDiskQrcodeByQrcodeId(qrcodeId));
    }

    /**
     * 新增智典云盘资源
     */
    @RequiresPermissions("qrcode:qrcode:add")
    @Log(title = "智典云盘资源", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DutpDiskQrcode dutpDiskQrcode)
    {
        return success(dutpDiskQrcodeService.insertDutpDiskQrcode(dutpDiskQrcode));
    }

    /**
     * 修改智典云盘资源
     */
    @RequiresPermissions("qrcode:qrcode:edit")
    @Log(title = "智典云盘资源", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DutpDiskQrcode dutpDiskQrcode)
    {
        return toAjax(dutpDiskQrcodeService.updateDutpDiskQrcode(dutpDiskQrcode));
    }

    /**
     * 删除智典云盘资源
     */
    @RequiresPermissions("qrcode:qrcode:remove")
    @Log(title = "智典云盘资源", businessType = BusinessType.DELETE)
    @DeleteMapping("/{qrcodeIds}")
    public AjaxResult remove(@PathVariable Long[] qrcodeIds)
    {
        return toAjax(dutpDiskQrcodeService.deleteDutpDiskQrcodeByQrcodeIds(Arrays.asList(qrcodeIds)));
    }


    @Log(title = "云盘二维码", businessType = BusinessType.IMPORT)
    @RequiresPermissions("qrcode:qrcode:add")
    @PostMapping("/importData")
    public AjaxResult importData(@RequestBody  List<DutpDiskQrcode> DutpDiskQrcodes) throws Exception
    {
        ExcelUtil<DutpDiskQrcode> util = new ExcelUtil<>(DutpDiskQrcode.class);
        String operName = SecurityUtils.getUsername();
        String message = dutpDiskQrcodeService.importQrcode(DutpDiskQrcodes,operName);
        return success(message);
    }


    @PostMapping("/downloadList/")
    public TableDataInfo downloadList(@RequestBody Map<String, List<Long>> request)
    {
        List<DutpDiskQrcode> list = dutpDiskQrcodeService.selectDutpDiskQrcodeListForDownload(request.get("qrcodeIds"),request.get("bookIds"));
        return getDataTable(list);
    }

    @PostMapping("/downloadResourceList/")
    public TableDataInfo downloadResourceList(@RequestBody Map<String, List<Long>> request)
    {
        List<DutpDiskQrcode> list = dutpDiskQrcodeService.selectDutpDiskQrcodeMongoForDownload(request.get("qrcodeIds"),request.get("bookIds"));
        return getDataTable(list);
    }


    @PostMapping("/addWatermark/")
    public ResponseEntity<byte[]> addWatermark(@RequestParam("file") MultipartFile file, @RequestParam("watermarkText") String watermarkText)
    {
        try {
            ByteArrayOutputStream outputStream = dutpDiskQrcodeService.addWatermarkToFile(file, watermarkText);
            
            // 获取原始文件名并保留扩展名
            String originalFilename = file.getOriginalFilename();
            String fileExtension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            
            // 构建新的文件名
            String newFilename = "watermarked" + fileExtension;
            
            // 使用 ResponseEntity.BodyBuilder 创建响应
            return ResponseEntity
                .ok()
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + newFilename + "\"")
                .body(outputStream.toByteArray());
        } catch (Exception e) {
            log.error("添加水印失败", e);
            throw new ServiceException("添加水印失败");
        }
    }
}

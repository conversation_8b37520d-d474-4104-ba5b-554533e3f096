package cn.dutp.qrcode.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.qrcode.domain.DutpDiskQrcodeTemplate;
import cn.dutp.qrcode.service.IDutpDiskQrcodeTemplateService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 二维码模板Controller
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@RestController
@RequestMapping("/template")
public class DutpDiskQrcodeTemplateController extends BaseController {
    @Autowired
    private IDutpDiskQrcodeTemplateService dutpDiskQrcodeTemplateService;

    /**
     * 查询二维码模板列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DutpDiskQrcodeTemplate dutpDiskQrcodeTemplate) {
        startPage();
        List<DutpDiskQrcodeTemplate> list = dutpDiskQrcodeTemplateService.selectDutpDiskQrcodeTemplateList(dutpDiskQrcodeTemplate);
        return getDataTable(list);
    }

    /**
     * 导出二维码模板列表
     */
    @RequiresPermissions("qrcode:template:export")
    @Log(title = "二维码模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DutpDiskQrcodeTemplate dutpDiskQrcodeTemplate) {
        List<DutpDiskQrcodeTemplate> list = dutpDiskQrcodeTemplateService.selectDutpDiskQrcodeTemplateList(dutpDiskQrcodeTemplate);
        ExcelUtil<DutpDiskQrcodeTemplate> util = new ExcelUtil<DutpDiskQrcodeTemplate>(DutpDiskQrcodeTemplate.class);
        util.exportExcel(response, list, "二维码模板数据");
    }

    /**
     * 获取二维码模板详细信息
     */

    @GetMapping(value = "/{templateId}")
    public AjaxResult getInfo(@PathVariable("templateId") Long templateId) {
        return success(dutpDiskQrcodeTemplateService.selectDutpDiskQrcodeTemplateByTemplateId(templateId));
    }

    /**
     * 新增二维码模板
     */
    @RequiresPermissions("qrcode:template:add")
    @Log(title = "二维码模板", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DutpDiskQrcodeTemplate dutpDiskQrcodeTemplate) {
        return toAjax(dutpDiskQrcodeTemplateService.insertDutpDiskQrcodeTemplate(dutpDiskQrcodeTemplate));
    }

    /**
     * 修改二维码模板
     */
    @RequiresPermissions("qrcode:template:edit")
    @Log(title = "二维码模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DutpDiskQrcodeTemplate dutpDiskQrcodeTemplate) {
        return toAjax(dutpDiskQrcodeTemplateService.updateDutpDiskQrcodeTemplate(dutpDiskQrcodeTemplate));
    }

    /**
     * 删除二维码模板
     */
    @RequiresPermissions("qrcode:template:remove")
    @Log(title = "二维码模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/{templateIds}")
    public AjaxResult remove(@PathVariable Long[] templateIds) {
        return toAjax(dutpDiskQrcodeTemplateService.deleteDutpDiskQrcodeTemplateByTemplateIds(Arrays.asList(templateIds)));
    }



    /**
     * 获取用户设置的的默认二维码模板详细信息
     */
    @RequiresPermissions("qrcode:template:query")
    @GetMapping(value = "/mineDefaultTemplate")
    public AjaxResult mineDefaultTemplate()
    {

        return success(dutpDiskQrcodeTemplateService.selectDutpDiskUserTemplateByUserId(SecurityUtils.getUserId()));
    }
}

package cn.dutp.qrcode.service;

import java.util.List;

import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.qrcode.domain.DutpDiskQrcodeScanLog;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 二维码扫描记录Service接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
public interface IDutpDiskQrcodeScanLogService extends IService<DutpDiskQrcodeScanLog>
{
    /**
     * 查询二维码扫描记录
     *
     * @param logId 二维码扫描记录主键
     * @return 二维码扫描记录
     */
    public DutpDiskQrcodeScanLog selectDutpDiskQrcodeScanLogByLogId(Long logId);

    /**
     * 查询二维码扫描记录列表
     *
     * @param dutpDiskQrcodeScanLog 二维码扫描记录
     * @return 二维码扫描记录集合
     */
    public List<DutpDiskQrcodeScanLog> selectDutpDiskQrcodeScanLogList(DutpDiskQrcodeScanLog dutpDiskQrcodeScanLog);

    /**
     * 新增二维码扫描记录
     *
     * @param dutpDiskQrcodeScanLog 二维码扫描记录
     * @return 结果
     */
    public AjaxResult insertDutpDiskQrcodeScanLog(DutpDiskQrcodeScanLog dutpDiskQrcodeScanLog);

    /**
     * 修改二维码扫描记录
     *
     * @param dutpDiskQrcodeScanLog 二维码扫描记录
     * @return 结果
     */
    public boolean updateDutpDiskQrcodeScanLog(DutpDiskQrcodeScanLog dutpDiskQrcodeScanLog);

    /**
     * 批量删除二维码扫描记录
     *
     * @param logIds 需要删除的二维码扫描记录主键集合
     * @return 结果
     */
    public boolean deleteDutpDiskQrcodeScanLogByLogIds(List<Long> logIds);

}

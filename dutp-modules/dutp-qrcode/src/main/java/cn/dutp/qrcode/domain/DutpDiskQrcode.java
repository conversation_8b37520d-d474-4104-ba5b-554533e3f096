package cn.dutp.qrcode.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.qrcode.domain.vo.DutpDiskBookClickVo;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 智典云盘资源对象 dutp_disk_qrcode
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@TableName("dutp_disk_qrcode")
public class DutpDiskQrcode extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** $column.columnComment */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long qrcodeId;

    /** 图片地址 */
        @Excel(name = "图片地址")
    private String imageUrl;

    /** 二维码名称 */
        @Excel(name = "二维码名称")
    private String qrcodeName;

    /** 1资源码2跳转码3样章码 */
        @Excel(name = "1资源码2跳转码3样章码")
    private Integer qrcodeType;

    /** 目录ID */
        @Excel(name = "目录ID")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long catalogId;

    /** 跳转码的URL */
        @Excel(name = "跳转码URL")
    private String jumpUrl;

    /** 码内容mongodb中的objectId */
        @Excel(name = "码内容mongodb中的objectId")
    private String mongoObjectId;

        /** 编辑的json **/
        @TableField(exist = false)
    private String mongoJson;

    /** 智典云盘书籍ID，不是数字教材bookID */
        @Excel(name = "智典云盘书籍ID，不是数字教材bookID")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /** 状态1空码2非空码 */
        @Excel(name = "状态1空码2非空码")
    private Integer state;

    /** 删除标志（0代表存在 1在回收站 2代表删除） */
    private String delFlag;

    /** 冗余一个书籍名称 */
    @TableField(exist = false)
    private String bookName;

    /** 冗余一个目录名称 */
    @TableField(exist = false)
    private String catalogName;

    /** ISBN */
    @TableField(exist = false)
    private String ISBN;
    /** 统计数据 */
    @TableField(exist = false)
    private DutpDiskBookClickVo qrcodeClickVo;

    @TableField(exist = false)
    private String sortField;

    @TableField(exist = false)
    private String sortOrder;



@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("qrcodeId", getQrcodeId())
            .append("imageUrl", getImageUrl())
            .append("qrcodeName", getQrcodeName())
            .append("qrcodeType", getQrcodeType())
            .append("catalogId", getCatalogId())
            .append("jumpUrl", getJumpUrl())
            .append("mongoObjectId", getMongoObjectId())
            .append("bookId", getBookId())
            .append("state", getState())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
        .toString();
        }
        }

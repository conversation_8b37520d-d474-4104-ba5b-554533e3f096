package cn.dutp.qrcode.service.impl;

import java.util.List;

import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.qrcode.domain.DutpDiskUserTemplate;
import cn.dutp.qrcode.mapper.DutpDiskUserTemplateMapper;
import cn.dutp.qrcode.service.IDutpDiskUserTemplateService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 用户的二维码模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
public class DutpDiskUserTemplateServiceImpl extends ServiceImpl<DutpDiskUserTemplateMapper, DutpDiskUserTemplate> implements IDutpDiskUserTemplateService
{
    @Autowired
    private DutpDiskUserTemplateMapper dutpDiskUserTemplateMapper;

    /**
     * 查询用户的二维码模板
     *
     * @param userTemplateId 用户的二维码模板主键
     * @return 用户的二维码模板
     */
    @Override
    public DutpDiskUserTemplate selectDutpDiskUserTemplateByUserTemplateId(Long userTemplateId)
    {
        return this.getById(userTemplateId);
    }

    /**
     * 查询用户的二维码模板列表
     *
     * @param dutpDiskUserTemplate 用户的二维码模板
     * @return 用户的二维码模板
     */
    @Override
    public List<DutpDiskUserTemplate> selectDutpDiskUserTemplateList(DutpDiskUserTemplate dutpDiskUserTemplate)
    {
        LambdaQueryWrapper<DutpDiskUserTemplate> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dutpDiskUserTemplate.getImageUrl())) {
                lambdaQueryWrapper.eq(DutpDiskUserTemplate::getImageUrl
                ,dutpDiskUserTemplate.getImageUrl());
            }
                if(ObjectUtil.isNotEmpty(dutpDiskUserTemplate.getUserId())) {
                lambdaQueryWrapper.eq(DutpDiskUserTemplate::getUserId
                ,dutpDiskUserTemplate.getUserId());
            }
                if(ObjectUtil.isNotEmpty(dutpDiskUserTemplate.getTemplateId())) {
                lambdaQueryWrapper.eq(DutpDiskUserTemplate::getTemplateId
                ,dutpDiskUserTemplate.getTemplateId());
            }
                if(ObjectUtil.isNotEmpty(dutpDiskUserTemplate.getIsDefault())) {
                lambdaQueryWrapper.eq(DutpDiskUserTemplate::getIsDefault
                ,dutpDiskUserTemplate.getIsDefault());
            }
                if(ObjectUtil.isNotEmpty(dutpDiskUserTemplate.getTemplateName())) {
                lambdaQueryWrapper.like(DutpDiskUserTemplate::getTemplateName
                ,dutpDiskUserTemplate.getTemplateName());
            }
                if(ObjectUtil.isNotEmpty(dutpDiskUserTemplate.getComponentPath())) {
                lambdaQueryWrapper.eq(DutpDiskUserTemplate::getComponentPath
                ,dutpDiskUserTemplate.getComponentPath());
            }
                if(ObjectUtil.isNotEmpty(dutpDiskUserTemplate.getDefaultData())) {
                lambdaQueryWrapper.eq(DutpDiskUserTemplate::getDefaultData
                ,dutpDiskUserTemplate.getDefaultData());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增用户的二维码模板
     *
     * @param dutpDiskUserTemplate 用户的二维码模板
     * @return 结果
     */
    @Override
    public boolean insertDutpDiskUserTemplate(DutpDiskUserTemplate dutpDiskUserTemplate)
    {
        //先用sql查出userTemplateName的个数
        int count = dutpDiskUserTemplateMapper.selectUserTemplateName(dutpDiskUserTemplate);
        //如果count > 0则抛异常 “该模板已添加到我的样式”
        if(count > 0){
            throw new ServiceException ("该模板已添加到我的样式");
        }
        return this.save(dutpDiskUserTemplate);
    }

    /**
     * 修改用户的二维码模板
     *
     * @param dutpDiskUserTemplate 用户的二维码模板
     * @return 结果
     */
    @Override
    public boolean updateDutpDiskUserTemplate(DutpDiskUserTemplate dutpDiskUserTemplate)
    {
        return this.updateById(dutpDiskUserTemplate);
    }

    /**
     * 批量删除用户的二维码模板
     *
     * @param userTemplateIds 需要删除的用户的二维码模板主键
     * @return 结果
     */
    @Override
    public boolean deleteDutpDiskUserTemplateByUserTemplateIds(List<Long> userTemplateIds)
    {
        return this.removeByIds(userTemplateIds);
    }

    @Override
    public boolean changeDefault(DutpDiskUserTemplate dutpDiskUserTemplate) {

        LambdaQueryWrapper<DutpDiskUserTemplate> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DutpDiskUserTemplate::getIsDefault, 1);
        List<DutpDiskUserTemplate> userTemplates = dutpDiskUserTemplateMapper.selectList(lambdaQueryWrapper);
        if(ObjectUtil.isNotEmpty(userTemplates)){
            for(DutpDiskUserTemplate userTemplate : userTemplates){
                dutpDiskUserTemplateMapper.changeDefaultIsZero(userTemplate);
            }
        }
        return dutpDiskUserTemplateMapper.changeDefault(dutpDiskUserTemplate);

    }

}

package cn.dutp.qrcode.mapper;

import java.util.List;

import cn.dutp.qrcode.domain.dto.DutpDiskRecycleDto;
import cn.dutp.qrcode.domain.dto.DutpDiskStatisticsDto;
import cn.dutp.qrcode.domain.vo.DutpDiskBookVO;
import org.springframework.stereotype.Repository;
import cn.dutp.qrcode.domain.DutpDiskBook;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
/**
 * 智典云盘书籍Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Repository
public interface DutpDiskBookMapper extends BaseMapper<DutpDiskBook>
{

    List<DutpDiskBook> getQrcodeRecycleList(DutpDiskRecycleDto dto);

    List<DutpDiskBook> getBookRecycleList(DutpDiskRecycleDto dto);

    Integer restoreBook(List<Long> ids);

    Integer restoreQrcode(List<Long> ids);

    List<DutpDiskBook> getBookRecycleListByIds(List<Long> ids);

    List<DutpDiskBookVO> getStatisticsBookList(DutpDiskStatisticsDto dto);

    List<DutpDiskBookVO> getStatisticsQrCodeList(DutpDiskStatisticsDto dto);

    List<DutpDiskBook> selectDutpDiskBookList(DutpDiskBook dutpDiskBook);
}

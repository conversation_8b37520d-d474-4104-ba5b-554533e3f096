package cn.dutp.qrcode.service;

import java.util.List;
import cn.dutp.qrcode.domain.DutpDiskCatalog;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 智典云盘文件夹Service接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
public interface IDutpDiskCatalogService extends IService<DutpDiskCatalog>
{
    /**
     * 查询智典云盘文件夹
     *
     * @param catalogId 智典云盘文件夹主键
     * @return 智典云盘文件夹
     */
    public DutpDiskCatalog selectDutpDiskCatalogByCatalogId(Long catalogId);

    /**
     * 查询智典云盘文件夹列表
     *
     * @param dutpDiskCatalog 智典云盘文件夹
     * @return 智典云盘文件夹集合
     */
    public List<DutpDiskCatalog> selectDutpDiskCatalogList(DutpDiskCatalog dutpDiskCatalog);

    /**
     * 新增智典云盘文件夹
     *
     * @param dutpDiskCatalog 智典云盘文件夹
     * @return 结果
     */
    public DutpDiskCatalog insertDutpDiskCatalog(DutpDiskCatalog dutpDiskCatalog);

    /**
     * 修改智典云盘文件夹
     *
     * @param dutpDiskCatalog 智典云盘文件夹
     * @return 结果
     */
    public boolean updateDutpDiskCatalog(DutpDiskCatalog dutpDiskCatalog);

    /**
     * 批量删除智典云盘文件夹
     *
     * @param catalogIds 需要删除的智典云盘文件夹主键集合
     * @return 结果
     */
    public boolean deleteDutpDiskCatalogByCatalogIds(List<Long> catalogIds);

}

package cn.dutp.qrcode.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 二维码模板对象 dutp_disk_qrcode_template
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@TableName("dutp_disk_qrcode_template")
public class DutpDiskQrcodeTemplate extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** $column.columnComment */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long templateId;

    /** 图片地址 */
        @Excel(name = "图片地址")
    private String imageUrl;

    /** 模板名称 */
        @Excel(name = "模板名称")
    private String templateName;

    /** 组件地址 */
        @Excel(name = "组件地址")
    private String componentPath;

    /** 默认值JSON */
        @Excel(name = "默认值JSON")
    private String defaultData;

    /** 删除标志（0代表存在 1在回收站 2代表删除） */
    private String delFlag;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("templateId", getTemplateId())
            .append("imageUrl", getImageUrl())
            .append("templateName", getTemplateName())
            .append("componentPath", getComponentPath())
            .append("defaultData", getDefaultData())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
        .toString();
        }
        }

package cn.dutp.qrcode.service.impl;

import java.awt.*;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.InputStream;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.utils.StringUtils;
import cn.dutp.common.mongo.service.MongoService;
import cn.dutp.common.redis.service.RedisService;
import cn.dutp.qrcode.domain.vo.DutpDiskBookClickVo;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Element;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;

import org.apache.poi.sl.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.ShapeTypes;
import org.apache.poi.util.Units;
import org.apache.poi.wp.usermodel.HeaderFooterType;
import org.apache.poi.xslf.usermodel.*;
import org.apache.poi.xssf.usermodel.*;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFHeader;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.bson.Document;
import org.openxmlformats.schemas.drawingml.x2006.main.CTGraphicalObject;
import org.openxmlformats.schemas.drawingml.x2006.main.CTPositiveSize2D;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import cn.dutp.qrcode.mapper.DutpDiskQrcodeMapper;
import cn.dutp.qrcode.mapper.DutpDiskQrcodeScanLogMapper;
import cn.dutp.qrcode.domain.DutpDiskQrcode;
import cn.dutp.qrcode.domain.DutpDiskQrcodeScanLog;
import cn.dutp.qrcode.service.IDutpDiskQrcodeService;
import cn.dutp.qrcode.utils.WaterMarkUtil;

import org.springframework.web.multipart.MultipartFile;

import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTDocument1;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBody;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTDrawing;
import org.apache.poi.ss.usermodel.ClientAnchor;

import javax.imageio.ImageIO;
import java.awt.AlphaComposite;
import java.awt.Color;
import java.awt.Font;
import java.awt.FontMetrics;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFPicture;
import java.util.Iterator;
import org.apache.poi.ss.usermodel.Row;


/**
 * 智典云盘资源Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Service
public class DutpDiskQrcodeServiceImpl extends ServiceImpl<DutpDiskQrcodeMapper, DutpDiskQrcode> implements IDutpDiskQrcodeService
{
    @Autowired
    private DutpDiskQrcodeMapper dutpDiskQrcodeMapper;

    @Autowired
    private MongoService mongoService;

    private static final String QRCODE_SCAN_COUNT_KEY = "qrcode:scan:count";
    
    @Autowired
    private RedisService redisService;

    @Autowired
    private DutpDiskQrcodeScanLogMapper dutpDiskQrcodeScanLogMapper;

    /**
     * 查询智典云盘资源
     *
     * @param qrcodeId 智典云盘资源主键
     * @return 智典云盘资源
     */
    @Override
    public DutpDiskQrcode selectDutpDiskQrcodeByQrcodeId(Long qrcodeId)
    {
        DutpDiskQrcode diskQrcode = this.getById(qrcodeId);

        Document one = mongoService.findOne("qrcodeContent", Query.query(Criteria.where("qrcodeId").is(diskQrcode.getQrcodeId())));
        if (ObjectUtil.isNotEmpty(one)){
            diskQrcode.setMongoJson(one.getString("json"));
        }

        return diskQrcode;
    }

    /**
     * 查询智典云盘资源列表
     *
     * @param dutpDiskQrcode 智典云盘资源
     * @return 智典云盘资源
     */
    @Override
    public List<DutpDiskQrcode> selectDutpDiskQrcodeList(DutpDiskQrcode dutpDiskQrcode)
    {
        LambdaQueryWrapper<DutpDiskQrcode> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dutpDiskQrcode.getImageUrl())) {
                lambdaQueryWrapper.eq(DutpDiskQrcode::getImageUrl
                ,dutpDiskQrcode.getImageUrl());
            }
                if(ObjectUtil.isNotEmpty(dutpDiskQrcode.getQrcodeName())) {
                lambdaQueryWrapper.like(DutpDiskQrcode::getQrcodeName
                ,dutpDiskQrcode.getQrcodeName());
            }
                if(ObjectUtil.isNotEmpty(dutpDiskQrcode.getQrcodeType())) {
                lambdaQueryWrapper.eq(DutpDiskQrcode::getQrcodeType
                ,dutpDiskQrcode.getQrcodeType());
            }
                if(ObjectUtil.isNotEmpty(dutpDiskQrcode.getCatalogId())) {
                lambdaQueryWrapper.eq(DutpDiskQrcode::getCatalogId
                ,dutpDiskQrcode.getCatalogId());
            }
                if(ObjectUtil.isNotEmpty(dutpDiskQrcode.getJumpUrl())) {
                lambdaQueryWrapper.eq(DutpDiskQrcode::getJumpUrl
                ,dutpDiskQrcode.getJumpUrl());
            }
                if(ObjectUtil.isNotEmpty(dutpDiskQrcode.getMongoObjectId())) {
                lambdaQueryWrapper.eq(DutpDiskQrcode::getMongoObjectId
                ,dutpDiskQrcode.getMongoObjectId());
            }
                if(ObjectUtil.isNotEmpty(dutpDiskQrcode.getBookId())) {
                lambdaQueryWrapper.eq(DutpDiskQrcode::getBookId
                ,dutpDiskQrcode.getBookId());
            }
                if(ObjectUtil.isNotEmpty(dutpDiskQrcode.getState())) {
                lambdaQueryWrapper.eq(DutpDiskQrcode::getState
                ,dutpDiskQrcode.getState());
            }
        if(ObjectUtil.isNotEmpty(dutpDiskQrcode.getQrcodeId())) {
            lambdaQueryWrapper.eq(DutpDiskQrcode::getQrcodeId
                    ,dutpDiskQrcode.getQrcodeId());
        }
        // 排序逻辑
        if (ObjectUtil.isNotEmpty(dutpDiskQrcode.getSortField()) && ObjectUtil.isNotEmpty(dutpDiskQrcode.getSortOrder())) {
            String sortField = dutpDiskQrcode.getSortField();
            String sortOrder = dutpDiskQrcode.getSortOrder();
            // 只允许部分字段排序，防止SQL注入
            if ("updateTime".equals(sortField)) {
                if ("asc".equalsIgnoreCase(sortOrder)) {
                    lambdaQueryWrapper.orderByAsc(DutpDiskQrcode::getUpdateTime);
                } else {
                    lambdaQueryWrapper.orderByDesc(DutpDiskQrcode::getUpdateTime);
                }
            } else if ("qrcodeName".equals(sortField)) {
                if ("asc".equalsIgnoreCase(sortOrder)) {
                    lambdaQueryWrapper.orderByAsc(DutpDiskQrcode::getQrcodeName);
                } else {
                    lambdaQueryWrapper.orderByDesc(DutpDiskQrcode::getQrcodeName);
                }
            }
        } else {
            // 默认按更新时间倒序
            lambdaQueryWrapper.orderByDesc(DutpDiskQrcode::getUpdateTime);
        }



        //如果有二维码名称当入参的时候，外连接一下book表把教材名称和isbn拉出来


        List<DutpDiskQrcode> qrcodeList = this.list(lambdaQueryWrapper);

        // 获取点击统计数据
        getTotalCountMap(qrcodeList);
        
        return qrcodeList;
    }

    private void getTotalCountMap(List<DutpDiskQrcode> list) {
        // 先从缓存获取
        Map<Long, DutpDiskBookClickVo> cacheMap = redisService.getCacheObject(QRCODE_SCAN_COUNT_KEY);
        
        // 记录未从缓存获取到的qrcodeId
        List<Long> remainingQrcodeIds = new ArrayList<>();
        
        // 处理缓存中有的数据
        if (cacheMap != null && !cacheMap.isEmpty()) {
            list.forEach(qrcode -> {
                DutpDiskBookClickVo clickVo = cacheMap.get(qrcode.getQrcodeId());
                if (clickVo != null) {
                    qrcode.setQrcodeClickVo(clickVo);
                } else {
                    remainingQrcodeIds.add(qrcode.getQrcodeId()); 
                }
            });
        } else {
            remainingQrcodeIds.addAll(list.stream()
                .map(DutpDiskQrcode::getQrcodeId)
                .collect(Collectors.toList()));
        }

        // 如果所有数据都从缓存获取到了，直接返回
        if (remainingQrcodeIds.isEmpty()) {
            return;
        }
        
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfToday = now.toLocalDate().atStartOfDay();
        LocalDateTime startOfWeek = now.with(DayOfWeek.MONDAY).toLocalDate().atStartOfDay();
        
        // 查询剩余二维码的今日、本周和总扫描数
        Map<Long, Integer> todayCountMap = dutpDiskQrcodeScanLogMapper.selectList(
            new LambdaQueryWrapper<DutpDiskQrcodeScanLog>()
                .in(DutpDiskQrcodeScanLog::getQrcodeId, remainingQrcodeIds)
                .ge(DutpDiskQrcodeScanLog::getCreateTime, startOfToday)
        ).stream().collect(
            Collectors.groupingBy(
                DutpDiskQrcodeScanLog::getQrcodeId,
                Collectors.collectingAndThen(Collectors.counting(), Long::intValue)
            )
        );
        
        Map<Long, Integer> weekCountMap = dutpDiskQrcodeScanLogMapper.selectList(
            new LambdaQueryWrapper<DutpDiskQrcodeScanLog>()
                .in(DutpDiskQrcodeScanLog::getQrcodeId, remainingQrcodeIds)
                .ge(DutpDiskQrcodeScanLog::getCreateTime, startOfWeek)
        ).stream().collect(
            Collectors.groupingBy(
                DutpDiskQrcodeScanLog::getQrcodeId,
                Collectors.collectingAndThen(Collectors.counting(), Long::intValue)
            )
        );
        
        Map<Long, Integer> totalCountMap = dutpDiskQrcodeScanLogMapper.selectList(
            new LambdaQueryWrapper<DutpDiskQrcodeScanLog>()
                .in(DutpDiskQrcodeScanLog::getQrcodeId, remainingQrcodeIds)
        ).stream().collect(
            Collectors.groupingBy(
                DutpDiskQrcodeScanLog::getQrcodeId,
                Collectors.collectingAndThen(Collectors.counting(), Long::intValue)
            )
        );
        
        // 将数据库查询的扫描数设置到对应的二维码对象中
        list.forEach(qrcode -> {
            if (remainingQrcodeIds.contains(qrcode.getQrcodeId())) {
                DutpDiskBookClickVo clickVo = new DutpDiskBookClickVo();
                clickVo.setToday(todayCountMap.getOrDefault(qrcode.getQrcodeId(), 0));
                clickVo.setWeek(weekCountMap.getOrDefault(qrcode.getQrcodeId(), 0));
                clickVo.setTotal(totalCountMap.getOrDefault(qrcode.getQrcodeId(), 0));
                qrcode.setQrcodeClickVo(clickVo);
            }
        });
    }

    /**
     * 新增智典云盘资源
     *
     * @param dutpDiskQrcode 智典云盘资源
     * @return 结果
     */
    @Override
    public DutpDiskQrcode insertDutpDiskQrcode(DutpDiskQrcode dutpDiskQrcode)
    {
        judgeSate(dutpDiskQrcode);

         this.save(dutpDiskQrcode);
        saveMongo(dutpDiskQrcode);

        return dutpDiskQrcode;
    }

    private void saveMongo(DutpDiskQrcode dutpDiskQrcode) {
        Document document = new Document();
        document.put("qrcodeId", dutpDiskQrcode.getQrcodeId());
        document.put("json", dutpDiskQrcode.getMongoJson());

        //如果mogno中不存在，则插入，如果存在，则更新
        if (ObjectUtil.isEmpty(mongoService.findOne("qrcodeContent", Query.query(Criteria.where("qrcodeId").is(dutpDiskQrcode.getQrcodeId()))))) {
            mongoService.insertOne("qrcodeContent",document);
        }else {
            Update mongoUpdate = Update.update("json", dutpDiskQrcode.getMongoJson());
            mongoService.updateOne("qrcodeContent", Query.query(Criteria.where("qrcodeId").is(dutpDiskQrcode.getQrcodeId())), mongoUpdate);
        }
    }

    private static void judgeSate(DutpDiskQrcode dutpDiskQrcode) {
        if (ObjectUtil.isEmpty(dutpDiskQrcode.getMongoJson())){

            int stateWithContent = 1;
            dutpDiskQrcode.setState(stateWithContent);

    }else {
            int stateWithoutContent = 2;
            dutpDiskQrcode.setState(stateWithoutContent);
        }
    }

    /**
     * 修改智典云盘资源
     *
     * @param dutpDiskQrcode 智典云盘资源
     * @return 结果
     */
    @Override
    public boolean updateDutpDiskQrcode(DutpDiskQrcode dutpDiskQrcode)

    {
        judgeSate(dutpDiskQrcode);
        boolean update = this.updateById(dutpDiskQrcode);
        saveMongo(dutpDiskQrcode);
        return update;
    }

    /**
     * 批量删除智典云盘资源
     *
     * @param qrcodeIds 需要删除的智典云盘资源主键
     * @return 结果
     */
    @Override
    public boolean deleteDutpDiskQrcodeByQrcodeIds(List<Long> qrcodeIds)
    {
        return this.removeByIds(qrcodeIds);
    }

    @Override
    public String importQrcode(List<DutpDiskQrcode> qrcodeList,  String operName) {
        if (ObjectUtil.isEmpty(qrcodeList)) {
            return "数据不能为空";
        }
        int successNum = 0;
        int failureNum = 0;


        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (DutpDiskQrcode qrcode : qrcodeList) {
            try {
                if (StringUtils.isNotBlank(qrcode.getQrcodeName())) {
                    insertDutpDiskQrcode(qrcode);
                    successNum++;
                }else {
                    failureNum++;
                    String msg = "名称不能为空";
                    failureMsg.append(msg).append("<br/>");
                }
            }catch (Exception e){
                failureNum++;
                String msg = "数据格式错误";
            }

        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    @Override
    public List<DutpDiskQrcode> selectDutpDiskQrcodeListForDownload(List<Long> qrcodeIds,List<Long> bookIds) {
        //根据id获取带图片的数据
        // 分别连俩表查bookName 和 catalogName
        List<DutpDiskQrcode> qrcodeList = dutpDiskQrcodeMapper.selectDutpDiskQrcodeListForDownload(qrcodeIds,bookIds,null, null);
        //过滤掉没有图片的数据
        qrcodeList = qrcodeList.stream().filter(qrcode -> qrcode.getImageUrl() != null).collect(Collectors.toList());

        return qrcodeList;
    }


    /**
     * 根据二维码ID和图书ID选择DutpDiskQrcode对象用于下载
     * 此方法专注于从MongoDB中选择符合条件的二维码数据，并将其与文档中的内容合并
     *
     * @param qrcodeIds 二维码ID列表
     * @param bookIds 图书ID列表
     * @return 返回合并了MongoDB内容的DutpDiskQrcode对象列表
     */
    @Override
    public List<DutpDiskQrcode> selectDutpDiskQrcodeMongoForDownload(List<Long> qrcodeIds, List<Long> bookIds) {

        //此种取资源码
        String qrcodeType = "1";

        //此种取非空
        String state = "2";

        List<DutpDiskQrcode> qrcodeList = dutpDiskQrcodeMapper.selectDutpDiskQrcodeListForDownload(qrcodeIds,bookIds,qrcodeType,state);
        //过滤掉没有图片的数据
        qrcodeList = qrcodeList.stream().filter(qrcode -> qrcode.getImageUrl() != null).collect(Collectors.toList());

        //然后去mongo里面取回对应id的资源
        List<Long> mongoObjectIds = qrcodeList.stream().map(DutpDiskQrcode::getQrcodeId).collect(Collectors.toList());
        List<Document> documents = mongoService.findByIds("qrcodeContent", Query.query(Criteria.where("qrcodeId").in(mongoObjectIds)));


        List<DutpDiskQrcode> finalQrcodeList = qrcodeList;
        documents.forEach(document -> {
            finalQrcodeList.stream().filter(qrcode -> qrcode.getQrcodeId().equals(document.get("qrcodeId"))).forEach(qrcode -> {
                qrcode.setMongoJson(document.get("json").toString());
            });
        });

        return finalQrcodeList;
    }

    @Override
    public List<DutpDiskQrcode> selectDutpDiskQrcodeSearchList(DutpDiskQrcode dutpDiskQrcode) {
        // 直接调用mapper查询,关联book表获取书名和ISBN
        return dutpDiskQrcodeMapper.selectDutpDiskQrcodeSearchList(dutpDiskQrcode);
    }

    @Override
    public ByteArrayOutputStream addWatermarkToFile(MultipartFile file,String watermarkText) {
        if (file == null || file.isEmpty()) {
            throw new ServiceException("文件不能为空");
        }
        
        // 如果水印文本为空，使用默认值
        if (StringUtils.isEmpty(watermarkText)) {
            watermarkText = "大连理工大学出版社";
        }
        
        try {
            String originalFilename = file.getOriginalFilename();
            if (StringUtils.isEmpty(originalFilename)) {
                throw new ServiceException("文件名不能为空");
            }
            
            String fileExtension = originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toLowerCase();
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            
            switch (fileExtension) {
                case "doc":
                case "docx":
                    addWatermarkToWord(file.getInputStream(), outputStream, watermarkText);
                    break;
                case "xls":
                case "xlsx":
                    addWatermarkToExcel(file.getInputStream(), outputStream, watermarkText);
                    break;
                case "ppt":
                case "pptx":
                    addWatermarkToPPT(file.getInputStream(), outputStream, watermarkText);
                    break;
                case "pdf":
                    addWatermarkToPDF(file.getInputStream(), outputStream, watermarkText);
                    break;
                default:
                    throw new ServiceException("不支持的文件类型: " + fileExtension);
            }
            
            return outputStream;
        } catch (Exception e) {
            throw new ServiceException("添加水印失败: " + e.getMessage());
        }
    }
    
    /**
     * 为Word文档添加水印
     */
    private void addWatermarkToWord(InputStream inputStream, ByteArrayOutputStream outputStream, String watermarkText) throws Exception {
        // 加载Word文档
        XWPFDocument document = new XWPFDocument(inputStream);
        
        // 设置水印参数
        String fontColor = "#D3D3D3"; // 浅灰色
        String fontSize = "36pt";     // 字体大小
        String styleRotation = "315"; // 旋转角度（315度=逆时针45度）
        
        // 使用WaterMarkUtil工具类添加平铺水印
        WaterMarkUtil.makeFullWaterMarkByWordArt(document, watermarkText, fontColor, fontSize, styleRotation);
        
        // 输出文档
        document.write(outputStream);
        document.close();
    }
    
    /**
     * 为Excel文档添加水印
     */
    private void addWatermarkToExcel(InputStream inputStream, ByteArrayOutputStream outputStream, String watermarkText) throws Exception {
        XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
        
        // 创建水印图片
        byte[] watermarkImageBytes = createWatermarkImage(watermarkText);
        int pictureIdx = workbook.addPicture(watermarkImageBytes, Workbook.PICTURE_TYPE_PNG);
        
        // 遍历所有工作表
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            XSSFSheet sheet = workbook.getSheetAt(i);
            
            // 计算实际内容范围
            int maxRow = calculateMaxRow(sheet);
            int maxCol = calculateMaxColumn(sheet);
            
            // 确保最小范围，但不要超出太多
            int rowCount = Math.max(10, maxRow + 5);
            int colCount = Math.max(10, maxCol + 3);
            
            // 创建绘图对象
            XSSFDrawing drawing = sheet.createDrawingPatriarch();
            
            // 水印间距和大小
            int rowStep = 10;
            int colStep = 5;
            int watermarkWidth = 5;
            int watermarkHeight = 3;
            
            // 在内容区域上平铺水印
            for (int row = 0; row < rowCount; row += rowStep) {
                for (int col = 0; col < colCount; col += colStep) {
                    // 创建水印锚点
                    XSSFClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, 
                            col, row, col + watermarkWidth, row + watermarkHeight);
                    
                    // 设置锚点位置为底层
                    anchor.setAnchorType(ClientAnchor.AnchorType.MOVE_AND_RESIZE);
                    
                    // 添加图片
                    XSSFPicture picture = drawing.createPicture(anchor, pictureIdx);
                    
                    // 设置图片透明度和位置
                    picture.resize();
                }
            }
        }
        
        workbook.write(outputStream);
        workbook.close();
    }
    
    /**
     * 计算工作表的最大行数
     */
    private int calculateMaxRow(XSSFSheet sheet) {
        int maxRow = 0;
        
        // 遍历所有行
        Iterator<Row> rowIterator = sheet.rowIterator();
        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            if (row.getLastCellNum() > 0) { // 只考虑有内容的行
                maxRow = Math.max(maxRow, row.getRowNum());
            }
        }
        
        return maxRow;
    }
    
    /**
     * 计算工作表的最大列数
     */
    private int calculateMaxColumn(XSSFSheet sheet) {
        int maxCol = 0;
        
        // 遍历所有行
        Iterator<Row> rowIterator = sheet.rowIterator();
        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            if (row.getLastCellNum() > 0) {
                maxCol = Math.max(maxCol, row.getLastCellNum());
            }
        }
        
        return maxCol;
    }
    
    /**
     * 创建水印图片
     */
    private byte[] createWatermarkImage(String watermarkText) throws Exception {
        // 创建图片
        int width = 400;
        int height = 200;
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = image.createGraphics();
        
        // 设置透明背景
        g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 0.2f));
        
        // 设置抗锯齿
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        
        // 设置字体
        Font font = new Font("宋体", Font.BOLD, 36);
        g2d.setFont(font);
        g2d.setColor(Color.GRAY);
        
        // 获取文本尺寸
        FontMetrics fm = g2d.getFontMetrics();
        int textWidth = fm.stringWidth(watermarkText);
        int textHeight = fm.getHeight();
        
        // 旋转45度
        g2d.rotate(Math.toRadians(315), width / 2, height / 2);
        
        // 绘制文本
        int x = (width - textWidth) / 2;
        int y = (height + textHeight / 2) / 2;
        g2d.drawString(watermarkText, x, y);
        
        g2d.dispose();
        
        // 将图片转换为字节数组
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "png", baos);
        byte[] imageBytes = baos.toByteArray();
        baos.close();
        
        return imageBytes;
    }
    
    /**
     * 为PPT文档添加水印
     */
    private void addWatermarkToPPT(InputStream inputStream, ByteArrayOutputStream outputStream, String watermarkText) throws Exception {
        XMLSlideShow ppt = new XMLSlideShow(inputStream);
        
        // 获取幻灯片尺寸
        java.awt.Dimension pgsize = ppt.getPageSize();
        
        // 获取所有幻灯片
        for (XSLFSlide slide : ppt.getSlides()) {
            // 创建文本框
            XSLFTextBox textBox = slide.createTextBox();
            
            // 设置水印位置
            textBox.setAnchor(new Rectangle2D.Double(pgsize.getWidth() / 2 - 200, pgsize.getHeight() / 2 - 100, 400, 200));
            
            // 添加水印文本
            XSLFTextParagraph paragraph = textBox.addNewTextParagraph();
            XSLFTextRun textRun = paragraph.addNewTextRun();
            textRun.setText(watermarkText);
            
            // 设置水印样式
            textRun.setFontSize(72.0);
            
            // 设置字体颜色为浅灰色
            try {
                textRun.setFontColor(new Color(211, 211, 211, 128)); // 浅灰色半透明
            } catch (Exception e) {
                // 如果不支持Alpha通道，使用不透明的浅灰色
                textRun.setFontColor(new Color(211, 211, 211));
            }
            
            // 设置水印旋转 - 使用可用的方法
            try {
                textBox.setRotation(45.0); // 逆时针旋转45度
            } catch (Exception e) {
                // 如果方法不可用，尝试替代方法
                try {
                    java.lang.reflect.Method setRotation = textBox.getClass().getMethod("setRotation", double.class);
                    setRotation.invoke(textBox, 45.0);
                } catch (Exception ex) {
                    // 忽略，如果无法设置旋转
                }
            }
        }
        
        ppt.write(outputStream);
        ppt.close();
    }
    
    /**
     * 为PDF文档添加水印
     */
    private void addWatermarkToPDF(InputStream inputStream, ByteArrayOutputStream outputStream, String watermarkText) throws Exception {
        PdfReader reader = new PdfReader(inputStream);
        PdfStamper stamper = new PdfStamper(reader, outputStream);
        
        // 获取PDF页数
        int pageCount = reader.getNumberOfPages();
        
        // 创建水印字体
        BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        
        // 遍历所有页面添加水印
        for (int i = 1; i <= pageCount; i++) {
            // 获取页面尺寸
            PdfContentByte content = stamper.getUnderContent(i);
            com.itextpdf.text.Rectangle pageSize = reader.getPageSize(i);
            float width = (float) pageSize.getWidth();
            float height = (float) pageSize.getHeight();
            
            // 开始添加水印
            content.beginText();
            
            // 设置水印字体和颜色
            content.setFontAndSize(baseFont, 120); // 调整字体大小以适应页面中心
            content.setColorFill(new BaseColor(211, 211, 211, 128)); // 浅灰色半透明
            
            // 设置水印旋转和位置
            content.showTextAligned(Element.ALIGN_CENTER, watermarkText, width / 2, height / 2, 45);
            
            // 结束添加水印
            content.endText();
        }
        
        stamper.close();
        reader.close();
    }


}

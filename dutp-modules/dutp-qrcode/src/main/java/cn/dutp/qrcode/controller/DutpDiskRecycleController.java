package cn.dutp.qrcode.controller;

import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.qrcode.domain.DutpDiskBook;
import cn.dutp.qrcode.domain.dto.DutpDiskRecycleDto;
import cn.dutp.qrcode.service.IDutpDiskBookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 智典云盘回收站Controller
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@RestController
@RequestMapping("/recycle")
public class DutpDiskRecycleController extends BaseController {

    @Autowired
    private IDutpDiskBookService dutpDiskBookService;

    /**
     * 查询智典云盘回收站列表
     */
    @GetMapping("/list")
    public TableDataInfo getRecycleList(DutpDiskRecycleDto dto)
    {
        startPage();
        List<DutpDiskBook> list = dutpDiskBookService.getRecycleList(dto);
        return getDataTable(list);
    }

    /**
     * 智典云盘回收站恢复
     */
//    @RequiresPermissions("qrcode:recycle:recycle")
    @PostMapping("/restore")
    public Integer restore(@RequestBody DutpDiskRecycleDto dto){
        return dutpDiskBookService.restore(dto);
    }


}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.qrcode.mapper.DutpDiskQrcodeMapper">
    
    <resultMap type="DutpDiskQrcode" id="DutpDiskQrcodeResult">
        <result property="qrcodeId"    column="qrcode_id"    />
        <result property="imageUrl"    column="image_url"    />
        <result property="qrcodeName"    column="qrcode_name"    />
        <result property="qrcodeType"    column="qrcode_type"    />
        <result property="catalogId"    column="catalog_id"    />
        <result property="jumpUrl"    column="jump_url"    />
        <result property="mongoObjectId"    column="mongo_object_id"    />
        <result property="bookId"    column="book_id"    />
        <result property="state"    column="state"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDutpDiskQrcodeVo">
        select qrcode_id, image_url, qrcode_name, qrcode_type, catalog_id, jump_url, mongo_object_id, book_id, state, del_flag, create_by, create_time, update_by, update_time from dutp_disk_qrcode
    </sql>

    <select id="selectDutpDiskQrcodeList" parameterType="DutpDiskQrcode" resultMap="DutpDiskQrcodeResult">
        <include refid="selectDutpDiskQrcodeVo"/>
        <where>  
            <if test="imageUrl != null  and imageUrl != ''"> and image_url = #{imageUrl}</if>
            <if test="qrcodeName != null  and qrcodeName != ''"> and qrcode_name like concat('%', #{qrcodeName}, '%')</if>
            <if test="qrcodeType != null "> and qrcode_type = #{qrcodeType}</if>
            <if test="catalogId != null "> and catalog_id = #{catalogId}</if>
            <if test="jumpUrl != null  and jumpUrl != ''"> and jump_url = #{jumpUrl}</if>
            <if test="mongoObjectId != null  and mongoObjectId != ''"> and mongo_object_id = #{mongoObjectId}</if>
            <if test="bookId != null "> and book_id = #{bookId}</if>
            <if test="state != null "> and state = #{state}</if>
        </where>
    </select>
    
    <select id="selectDutpDiskQrcodeByQrcodeId" parameterType="Long" resultMap="DutpDiskQrcodeResult">
        <include refid="selectDutpDiskQrcodeVo"/>
        where qrcode_id = #{qrcodeId}
    </select>

    <insert id="insertDutpDiskQrcode" parameterType="DutpDiskQrcode" useGeneratedKeys="true" keyProperty="qrcodeId">
        insert into dutp_disk_qrcode
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="imageUrl != null">image_url,</if>
            <if test="qrcodeName != null and qrcodeName != ''">qrcode_name,</if>
            <if test="qrcodeType != null">qrcode_type,</if>
            <if test="catalogId != null">catalog_id,</if>
            <if test="jumpUrl != null">jump_url,</if>
            <if test="mongoObjectId != null">mongo_object_id,</if>
            <if test="bookId != null">book_id,</if>
            <if test="state != null">state,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="qrcodeName != null and qrcodeName != ''">#{qrcodeName},</if>
            <if test="qrcodeType != null">#{qrcodeType},</if>
            <if test="catalogId != null">#{catalogId},</if>
            <if test="jumpUrl != null">#{jumpUrl},</if>
            <if test="mongoObjectId != null">#{mongoObjectId},</if>
            <if test="bookId != null">#{bookId},</if>
            <if test="state != null">#{state},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDutpDiskQrcode" parameterType="DutpDiskQrcode">
        update dutp_disk_qrcode
        <trim prefix="SET" suffixOverrides=",">
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="qrcodeName != null and qrcodeName != ''">qrcode_name = #{qrcodeName},</if>
            <if test="qrcodeType != null">qrcode_type = #{qrcodeType},</if>
            <if test="catalogId != null">catalog_id = #{catalogId},</if>
            <if test="jumpUrl != null">jump_url = #{jumpUrl},</if>
            <if test="mongoObjectId != null">mongo_object_id = #{mongoObjectId},</if>
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="state != null">state = #{state},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where qrcode_id = #{qrcodeId}
    </update>

    <delete id="deleteDutpDiskQrcodeByQrcodeId" parameterType="Long">
        delete from dutp_disk_qrcode where qrcode_id = #{qrcodeId}
    </delete>

    <delete id="deleteDutpDiskQrcodeByQrcodeIds" parameterType="String">
        delete from dutp_disk_qrcode where qrcode_id in 
        <foreach item="qrcodeId" collection="array" open="(" separator="," close=")">
            #{qrcodeId}
        </foreach>
    </delete>

    <select id="getStatisticsDetailList" parameterType="cn.dutp.qrcode.domain.dto.DutpDiskStatisticsDto" resultType="cn.dutp.qrcode.domain.vo.DutpDiskBookVO">
        select
            ddq.qrcode_id,
            ddq.book_id,
            ddq.qrcode_name,
            ddq.image_url as imageUrl,
            ddb.book_name,
            ddb.isbn,
            dl.create_time
        from
            dutp_disk_qrcode_scan_log dl
        left join
            dutp_disk_qrcode ddq on ddq.qrcode_id = dl.qrcode_id and ddq.book_id = dl.book_id
        left join
            dutp_disk_book ddb on ddb.book_id = dl.book_id
        <where>
            ddb.del_flag = 0 and ddq.del_flag = 0 and ddq.qrcode_type = 1
            <if test="bookName != null and bookName != ''">and ddb.book_name like concat('%', #{bookName}, '%')</if>
            <if test="qrcodeName != null and qrcodeName != ''">and ddq.qrcode_name like concat('%', #{qrcodeName}, '%')</if>
            <choose>
                <when test="startDate != null and endDate != null and startDate == endDate">
                    and DATE(dl.create_time) = #{startDate}
                </when>
                <when test="startDate != null and endDate != null">
                    and DATE(dl.create_time) between #{startDate} and #{endDate}
                </when>
            </choose>
        </where>
    </select>
    <select id="selectDutpDiskQrcodeListForDownload" resultType="cn.dutp.qrcode.domain.DutpDiskQrcode">
        <!-->分别连俩表查bookName 和 catalogName        <-->
select
            ddq.qrcode_id as qrcodeId,
            ddq.book_id as bookId,
            ddq.qrcode_name as qrcodeName,
            ddq.image_url as imageUrl,
            ddq.jump_url as jumpUrl,
            ddq.state,
            ddb.book_name as bookName,
            ddc.catalog_name as catalogName
        from
            dutp_disk_qrcode ddq
        left join
        dutp_disk_book ddb on ddb.book_id = ddq.book_id
        left join
        dutp_disk_catalog ddc on ddc.catalog_id = ddq.catalog_id
        <where>
            ddq.del_flag = 0
            and
            ddc.del_flag = 0
            and
            ddb.del_flag = 0
            <if test="bookIds != null"> and ddq.book_id in
                <foreach item="bookId" collection="bookIds" open="(" separator="," close=")">
                    #{bookId}
                </foreach>
            </if>
        <if test="qrcodeIds != null">and ddq.qrcode_id in
        <foreach item="qrcodeId" collection="qrcodeIds" open="(" separator="," close=")">
            #{qrcodeId}
        </foreach>
        </if>
        <if test="qrcodeType != null">and ddq.qrcode_type = #{qrcodeType}</if>
        <if test="state != null">and ddq.state = #{state}</if>
        </where>
    </select>

    <select id="selectDutpDiskQrcodeSearchList" parameterType="DutpDiskQrcode" resultType="cn.dutp.qrcode.domain.DutpDiskQrcode">
        select 
            ddq.qrcode_id as qrcodeId,
            ddq.qrcode_name as qrcodeName,
            ddq.image_url as imageUrl,
            ddq.jump_url as jumpUrl,
            ddq.qrcode_type as qrcodeType,
            ddq.state,
            ddq.book_id as bookId,
            ddb.book_name as bookName,
            ddb.isbn as isbn,
            ddq.catalog_id as catalogId
        from 
            dutp_disk_qrcode ddq
        left join
            dutp_disk_book ddb on ddb.book_id = ddq.book_id
        <where>
            ddq.del_flag = 0 and ddb.del_flag = 0
            <if test="qrcodeName != null and qrcodeName != ''">
                and ddq.qrcode_name like concat('%', #{qrcodeName}, '%')
            </if>
            <if test="bookId != null">
                and ddq.book_id = #{bookId}
            </if>
            <if test="state != null">
                and ddq.state = #{state}
            </if>
            <if test="qrcodeType != null">
                and ddq.qrcode_type = #{qrcodeType} 
            </if>
        </where>
    </select>

</mapper>
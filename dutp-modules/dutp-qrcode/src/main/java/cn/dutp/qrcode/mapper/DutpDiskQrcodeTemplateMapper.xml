<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.qrcode.mapper.DutpDiskQrcodeTemplateMapper">
    
    <resultMap type="DutpDiskQrcodeTemplate" id="DutpDiskQrcodeTemplateResult">
        <result property="templateId"    column="template_id"    />
        <result property="imageUrl"    column="image_url"    />
        <result property="templateName"    column="template_name"    />
        <result property="componentPath"    column="component_path"    />
        <result property="defaultData"    column="default_data"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDutpDiskQrcodeTemplateVo">
        select template_id, image_url, template_name, component_path, default_data, del_flag, create_by, create_time, update_by, update_time from dutp_disk_qrcode_template
    </sql>

    <select id="selectDutpDiskQrcodeTemplateList" parameterType="DutpDiskQrcodeTemplate" resultMap="DutpDiskQrcodeTemplateResult">
        <include refid="selectDutpDiskQrcodeTemplateVo"/>
        <where>  
            <if test="imageUrl != null  and imageUrl != ''"> and image_url = #{imageUrl}</if>
            <if test="templateName != null  and templateName != ''"> and template_name like concat('%', #{templateName}, '%')</if>
            <if test="componentPath != null  and componentPath != ''"> and component_path = #{componentPath}</if>
            <if test="defaultData != null  and defaultData != ''"> and default_data = #{defaultData}</if>
        </where>
    </select>
    
    <select id="selectDutpDiskQrcodeTemplateByTemplateId" parameterType="Long" resultMap="DutpDiskQrcodeTemplateResult">
        <include refid="selectDutpDiskQrcodeTemplateVo"/>
        where template_id = #{templateId}
    </select>

    <insert id="insertDutpDiskQrcodeTemplate" parameterType="DutpDiskQrcodeTemplate" useGeneratedKeys="true" keyProperty="templateId">
        insert into dutp_disk_qrcode_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="imageUrl != null">image_url,</if>
            <if test="templateName != null">template_name,</if>
            <if test="componentPath != null">component_path,</if>
            <if test="defaultData != null">default_data,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="templateName != null">#{templateName},</if>
            <if test="componentPath != null">#{componentPath},</if>
            <if test="defaultData != null">#{defaultData},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDutpDiskQrcodeTemplate" parameterType="DutpDiskQrcodeTemplate">
        update dutp_disk_qrcode_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="templateName != null">template_name = #{templateName},</if>
            <if test="componentPath != null">component_path = #{componentPath},</if>
            <if test="defaultData != null">default_data = #{defaultData},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where template_id = #{templateId}
    </update>

    <delete id="deleteDutpDiskQrcodeTemplateByTemplateId" parameterType="Long">
        delete from dutp_disk_qrcode_template where template_id = #{templateId}
    </delete>

    <delete id="deleteDutpDiskQrcodeTemplateByTemplateIds" parameterType="String">
        delete from dutp_disk_qrcode_template where template_id in 
        <foreach item="templateId" collection="array" open="(" separator="," close=")">
            #{templateId}
        </foreach>
    </delete>
</mapper>
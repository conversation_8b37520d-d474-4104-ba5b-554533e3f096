package cn.dutp.qrcode;

import cn.dutp.common.security.annotation.EnableCustomConfig;
import cn.dutp.common.security.annotation.EnableDutpFeignClients;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 代码生成
 * 
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableDutpFeignClients
@SpringBootApplication
public class DutpQrcodeApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(DutpQrcodeApplication.class, args);
        System.out.println("智典云盘模块启动成功");
    }
}

package cn.dutp.qrcode.domain.dto;

import cn.dutp.common.core.serialize.LongListToStringSerializer;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.util.List;

@Data
public class DutpDiskRecycleDto extends BaseEntity {

    /**
     * 书籍名称
     */
    private String bookName;

    /**
     * 二维码名称
     */
    private String qrcodeName;

    /**
     * 查询类型basic教材删除 columnInfo二维码删除
     */
    private String activeName;

    /**
     * 待恢复id
     */
    @JsonSerialize(using = LongListToStringSerializer.class)
    List<Long> restoreList;
}

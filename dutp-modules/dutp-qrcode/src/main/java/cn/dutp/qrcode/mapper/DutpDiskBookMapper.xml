<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.qrcode.mapper.DutpDiskBookMapper">

    <resultMap type="DutpDiskBook" id="DutpDiskBookResult">
        <result property="bookId"    column="book_id"    />
        <result property="bookName"    column="book_name"    />
        <result property="author"    column="author"    />
        <result property="isbn"    column="isbn"    />
        <result property="issn"    column="issn"    />
        <result property="version"    column="version"    />
        <result property="coverUrl"    column="cover_url"    />
        <result property="subjectId"    column="subject_id"    />
        <result property="schoolId"    column="school_id"    />
        <result property="hourseId"    column="house_id"    />
        <result property="houseName"    column="house_name"    />
        <result property="bookIntroduce"    column="book_introduce"    />
        <result property="templateId"    column="template_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDutpDiskBookVo">
        select book_id, book_name, author, isbn, issn, version, cover_url, subject_id, school_id, hourse_id, book_introduce, template_id, del_flag, create_by, create_time, update_by, update_time from dutp_disk_book
    </sql>

    <select id="selectDutpDiskBookList" parameterType="DutpDiskBook" resultMap="DutpDiskBookResult">
        select
            b.book_id,
            b.book_name,
            b.author,
            b.isbn,
            b.issn,
            b.version,
            b.cover_url,
            b.subject_id,
            b.school_id,
            b.hourse_id,
            b.book_introduce,
            b.template_id,
            b.del_flag,
            b.create_by,
            b.create_time,
            b.update_by,
            b.update_time,
            h.house_name
        from dutp_disk_book b
        left join dutp_publishing_house h on b.hourse_id = h.house_id
        <where>
            <if test="bookName != null  and bookName != ''"> and b.book_name like concat('%', #{bookName}, '%')</if>
            <if test="author != null  and author != ''"> and b.author = #{author}</if>
            <if test="isbn != null  and isbn != ''"> and b.isbn = #{isbn}</if>
            <if test="issn != null  and issn != ''"> and b.issn = #{issn}</if>
            <if test="version != null  and version != ''"> and b.version = #{version}</if>
            <if test="coverUrl != null  and coverUrl != ''"> and b.cover_url = #{coverUrl}</if>
            <if test="subjectId != null"> and b.subject_id = #{subjectId}</if>
            <if test="schoolId != null"> and b.school_id = #{schoolId}</if>
            <if test="hourseId != null"> and b.hourse_id = #{hourseId}</if>
            <if test="bookIntroduce != null  and bookIntroduce != ''"> and b.book_introduce = #{bookIntroduce}</if>
            <if test="templateId != null"> and b.template_id = #{templateId}</if>
            <if test="delFlag != null  and delFlag != ''"> and b.del_flag = #{delFlag}</if>
            <if test="delFlag == null or delFlag == ''"> and b.del_flag = '0'</if>

            <if test="keyword != null and keyword != ''">
                and (
                    b.book_name like concat('%', #{keyword}, '%')
                    or b.book_id = #{keyword}
                )
            </if>
        </where>
        order by b.create_time desc
    </select>

    <select id="selectDutpDiskBookByBookId" parameterType="Long" resultMap="DutpDiskBookResult">
        <include refid="selectDutpDiskBookVo"/>
        where book_id = #{bookId}
    </select>

    <insert id="insertDutpDiskBook" parameterType="DutpDiskBook" useGeneratedKeys="true" keyProperty="bookId">
        insert into dutp_disk_book
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bookName != null">book_name,</if>
            <if test="author != null">author,</if>
            <if test="isbn != null">isbn,</if>
            <if test="issn != null">issn,</if>
            <if test="version != null">version,</if>
            <if test="coverUrl != null">cover_url,</if>
            <if test="subjectId != null">subject_id,</if>
            <if test="schoolId != null">school_id,</if>
            <if test="hourseId != null">hourse_id,</if>
            <if test="bookIntroduce != null">book_introduce,</if>
            <if test="templateId != null">template_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bookName != null">#{bookName},</if>
            <if test="author != null">#{author},</if>
            <if test="isbn != null">#{isbn},</if>
            <if test="issn != null">#{issn},</if>
            <if test="version != null">#{version},</if>
            <if test="coverUrl != null">#{coverUrl},</if>
            <if test="subjectId != null">#{subjectId},</if>
            <if test="schoolId != null">#{schoolId},</if>
            <if test="hourseId != null">#{hourseId},</if>
            <if test="bookIntroduce != null">#{bookIntroduce},</if>
            <if test="templateId != null">#{templateId},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateDutpDiskBook" parameterType="DutpDiskBook">
        update dutp_disk_book
        <trim prefix="SET" suffixOverrides=",">
            <if test="bookName != null">book_name = #{bookName},</if>
            <if test="author != null">author = #{author},</if>
            <if test="isbn != null">isbn = #{isbn},</if>
            <if test="issn != null">issn = #{issn},</if>
            <if test="version != null">version = #{version},</if>
            <if test="coverUrl != null">cover_url = #{coverUrl},</if>
            <if test="subjectId != null">subject_id = #{subjectId},</if>
            <if test="schoolId != null">school_id = #{schoolId},</if>
            <if test="hourseId != null">hourse_id = #{hourseId},</if>
            <if test="bookIntroduce != null">book_introduce = #{bookIntroduce},</if>
            <if test="templateId != null">template_id = #{templateId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where book_id = #{bookId}
    </update>

    <delete id="deleteDutpDiskBookByBookId" parameterType="Long">
        delete from dutp_disk_book where book_id = #{bookId}
    </delete>

    <delete id="deleteDutpDiskBookByBookIds" parameterType="String">
        delete from dutp_disk_book where book_id in
        <foreach item="bookId" collection="array" open="(" separator="," close=")">
            #{bookId}
        </foreach>
    </delete>

    <select id="getQrcodeRecycleList" parameterType="cn.dutp.qrcode.domain.dto.DutpDiskRecycleDto" resultType="cn.dutp.qrcode.domain.DutpDiskBook">
        select
            ddq.qrcode_name,
            ddb.isbn,
            ddq.book_id,
            ddq.qrcode_id,
            ddq.del_flag as qrcodeDelFlag,
            ddb.book_name,
            ddb.del_flag,
            ddq.update_by,
            ddq.update_time
        from
            dutp_disk_qrcode ddq
        left join
            dutp_disk_book ddb on ddb.book_id = ddq.book_id
        <where>
            ddq.del_flag = 2
            <if test="bookName != null and bookName != ''">and ddb.book_name like concat('%', #{bookName}, '%')</if>
            <if test="qrcodeName != null and qrcodeName != ''">and ddq.qrcode_name like concat('%', #{qrcodeName}, '%')</if>
        </where>

    </select>

    <select id="getBookRecycleList" parameterType="cn.dutp.qrcode.domain.dto.DutpDiskRecycleDto" resultType="cn.dutp.qrcode.domain.DutpDiskBook">
        select
            ddb.book_id,
            ddb.book_name,
            ddb.isbn,
            ddb.del_flag,
            ddb.update_by,
            ddb.update_time
        from
            dutp_disk_book ddb
        <where>
            ddb.del_flag = 2
            <if test="bookName != null and bookName != ''">and ddb.book_name like concat('%', #{bookName}, '%')</if>
        </where>
    </select>

    <update id="restoreBook">
        update
            dutp_disk_book
        set
            del_flag = 0
        <where>
            book_id in
            <foreach collection="list" item="bookId" open="(" separator="," close=")">
                #{bookId}
            </foreach>
        </where>
    </update>

    <update id="restoreQrcode">
        update
            dutp_disk_qrcode
        set
            del_flag = 0
        <where>
            qrcode_id in
            <foreach collection="list" item="qrcodeId" open="(" separator="," close=")">
                #{qrcodeId}
            </foreach>
        </where>
    </update>

    <select id="getBookRecycleListByIds" resultType="cn.dutp.qrcode.domain.DutpDiskBook">
        select
        ddq.qrcode_name,
        ddb.isbn,
        ddq.book_id,
        ddq.qrcode_id,
        ddq.del_flag as qrcodeDelFlag,
        ddb.book_name,
        ddb.del_flag,
        ddq.update_by,
        ddq.update_time
        from
        dutp_disk_qrcode ddq
        left join
        dutp_disk_book ddb on ddb.book_id = ddq.book_id
        <where>
            ddq.del_flag = '2' and ddb.del_flag = '2' and
            qrcode_id in
            <foreach collection="list" item="qrcodeId" open="(" separator="," close=")">
                #{qrcodeId}
            </foreach>
        </where>
    </select>

    <select id="getStatisticsBookList" parameterType="cn.dutp.qrcode.domain.dto.DutpDiskStatisticsDto" resultType="cn.dutp.qrcode.domain.vo.DutpDiskBookVO">
        select
            ddb.book_id,
            ddb.book_name,
            ddb.isbn,
            (select count(0) from dutp_disk_qrcode where book_id = ddb.book_id and qrcode_type = 1 and del_flag = 0) as qrCodeCount,
            (select count(0) from dutp_disk_qrcode_scan_log where book_id = ddb.book_id) as scanningCount
        from
            dutp_disk_book ddb
        left join
            dutp_disk_qrcode_scan_log dl on dl.book_id = ddb.book_id
        <where>
            ddb.del_flag = 0
            <if test="bookName != null and bookName != ''">and ddb.book_name like concat('%', #{bookName}, '%')</if>
            <if test="startDate != null and endDate != null and startDate == endDate">and DATE(dl.create_time) = #{startDate}</if>
            <if test="startDate != null and endDate != null">and DATE(dl.create_time) between #{startDate} and #{endDate}</if>
        </where>
        GROUP BY ddb.book_id order by scanningCount desc
    </select>
    <select id="getStatisticsQrCodeList" parameterType="cn.dutp.qrcode.domain.dto.DutpDiskStatisticsDto" resultType="cn.dutp.qrcode.domain.vo.DutpDiskBookVO">
        select
            ddq.qrcode_id,
            ddq.book_id,
            ddq.qrcode_name,
            ddb.book_name,
            ddb.isbn,
            (select count(0) from dutp_disk_qrcode_scan_log where qrcode_id = ddq.qrcode_id) as scanningCount
        from
            dutp_disk_qrcode ddq
        left join
            dutp_disk_book ddb on ddq.book_id = ddb.book_id
        left join
            dutp_disk_qrcode_scan_log dl on dl.qrcode_id = ddq.qrcode_id
        <where>
            ddb.del_flag = 0 and ddq.del_flag = 0 and ddq.qrcode_type = 1
            <if test="bookName != null and bookName != ''">and ddb.book_name like concat('%', #{bookName}, '%')</if>
            <if test="startDate != null and endDate != null and startDate == endDate">and DATE(dl.create_time) = #{startDate}</if>
            <if test="startDate != null and endDate != null">and DATE(dl.create_time) between #{startDate} and #{endDate}</if>
        </where>
        GROUP BY ddq.qrcode_id order by scanningCount desc
    </select>

</mapper>
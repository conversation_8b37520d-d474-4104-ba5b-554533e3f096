package cn.dutp.qrcode.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.List;

/**
 * 智典云盘文件夹对象 dutp_disk_catalog
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@TableName("dutp_disk_catalog")
public class DutpDiskCatalog extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** $column.columnComment */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long catalogId;

    /** 目录名称 */
        @Excel(name = "目录名称")
    private String catalogName;

    /** 父级ID，0表示是书籍 */
        @Excel(name = "父级ID，0表示是书籍")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    /** 智典云盘书籍ID，不是数字教材bookID */
        @Excel(name = "智典云盘书籍ID，不是数字教材bookID")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    @TableField(exist = false)
    private List<DutpDiskCatalog> children;

    /** 总数量 */
    @TableField(exist = false)
    private int totalCount;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("catalogId", getCatalogId())
            .append("catalogName", getCatalogName())
            .append("parentId", getParentId())
            .append("bookId", getBookId())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
        .toString();
        }
        }

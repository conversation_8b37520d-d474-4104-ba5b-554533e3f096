package cn.dutp.qrcode.service;

import java.io.ByteArrayOutputStream;
import java.util.List;
import cn.dutp.qrcode.domain.DutpDiskQrcode;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

/**
 * 智典云盘资源Service接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
public interface IDutpDiskQrcodeService extends IService<DutpDiskQrcode>
{
    /**
     * 查询智典云盘资源
     *
     * @param qrcodeId 智典云盘资源主键
     * @return 智典云盘资源
     */
    public DutpDiskQrcode selectDutpDiskQrcodeByQrcodeId(Long qrcodeId);

    /**
     * 查询智典云盘资源列表
     *
     * @param dutpDiskQrcode 智典云盘资源
     * @return 智典云盘资源集合
     */
    public List<DutpDiskQrcode> selectDutpDiskQrcodeList(DutpDiskQrcode dutpDiskQrcode);

    /**
     * 新增智典云盘资源
     *
     * @param dutpDiskQrcode 智典云盘资源
     * @return 结果
     */
    public DutpDiskQrcode  insertDutpDiskQrcode(DutpDiskQrcode dutpDiskQrcode);

    /**
     * 修改智典云盘资源
     *
     * @param dutpDiskQrcode 智典云盘资源
     * @return 结果
     */
    public boolean updateDutpDiskQrcode(DutpDiskQrcode dutpDiskQrcode);

    /**
     * 批量删除智典云盘资源
     *
     * @param qrcodeIds 需要删除的智典云盘资源主键集合
     * @return 结果
     */
    public boolean deleteDutpDiskQrcodeByQrcodeIds(List<Long> qrcodeIds);

    /**
     * 导入智典云盘资源信息
     *
     * @param  qrcodeList 智典云盘资源
     * @return 结果
     */
    String importQrcode(List<DutpDiskQrcode> qrcodeList, String operName);

    /**
     * 拉取二维码图片列表
     * @param qrcodeIds 具体的二维码id
     * @param bookIds  或某本书的id
     */
    List<DutpDiskQrcode> selectDutpDiskQrcodeListForDownload(List<Long> qrcodeIds,List<Long> bookIds);


    /**
     * 拉取二维码中mongo里面的资源列表
     * @param qrcodeIds 具体的二维码id
     * @param bookIds  或某本书的id
     */
    List<DutpDiskQrcode> selectDutpDiskQrcodeMongoForDownload(List<Long> qrcodeIds, List<Long> bookIds);

    /**
     * 查询智典云盘资源列表(检索窗口使用)
     */
     List<DutpDiskQrcode> selectDutpDiskQrcodeSearchList(DutpDiskQrcode dutpDiskQrcode);

    ByteArrayOutputStream addWatermarkToFile(MultipartFile file,String watermarkText);

}

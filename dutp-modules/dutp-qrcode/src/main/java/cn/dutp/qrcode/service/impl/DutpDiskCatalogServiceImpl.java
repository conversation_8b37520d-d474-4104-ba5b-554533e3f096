package cn.dutp.qrcode.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.TreeSet;
import java.util.Comparator;
import java.util.stream.Collectors;
import java.util.HashSet;
import java.util.Set;

import cn.dutp.common.core.utils.StringUtils;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.qrcode.mapper.DutpDiskCatalogMapper;
import cn.dutp.qrcode.domain.DutpDiskCatalog;
import cn.dutp.qrcode.service.IDutpDiskCatalogService;

/**
 * 智典云盘文件夹Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Service
public class DutpDiskCatalogServiceImpl extends ServiceImpl<DutpDiskCatalogMapper, DutpDiskCatalog> implements IDutpDiskCatalogService
{
    @Autowired
    private DutpDiskCatalogMapper dutpDiskCatalogMapper;

    /**
     * 查询智典云盘文件夹
     *
     * @param catalogId 智典云盘文件夹主键
     * @return 智典云盘文件夹
     */
    @Override
    public DutpDiskCatalog selectDutpDiskCatalogByCatalogId(Long catalogId)
    {
        return this.getById(catalogId);
    }

    /**
     * 查询智典云盘文件夹列表
     *
     * @param dutpDiskCatalog 智典云盘文件夹
     * @return 智典云盘文件夹
     */
    @Override
    public List<DutpDiskCatalog> selectDutpDiskCatalogList(DutpDiskCatalog dutpDiskCatalog)
    {
        // 查询整本书的所有目录
        LambdaQueryWrapper<DutpDiskCatalog> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(ObjectUtil.isNotEmpty(dutpDiskCatalog.getCatalogName())) {
            lambdaQueryWrapper.like(DutpDiskCatalog::getCatalogName, dutpDiskCatalog.getCatalogName());
        }
        if(ObjectUtil.isNotEmpty(dutpDiskCatalog.getParentId())) {
            lambdaQueryWrapper.eq(DutpDiskCatalog::getParentId, dutpDiskCatalog.getParentId());
        }
        if(ObjectUtil.isNotEmpty(dutpDiskCatalog.getBookId())) {
            lambdaQueryWrapper.eq(DutpDiskCatalog::getBookId, dutpDiskCatalog.getBookId());
        }
        lambdaQueryWrapper.eq(DutpDiskCatalog::getDelFlag,0);
        List<DutpDiskCatalog> list = this.list(lambdaQueryWrapper);

        // 处理查询条件, 根据名称查询父级学科
        if (StringUtils.isNotBlank(dutpDiskCatalog.getCatalogName())) {
            // 查询整本书的所有目录
            LambdaQueryWrapper<DutpDiskCatalog> allQueryWrapper = new LambdaQueryWrapper<>();
            allQueryWrapper.eq(DutpDiskCatalog::getBookId, dutpDiskCatalog.getBookId())
                          .eq(DutpDiskCatalog::getDelFlag, 0);
            List<DutpDiskCatalog> allList = this.list(allQueryWrapper);
            
            // 获取所有匹配目录的父级路径
            Set<DutpDiskCatalog> resultSet = new HashSet<>(list);
            for (DutpDiskCatalog catalog : list) {
                addParentPath(catalog, allList, resultSet);
            }
            
            list = new ArrayList<>(resultSet);
        }

        // 去重处理
        list = list.stream()
            .collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> 
                    new TreeSet<>(Comparator.comparing(DutpDiskCatalog::getCatalogId))),
                ArrayList::new));

        return buildTree(list);
    }

    private List<DutpDiskCatalog> getTypeByIds(ArrayList<Long> ids) {
        LambdaQueryWrapper<DutpDiskCatalog> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(DutpDiskCatalog::getCatalogId, ids);
        return this.list(lambdaQueryWrapper);
    }


    private Collection<? extends DutpDiskCatalog> getChildrenTypes(DutpDiskCatalog subject) {
        QueryWrapper<DutpDiskCatalog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parent_id", subject.getCatalogId());
        return dutpDiskCatalogMapper.selectList(queryWrapper);
    }



    private List<DutpDiskCatalog> buildTree(List<DutpDiskCatalog> list) {

        List<DutpDiskCatalog> treeList = new ArrayList<>();

        for (DutpDiskCatalog subject : list) {
            subject.setChildren(new ArrayList<>());
        }

        //顶级根节点
        for (DutpDiskCatalog diskCatalog : list) {
            if (diskCatalog.getParentId() == null||diskCatalog.getParentId()==0){
                treeList.add(diskCatalog);
            }
        }

        //子类加入
        for (DutpDiskCatalog diskCatalog : list) {
            if (diskCatalog.getParentId() != null&&diskCatalog.getParentId()!=0){
                // 找到父节点
                for (DutpDiskCatalog treeSubject : list) {
                    if (treeSubject.getCatalogId().equals(diskCatalog.getParentId())) {
                        treeSubject.getChildren().add(diskCatalog);
                    }
                }
            }
        }

        return treeList;
    }

    // 新增方法：递归添加父级路径
    private void addParentPath(DutpDiskCatalog catalog, List<DutpDiskCatalog> allList, Set<DutpDiskCatalog> resultSet) {
        if (catalog.getParentId() != null && catalog.getParentId() != 0) {
            for (DutpDiskCatalog parent : allList) {
                if (parent.getCatalogId().equals(catalog.getParentId())) {
                    resultSet.add(parent);
                    addParentPath(parent, allList, resultSet); // 递归查找父级
                    break;
                }
            }
        }
    }

    /**
     * 新增智典云盘文件夹
     *
     * @param dutpDiskCatalog 智典云盘文件夹
     * @return 结果
     */
    @Override
    public DutpDiskCatalog insertDutpDiskCatalog(DutpDiskCatalog dutpDiskCatalog)
    {
        // 检查同一父级下是否有重名
        LambdaQueryWrapper<DutpDiskCatalog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DutpDiskCatalog::getParentId, dutpDiskCatalog.getParentId())
                   .eq(DutpDiskCatalog::getCatalogName, dutpDiskCatalog.getCatalogName());
        
        long count = this.count(queryWrapper);
        if (count > 0) {
            throw new RuntimeException("同一目录下已存在相同名称的文件夹");
        }

        this.save(dutpDiskCatalog);
        return dutpDiskCatalog;
    }

    /**
     * 修改智典云盘文件夹
     *
     * @param dutpDiskCatalog 智典云盘文件夹
     * @return 结果
     */
    @Override
    public boolean updateDutpDiskCatalog(DutpDiskCatalog dutpDiskCatalog)
    {
        return this.updateById(dutpDiskCatalog);
    }

    /**
     * 批量删除智典云盘文件夹
     *
     * @param catalogIds 需要删除的智典云盘文件夹主键
     * @return 结果
     */
    @Override
    public boolean deleteDutpDiskCatalogByCatalogIds(List<Long> catalogIds)
    {
        return this.removeByIds(catalogIds);
    }

}

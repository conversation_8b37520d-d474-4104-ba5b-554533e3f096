package cn.dutp.edu.domain.vo;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

@Data
public class DtbBookUserExport {

    /**
     * 用户编号，学生为学生编号，教师为教师员工号
     */
    @Excel(name = "学号/工号",sort = 1)
    private String userNo;

    /**
     * 用户昵称
     */
    @Excel(name = "用户姓名",sort = 2)
    private String realName;

    /**
     * 用户类型
     */
    @Excel(name = "用户类型",readConverterExp = "0 = 读者,1=学生,2=教师",sort = 3)
    private String userType;

    /**
     * 学院名称
     */
    @Excel(name = "学院名称",sort = 4)
    private String academyName;

    /**
     * 专业
     */
    @Excel(name = "专业名称",sort = 5)
    private String specialityName;

    /**
     * 手机号码
     */
    @Excel(name = "电话",sort = 6)
    private String phonenumber;


}

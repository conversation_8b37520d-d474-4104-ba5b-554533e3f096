package cn.dutp.edu.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import cn.dutp.edu.domain.MoocSmartCourseExtensionRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
/**
 * 互动课堂的拓展内容学生记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Repository
public interface MoocSmartCourseExtensionRecordMapper extends BaseMapper<MoocSmartCourseExtensionRecord>
{
    List<MoocSmartCourseExtensionRecord> selectRecords(@Param("extensionId") Long extensionId);
}

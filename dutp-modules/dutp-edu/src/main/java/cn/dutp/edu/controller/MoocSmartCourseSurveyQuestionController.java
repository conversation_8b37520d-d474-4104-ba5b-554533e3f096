package cn.dutp.edu.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.edu.domain.MoocSmartCourseSurveyQuestion;
import cn.dutp.edu.service.IMoocSmartCourseSurveyQuestionService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 互动课堂的问卷调查问题Controller
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@RestController
@RequestMapping("/surveyQuestion")
public class MoocSmartCourseSurveyQuestionController extends BaseController
{
    @Autowired
    private IMoocSmartCourseSurveyQuestionService moocSmartCourseSurveyQuestionService;

    /**
     * 查询互动课堂的问卷调查问题列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocSmartCourseSurveyQuestion moocSmartCourseSurveyQuestion)
    {
        startPage();
        List<MoocSmartCourseSurveyQuestion> list = moocSmartCourseSurveyQuestionService.selectMoocSmartCourseSurveyQuestionList(moocSmartCourseSurveyQuestion);
        return getDataTable(list);
    }

    /**
     * 导出互动课堂的问卷调查问题列表
     */
    @RequiresPermissions("edu:question:export")
    @Log(title = "导出互动课堂的问卷调查问题", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocSmartCourseSurveyQuestion moocSmartCourseSurveyQuestion)
    {
        List<MoocSmartCourseSurveyQuestion> list = moocSmartCourseSurveyQuestionService.selectMoocSmartCourseSurveyQuestionList(moocSmartCourseSurveyQuestion);
        ExcelUtil<MoocSmartCourseSurveyQuestion> util = new ExcelUtil<MoocSmartCourseSurveyQuestion>(MoocSmartCourseSurveyQuestion.class);
        util.exportExcel(response, list, "互动课堂的问卷调查问题数据");
    }

    /**
     * 获取互动课堂的问卷调查问题详细信息
     */
    @RequiresPermissions("edu:question:query")
    @GetMapping(value = "/{questionId}")
    public AjaxResult getInfo(@PathVariable("questionId") Long questionId)
    {
        return success(moocSmartCourseSurveyQuestionService.selectMoocSmartCourseSurveyQuestionByQuestionId(questionId));
    }

    /**
     * 新增互动课堂的问卷调查问题
     */
    @RequiresPermissions("edu:question:add")
    @Log(title = "新增互动课堂的问卷调查问题", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocSmartCourseSurveyQuestion moocSmartCourseSurveyQuestion)
    {
        return toAjax(moocSmartCourseSurveyQuestionService.insertMoocSmartCourseSurveyQuestion(moocSmartCourseSurveyQuestion));
    }

    /**
     * 修改互动课堂的问卷调查问题
     */
    @RequiresPermissions("edu:question:edit")
    @Log(title = "修改互动课堂的问卷调查问题", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocSmartCourseSurveyQuestion moocSmartCourseSurveyQuestion)
    {
        return toAjax(moocSmartCourseSurveyQuestionService.updateMoocSmartCourseSurveyQuestion(moocSmartCourseSurveyQuestion));
    }

    /**
     * 删除互动课堂的问卷调查问题
     */
    @RequiresPermissions("edu:question:remove")
    @Log(title = "删除互动课堂的问卷调查问题", businessType = BusinessType.DELETE)
    @DeleteMapping("/{questionIds}")
    public AjaxResult remove(@PathVariable Long[] questionIds)
    {
        return toAjax(moocSmartCourseSurveyQuestionService.deleteMoocSmartCourseSurveyQuestionByQuestionIds(Arrays.asList(questionIds)));
    }
}

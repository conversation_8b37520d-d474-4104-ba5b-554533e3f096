package cn.dutp.edu.service;

import java.util.List;
import cn.dutp.edu.domain.MoocSmartCourseHomeworkSubmission;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 学生作业提交记录Service接口
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface IMoocSmartCourseHomeworkSubmissionService extends IService<MoocSmartCourseHomeworkSubmission>
{
    /**
     * 查询学生作业提交记录
     *
     * @param submissionId 学生作业提交记录主键
     * @return 学生作业提交记录
     */
    public MoocSmartCourseHomeworkSubmission selectMoocSmartCourseHomeworkSubmissionBySubmissionId(Long submissionId);

    /**
     * 根据作业ID和学生ID查询学生作业提交记录（包含提交的文件）
     *
     * @param assignmentId 作业ID
     * @param studentId 学生ID
     * @return 学生作业提交记录
     */
    public MoocSmartCourseHomeworkSubmission selectMoocSmartCourseHomeworkSubmissionByAssignmentIdAndStudentId(Long assignmentId, Long studentId);

    /**
     * 查询学生作业提交记录列表
     *
     * @param moocSmartCourseHomeworkSubmission 学生作业提交记录
     * @return 学生作业提交记录集合
     */
    public List<MoocSmartCourseHomeworkSubmission> selectMoocSmartCourseHomeworkSubmissionList(MoocSmartCourseHomeworkSubmission moocSmartCourseHomeworkSubmission);

    /**
     * 新增学生作业提交记录（包含提交的文件）
     *
     * @param moocSmartCourseHomeworkSubmission 学生作业提交记录
     * @return 结果
     */
    public boolean insertMoocSmartCourseHomeworkSubmission(MoocSmartCourseHomeworkSubmission moocSmartCourseHomeworkSubmission);

    /**
     * 修改学生作业提交记录（包含提交的文件）
     *
     * @param moocSmartCourseHomeworkSubmission 学生作业提交记录
     * @return 结果
     */
    public boolean updateMoocSmartCourseHomeworkSubmission(MoocSmartCourseHomeworkSubmission moocSmartCourseHomeworkSubmission);

    /**
     * 批量删除学生作业提交记录
     *
     * @param submissionIds 需要删除的学生作业提交记录主键集合
     * @return 结果
     */
    public boolean deleteMoocSmartCourseHomeworkSubmissionBySubmissionIds(List<Long> submissionIds);

}

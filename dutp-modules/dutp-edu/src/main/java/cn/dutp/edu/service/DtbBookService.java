package cn.dutp.edu.service;

import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.domain.DtbBook;
import cn.dutp.domain.DutpSubject;
import cn.dutp.edu.domain.dto.BookDto;
import cn.dutp.edu.domain.dto.DtbUserBookDto;
import cn.dutp.edu.domain.vo.*;
import cn.dutp.edu.domian.DutpUser;
import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【dtb_book(DUTP-DTB_002数字教材)】的数据库操作Service
 * @createDate 2025-01-14 09:22:08
 */
public interface DtbBookService extends IService<DtbBook> {
    /**
     * 查询学科信息列表(树形结构)
     * 教务端教材管理用
     */
    List<Tree<String>> selectSubjectList(DutpSubject dutpSubject);

    List<BookVo> selectEducationalBookList(BookDto dto);

    List<BookVo> selectBookListForSchool(BookDto dto);

    BookVo getBookDetail(BookDto dto);

    /**
     * 获取教材数据总览
     */
    BookDataOverviewVo dataOverview(BookDataOverviewVo dto);


    /**
     * 导出数据总览
     */
    void exportDataOverview(HttpServletResponse response, BookDto dto);

    /**
     * 导出应用数据
     */
    void exportAppData(HttpServletResponse response, BookDto dto);

    DtbBook getRemoteBookDetail(String bookId);

    List<BookDataOverviewVo> getChapterDataList(BookDataOverviewVo dto);

    List<DtbBookChapter> chapterListForSelect(DtbBookChapter dto);


    DtbBookApplicationUserDataVo selectBookApplicationUserData(DtbBookApplicationUserDataVo dto);


    List<BookVo> selectBookList(BookDto dto);

    AjaxResult importBookUser(BookDto dto);

    void removeBookUser(BookDto dto);

    List<DutpUser> exportBookUser(BookDto dto);

    List<DutpUser> selectBookUserList(BookDto dto);

    List<DutpEduOrderVo> getOrderBySchoolList(BookDto dto);

    DutpEduOrderDetailVo getOrderDetailBySchool(BookDto dto);

    AjaxResult addBookUser(DtbUserBookDto dto);

    void downLoadFile(HttpServletResponse response, BookDto dto) throws IOException;

    void downloadErrorList(List<DtbBookUserExport> errorUserBookList, HttpServletResponse response);

    List<Tree<String>> selectChapterByBookId(DtbBookApplicationUserDataVo dto);

    Integer pushBook(BookDto dto);

    TableDataInfo selectEduBookList(BookDto dto);
}


package cn.dutp.edu.service.impl;

import cn.dutp.api.common.constant.DutpConstant;
import cn.dutp.common.redis.service.RedisService;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.edu.domain.MoocSmartCourseClass;
import cn.dutp.edu.domain.MoocSmartCourseClassMember;
import cn.dutp.edu.domain.dto.MoocSmartCourseClassActivityDto;
import cn.dutp.edu.domain.vo.MoocSmartCourseClassActivityVo;
import cn.dutp.edu.mapper.MoocSmartCourseBrainstormMapper;
import cn.dutp.edu.mapper.MoocSmartCourseClassMapper;
import cn.dutp.edu.service.*;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 互动课堂班级Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class MoocSmartCourseClassServiceImpl extends ServiceImpl<MoocSmartCourseClassMapper, MoocSmartCourseClass> implements IMoocSmartCourseClassService
{
    @Autowired
    public RedisService redisService;

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    @Autowired
    private MoocSmartCourseClassMapper moocSmartCourseClassMapper;

    @Autowired
    private IMoocSmartCourseClassAttendanceService moocSmartCourseClassAttendanceService;

    @Autowired
    private IMoocSmartCourseLessonService moocSmartCourseLessonService;

    @Autowired
    private IMoocSmartCourseClassMemberService moocSmartCourseClassMemberService;

    @Autowired
    private IMoocSmartCourseClassQuestionService moocSmartCourseClassQuestionService;

    @Autowired
    private IMoocSmartCourseBrainstormService moocSmartCourseBrainstormService;

    @Autowired
    private MoocSmartCourseBrainstormMapper moocSmartCourseBrainstormMapper;

    @Autowired
    private IMoocSmartCourseExtensionService moocSmartCourseExtensionService;

    /**
     * 查询互动课堂班级
     *
     * @param classId 互动课堂班级主键
     * @return 互动课堂班级
     */
    @Override
    public MoocSmartCourseClass selectMoocSmartCourseClassByClassId(Long classId)
    {
        return this.getById(classId);
    }

    /**
     * 查询互动课堂班级列表
     *
     * @param moocSmartCourseClass 互动课堂班级
     * @return 互动课堂班级
     */
    @Override
    public List<MoocSmartCourseClass> selectMoocSmartCourseClassList(MoocSmartCourseClass moocSmartCourseClass)
    {
        LambdaQueryWrapper<MoocSmartCourseClass> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(ObjectUtil.isNotEmpty(moocSmartCourseClass.getCourseId())) {
            lambdaQueryWrapper.eq(MoocSmartCourseClass::getCourseId
                    ,moocSmartCourseClass.getCourseId());
        }
        if(ObjectUtil.isNotEmpty(moocSmartCourseClass.getClassName())) {
            lambdaQueryWrapper.like(MoocSmartCourseClass::getClassName
                    ,moocSmartCourseClass.getClassName());
        }
        if(ObjectUtil.isNotEmpty(moocSmartCourseClass.getClassCode())) {
            lambdaQueryWrapper.eq(MoocSmartCourseClass::getClassCode
                    ,moocSmartCourseClass.getClassCode());
        }
        if(ObjectUtil.isNotEmpty(moocSmartCourseClass.getClassType())) {
            lambdaQueryWrapper.eq(MoocSmartCourseClass::getClassType
                    ,moocSmartCourseClass.getClassType());
        }
        if(ObjectUtil.isNotEmpty(moocSmartCourseClass.getUserId())) {
            lambdaQueryWrapper.eq(MoocSmartCourseClass::getUserId
                    ,moocSmartCourseClass.getUserId());
        }
        if(ObjectUtil.isNotEmpty(moocSmartCourseClass.getBookId())) {
            lambdaQueryWrapper.eq(MoocSmartCourseClass::getBookId
                    ,moocSmartCourseClass.getBookId());
        }
        if(ObjectUtil.isNotEmpty(moocSmartCourseClass.getDescription())) {
            lambdaQueryWrapper.eq(MoocSmartCourseClass::getDescription
                    ,moocSmartCourseClass.getDescription());
        }
        if(ObjectUtil.isNotEmpty(moocSmartCourseClass.getCoverImageUrl())) {
            lambdaQueryWrapper.eq(MoocSmartCourseClass::getCoverImageUrl
                    ,moocSmartCourseClass.getCoverImageUrl());
        }
        if(ObjectUtil.isNotEmpty(moocSmartCourseClass.getStatus())) {
            lambdaQueryWrapper.eq(MoocSmartCourseClass::getStatus
                    ,moocSmartCourseClass.getStatus());
        }
        if(ObjectUtil.isNotEmpty(moocSmartCourseClass.getEduClassId())) {
            lambdaQueryWrapper.eq(MoocSmartCourseClass::getEduClassId
                    ,moocSmartCourseClass.getEduClassId());
        }
        if(ObjectUtil.isNotEmpty(moocSmartCourseClass.getCreateBy())) {
            lambdaQueryWrapper.eq(MoocSmartCourseClass::getCreateBy
                    ,moocSmartCourseClass.getCreateBy());
        }
        if(ObjectUtil.isNotEmpty(moocSmartCourseClass.getUpdateBy())) {
            lambdaQueryWrapper.eq(MoocSmartCourseClass::getUpdateBy
                    ,moocSmartCourseClass.getUpdateBy());
        }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增互动课堂班级
     *
     * @param moocSmartCourseClass 互动课堂班级
     * @return 结果
     */
    @Override
    public boolean insertMoocSmartCourseClass(MoocSmartCourseClass moocSmartCourseClass)
    {
        return this.save(moocSmartCourseClass);
    }

    /**
     * 修改互动课堂班级
     *
     * @param moocSmartCourseClass 互动课堂班级
     * @return 结果
     */
    @Override
    public boolean updateMoocSmartCourseClass(MoocSmartCourseClass moocSmartCourseClass)
    {
        return this.updateById(moocSmartCourseClass);
    }

    /**
     * 批量删除互动课堂班级
     *
     * @param classIds 需要删除的互动课堂班级主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocSmartCourseClassByClassIds(List<Long> classIds)
    {
        return this.removeByIds(classIds);
    }

    /**
     * 查询互动课堂班级
     *
     * @param moocSmartCourseClass 互动课堂班级
     * @return 互动课堂班级
     */
    @Override
    public MoocSmartCourseClass getInfo(MoocSmartCourseClass moocSmartCourseClass)
    {
        return baseMapper.getInfo(moocSmartCourseClass);
    }

    @Override
    public List<MoocSmartCourseClass> getListBySchool(MoocSmartCourseClass moocSmartCourseClass) {
        return moocSmartCourseClassMapper.getListBySchool(moocSmartCourseClass);
    }

    @Override
    public MoocSmartCourseClass getInfoByClassId(MoocSmartCourseClass moocSmartCourseClass) {
        return moocSmartCourseClassMapper.getInfoByClassId(moocSmartCourseClass);
    }

    @Override
    public List<MoocSmartCourseClass> getListByTeacherIdAndCourseIdNoPage(MoocSmartCourseClass moocSmartCourseClass) {
        LambdaQueryWrapper<MoocSmartCourseClass> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(ObjectUtil.isNotEmpty(moocSmartCourseClass.getCourseId())) {
            lambdaQueryWrapper.eq(MoocSmartCourseClass::getCourseId
                    ,moocSmartCourseClass.getCourseId());
        }
        if(ObjectUtil.isNotEmpty(moocSmartCourseClass.getClassName())) {
            lambdaQueryWrapper.like(MoocSmartCourseClass::getClassName
                    ,moocSmartCourseClass.getClassName());
        }
        if(ObjectUtil.isNotEmpty(moocSmartCourseClass.getUserId())) {
            lambdaQueryWrapper.eq(MoocSmartCourseClass::getUserId
                    ,moocSmartCourseClass.getUserId());
        }
        lambdaQueryWrapper.eq(MoocSmartCourseClass::getDelFlag,0);
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 查询互动课堂班级活动
     * @param dto 查询互动课堂班级活动Dto
     *
     * @return 互动课堂班级活动
     */
    @Override
    public Object getActivityList(MoocSmartCourseClassActivityDto dto) {
        return redisService.getCacheObject("activity:" + dto.getLessonId());
    }

    /**
     * 追加互动课堂班级活动
     * @param dto 查询互动课堂班级活动Dto
     *
     * @return 互动课堂班级活动操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addActivityList(MoocSmartCourseClassActivityDto dto) {
        List<MoocSmartCourseClassActivityVo> resVos =
                redisService.getCacheObject("activity:" + dto.getLessonId()) != null ?
                        redisService.getCacheObject("activity:" + dto.getLessonId()) :
                        new ArrayList<>();
        try {
            MoocSmartCourseClassActivityVo resVo = new MoocSmartCourseClassActivityVo();
            resVo.setLessonId(dto.getLessonId());
            resVo.setUpFlag(false);
            resVo.setCreateTime(new Date());
            resVo.setState(DutpConstant.NUM_TWO);
            resVo.setCompleteNumber(DutpConstant.NUM_ZERO);
            LambdaQueryWrapper<MoocSmartCourseClassMember> moocSmartCourseClassMemberLqw =  new LambdaQueryWrapper<>();
            moocSmartCourseClassMemberLqw.eq(MoocSmartCourseClassMember::getClassId, dto.getClassId());
            resVo.setTotalNumber(moocSmartCourseClassMemberService.getBaseMapper().selectCount(moocSmartCourseClassMemberLqw));
            // 追加教师签到
            if(DutpConstant.NUM_ZERO.equals(dto.getType())){
                if(moocSmartCourseLessonService.updateMoocSmartCourseLesson(dto.getMoocSmartCourseLesson())){
                    resVo.setType(DutpConstant.NUM_ZERO);
                    resVo.setState(DutpConstant.NUM_THREE);
                    resVo.setActivityId(dto.getMoocSmartCourseLesson().getTeacherId());
                    resVo.setTitle(DutpConstant.SIGNIN_TEACHER);
                    resVo.setSignInTeacherName(SecurityUtils.getLoginUser().getRealName());
                    resVo.setCreateTime(new Date());
                    resVos.add(resVo);
                }
            }
            // 追加签到活动
            else if(DutpConstant.NUM_ONE.equals(dto.getType())){
                dto.getMoocSmartCourseClassAttendance().setActivityStartTime(new Date());
                moocSmartCourseClassAttendanceService.insertMoocSmartCourseClassAttendance(dto.getMoocSmartCourseClassAttendance());
                resVo.setType(DutpConstant.NUM_ONE);
                resVo.setUpFlag(false);
                resVo.setEndMethod(dto.getMoocSmartCourseClassAttendance().getEndMethod());
                resVo.setActivityId(dto.getMoocSmartCourseClassAttendance().getAttendanceActivityId());
                resVo.setTitle(dto.getMoocSmartCourseClassAttendance().getTitle());
                resVo.setStartTime(dto.getMoocSmartCourseClassAttendance().getActivityStartTime());
                resVo.setEndTime(dto.getMoocSmartCourseClassAttendance().getActivityEndTime());
                resVo.setDisplayType(DutpConstant.NUM_ONE.equals(dto.getMoocSmartCourseClassAttendance().getAttendanceType())
                        ? DutpConstant.SIGNIN_TYPE_ONE
                        : DutpConstant.NUM_TWO.equals(dto.getMoocSmartCourseClassAttendance().getAttendanceType())?
                        DutpConstant.SIGNIN_TYPE_TWO : DutpConstant.SIGNIN_TYPE_THREE);
                resVos.add(resVo);
            }
            // 追加提问活动
            else if(DutpConstant.NUM_TWO.equals(dto.getType())){
                dto.getMoocSmartCourseClassQuestion().setQuestionCreatorId(SecurityUtils.getUserId());
                moocSmartCourseClassQuestionService.insertMoocSmartCourseClassQuestion(dto.getMoocSmartCourseClassQuestion());
                resVo.setType(DutpConstant.NUM_TWO);
                resVo.setActivityId(dto.getMoocSmartCourseClassQuestion().getQuestionId());
                resVo.setTitle(dto.getMoocSmartCourseClassQuestion().getQuestionTitle());
                resVo.setStartTime(dto.getMoocSmartCourseClassQuestion().getQuestionStartDate());
                resVo.setEndTime(dto.getMoocSmartCourseClassQuestion().getQuestionEndDate());
                resVo.setDisplayType(DutpConstant.STR_ONE.equals(dto.getMoocSmartCourseClassQuestion().getQuestionType())
                        ? DutpConstant.QUESTION_TYPE_ZERO
                        : DutpConstant.QUESTION_TYPE_ONE );
                resVos.add(resVo);
            }
            // 追加头脑风暴活动
            else if(DutpConstant.NUM_THREE.equals(dto.getType())){
                moocSmartCourseBrainstormService.insertMoocSmartCourseBrainstorm(dto.getMoocSmartCourseBrainstorm());
                dto.getMoocSmartCourseBrainstorm().getFileList().forEach(file ->{
                    file.setBusinessId(dto.getMoocSmartCourseBrainstorm().getBrainstormId());
                    file.setDelFlag(DutpConstant.STR_ZERO);
                    file.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
                    file.setCreateTime(new Date());
                });
                moocSmartCourseBrainstormMapper.batchInsertDutpUserCommonFile(dto.getMoocSmartCourseBrainstorm().getFileList());
                resVo.setType(DutpConstant.NUM_THREE);
                resVo.setState(dto.getMoocSmartCourseBrainstorm().getStatus());
                resVo.setActivityId(dto.getMoocSmartCourseBrainstorm().getBrainstormId());
                resVo.setTitle(dto.getMoocSmartCourseBrainstorm().getBrainstormTitle());
                resVo.setStartTime(dto.getMoocSmartCourseBrainstorm().getBrainstormStartTime());
                resVo.setEndTime(dto.getMoocSmartCourseBrainstorm().getBrainstormEndTime());
                resVos.add(resVo);
            }
            // 追加拓展内容活动
            else if(DutpConstant.NUM_FOUR.equals(dto.getType())){
                dto.getMoocSmartCourseExtension().setExtensionCreatorId(SecurityUtils.getUserId());
                moocSmartCourseExtensionService.insertMoocSmartCourseExtension(dto.getMoocSmartCourseExtension());
                dto.getMoocSmartCourseExtension().getFileList().forEach(file ->{
                    file.setBusinessId(dto.getMoocSmartCourseExtension().getExtensionId());
                    file.setDelFlag(DutpConstant.STR_ZERO);
                    file.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
                    file.setCreateTime(new Date());
                });
                moocSmartCourseBrainstormMapper.batchInsertDutpUserCommonFile(dto.getMoocSmartCourseExtension().getFileList());
                resVo.setType(DutpConstant.NUM_FOUR);
                resVo.setState(dto.getMoocSmartCourseExtension().getStatus());
                resVo.setActivityId(dto.getMoocSmartCourseExtension().getExtensionId());
                resVo.setTitle(dto.getMoocSmartCourseExtension().getExtensionName());
                resVo.setStartTime(dto.getMoocSmartCourseExtension().getExtensionStartTime());
                resVo.setEndTime(dto.getMoocSmartCourseExtension().getExtensionEndTime());
                resVos.add(resVo);
            }
            redisService.setCacheObject("activity:" + dto.getLessonId(), resVos);
            // 活动创建成功，推送 WebSocket 更新
            messagingTemplate.convertAndSend("/topic/activity", resVos);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    /**
     * 修改互动课堂班级活动
     * @param dto 查询互动课堂班级活动Dto
     *
     * @return 互动课堂班级活动操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateActivityList(MoocSmartCourseClassActivityDto dto) {
        List<MoocSmartCourseClassActivityVo> resVos =
                redisService.getCacheObject("activity:" + dto.getLessonId()) != null ?
                        redisService.getCacheObject("activity:" + dto.getLessonId()) :
                        new ArrayList<>();
        try {
            // 更新签到任务
             if(DutpConstant.NUM_ONE.equals(dto.getType())){
                moocSmartCourseClassAttendanceService.updateMoocSmartCourseClassAttendance(dto.getMoocSmartCourseClassAttendance());
                for(MoocSmartCourseClassActivityVo resVo : resVos){
                    if(resVo.getActivityId().equals(dto.getMoocSmartCourseClassAttendance().getAttendanceActivityId())){
                        resVo.setState(DutpConstant.NUM_THREE);
                        resVo.setUpFlag(true);
                        resVo.setEndTime(dto.getMoocSmartCourseClassAttendance().getActivityEndTime());
                    }
                }
            }
             // 更新提问活动
             else if(DutpConstant.NUM_TWO.equals(dto.getType())){
                 moocSmartCourseClassQuestionService.updateMoocSmartCourseClassQuestion(dto.getMoocSmartCourseClassQuestion());
                 for(MoocSmartCourseClassActivityVo resVo : resVos){
                     if(resVo.getActivityId().equals(dto.getMoocSmartCourseClassQuestion().getQuestionId())){
                         resVo.setState(DutpConstant.NUM_THREE);
                         resVo.setUpFlag(true);
                         resVo.setEndTime(dto.getMoocSmartCourseClassQuestion().getQuestionEndDate());
                     }
                 }
             }
             // 更新头脑风暴活动
             else if(DutpConstant.NUM_THREE.equals(dto.getType())){
                 moocSmartCourseBrainstormService.updateMoocSmartCourseBrainstorm(dto.getMoocSmartCourseBrainstorm());
                 if(ObjectUtil.isNotEmpty(dto.getMoocSmartCourseBrainstorm().getFileList())){
                     moocSmartCourseBrainstormMapper.deleteDutpUserCommonFileByBusinessId(dto.getMoocSmartCourseBrainstorm().getBrainstormId());
                     dto.getMoocSmartCourseBrainstorm().getFileList().forEach(file ->{
                         file.setFileId(null);
                         file.setBusinessId(dto.getMoocSmartCourseBrainstorm().getBrainstormId());
                         file.setDelFlag(DutpConstant.STR_ZERO);
                         file.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
                         file.setCreateTime(new Date());
                     });
                     moocSmartCourseBrainstormMapper.batchInsertDutpUserCommonFile(dto.getMoocSmartCourseBrainstorm().getFileList());
                 }
                 for(MoocSmartCourseClassActivityVo resVo : resVos){
                     if(resVo.getActivityId().equals(dto.getMoocSmartCourseBrainstorm().getBrainstormId())){
                         resVo.setState(dto.getMoocSmartCourseBrainstorm().getStatus());
                         resVo.setUpFlag(true);
                         resVo.setStartTime(dto.getMoocSmartCourseBrainstorm().getBrainstormStartTime());
                         resVo.setEndTime(dto.getMoocSmartCourseBrainstorm().getBrainstormEndTime());
                     }
                 }
             }
             // 更新拓展内容活动
             else if(DutpConstant.NUM_FOUR.equals(dto.getType())){
                 dto.getMoocSmartCourseExtension().setExtensionCreatorId(SecurityUtils.getUserId());
                 moocSmartCourseExtensionService.updateMoocSmartCourseExtension(dto.getMoocSmartCourseExtension());
                 if(ObjectUtil.isNotEmpty(dto.getMoocSmartCourseExtension().getFileList())){
                     moocSmartCourseBrainstormMapper.deleteDutpUserCommonFileByBusinessId(dto.getMoocSmartCourseExtension().getExtensionId());
                     dto.getMoocSmartCourseExtension().getFileList().forEach(file ->{
                         file.setFileId(null);
                         file.setBusinessId(dto.getMoocSmartCourseExtension().getExtensionId());
                         file.setDelFlag(DutpConstant.STR_ZERO);
                         file.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
                         file.setCreateTime(new Date());
                     });
                     moocSmartCourseBrainstormMapper.batchInsertDutpUserCommonFile(dto.getMoocSmartCourseExtension().getFileList());
                 }
                 for(MoocSmartCourseClassActivityVo resVo : resVos){
                     if(resVo.getActivityId().equals(dto.getMoocSmartCourseExtension().getExtensionId())){
                         resVo.setState(dto.getMoocSmartCourseExtension().getStatus());
                         resVo.setUpFlag(true);
                         resVo.setStartTime(dto.getMoocSmartCourseExtension().getExtensionStartTime());
                         resVo.setEndTime(dto.getMoocSmartCourseExtension().getExtensionEndTime());
                     }
                 }
             }
            redisService.setCacheObject("activity:" + dto.getLessonId(), resVos);
            // 推送 WebSocket 更新
            messagingTemplate.convertAndSend("/topic/activity", resVos);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 删除互动课堂班级活动
     * @param dto 查询互动课堂班级活动Dto
     *
     * @return 互动课堂班级活动操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delActivityList(MoocSmartCourseClassActivityDto dto) {
        List<MoocSmartCourseClassActivityVo> resVos =
                redisService.getCacheObject("activity:" + dto.getLessonId()) != null ?
                        redisService.getCacheObject("activity:" + dto.getLessonId()) :
                        new ArrayList<>();
        try {
            List<Long> listIds = new ArrayList<>();
            // 删除签到任务
            if(DutpConstant.NUM_ONE.equals(dto.getType())){
                listIds.add(dto.getMoocSmartCourseClassAttendance().getAttendanceActivityId());
                moocSmartCourseClassAttendanceService.deleteMoocSmartCourseClassAttendanceByAttendanceActivityIds(listIds);
                resVos.removeIf(resVo -> resVo.getActivityId().equals(dto.getMoocSmartCourseClassAttendance().getAttendanceActivityId()));
            }
            // 删除提问活动
            else if(DutpConstant.NUM_TWO.equals(dto.getType())){
                listIds.add(dto.getMoocSmartCourseClassQuestion().getQuestionId());
                moocSmartCourseClassQuestionService.deleteMoocSmartCourseClassQuestionByQuestionIds(listIds);
                resVos.removeIf(resVo -> resVo.getActivityId().equals(dto.getMoocSmartCourseClassQuestion().getQuestionId()));
            }
            // 删除头脑风暴活动
            else if(DutpConstant.NUM_THREE.equals(dto.getType())){
                listIds.add(dto.getMoocSmartCourseBrainstorm().getBrainstormId());
                moocSmartCourseBrainstormService.deleteMoocSmartCourseBrainstormByBrainstormIds(listIds);
                resVos.removeIf(resVo -> resVo.getActivityId().equals(dto.getMoocSmartCourseBrainstorm().getBrainstormId()));
            }
            // 删除拓展内容活动
            else if(DutpConstant.NUM_FOUR.equals(dto.getType())){
                dto.getMoocSmartCourseExtension().setExtensionCreatorId(SecurityUtils.getUserId());
                listIds.add(dto.getMoocSmartCourseExtension().getExtensionId());
                moocSmartCourseExtensionService.deleteMoocSmartCourseExtensionByExtensionIds(listIds);
                resVos.removeIf(resVo -> resVo.getActivityId().equals(dto.getMoocSmartCourseExtension().getExtensionId()));
            }
            redisService.setCacheObject("activity:" + dto.getLessonId(), resVos);
            // 推送 WebSocket 更新
            messagingTemplate.convertAndSend("/topic/activity", resVos);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}

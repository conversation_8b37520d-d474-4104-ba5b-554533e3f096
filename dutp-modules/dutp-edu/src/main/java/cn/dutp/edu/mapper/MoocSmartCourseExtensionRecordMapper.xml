<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocSmartCourseExtensionRecordMapper">
    
    <resultMap type="MoocSmartCourseExtensionRecord" id="MoocSmartCourseExtensionRecordResult">
        <result property="recordId"    column="record_id"    />
        <result property="studentId"    column="student_id"    />
        <result property="questionContent"    column="question_content"    />
        <result property="submitTime"    column="submit_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMoocSmartCourseExtensionRecordVo">
        select record_id, student_id, question_content, submit_time, del_flag, create_by, create_time, update_by, update_time from mooc_smart_course_extension_record
    </sql>

    <select id="selectMoocSmartCourseExtensionRecordList" parameterType="MoocSmartCourseExtensionRecord" resultMap="MoocSmartCourseExtensionRecordResult">
        <include refid="selectMoocSmartCourseExtensionRecordVo"/>
        <where>  
            <if test="studentId != null "> and student_id = #{studentId}</if>
            <if test="questionContent != null  and questionContent != ''"> and question_content = #{questionContent}</if>
            <if test="submitTime != null "> and submit_time = #{submitTime}</if>
        </where>
    </select>
    
    <select id="selectMoocSmartCourseExtensionRecordByRecordId" parameterType="Long" resultMap="MoocSmartCourseExtensionRecordResult">
        <include refid="selectMoocSmartCourseExtensionRecordVo"/>
        where record_id = #{recordId}
    </select>
    <select id="selectRecords" resultType="cn.dutp.edu.domain.MoocSmartCourseExtensionRecord">
        select
            record_id,
            extension_id,
            student_id
        from mooc_smart_course_extension_record mscer
        left join 

    </select>

    <insert id="insertMoocSmartCourseExtensionRecord" parameterType="MoocSmartCourseExtensionRecord">
        insert into mooc_smart_course_extension_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recordId != null">record_id,</if>
            <if test="studentId != null">student_id,</if>
            <if test="questionContent != null">question_content,</if>
            <if test="submitTime != null">submit_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="recordId != null">#{recordId},</if>
            <if test="studentId != null">#{studentId},</if>
            <if test="questionContent != null">#{questionContent},</if>
            <if test="submitTime != null">#{submitTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMoocSmartCourseExtensionRecord" parameterType="MoocSmartCourseExtensionRecord">
        update mooc_smart_course_extension_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="questionContent != null">question_content = #{questionContent},</if>
            <if test="submitTime != null">submit_time = #{submitTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where record_id = #{recordId}
    </update>

    <delete id="deleteMoocSmartCourseExtensionRecordByRecordId" parameterType="Long">
        delete from mooc_smart_course_extension_record where record_id = #{recordId}
    </delete>

    <delete id="deleteMoocSmartCourseExtensionRecordByRecordIds" parameterType="String">
        delete from mooc_smart_course_extension_record where record_id in 
        <foreach item="recordId" collection="array" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>
</mapper>
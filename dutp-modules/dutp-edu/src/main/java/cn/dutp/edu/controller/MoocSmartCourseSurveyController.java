package cn.dutp.edu.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.edu.domain.MoocSmartCourseSurveyQuestion;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.edu.domain.MoocSmartCourseSurvey;
import cn.dutp.edu.service.IMoocSmartCourseSurveyService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 互动课堂的问卷调查Controller
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@RestController
@RequestMapping("/survey")
public class MoocSmartCourseSurveyController extends BaseController
{
    @Autowired
    private IMoocSmartCourseSurveyService moocSmartCourseSurveyService;

    /**
     * 查询互动课堂的问卷调查列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocSmartCourseSurvey moocSmartCourseSurvey)
    {
        startPage();
        List<MoocSmartCourseSurvey> list = moocSmartCourseSurveyService.selectMoocSmartCourseSurveyList(moocSmartCourseSurvey);
        return getDataTable(list);
    }

    /**
     * 导出互动课堂的问卷调查列表
     */

    @Log(title = "导出互动课堂的问卷调查", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocSmartCourseSurvey moocSmartCourseSurvey)
    {
        List<MoocSmartCourseSurvey> list = moocSmartCourseSurveyService.selectMoocSmartCourseSurveyList(moocSmartCourseSurvey);
        ExcelUtil<MoocSmartCourseSurvey> util = new ExcelUtil<MoocSmartCourseSurvey>(MoocSmartCourseSurvey.class);
        util.exportExcel(response, list, "互动课堂的问卷调查数据");
    }

    /**
     * 获取互动课堂的问卷调查详细信息
     */

    @GetMapping(value = "/{surveyId}")
    public AjaxResult getInfo(@PathVariable("surveyId") Long surveyId)
    {
        return success(moocSmartCourseSurveyService.selectMoocSmartCourseSurveyBySurveyId(surveyId));
    }

    /**
     * 新增互动课堂的问卷调查
     */

    @Log(title = "新增互动课堂的问卷调查", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocSmartCourseSurvey moocSmartCourseSurvey)
    {
        return toAjax(moocSmartCourseSurveyService.insertMoocSmartCourseSurvey(moocSmartCourseSurvey));
    }

    /**
     * 修改互动课堂的问卷调查
     */

    @Log(title = "修改互动课堂的问卷调查", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocSmartCourseSurvey moocSmartCourseSurvey)
    {
        return toAjax(moocSmartCourseSurveyService.updateMoocSmartCourseSurvey(moocSmartCourseSurvey));
    }

    /**
     * 删除互动课堂的问卷调查
     */

    @Log(title = "删除互动课堂的问卷调查", businessType = BusinessType.DELETE)
    @DeleteMapping("/{surveyIds}")
    public AjaxResult remove(@PathVariable Long[] surveyIds)
    {
        return toAjax(moocSmartCourseSurveyService.deleteMoocSmartCourseSurveyBySurveyIds(Arrays.asList(surveyIds)));
    }

    /**
     * 获取问卷详情（含问题、选项和用户回答）
     */
    @GetMapping("/details/{surveyId}")
    public AjaxResult getSurveyDetails(@PathVariable("surveyId") Long surveyId)
    {
        return success(moocSmartCourseSurveyService.getSurveyQuestionDetails(surveyId));
    }

    /**
     * 查询互动课堂的问卷调查列表
     */
    @GetMapping("/getCourseSurvey")
    public AjaxResult getCourseSurvey(MoocSmartCourseSurvey moocSmartCourseSurvey)
    {
        return success(moocSmartCourseSurveyService.getCourseSurvey(moocSmartCourseSurvey));
    }
}

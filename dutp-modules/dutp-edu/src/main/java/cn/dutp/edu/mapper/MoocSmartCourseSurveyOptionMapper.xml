<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocSmartCourseSurveyOptionMapper">
    
    <resultMap type="MoocSmartCourseSurveyOption" id="MoocSmartCourseSurveyOptionResult">
        <result property="optionId"    column="option_id"    />
        <result property="questionId"    column="question_id"    />
        <result property="questionContent"    column="question_content"    />
        <result property="selectionRate"    column="selection_rate"    />
        <result property="sort"    column="sort"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMoocSmartCourseSurveyOptionVo">
        select option_id, question_id, question_content, selection_rate, sort, del_flag, create_by, create_time, update_by, update_time from mooc_smart_course_survey_option
    </sql>

    <select id="selectMoocSmartCourseSurveyOptionList" parameterType="MoocSmartCourseSurveyOption" resultMap="MoocSmartCourseSurveyOptionResult">
        <include refid="selectMoocSmartCourseSurveyOptionVo"/>
        <where>  
            <if test="questionId != null "> and question_id = #{questionId}</if>
            <if test="questionContent != null  and questionContent != ''"> and question_content = #{questionContent}</if>
            <if test="selectionRate != null "> and selection_rate = #{selectionRate}</if>
            <if test="sort != null "> and sort = #{sort}</if>
        </where>
    </select>
    
    <select id="selectMoocSmartCourseSurveyOptionByOptionId" parameterType="Long" resultMap="MoocSmartCourseSurveyOptionResult">
        <include refid="selectMoocSmartCourseSurveyOptionVo"/>
        where option_id = #{optionId}
    </select>

    <insert id="insertMoocSmartCourseSurveyOption" parameterType="MoocSmartCourseSurveyOption">
        insert into mooc_smart_course_survey_option
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="optionId != null">option_id,</if>
            <if test="questionId != null">question_id,</if>
            <if test="questionContent != null">question_content,</if>
            <if test="selectionRate != null">selection_rate,</if>
            <if test="sort != null">sort,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="optionId != null">#{optionId},</if>
            <if test="questionId != null">#{questionId},</if>
            <if test="questionContent != null">#{questionContent},</if>
            <if test="selectionRate != null">#{selectionRate},</if>
            <if test="sort != null">#{sort},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMoocSmartCourseSurveyOption" parameterType="MoocSmartCourseSurveyOption">
        update mooc_smart_course_survey_option
        <trim prefix="SET" suffixOverrides=",">
            <if test="questionId != null">question_id = #{questionId},</if>
            <if test="questionContent != null">question_content = #{questionContent},</if>
            <if test="selectionRate != null">selection_rate = #{selectionRate},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where option_id = #{optionId}
    </update>

    <delete id="deleteMoocSmartCourseSurveyOptionByOptionId" parameterType="Long">
        delete from mooc_smart_course_survey_option where option_id = #{optionId}
    </delete>

    <delete id="deleteMoocSmartCourseSurveyOptionByOptionIds" parameterType="String">
        delete from mooc_smart_course_survey_option where option_id in 
        <foreach item="optionId" collection="array" open="(" separator="," close=")">
            #{optionId}
        </foreach>
    </delete>
</mapper>
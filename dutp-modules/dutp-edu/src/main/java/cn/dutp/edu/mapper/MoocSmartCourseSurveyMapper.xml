<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocSmartCourseSurveyMapper">
    
    <resultMap type="MoocSmartCourseSurvey" id="MoocSmartCourseSurveyResult">
        <result property="surveyId"    column="survey_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="creatorId"    column="creator_id"    />
        <result property="userId"    column="user_id"    />
        <result property="surveyTopic"    column="survey_topic"    />
        <result property="surveyName"    column="survey_name"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="participationRate"    column="participation_rate"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMoocSmartCourseSurveyVo">
        select survey_id, course_id, creator_id, survey_topic, survey_name, participation_rate, remark, del_flag, create_by, create_time, update_by, update_time from mooc_smart_course_survey
    </sql>

    <select id="selectMoocSmartCourseSurveyList" parameterType="MoocSmartCourseSurvey" resultMap="MoocSmartCourseSurveyResult">
        <include refid="selectMoocSmartCourseSurveyVo"/>
        <where>  
            <if test="courseId != null "> and course_id = #{courseId}</if>
            <if test="creatorId != null "> and creator_id = #{creatorId}</if>
            <if test="participationRate != null "> and participation_rate = #{participationRate}</if>
        </where>
    </select>
    
    <select id="selectMoocSmartCourseSurveyBySurveyId" parameterType="Long" resultMap="MoocSmartCourseSurveyResult">
        <include refid="selectMoocSmartCourseSurveyVo"/>
        where survey_id = #{surveyId}
    </select>

    <insert id="insertMoocSmartCourseSurvey" parameterType="MoocSmartCourseSurvey">
        insert into mooc_smart_course_survey
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="surveyId != null">survey_id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="creatorId != null">creator_id,</if>
            <if test="participationRate != null">participation_rate,</if>
            <if test="remark != null">remark,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="surveyId != null">#{surveyId},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="participationRate != null">#{participationRate},</if>
            <if test="remark != null">#{remark},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMoocSmartCourseSurvey" parameterType="MoocSmartCourseSurvey">
        update mooc_smart_course_survey
        <trim prefix="SET" suffixOverrides=",">
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="creatorId != null">creator_id = #{creatorId},</if>
            <if test="participationRate != null">participation_rate = #{participationRate},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where survey_id = #{surveyId}
    </update>

    <delete id="deleteMoocSmartCourseSurveyBySurveyId" parameterType="Long">
        delete from mooc_smart_course_survey where survey_id = #{surveyId}
    </delete>

    <delete id="deleteMoocSmartCourseSurveyBySurveyIds" parameterType="String">
        delete from mooc_smart_course_survey where survey_id in 
        <foreach item="surveyId" collection="array" open="(" separator="," close=")">
            #{surveyId}
        </foreach>
    </delete>

    <select id="getCourseSurvey" parameterType="cn.dutp.edu.domain.MoocSmartCourseSurvey" resultType="cn.dutp.edu.domain.MoocSmartCourseSurvey">
        SELECT
            s.survey_id,
            s.course_id,
            s.creator_id,
            s.survey_topic,
            s.survey_name,
            s.participation_rate,
            s.remark,
            s.start_time,
            s.end_time,
            a.user_id,
            a.answer_content,
            u.real_name,
            CASE
            WHEN a.survey_id IS NULL THEN
            '0' -- 无关联数据时标记为未参与
            ELSE '1' -- 有关联数据时标记为已参与
            END AS answerStatus
            FROM
            mooc_smart_course_survey AS s
            LEFT JOIN mooc_smart_course_survey_answer AS a ON s.survey_id = a.survey_id
            AND s.course_id = a.course_id AND a.user_id = #{userId}
            AND a.del_flag = '0'
            LEFT JOIN dutp_user AS u ON s.creator_id = u.user_id
            AND u.del_flag = '0'
        <where>
            s.del_flag = '0'
            <if test="courseIds != null and !courseIds.isEmpty()">
                AND s.course_id IN
                <foreach item="courseId" collection="courseIds" open="(" separator="," close=")">
                    #{courseId}
                </foreach>
            </if>
            <if test="progressStatus == '1'">
                and CURRENT_TIMESTAMP BETWEEN s.start_time AND s.end_time
            </if>
            <if test="progressStatus == '2'">
                and CURRENT_TIMESTAMP > s.end_time
            </if>
            <if test="surveyName != null  and surveyName != ''">
                and s.survey_name like concat('%', #{surveyName}, '%')
            </if>
            AND s.start_time IS NOT NULL
            AND s.start_time &lt;= CURRENT_TIMESTAMP
        </where>
    </select>
</mapper>
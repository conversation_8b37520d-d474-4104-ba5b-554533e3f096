package cn.dutp.edu.service;

import cn.dutp.domain.MoocCoursewareDesignResource;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 公开课课件设计资源Service接口
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
public interface IMoocCoursewareDesignResourceService extends IService<MoocCoursewareDesignResource> {
    /**
     * 查询公开课课件设计资源
     *
     * @param chapterResourceId 公开课课件设计资源主键
     * @return 公开课课件设计资源
     */
    public MoocCoursewareDesignResource selectMoocCoursewareDesignResourceByChapterResourceId(Long chapterResourceId);

    /**
     * 查询公开课课件设计资源列表
     *
     * @param moocCoursewareDesignResource 公开课课件设计资源
     * @return 公开课课件设计资源集合
     */
    public List<MoocCoursewareDesignResource> selectMoocCoursewareDesignResourceList(MoocCoursewareDesignResource moocCoursewareDesignResource);

    /**
     * 新增公开课课件设计资源
     *
     * @param moocCoursewareDesignResource 公开课课件设计资源
     * @return 结果
     */
    public boolean insertMoocCoursewareDesignResource(MoocCoursewareDesignResource moocCoursewareDesignResource);

    /**
     * 修改公开课课件设计资源
     *
     * @param moocCoursewareDesignResource 公开课课件设计资源
     * @return 结果
     */
    public boolean updateMoocCoursewareDesignResource(MoocCoursewareDesignResource moocCoursewareDesignResource);

    /**
     * 批量删除公开课课件设计资源
     *
     * @param chapterResourceIds 需要删除的公开课课件设计资源主键集合
     * @return 结果
     */
    public boolean deleteMoocCoursewareDesignResourceByChapterResourceIds(List<Long> chapterResourceIds);

}

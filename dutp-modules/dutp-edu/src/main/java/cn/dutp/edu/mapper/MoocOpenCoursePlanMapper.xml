<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocOpenCoursePlanMapper">
    
    <resultMap type="MoocOpenCoursePlan" id="MoocOpenCoursePlanResult">
        <result property="planId"    column="plan_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="name"    column="name"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="sort"    column="sort"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMoocOpenCoursePlanVo">
        select plan_id, course_id, name, start_time, end_time, sort, del_flag, create_by, create_time, update_by, update_time from mooc_open_course_plan
    </sql>

    <select id="selectMoocOpenCoursePlanList" parameterType="MoocOpenCoursePlan" resultMap="MoocOpenCoursePlanResult">
        <include refid="selectMoocOpenCoursePlanVo"/>
        <where>  
            <if test="courseId != null "> and course_id = #{courseId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="sort != null "> and sort = #{sort}</if>
        </where>
    </select>
    
    <select id="selectMoocOpenCoursePlanByPlanId" parameterType="Long" resultMap="MoocOpenCoursePlanResult">
        <include refid="selectMoocOpenCoursePlanVo"/>
        where plan_id = #{planId}
    </select>

    <insert id="insertMoocOpenCoursePlan" parameterType="MoocOpenCoursePlan">
        insert into mooc_open_course_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planId != null">plan_id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="name != null">name,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="sort != null">sort,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planId != null">#{planId},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="name != null">#{name},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="sort != null">#{sort},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMoocOpenCoursePlan" parameterType="MoocOpenCoursePlan">
        update mooc_open_course_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where plan_id = #{planId}
    </update>

    <delete id="deleteMoocOpenCoursePlanByPlanId" parameterType="Long">
        delete from mooc_open_course_plan where plan_id = #{planId}
    </delete>

    <delete id="deleteMoocOpenCoursePlanByPlanIds" parameterType="String">
        delete from mooc_open_course_plan where plan_id in 
        <foreach item="planId" collection="array" open="(" separator="," close=")">
            #{planId}
        </foreach>
    </delete>
</mapper>
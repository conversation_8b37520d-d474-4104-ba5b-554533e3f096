package cn.dutp.edu.service;

import cn.dutp.domain.MoocOpenCourseApply;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 公开课的审核过程Service接口
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
public interface IMoocOpenCourseApplyService extends IService<MoocOpenCourseApply> {
    /**
     * 查询公开课的审核过程
     *
     * @param applyId 公开课的审核过程主键
     * @return 公开课的审核过程
     */
    public MoocOpenCourseApply selectMoocOpenCourseApplyByApplyId(Long applyId);

    /**
     * 查询公开课的审核过程列表
     *
     * @param moocOpenCourseApply 公开课的审核过程
     * @return 公开课的审核过程集合
     */
    public List<MoocOpenCourseApply> selectMoocOpenCourseApplyList(MoocOpenCourseApply moocOpenCourseApply);

    /**
     * 新增公开课的审核过程
     *
     * @param moocOpenCourseApply 公开课的审核过程
     * @return 结果
     */
    public boolean insertMoocOpenCourseApply(MoocOpenCourseApply moocOpenCourseApply);

    /**
     * 修改公开课的审核过程
     *
     * @param moocOpenCourseApply 公开课的审核过程
     * @return 结果
     */
    public boolean updateMoocOpenCourseApply(MoocOpenCourseApply moocOpenCourseApply);

    /**
     * 批量删除公开课的审核过程
     *
     * @param applyIds 需要删除的公开课的审核过程主键集合
     * @return 结果
     */
    public boolean deleteMoocOpenCourseApplyByApplyIds(List<Long> applyIds);

}

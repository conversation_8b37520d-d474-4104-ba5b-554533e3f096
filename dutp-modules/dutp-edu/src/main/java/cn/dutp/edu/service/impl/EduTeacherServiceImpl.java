package cn.dutp.edu.service.impl;

import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.edu.domain.vo.TeacherVo;
import cn.dutp.edu.domian.DutpUser;
import cn.dutp.edu.mapper.EduTeacherMapper;
import cn.dutp.edu.service.IEduTeacherService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: dutp
 * @date: 2025/7/18 10:05
 */
@Service
public class EduTeacherServiceImpl extends ServiceImpl<EduTeacherMapper, DutpUser> implements IEduTeacherService {
    @Resource
    private EduTeacherMapper eduTeacherMapper;

    @Override
    public List<DutpUser> selectTeacherList(TeacherVo teacher) {
        return eduTeacherMapper.selectTeacherList(teacher);
    }

    @Override
    public boolean addTeacher(DutpUser teacher) {
        if (ObjectUtil.isNotEmpty(teacher.getUserName())){
            LambdaQueryWrapper<DutpUser> dutpUserWrapper = new LambdaQueryWrapper<DutpUser>();
            dutpUserWrapper.eq(DutpUser::getUserName, teacher.getUserName());
            List<DutpUser> list = this.list(dutpUserWrapper);
            if (ObjectUtil.isNotEmpty(list)){
                throw new ServiceException("账号不能重复");
            }
        }
        teacher.setUserType("2");
        teacher.setPassword("123456");
        teacher.setPhonenumber(teacher.getUserName());
        teacher.setNickName(teacher.getUserName());
        return this.save(teacher);
    }

    @Override
    public boolean editTeacher(DutpUser teacher) {
        return this.updateById(teacher);
    }

    @Override
    public boolean deleteTeacher(List<Long> userId) {
        return this.removeByIds(userId);
    }

    @Override
    public TeacherVo getByUserId(Long userId) {
        return eduTeacherMapper.selectTeacherById(userId);
    }
}

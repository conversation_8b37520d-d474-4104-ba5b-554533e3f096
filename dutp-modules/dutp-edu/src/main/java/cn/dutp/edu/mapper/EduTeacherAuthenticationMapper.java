package cn.dutp.edu.mapper;

import cn.dutp.edu.domain.EduTeacherAuthentication;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
/**
 * 教师认证Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
@Repository
public interface EduTeacherAuthenticationMapper extends BaseMapper<EduTeacherAuthentication>
{
    public List<EduTeacherAuthentication> selectEduTeacherAuthenticationList(EduTeacherAuthentication eduTeacherAuthentication);

    public EduTeacherAuthentication selectEduTeacherAuthenticationById(@Param("id") Long id);

    public Long deleteEduTeacherAuthenticationById(@Param("authId") Long authId);

    public List<Long> getSuperUser();

}

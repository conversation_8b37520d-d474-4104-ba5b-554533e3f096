<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocSmartCourseUserMindmapMapper">
    
    <resultMap type="MoocSmartCourseUserMindmap" id="MoocSmartCourseUserMindmapResult">
        <result property="mindmapId"    column="mindmap_id"    />
        <result property="userId"    column="user_id"    />
        <result property="bookId"    column="book_id"    />
        <result property="mindMapData"    column="mind_map_data"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMoocSmartCourseUserMindmapVo">
        select mindmap_id, user_id, book_id, mind_map_data, del_flag, create_by, create_time, update_by, update_time from mooc_smart_course_user_mindmap
    </sql>

    <select id="selectByUserIdAndBookId" resultMap="MoocSmartCourseUserMindmapResult">
        <include refid="selectMoocSmartCourseUserMindmapVo"/>
        where user_id = #{userId} and book_id = #{bookId} and del_flag = '0'
    </select>

    <select id="selectMoocSmartCourseUserMindmapList" parameterType="MoocSmartCourseUserMindmap" resultMap="MoocSmartCourseUserMindmapResult">
        <include refid="selectMoocSmartCourseUserMindmapVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="bookId != null "> and book_id = #{bookId}</if>
            <if test="mindMapData != null  and mindMapData != ''"> and mind_map_data = #{mindMapData}</if>
        </where>
    </select>
    
    <select id="selectMoocSmartCourseUserMindmapByMindmapId" parameterType="Long" resultMap="MoocSmartCourseUserMindmapResult">
        <include refid="selectMoocSmartCourseUserMindmapVo"/>
        where mindmap_id = #{mindmapId}
    </select>

    <insert id="insertMoocSmartCourseUserMindmap" parameterType="MoocSmartCourseUserMindmap" useGeneratedKeys="true" keyProperty="mindmapId">
        insert into mooc_smart_course_user_mindmap
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="bookId != null">book_id,</if>
            <if test="mindMapData != null">mind_map_data,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="bookId != null">#{bookId},</if>
            <if test="mindMapData != null">#{mindMapData},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMoocSmartCourseUserMindmap" parameterType="MoocSmartCourseUserMindmap">
        update mooc_smart_course_user_mindmap
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="mindMapData != null">mind_map_data = #{mindMapData},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where mindmap_id = #{mindmapId}
    </update>

    <delete id="deleteMoocSmartCourseUserMindmapByMindmapId" parameterType="Long">
        delete from mooc_smart_course_user_mindmap where mindmap_id = #{mindmapId}
    </delete>

    <delete id="deleteMoocSmartCourseUserMindmapByMindmapIds" parameterType="String">
        delete from mooc_smart_course_user_mindmap where mindmap_id in 
        <foreach item="mindmapId" collection="array" open="(" separator="," close=")">
            #{mindmapId}
        </foreach>
    </delete>
</mapper>
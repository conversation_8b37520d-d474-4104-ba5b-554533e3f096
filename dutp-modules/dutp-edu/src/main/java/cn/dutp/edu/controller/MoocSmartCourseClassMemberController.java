package cn.dutp.edu.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.edu.domain.MoocSmartCourseClass;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.edu.domain.MoocSmartCourseClassMember;
import cn.dutp.edu.service.IMoocSmartCourseClassMemberService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 互动课堂班级成员Controller
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@RestController
@RequestMapping("/smartCourseMember")
public class MoocSmartCourseClassMemberController extends BaseController
{
    @Autowired
    private IMoocSmartCourseClassMemberService moocSmartCourseClassMemberService;

    /**
     * 查询互动课堂班级成员列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocSmartCourseClassMember moocSmartCourseClassMember)
    {
        startPage();
        List<MoocSmartCourseClassMember> list = moocSmartCourseClassMemberService.selectMoocSmartCourseClassMemberList(moocSmartCourseClassMember);
        return getDataTable(list);
    }

    /**
     * 导出互动课堂班级成员列表
     */
    @RequiresPermissions("edu:member:export")
    @Log(title = "导出互动课堂班级成员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocSmartCourseClassMember moocSmartCourseClassMember)
    {
        List<MoocSmartCourseClassMember> list = moocSmartCourseClassMemberService.selectMoocSmartCourseClassMemberList(moocSmartCourseClassMember);
        ExcelUtil<MoocSmartCourseClassMember> util = new ExcelUtil<MoocSmartCourseClassMember>(MoocSmartCourseClassMember.class);
        util.exportExcel(response, list, "互动课堂班级成员数据");
    }

    /**
     * 获取互动课堂班级成员详细信息
     */
    @RequiresPermissions("edu:member:query")
    @GetMapping(value = "/{classMemberId}")
    public AjaxResult getInfo(@PathVariable("classMemberId") Long classMemberId)
    {
        return success(moocSmartCourseClassMemberService.selectMoocSmartCourseClassMemberByClassMemberId(classMemberId));
    }

    /**
     * 新增互动课堂班级成员
     */
    @Log(title = "新增互动课堂班级成员", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocSmartCourseClassMember moocSmartCourseClassMember)
    {
        return toAjax(moocSmartCourseClassMemberService.insertMoocSmartCourseClassMember(moocSmartCourseClassMember));
    }

    /**
     * 修改互动课堂班级成员
     */
    @RequiresPermissions("edu:member:edit")
    @Log(title = "修改互动课堂班级成员", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocSmartCourseClassMember moocSmartCourseClassMember)
    {
        return toAjax(moocSmartCourseClassMemberService.updateMoocSmartCourseClassMember(moocSmartCourseClassMember));
    }

    /**
     * 删除互动课堂班级成员
     */
    @RequiresPermissions("edu:member:remove")
    @Log(title = "删除互动课堂班级成员", businessType = BusinessType.DELETE)
    @DeleteMapping("/{classMemberIds}")
    public AjaxResult remove(@PathVariable Long[] classMemberIds)
    {
        return toAjax(moocSmartCourseClassMemberService.deleteMoocSmartCourseClassMemberByClassMemberIds(Arrays.asList(classMemberIds)));
    }

    /**
     * 互动课堂成员信息统计
     */
    @GetMapping(value = "/getLearnInformationStatistics")
    public AjaxResult getLearnInformationStatistics()
    {
        return success(moocSmartCourseClassMemberService.getLearnInformationStatistics());
    }

    /**
     * 成员互动课堂班级查询
     */
    @GetMapping(value = "/getLearnClass")
    public TableDataInfo getLearnClass(MoocSmartCourseClass moocSmartCourseClass)
    {
        startPage();
        return getDataTable(moocSmartCourseClassMemberService.getLearnClass(moocSmartCourseClass));
    }
}

package cn.dutp.edu.service;

import java.util.List;
import cn.dutp.edu.domain.MoocSmartCourseUserMindmap;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 用户书籍思维导图数据Service接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface IMoocSmartCourseUserMindmapService extends IService<MoocSmartCourseUserMindmap>
{
    /**
     * 根据用户ID和书籍ID查询用户书籍思维导图数据
     *
     * @param userId 用户ID
     * @param bookId 书籍ID
     * @return 用户书籍思维导图数据
     */
    public MoocSmartCourseUserMindmap selectByUserIdAndBookId(Long userId, Long bookId);

    /**
     * 查询用户书籍思维导图数据
     *
     * @param mindmapId 用户书籍思维导图数据主键
     * @return 用户书籍思维导图数据
     */
    public MoocSmartCourseUserMindmap selectMoocSmartCourseUserMindmapByMindmapId(Long mindmapId);

    /**
     * 查询用户书籍思维导图数据列表
     *
     * @param moocSmartCourseUserMindmap 用户书籍思维导图数据
     * @return 用户书籍思维导图数据集合
     */
    public List<MoocSmartCourseUserMindmap> selectMoocSmartCourseUserMindmapList(MoocSmartCourseUserMindmap moocSmartCourseUserMindmap);

    /**
     * 新增用户书籍思维导图数据
     *
     * @param moocSmartCourseUserMindmap 用户书籍思维导图数据
     * @return 结果
     */
    public boolean insertMoocSmartCourseUserMindmap(MoocSmartCourseUserMindmap moocSmartCourseUserMindmap);

    /**
     * 修改用户书籍思维导图数据
     *
     * @param moocSmartCourseUserMindmap 用户书籍思维导图数据
     * @return 结果
     */
    public boolean updateMoocSmartCourseUserMindmap(MoocSmartCourseUserMindmap moocSmartCourseUserMindmap);

    /**
     * 批量删除用户书籍思维导图数据
     *
     * @param mindmapIds 需要删除的用户书籍思维导图数据主键集合
     * @return 结果
     */
    public boolean deleteMoocSmartCourseUserMindmapByMindmapIds(List<Long> mindmapIds);

}

package cn.dutp.edu.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.edu.domain.MoocSmartCourseBrainstorm;
import cn.dutp.edu.service.IMoocSmartCourseBrainstormService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 互动课堂的头脑风暴Controller
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@RestController
@RequestMapping("/brainstorm")
public class MoocSmartCourseBrainstormController extends BaseController
{
    @Autowired
    private IMoocSmartCourseBrainstormService moocSmartCourseBrainstormService;

    /**
     * 查询互动课堂的头脑风暴列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocSmartCourseBrainstorm moocSmartCourseBrainstorm)
    {
        startPage();
        List<MoocSmartCourseBrainstorm> list = moocSmartCourseBrainstormService.selectMoocSmartCourseBrainstormList(moocSmartCourseBrainstorm);
        return getDataTable(list);
    }

    /**
     * 导出互动课堂的头脑风暴列表
     */
    @RequiresPermissions("edu:brainstorm:export")
    @Log(title = "导出互动课堂的头脑风暴", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocSmartCourseBrainstorm moocSmartCourseBrainstorm)
    {
        List<MoocSmartCourseBrainstorm> list = moocSmartCourseBrainstormService.selectMoocSmartCourseBrainstormList(moocSmartCourseBrainstorm);
        ExcelUtil<MoocSmartCourseBrainstorm> util = new ExcelUtil<MoocSmartCourseBrainstorm>(MoocSmartCourseBrainstorm.class);
        util.exportExcel(response, list, "互动课堂的头脑风暴数据");
    }

    /**
     * 获取互动课堂的头脑风暴详细信息
     */
    @GetMapping(value = "/{brainstormId}")
    public AjaxResult getInfo(@PathVariable("brainstormId") Long brainstormId)
    {
        return success(moocSmartCourseBrainstormService.selectMoocSmartCourseBrainstormByBrainstormId(brainstormId));
    }

    /**
     * 新增互动课堂的头脑风暴
     */
    @RequiresPermissions("edu:brainstorm:add")
    @Log(title = "新增互动课堂的头脑风暴", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocSmartCourseBrainstorm moocSmartCourseBrainstorm)
    {
        return toAjax(moocSmartCourseBrainstormService.insertMoocSmartCourseBrainstorm(moocSmartCourseBrainstorm));
    }

    /**
     * 修改互动课堂的头脑风暴
     */
    @RequiresPermissions("edu:brainstorm:edit")
    @Log(title = "修改互动课堂的头脑风暴", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocSmartCourseBrainstorm moocSmartCourseBrainstorm)
    {
        return toAjax(moocSmartCourseBrainstormService.updateMoocSmartCourseBrainstorm(moocSmartCourseBrainstorm));
    }

    /**
     * 删除互动课堂的头脑风暴
     */
    @RequiresPermissions("edu:brainstorm:remove")
    @Log(title = "删除互动课堂的头脑风暴", businessType = BusinessType.DELETE)
    @DeleteMapping("/{brainstormIds}")
    public AjaxResult remove(@PathVariable Long[] brainstormIds)
    {
        return toAjax(moocSmartCourseBrainstormService.deleteMoocSmartCourseBrainstormByBrainstormIds(Arrays.asList(brainstormIds)));
    }
}

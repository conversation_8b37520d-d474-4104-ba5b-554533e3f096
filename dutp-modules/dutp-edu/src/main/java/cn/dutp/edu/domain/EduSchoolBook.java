package cn.dutp.edu.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.serialize.LongListToStringSerializer;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 校本教材推送对象 edu_school_book
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Data
@TableName("edu_school_book")
public class EduSchoolBook extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolBookId;

    /**
     * 学校Id
     */
    @Excel(name = "学校Id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolId;

    /**
     * 教材ID
     */
    @Excel(name = "教材ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 有效期
     */
    @Excel(name = "有效期")
    private Long dayLimit;

    /**
     * 截止日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "截止日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date expireDate;

    /**
     * 0未推送1已推送
     */
    private Integer pushFlag;

    /**
     * 学校名称
     */
    @TableField(exist = false)
    private String schoolName;

    /**
     * 学校编码
     */
    @TableField(exist = false)
    private String schoolCode;

    /**
     * 学校ID列表
     */
    @JsonSerialize(using = LongListToStringSerializer.class)
    @TableField(exist = false)
    private List<Long> schoolIdList;
}

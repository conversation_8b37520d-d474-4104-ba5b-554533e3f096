package cn.dutp.edu.service;

import java.util.List;
import cn.dutp.edu.domain.MoocSmartCourseExtensionRecord;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 互动课堂的拓展内容学生记录Service接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IMoocSmartCourseExtensionRecordService extends IService<MoocSmartCourseExtensionRecord>
{
    /**
     * 查询互动课堂的拓展内容学生记录
     *
     * @param recordId 互动课堂的拓展内容学生记录主键
     * @return 互动课堂的拓展内容学生记录
     */
    public MoocSmartCourseExtensionRecord selectMoocSmartCourseExtensionRecordByRecordId(Long recordId);

    /**
     * 查询互动课堂的拓展内容学生记录列表
     *
     * @param moocSmartCourseExtensionRecord 互动课堂的拓展内容学生记录
     * @return 互动课堂的拓展内容学生记录集合
     */
    public List<MoocSmartCourseExtensionRecord> selectMoocSmartCourseExtensionRecordList(MoocSmartCourseExtensionRecord moocSmartCourseExtensionRecord);

    /**
     * 根据拓展id查询学生提交记录
     * @param extensionId
     * @param userId
     * @return
     */
    public MoocSmartCourseExtensionRecord getRecordByExtensionId(Long extensionId, Long userId);

    /**
     * 新增互动课堂的拓展内容学生记录
     *
     * @param moocSmartCourseExtensionRecord 互动课堂的拓展内容学生记录
     * @return 结果
     */
    public boolean insertMoocSmartCourseExtensionRecord(MoocSmartCourseExtensionRecord moocSmartCourseExtensionRecord);

    /**
     * 修改互动课堂的拓展内容学生记录
     *
     * @param moocSmartCourseExtensionRecord 互动课堂的拓展内容学生记录
     * @return 结果
     */
    public boolean updateMoocSmartCourseExtensionRecord(MoocSmartCourseExtensionRecord moocSmartCourseExtensionRecord);

    /**
     * 批量删除互动课堂的拓展内容学生记录
     *
     * @param recordIds 需要删除的互动课堂的拓展内容学生记录主键集合
     * @return 结果
     */
    public boolean deleteMoocSmartCourseExtensionRecordByRecordIds(List<Long> recordIds);

}

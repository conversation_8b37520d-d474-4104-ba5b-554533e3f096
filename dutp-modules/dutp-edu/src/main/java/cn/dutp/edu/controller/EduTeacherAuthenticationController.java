package cn.dutp.edu.controller;

import cn.dutp.api.common.constant.DutpConstant;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.log.enums.OperatorType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.edu.domain.EduTeacherAuthentication;
import cn.dutp.edu.service.IEduTeacherAuthenticationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 教师认证Controller
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
@RestController
@RequestMapping("/authentication")
public class EduTeacherAuthenticationController extends BaseController
{
    @Autowired
    private IEduTeacherAuthenticationService eduTeacherAuthenticationService;

    /**
     * 查询教师认证列表
     */
    @RequiresPermissions("edu:authentication:list")
    @GetMapping("/list")
    public TableDataInfo list(EduTeacherAuthentication eduTeacherAuthentication)
    {
        startPage();
        List<EduTeacherAuthentication> list = eduTeacherAuthenticationService.selectEduTeacherAuthenticationList(eduTeacherAuthentication);
        return getDataTable(list);
    }

    /**
     * 导出教师认证列表
     */
    @RequiresPermissions("edu:authentication:export")
    @Log(title = "导出教师认证", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EduTeacherAuthentication eduTeacherAuthentication)
    {
        List<EduTeacherAuthentication> list = eduTeacherAuthenticationService.selectEduTeacherAuthenticationList(eduTeacherAuthentication);
        ExcelUtil<EduTeacherAuthentication> util = new ExcelUtil<EduTeacherAuthentication>(EduTeacherAuthentication.class);
        util.exportExcel(response, list, "教师认证数据");
    }

    /**
     * 获取教师认证详细信息
     */
    @RequiresPermissions("edu:authentication:query")
    @GetMapping(value = "/{Id}")
    public AjaxResult getInfo(@PathVariable("Id") Long Id)
    {
        return success(eduTeacherAuthenticationService.selectEduTeacherAuthenticationById(Id));
    }

    /**
     * 新增教师认证
     */
    @RequiresPermissions("edu:authentication:add")
    @Log(title = "新增教师认证", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EduTeacherAuthentication eduTeacherAuthentication)
    {
        return toAjax(eduTeacherAuthenticationService.insertEduTeacherAuthentication(eduTeacherAuthentication));
    }

    /**
     * 新增教师认证
     */
    @Log(title = "教师认证申请", businessType = BusinessType.INSERT, operatorType = OperatorType.READER)
    @PostMapping("/addEducation")
    public AjaxResult addEducation(@RequestBody EduTeacherAuthentication eduTeacherAuthentication)
    {
        return toAjax(eduTeacherAuthenticationService.insertEduTeacherAuthenticationEducation(eduTeacherAuthentication));
    }

    /**
     * 修改教师认证
     */
    @RequiresPermissions("edu:authentication:edit")
    @Log(title = "教师认证", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EduTeacherAuthentication eduTeacherAuthentication)
    {
        return toAjax(eduTeacherAuthenticationService.updateEduTeacherAuthentication(eduTeacherAuthentication));
    }


    /**
     * 删除教师认证
     */
    @RequiresPermissions("edu:authentication:remove")
    @Log(title = "教师认证", businessType = BusinessType.DELETE)
    @DeleteMapping("/{Ids}")
    public AjaxResult remove(@PathVariable Long[] Ids)
    {
        return toAjax(eduTeacherAuthenticationService.deleteEduTeacherAuthenticationByIds(Arrays.asList(Ids)));
    }

    /**
     * 查询教师认证列表
     */
    @GetMapping("/listEducation")
    public TableDataInfo listEducation(EduTeacherAuthentication eduTeacherAuthentication)
    {
        startPage();
        List<EduTeacherAuthentication> list = eduTeacherAuthenticationService.selectEduTeacherAuthenticationList(eduTeacherAuthentication);
        list = list.stream().filter(authentication -> DutpConstant.STR_ZERO.equals(authentication.getDelFlag())).collect(Collectors.toList());
        return getDataTable(list);
    }
}

package cn.dutp.edu.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.edu.mapper.MoocSmartCourseDiscussionInteractionMapper;
import cn.dutp.edu.domain.MoocSmartCourseDiscussionInteraction;
import cn.dutp.edu.service.IMoocSmartCourseDiscussionInteractionService;

/**
 * 互动课堂的课上主题讨论点赞/举报/评论Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class MoocSmartCourseDiscussionInteractionServiceImpl extends ServiceImpl<MoocSmartCourseDiscussionInteractionMapper, MoocSmartCourseDiscussionInteraction> implements IMoocSmartCourseDiscussionInteractionService
{
    @Autowired
    private MoocSmartCourseDiscussionInteractionMapper moocSmartCourseDiscussionInteractionMapper;

    /**
     * 查询互动课堂的课上主题讨论点赞/举报/评论
     *
     * @param interactionId 互动课堂的课上主题讨论点赞/举报/评论主键
     * @return 互动课堂的课上主题讨论点赞/举报/评论
     */
    @Override
    public MoocSmartCourseDiscussionInteraction selectMoocSmartCourseDiscussionInteractionByInteractionId(Long interactionId)
    {
        return this.getById(interactionId);
    }

    /**
     * 查询互动课堂的课上主题讨论点赞/举报/评论列表
     *
     * @param moocSmartCourseDiscussionInteraction 互动课堂的课上主题讨论点赞/举报/评论
     * @return 互动课堂的课上主题讨论点赞/举报/评论
     */
    @Override
    public List<MoocSmartCourseDiscussionInteraction> selectMoocSmartCourseDiscussionInteractionList(MoocSmartCourseDiscussionInteraction moocSmartCourseDiscussionInteraction)
    {
        LambdaQueryWrapper<MoocSmartCourseDiscussionInteraction> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(moocSmartCourseDiscussionInteraction.getReplyId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseDiscussionInteraction::getReplyId
                ,moocSmartCourseDiscussionInteraction.getReplyId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseDiscussionInteraction.getStudentId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseDiscussionInteraction::getStudentId
                ,moocSmartCourseDiscussionInteraction.getStudentId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseDiscussionInteraction.getActionType())) {
                lambdaQueryWrapper.eq(MoocSmartCourseDiscussionInteraction::getActionType
                ,moocSmartCourseDiscussionInteraction.getActionType());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseDiscussionInteraction.getContent())) {
                lambdaQueryWrapper.eq(MoocSmartCourseDiscussionInteraction::getContent
                ,moocSmartCourseDiscussionInteraction.getContent());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseDiscussionInteraction.getStatus())) {
                lambdaQueryWrapper.eq(MoocSmartCourseDiscussionInteraction::getStatus
                ,moocSmartCourseDiscussionInteraction.getStatus());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增互动课堂的课上主题讨论点赞/举报/评论
     *
     * @param moocSmartCourseDiscussionInteraction 互动课堂的课上主题讨论点赞/举报/评论
     * @return 结果
     */
    @Override
    public boolean insertMoocSmartCourseDiscussionInteraction(MoocSmartCourseDiscussionInteraction moocSmartCourseDiscussionInteraction)
    {
        return this.save(moocSmartCourseDiscussionInteraction);
    }

    /**
     * 修改互动课堂的课上主题讨论点赞/举报/评论
     *
     * @param moocSmartCourseDiscussionInteraction 互动课堂的课上主题讨论点赞/举报/评论
     * @return 结果
     */
    @Override
    public boolean updateMoocSmartCourseDiscussionInteraction(MoocSmartCourseDiscussionInteraction moocSmartCourseDiscussionInteraction)
    {
        return this.updateById(moocSmartCourseDiscussionInteraction);
    }

    /**
     * 批量删除互动课堂的课上主题讨论点赞/举报/评论
     *
     * @param interactionIds 需要删除的互动课堂的课上主题讨论点赞/举报/评论主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocSmartCourseDiscussionInteractionByInteractionIds(List<Long> interactionIds)
    {
        return this.removeByIds(interactionIds);
    }

}

package cn.dutp.edu.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.edu.domain.MoocSmartCourseHomeworkSubmission;
import cn.dutp.edu.service.IMoocSmartCourseHomeworkSubmissionService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.security.utils.SecurityUtils;

/**
 * 学生作业提交记录Controller
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@RestController
@RequestMapping("/submission")
public class MoocSmartCourseHomeworkSubmissionController extends BaseController
{
    @Autowired
    private IMoocSmartCourseHomeworkSubmissionService moocSmartCourseHomeworkSubmissionService;

    /**
     * 查询学生作业提交记录列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocSmartCourseHomeworkSubmission moocSmartCourseHomeworkSubmission)
    {
        startPage();
        List<MoocSmartCourseHomeworkSubmission> list = moocSmartCourseHomeworkSubmissionService.selectMoocSmartCourseHomeworkSubmissionList(moocSmartCourseHomeworkSubmission);
        return getDataTable(list);
    }

    /**
     * 导出学生作业提交记录列表
     */

    @Log(title = "导出学生作业提交记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocSmartCourseHomeworkSubmission moocSmartCourseHomeworkSubmission)
    {
        List<MoocSmartCourseHomeworkSubmission> list = moocSmartCourseHomeworkSubmissionService.selectMoocSmartCourseHomeworkSubmissionList(moocSmartCourseHomeworkSubmission);
        ExcelUtil<MoocSmartCourseHomeworkSubmission> util = new ExcelUtil<MoocSmartCourseHomeworkSubmission>(MoocSmartCourseHomeworkSubmission.class);
        util.exportExcel(response, list, "学生作业提交记录数据");
    }

    /**
     * 获取学生作业提交记录详细信息
     */

    @GetMapping(value = "/{submissionId}")
    public AjaxResult getInfo(@PathVariable("submissionId") Long submissionId)
    {
        return success(moocSmartCourseHomeworkSubmissionService.selectMoocSmartCourseHomeworkSubmissionBySubmissionId(submissionId));
    }

    /**
     * 根据作业ID获取当前学生作业提交记录详细信息
     */
    @GetMapping(value = "/getByAssignment/{assignmentId}")
    public AjaxResult getSubmissionByAssignmentId(@PathVariable("assignmentId") Long assignmentId)
    {
        Long studentId = SecurityUtils.getUserId();
        return success(moocSmartCourseHomeworkSubmissionService.selectMoocSmartCourseHomeworkSubmissionByAssignmentIdAndStudentId(assignmentId, studentId));
    }

    /**
     * 新增学生作业提交记录
     */

    @Log(title = "新增学生作业提交记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocSmartCourseHomeworkSubmission moocSmartCourseHomeworkSubmission)
    {
        return toAjax(moocSmartCourseHomeworkSubmissionService.insertMoocSmartCourseHomeworkSubmission(moocSmartCourseHomeworkSubmission));
    }

    /**
     * 修改学生作业提交记录
     */

    @Log(title = "修改学生作业提交记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocSmartCourseHomeworkSubmission moocSmartCourseHomeworkSubmission)
    {
        return toAjax(moocSmartCourseHomeworkSubmissionService.updateMoocSmartCourseHomeworkSubmission(moocSmartCourseHomeworkSubmission));
    }

    /**
     * 删除学生作业提交记录
     */

    @Log(title = "删除学生作业提交记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{submissionIds}")
    public AjaxResult remove(@PathVariable Long[] submissionIds)
    {
        return toAjax(moocSmartCourseHomeworkSubmissionService.deleteMoocSmartCourseHomeworkSubmissionBySubmissionIds(Arrays.asList(submissionIds)));
    }
}

package cn.dutp.edu.service;

import cn.dutp.domain.MoocCoursewareDesignFolder;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 公开课课件设计目录Service接口
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
public interface IMoocCoursewareDesignFolderService extends IService<MoocCoursewareDesignFolder> {
    /**
     * 查询公开课课件设计目录
     *
     * @param chapterId 公开课课件设计目录主键
     * @return 公开课课件设计目录
     */
    public MoocCoursewareDesignFolder selectMoocCoursewareDesignFolderByChapterId(Long chapterId);

    /**
     * 查询公开课课件设计目录列表
     *
     * @param moocCoursewareDesignFolder 公开课课件设计目录
     * @return 公开课课件设计目录集合
     */
    public List<MoocCoursewareDesignFolder> selectMoocCoursewareDesignFolderList(MoocCoursewareDesignFolder moocCoursewareDesignFolder);

    /**
     * 新增公开课课件设计目录
     *
     * @param moocCoursewareDesignFolder 公开课课件设计目录
     * @return 结果
     */
    public boolean insertMoocCoursewareDesignFolder(MoocCoursewareDesignFolder moocCoursewareDesignFolder);

    /**
     * 修改公开课课件设计目录
     *
     * @param moocCoursewareDesignFolder 公开课课件设计目录
     * @return 结果
     */
    public boolean updateMoocCoursewareDesignFolder(MoocCoursewareDesignFolder moocCoursewareDesignFolder);

    /**
     * 批量删除公开课课件设计目录
     *
     * @param chapterIds 需要删除的公开课课件设计目录主键集合
     * @return 结果
     */
    public boolean deleteMoocCoursewareDesignFolderByChapterIds(List<Long> chapterIds);

}

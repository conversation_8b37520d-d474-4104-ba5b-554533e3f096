package cn.dutp.edu.controller;

import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.domain.MoocCoursewareDesignFolder;
import cn.dutp.edu.service.IMoocCoursewareDesignFolderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 公开课课件设计目录Controller
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@RestController
@RequestMapping("coursewareFolder")
public class MoocCoursewareDesignFolderController extends BaseController {
    @Autowired
    private IMoocCoursewareDesignFolderService moocCoursewareDesignFolderService;

    /**
     * 查询公开课课件设计目录列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocCoursewareDesignFolder moocCoursewareDesignFolder) {
        startPage();
        List<MoocCoursewareDesignFolder> list = moocCoursewareDesignFolderService.selectMoocCoursewareDesignFolderList(moocCoursewareDesignFolder);
        return getDataTable(list);
    }

    /**
     * 导出公开课课件设计目录列表
     */
    @Log(title = "导出公开课课件设计目录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocCoursewareDesignFolder moocCoursewareDesignFolder) {
        List<MoocCoursewareDesignFolder> list = moocCoursewareDesignFolderService.selectMoocCoursewareDesignFolderList(moocCoursewareDesignFolder);
        ExcelUtil<MoocCoursewareDesignFolder> util = new ExcelUtil<MoocCoursewareDesignFolder>(MoocCoursewareDesignFolder.class);
        util.exportExcel(response, list, "公开课课件设计目录数据");
    }

    /**
     * 获取公开课课件设计目录详细信息
     */
    @GetMapping(value = "/{chapterId}")
    public AjaxResult getInfo(@PathVariable("chapterId") Long chapterId) {
        return success(moocCoursewareDesignFolderService.selectMoocCoursewareDesignFolderByChapterId(chapterId));
    }

    /**
     * 新增公开课课件设计目录
     */
    @Log(title = "新增公开课课件设计目录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocCoursewareDesignFolder moocCoursewareDesignFolder) {
        return toAjax(moocCoursewareDesignFolderService.insertMoocCoursewareDesignFolder(moocCoursewareDesignFolder));
    }

    /**
     * 修改公开课课件设计目录
     */
    @Log(title = "修改公开课课件设计目录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocCoursewareDesignFolder moocCoursewareDesignFolder) {
        return toAjax(moocCoursewareDesignFolderService.updateMoocCoursewareDesignFolder(moocCoursewareDesignFolder));
    }

    /**
     * 删除公开课课件设计目录
     */
    @Log(title = "删除公开课课件设计目录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{chapterIds}")
    public AjaxResult remove(@PathVariable Long[] chapterIds) {
        return toAjax(moocCoursewareDesignFolderService.deleteMoocCoursewareDesignFolderByChapterIds(Arrays.asList(chapterIds)));
    }
}

package cn.dutp.edu.service.impl;

import cn.dutp.edu.domain.EduClassMember;
import cn.dutp.edu.domain.vo.EduStudentVo;
import cn.dutp.edu.domian.DutpUser;
import cn.dutp.edu.mapper.EduClassMemberMapper;
import cn.dutp.edu.mapper.EduStudentMapper;
import cn.dutp.edu.service.IEduStudentService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @author: dutp
 * @date: 2025/7/21 9:47
 */
@Service
public class EduStudentServiceImpl extends ServiceImpl<EduStudentMapper, DutpUser> implements IEduStudentService {

    @Resource
    private EduStudentMapper eduStudentMapper;
    @Resource
    private EduClassMemberMapper eduClassMemberMapper;

    @Override
    public List<EduStudentVo> studentListInSchool(EduStudentVo student) {
        return eduStudentMapper.studentListInSchool(student);
    }

    @Override
    public List<EduStudentVo> studentListInClass(EduStudentVo student) {
        return eduStudentMapper.studentListInClass(student);
    }

    @Override
    public boolean addStudentInfo(DutpUser student) {

        //add student
        student.setUserType("1");
        student.setUserName(student.getPhonenumber());
        student.setNickName(student.getPhonenumber());
        boolean save = this.save(student);

        // add studentMember
        EduClassMember member = new EduClassMember();
        member.setEduClassId(student.getEduClassId());
        member.setUserId(student.getUserId());
        member.setJoinTime(new Date());
        eduClassMemberMapper.insert(member);
        return save;

    }

    @Override
    public boolean editStudentInfo(DutpUser student) {
        return this.updateById(student);
    }

    @Override
    public boolean addStudentMessage(DutpUser student) {
        //add student
        student.setUserType("1");
        student.setUserName(student.getPhonenumber());
        student.setNickName(student.getPhonenumber());
        boolean save = this.save(student);
        return save;
    }


}

package cn.dutp.edu.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 购书码发行管理对象 dtb_book_purchase_code
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@ColumnWidth(value = 20)// 列宽
@Data
public class DtbBookPurchaseCodeExport {
    // 名字
    @ExcelProperty(value = "名字", order = 1)
    private String nickName;
    // 类别 0普通读者1学生2教师
    @ExcelProperty(value = "身份", order = 2, converter = UserTypeConverter.class)
    private String userType;
    // 院系名称
    @ExcelProperty(value = "学院名称", order = 3)
    private String schoolName;
    // 购书码
    @ExcelProperty(value = "购书码", order = 4)
    private String code;
    // 购书码
    @ExcelProperty(value = "绑定日期", order = 5)
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss", timezone = "GMT+8")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.LEFT) // 设置左对齐
    private Date bindDate;
    // 购书码绑定手机号
    @ExcelProperty(value = "手机号", order = 6)
    private String phone;
    // 绑定状态
    @ExcelProperty(value = "绑定状态", order = 7)
    private String bindingState = "已绑定";
    // 院系id
    @ExcelIgnore
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolId;
    // 购书码id
    @ExcelIgnore
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long codeId;
    // 教材ID
    @ExcelIgnore
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;
    // 解绑次数
    @ExcelIgnore
    private Integer unbindQuantity;
    // 导出次数
    @ExcelIgnore
    private Integer exportQuantity;

    // 是否可解绑 0否1是(超过绑定日期30天不可以解绑)
    @ExcelIgnore
    private Integer isUnbind;
    // 订单下的购书码id
    @ExcelIgnore
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderCodeId;
    // 教材名称
    @ExcelIgnore
    private String bookName;
    // 教材ISBN
    @ExcelIgnore
    private String isbn;
    // 上架状态1已上架2未上架3召回4即将上架
    @ExcelIgnore
    private Integer shelfState;


}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocSmartCourseLessonEvaluationMapper">
    
    <resultMap type="MoocSmartCourseLessonEvaluation" id="MoocSmartCourseLessonEvaluationResult">
        <result property="evaluationId"    column="evaluation_id"    />
        <result property="lessonId"    column="lesson_id"    />
        <result property="studentId"    column="student_id"    />
        <result property="rating"    column="rating"    />
        <result property="comment"    column="comment"    />
        <result property="createdBy"    column="created_by"    />
        <result property="updatedBy"    column="updated_by"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMoocSmartCourseLessonEvaluationVo">
        select evaluation_id, lesson_id, student_id, rating, comment, created_by, updated_by, del_flag, create_time, update_time from mooc_smart_course_lesson_evaluation
    </sql>

    <select id="selectMoocSmartCourseLessonEvaluationList" parameterType="MoocSmartCourseLessonEvaluation" resultMap="MoocSmartCourseLessonEvaluationResult">
        <include refid="selectMoocSmartCourseLessonEvaluationVo"/>
        <where>  
            <if test="lessonId != null "> and lesson_id = #{lessonId}</if>
            <if test="studentId != null "> and student_id = #{studentId}</if>
            <if test="rating != null "> and rating = #{rating}</if>
            <if test="comment != null  and comment != ''"> and comment = #{comment}</if>
            <if test="createdBy != null "> and created_by = #{createdBy}</if>
            <if test="updatedBy != null "> and updated_by = #{updatedBy}</if>
        </where>
    </select>
    
    <select id="selectMoocSmartCourseLessonEvaluationByEvaluationId" parameterType="Long" resultMap="MoocSmartCourseLessonEvaluationResult">
        <include refid="selectMoocSmartCourseLessonEvaluationVo"/>
        where evaluation_id = #{evaluationId}
    </select>

    <insert id="insertMoocSmartCourseLessonEvaluation" parameterType="MoocSmartCourseLessonEvaluation" useGeneratedKeys="true" keyProperty="evaluationId">
        insert into mooc_smart_course_lesson_evaluation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="lessonId != null">lesson_id,</if>
            <if test="studentId != null">student_id,</if>
            <if test="rating != null">rating,</if>
            <if test="comment != null">comment,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="lessonId != null">#{lessonId},</if>
            <if test="studentId != null">#{studentId},</if>
            <if test="rating != null">#{rating},</if>
            <if test="comment != null">#{comment},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMoocSmartCourseLessonEvaluation" parameterType="MoocSmartCourseLessonEvaluation">
        update mooc_smart_course_lesson_evaluation
        <trim prefix="SET" suffixOverrides=",">
            <if test="lessonId != null">lesson_id = #{lessonId},</if>
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="rating != null">rating = #{rating},</if>
            <if test="comment != null">comment = #{comment},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where evaluation_id = #{evaluationId}
    </update>

    <delete id="deleteMoocSmartCourseLessonEvaluationByEvaluationId" parameterType="Long">
        delete from mooc_smart_course_lesson_evaluation where evaluation_id = #{evaluationId}
    </delete>

    <delete id="deleteMoocSmartCourseLessonEvaluationByEvaluationIds" parameterType="String">
        delete from mooc_smart_course_lesson_evaluation where evaluation_id in 
        <foreach item="evaluationId" collection="array" open="(" separator="," close=")">
            #{evaluationId}
        </foreach>
    </delete>

    <select id="getByClassId" resultType="cn.dutp.edu.domain.MoocSmartCourseLessonEvaluation" parameterType="cn.dutp.edu.domain.MoocSmartCourseLessonEvaluation">
        select
            me.evaluation_id,
            du.real_name as studentName,
            dy.name as eduAcademicName,
            ds.school_name as academyName,
            dl.school_name as specialityName,
            ec.class_name as eduClassName,
            me.create_time,
            me.rating,
            me.comment
        from
            mooc_smart_course_lesson_evaluation me
        left join
            mooc_smart_course_lesson ml on ml.lesson_id = me.lesson_id
        left join
            dutp_user du on du.user_id = me.student_id
        left join
            edu_academic_year dy on dy.school_id = du.school_id and dy.del_flag = 0
        left join
            dutp_school ds on ds.school_id = du.academy_id
        left join
            dutp_school dl on dl.school_id = du.speciality_id
        left join
            edu_class_member em on em.user_id = du.user_id and em.del_flag = 0
        left join
            edu_class ec on ec.edu_class_id = em.edu_class_id and ec.del_flag = 0
        where
            me.del_flag = 0 and ml.class_id = #{classId}
    </select>
</mapper>
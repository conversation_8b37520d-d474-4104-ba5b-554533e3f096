<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocSmartCourseClassMemberScoreMapper">
    
    <resultMap type="MoocSmartCourseClassMemberScore" id="MoocSmartCourseClassMemberScoreResult">
        <result property="memberScoreId"    column="member_score_id"    />
        <result property="classMemberId"    column="class_member_id"    />
        <result property="totalScore"    column="total_score"    />
        <result property="coursewareScore"    column="courseware_score"    />
        <result property="classroomQuestionScore"    column="classroom_question_score"    />
        <result property="homeworkScore"    column="homework_score"    />
        <result property="examScore"    column="exam_score"    />
        <result property="notes"    column="notes"    />
        <result property="classId"    column="class_id"    />
        <result property="userId"    column="user_id"    />
        <result property="createdBy"    column="created_by"    />
        <result property="updatedBy"    column="updated_by"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMoocSmartCourseClassMemberScoreVo">
        select member_score_id, class_member_id, total_score, courseware_score, classroom_question_score, homework_score, exam_score, notes, class_id, user_id, created_by, updated_by, del_flag, create_time, update_time from mooc_smart_course_class_member_score
    </sql>

    <select id="selectMoocSmartCourseClassMemberScoreList" parameterType="MoocSmartCourseClassMemberScore" resultMap="MoocSmartCourseClassMemberScoreResult">
        <include refid="selectMoocSmartCourseClassMemberScoreVo"/>
        <where>  
            <if test="classMemberId != null "> and class_member_id = #{classMemberId}</if>
            <if test="totalScore != null "> and total_score = #{totalScore}</if>
            <if test="coursewareScore != null "> and courseware_score = #{coursewareScore}</if>
            <if test="classroomQuestionScore != null "> and classroom_question_score = #{classroomQuestionScore}</if>
            <if test="homeworkScore != null "> and homework_score = #{homeworkScore}</if>
            <if test="examScore != null "> and exam_score = #{examScore}</if>
            <if test="notes != null  and notes != ''"> and notes = #{notes}</if>
            <if test="classId != null "> and class_id = #{classId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="createdBy != null "> and created_by = #{createdBy}</if>
            <if test="updatedBy != null "> and updated_by = #{updatedBy}</if>
        </where>
    </select>
    
    <select id="selectMoocSmartCourseClassMemberScoreByMemberScoreId" parameterType="Long" resultMap="MoocSmartCourseClassMemberScoreResult">
        <include refid="selectMoocSmartCourseClassMemberScoreVo"/>
        where member_score_id = #{memberScoreId}
    </select>

    <insert id="insertMoocSmartCourseClassMemberScore" parameterType="MoocSmartCourseClassMemberScore" useGeneratedKeys="true" keyProperty="memberScoreId">
        insert into mooc_smart_course_class_member_score
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="classMemberId != null">class_member_id,</if>
            <if test="totalScore != null">total_score,</if>
            <if test="coursewareScore != null">courseware_score,</if>
            <if test="classroomQuestionScore != null">classroom_question_score,</if>
            <if test="homeworkScore != null">homework_score,</if>
            <if test="examScore != null">exam_score,</if>
            <if test="notes != null">notes,</if>
            <if test="classId != null">class_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="classMemberId != null">#{classMemberId},</if>
            <if test="totalScore != null">#{totalScore},</if>
            <if test="coursewareScore != null">#{coursewareScore},</if>
            <if test="classroomQuestionScore != null">#{classroomQuestionScore},</if>
            <if test="homeworkScore != null">#{homeworkScore},</if>
            <if test="examScore != null">#{examScore},</if>
            <if test="notes != null">#{notes},</if>
            <if test="classId != null">#{classId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMoocSmartCourseClassMemberScore" parameterType="MoocSmartCourseClassMemberScore">
        update mooc_smart_course_class_member_score
        <trim prefix="SET" suffixOverrides=",">
            <if test="classMemberId != null">class_member_id = #{classMemberId},</if>
            <if test="totalScore != null">total_score = #{totalScore},</if>
            <if test="coursewareScore != null">courseware_score = #{coursewareScore},</if>
            <if test="classroomQuestionScore != null">classroom_question_score = #{classroomQuestionScore},</if>
            <if test="homeworkScore != null">homework_score = #{homeworkScore},</if>
            <if test="examScore != null">exam_score = #{examScore},</if>
            <if test="notes != null">notes = #{notes},</if>
            <if test="classId != null">class_id = #{classId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where member_score_id = #{memberScoreId}
    </update>

    <delete id="deleteMoocSmartCourseClassMemberScoreByMemberScoreId" parameterType="Long">
        delete from mooc_smart_course_class_member_score where member_score_id = #{memberScoreId}
    </delete>

    <delete id="deleteMoocSmartCourseClassMemberScoreByMemberScoreIds" parameterType="String">
        delete from mooc_smart_course_class_member_score where member_score_id in 
        <foreach item="memberScoreId" collection="array" open="(" separator="," close=")">
            #{memberScoreId}
        </foreach>
    </delete>

    <select id="getListByClassId" parameterType="cn.dutp.edu.domain.MoocSmartCourseClassMemberScore" resultType="cn.dutp.edu.domain.MoocSmartCourseClassMemberScore">
        select
            u.real_name,
            u.user_no,
            ms.total_score,
            ms.courseware_score,
            ms.classroom_question_score,
            ms.homework_score,
            ms.exam_score,
            ms.notes,
            msc.classroom_question_weight,
            msc.homework_weight,
            msc.exam_weight
        from
            mooc_smart_course_class_member cm
        left join
            mooc_smart_course_class_member_score ms on ms.class_member_id = cm.class_member_id and ms.del_flag = 0 and ms.class_id = cm.class_id
        left join
            dutp_user u on u.user_id = cm.user_id and u.del_flag = 0
        left join
            mooc_smart_course_class scc on scc.class_id = cm.class_id and scc.del_flag = 0
        left join
            mooc_smart_course msc on msc.course_id = scc.course_id and msc.del_flag = 0
        where
            cm.class_id = #{classId} and cm.del_flag = 0
            <if test="realName != null and realName != ''">
                AND u.real_name LIKE CONCAT('%', #{realName}, '%') or u.user_no LIKE CONCAT('%', #{realName}, '%')
            </if>
    </select>
</mapper>
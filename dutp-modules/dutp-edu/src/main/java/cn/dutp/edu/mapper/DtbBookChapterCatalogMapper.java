package cn.dutp.edu.mapper;

import cn.dutp.book.domain.DtbBookChapterCatalog;
import cn.dutp.edu.domain.vo.DtbBookChapter;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
/**
 * 教材章节目录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-30
 */
@Repository
public interface DtbBookChapterCatalogMapper extends BaseMapper<DtbBookChapterCatalog>
{

    List<DtbBookChapterCatalog> getCatalogByBookId(@Param("ids") List<Long> ids);

    List<DtbBookChapter> getChapterCatalogByBookId(@Param("bookId") Long bookId, @Param("currentVersionId") Long currentVersionId);
}

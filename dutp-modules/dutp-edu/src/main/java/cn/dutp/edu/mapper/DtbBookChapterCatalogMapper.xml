<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.DtbBookChapterCatalogMapper">
    <select id="getCatalogByBookId" resultType="cn.dutp.book.domain.DtbBookChapterCatalog">
        select
            catalog_id,
            title,
            parent_id,
            chapter_id
        from
            dtb_book_chapter_catalog
        where
            chapter_id in
            <foreach item="chapterId" collection="ids" open="(" separator="," close=")">
                #{chapterId}
            </foreach>
    </select>

    <select id="getChapterCatalogByBookId" resultType="cn.dutp.edu.domain.vo.DtbBookChapter">
        select
            chapter_id,
            chapter_name
        from
            dtb_book_chapter
        where
            book_id = #{bookId} and version_id = #{currentVersionId}
        order by sort
    </select>

</mapper>
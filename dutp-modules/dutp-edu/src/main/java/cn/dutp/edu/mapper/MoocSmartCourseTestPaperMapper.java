package cn.dutp.edu.mapper;

import java.util.List;
import org.springframework.stereotype.Repository;
import cn.dutp.edu.domain.MoocSmartCourseTestPaper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
/**
 * 互动课堂试卷Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Repository
public interface MoocSmartCourseTestPaperMapper extends BaseMapper<MoocSmartCourseTestPaper>
{
    /**
     * 将试卷移入回收站
     * @param paperIds 试卷ID列表
     * @param username 操作用户名
     * @return 结果
     */
    int moveToRecycleBin(@Param("paperIds") List<Long> paperIds, @Param("username") String username);

    /**
     * 从回收站恢复试卷
     * @param paperIds 试卷ID列表
     * @param username 操作用户名
     * @return 结果
     */
    int restoreFromRecycleBin(@Param("paperIds") List<Long> paperIds, @Param("username") String username);

    /**
     * 查询回收站试卷列表
     * @param paper 查询条件
     * @return 回收站试卷列表
     */
    List<MoocSmartCourseTestPaper> selectRecycleBinList(MoocSmartCourseTestPaper paper);
}

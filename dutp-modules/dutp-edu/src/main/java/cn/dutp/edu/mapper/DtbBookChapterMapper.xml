<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.DtbBookChapterMapper">

    <select id="queryBookChapterDataList" resultType="cn.dutp.edu.domain.vo.DtbBookChapter">
        SELECT
            c.chapter_name,
            c.chapter_object_id,
            c.book_id,
            c.sort,
            c.version_id,
            c.chapter_total_page,
            c.free,
            c.chapter_status,
            c.back_apply,
            c.frozen,
            c.state,
            c.complete_rate,
            c.del_user_id,
            c.template_id,
            c.remark,
            c.chapter_id
        FROM
            dtb_book_chapter c
                INNER JOIN dtb_book b ON c.book_id = b.book_id
                AND b.current_version_id = c.version_id
        WHERE
            c.book_id = #{bookId} and b.current_version_id = #{currentVersionId}
          AND c.del_flag = '0'
        ORDER BY
            c.sort
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocUserResourceMapper">
    
    <resultMap type="MoocUserResource" id="MoocUserResourceResult">
        <result property="resourceId"    column="resource_id"    />
        <result property="userId"    column="user_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileType"    column="file_type"    />
        <result property="fileSize"    column="file_size"    />
        <result property="questionId"    column="question_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="folderId"    column="folder_id"    />
    </resultMap>

    <sql id="selectMoocUserResourceVo">
        select resource_id, user_id, file_name, file_url, file_type, file_size, question_id, create_by, create_time, update_by, update_time, del_flag, folder_id from mooc_user_resource
    </sql>

    <select id="selectMoocUserResourceList" parameterType="MoocUserResource" resultMap="MoocUserResourceResult">
        <include refid="selectMoocUserResourceVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="fileType != null  and fileType != ''"> and file_type = #{fileType}</if>
            <if test="fileSize != null "> and file_size = #{fileSize}</if>
            <if test="questionId != null "> and question_id = #{questionId}</if>
            <if test="folderId != null "> and folder_id = #{folderId}</if>
        </where>
    </select>
    
    <select id="selectMoocUserResourceByResourceId" parameterType="Long" resultMap="MoocUserResourceResult">
        <include refid="selectMoocUserResourceVo"/>
        where resource_id = #{resourceId}
    </select>

    <insert id="insertMoocUserResource" parameterType="MoocUserResource" useGeneratedKeys="true" keyProperty="resourceId">
        insert into mooc_user_resource
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="fileName != null">file_name,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="fileType != null and fileType != ''">file_type,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="questionId != null">question_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="folderId != null">folder_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="fileType != null and fileType != ''">#{fileType},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="questionId != null">#{questionId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="folderId != null">#{folderId},</if>
         </trim>
    </insert>

    <update id="updateMoocUserResource" parameterType="MoocUserResource">
        update mooc_user_resource
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="fileType != null and fileType != ''">file_type = #{fileType},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="questionId != null">question_id = #{questionId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="folderId != null">folder_id = #{folderId},</if>
        </trim>
        where resource_id = #{resourceId}
    </update>

    <delete id="deleteMoocUserResourceByResourceId" parameterType="Long">
        delete from mooc_user_resource where resource_id = #{resourceId}
    </delete>

    <delete id="deleteMoocUserResourceByResourceIds" parameterType="String">
        delete from mooc_user_resource where resource_id in 
        <foreach item="resourceId" collection="array" open="(" separator="," close=")">
            #{resourceId}
        </foreach>
    </delete>
</mapper>
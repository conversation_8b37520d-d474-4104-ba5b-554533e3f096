package cn.dutp.edu.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.dutp.edu.mapper.MoocSmartCourseHomeworkSubmissionMapper;
import cn.dutp.edu.domain.MoocSmartCourseHomeworkSubmission;
import cn.dutp.edu.domain.MoocSmartCourseHomeworkSubmissionFile;
import cn.dutp.edu.service.IMoocSmartCourseHomeworkSubmissionService;
import cn.dutp.edu.service.IMoocSmartCourseHomeworkSubmissionFileService;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.hutool.core.collection.CollUtil;

/**
 * 学生作业提交记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Service
public class MoocSmartCourseHomeworkSubmissionServiceImpl extends ServiceImpl<MoocSmartCourseHomeworkSubmissionMapper, MoocSmartCourseHomeworkSubmission> implements IMoocSmartCourseHomeworkSubmissionService
{
    @Autowired
    private MoocSmartCourseHomeworkSubmissionMapper moocSmartCourseHomeworkSubmissionMapper;

    @Autowired
    private IMoocSmartCourseHomeworkSubmissionFileService moocSmartCourseHomeworkSubmissionFileService;

    /**
     * 查询学生作业提交记录
     *
     * @param submissionId 学生作业提交记录主键
     * @return 学生作业提交记录
     */
    @Override
    public MoocSmartCourseHomeworkSubmission selectMoocSmartCourseHomeworkSubmissionBySubmissionId(Long submissionId)
    {
        MoocSmartCourseHomeworkSubmission submission = this.getById(submissionId);
        if (submission != null) {
            LambdaQueryWrapper<MoocSmartCourseHomeworkSubmissionFile> fileWrapper = new LambdaQueryWrapper<>();
            fileWrapper.eq(MoocSmartCourseHomeworkSubmissionFile::getSubmissionId, submission.getSubmissionId());
            List<MoocSmartCourseHomeworkSubmissionFile> files = moocSmartCourseHomeworkSubmissionFileService.list(fileWrapper);
            submission.setFiles(files);
        }
        return submission;
    }

    @Override
    public MoocSmartCourseHomeworkSubmission selectMoocSmartCourseHomeworkSubmissionByAssignmentIdAndStudentId(Long assignmentId, Long studentId) {
        LambdaQueryWrapper<MoocSmartCourseHomeworkSubmission> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MoocSmartCourseHomeworkSubmission::getAssignmentId, assignmentId);
        queryWrapper.eq(MoocSmartCourseHomeworkSubmission::getStudentId, studentId);
        MoocSmartCourseHomeworkSubmission submission = this.getOne(queryWrapper);

        if (submission != null) {
            LambdaQueryWrapper<MoocSmartCourseHomeworkSubmissionFile> fileWrapper = new LambdaQueryWrapper<>();
            fileWrapper.eq(MoocSmartCourseHomeworkSubmissionFile::getSubmissionId, submission.getSubmissionId());
            List<MoocSmartCourseHomeworkSubmissionFile> files = moocSmartCourseHomeworkSubmissionFileService.list(fileWrapper);
            submission.setFiles(files);
        }
        return submission;
    }

    /**
     * 查询学生作业提交记录列表
     *
     * @param moocSmartCourseHomeworkSubmission 学生作业提交记录
     * @return 学生作业提交记录
     */
    @Override
    public List<MoocSmartCourseHomeworkSubmission> selectMoocSmartCourseHomeworkSubmissionList(MoocSmartCourseHomeworkSubmission moocSmartCourseHomeworkSubmission)
    {
        LambdaQueryWrapper<MoocSmartCourseHomeworkSubmission> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(moocSmartCourseHomeworkSubmission.getAssignmentId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseHomeworkSubmission::getAssignmentId
                ,moocSmartCourseHomeworkSubmission.getAssignmentId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseHomeworkSubmission.getStudentId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseHomeworkSubmission::getStudentId
                ,moocSmartCourseHomeworkSubmission.getStudentId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseHomeworkSubmission.getSubmitContent())) {
                lambdaQueryWrapper.eq(MoocSmartCourseHomeworkSubmission::getSubmitContent
                ,moocSmartCourseHomeworkSubmission.getSubmitContent());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseHomeworkSubmission.getSubmitTime())) {
                lambdaQueryWrapper.eq(MoocSmartCourseHomeworkSubmission::getSubmitTime
                ,moocSmartCourseHomeworkSubmission.getSubmitTime());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增学生作业提交记录
     *
     * @param moocSmartCourseHomeworkSubmission 学生作业提交记录
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertMoocSmartCourseHomeworkSubmission(MoocSmartCourseHomeworkSubmission moocSmartCourseHomeworkSubmission)
    {
        moocSmartCourseHomeworkSubmission.setStudentId(SecurityUtils.getUserId());
        boolean result = this.save(moocSmartCourseHomeworkSubmission);
        if (result && CollUtil.isNotEmpty(moocSmartCourseHomeworkSubmission.getFiles())) {
            List<MoocSmartCourseHomeworkSubmissionFile> files = moocSmartCourseHomeworkSubmission.getFiles();
            files.forEach(file -> file.setSubmissionId(moocSmartCourseHomeworkSubmission.getSubmissionId()));
            moocSmartCourseHomeworkSubmissionFileService.saveBatch(files);
        }
        return result;
    }

    /**
     * 修改学生作业提交记录
     *
     * @param moocSmartCourseHomeworkSubmission 学生作业提交记录
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMoocSmartCourseHomeworkSubmission(MoocSmartCourseHomeworkSubmission moocSmartCourseHomeworkSubmission)
    {
        boolean result = this.updateById(moocSmartCourseHomeworkSubmission);
        if (result) {
            // 删除旧文件
            LambdaQueryWrapper<MoocSmartCourseHomeworkSubmissionFile> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(MoocSmartCourseHomeworkSubmissionFile::getSubmissionId, moocSmartCourseHomeworkSubmission.getSubmissionId());
            moocSmartCourseHomeworkSubmissionFileService.remove(deleteWrapper);

            // 添加新文件
            if (CollUtil.isNotEmpty(moocSmartCourseHomeworkSubmission.getFiles())) {
                List<MoocSmartCourseHomeworkSubmissionFile> files = moocSmartCourseHomeworkSubmission.getFiles();
                files.forEach(file -> file.setSubmissionId(moocSmartCourseHomeworkSubmission.getSubmissionId()));
                moocSmartCourseHomeworkSubmissionFileService.saveBatch(files);
            }
        }
        return result;
    }

    /**
     * 批量删除学生作业提交记录
     *
     * @param submissionIds 需要删除的学生作业提交记录主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocSmartCourseHomeworkSubmissionBySubmissionIds(List<Long> submissionIds)
    {
        return this.removeByIds(submissionIds);
    }

}

package cn.dutp.edu.service.impl;

import cn.dutp.domain.MoocCoursewareDesignFolder;
import cn.dutp.edu.mapper.MoocCoursewareDesignFolderMapper;
import cn.dutp.edu.service.IMoocCoursewareDesignFolderService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 公开课课件设计目录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Service
public class MoocCoursewareDesignFolderServiceImpl extends ServiceImpl<MoocCoursewareDesignFolderMapper, MoocCoursewareDesignFolder> implements IMoocCoursewareDesignFolderService {
    @Autowired
    private MoocCoursewareDesignFolderMapper moocCoursewareDesignFolderMapper;

    /**
     * 查询公开课课件设计目录
     *
     * @param chapterId 公开课课件设计目录主键
     * @return 公开课课件设计目录
     */
    @Override
    public MoocCoursewareDesignFolder selectMoocCoursewareDesignFolderByChapterId(Long chapterId) {
        return this.getById(chapterId);
    }

    /**
     * 查询公开课课件设计目录列表
     *
     * @param moocCoursewareDesignFolder 公开课课件设计目录
     * @return 公开课课件设计目录
     */
    @Override
    public List<MoocCoursewareDesignFolder> selectMoocCoursewareDesignFolderList(MoocCoursewareDesignFolder moocCoursewareDesignFolder) {
        LambdaQueryWrapper<MoocCoursewareDesignFolder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotEmpty(moocCoursewareDesignFolder.getChapterName())) {
            lambdaQueryWrapper.like(MoocCoursewareDesignFolder::getChapterName
                    , moocCoursewareDesignFolder.getChapterName());
        }
        if (ObjectUtil.isNotEmpty(moocCoursewareDesignFolder.getCoursewareDesignId())) {
            lambdaQueryWrapper.eq(MoocCoursewareDesignFolder::getCoursewareDesignId
                    , moocCoursewareDesignFolder.getCoursewareDesignId());
        }
        if (ObjectUtil.isNotEmpty(moocCoursewareDesignFolder.getSort())) {
            lambdaQueryWrapper.eq(MoocCoursewareDesignFolder::getSort
                    , moocCoursewareDesignFolder.getSort());
        }
        if (ObjectUtil.isNotEmpty(moocCoursewareDesignFolder.getParentId())) {
            lambdaQueryWrapper.eq(MoocCoursewareDesignFolder::getParentId
                    , moocCoursewareDesignFolder.getParentId());
        }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增公开课课件设计目录
     *
     * @param moocCoursewareDesignFolder 公开课课件设计目录
     * @return 结果
     */
    @Override
    public boolean insertMoocCoursewareDesignFolder(MoocCoursewareDesignFolder moocCoursewareDesignFolder) {
        return this.save(moocCoursewareDesignFolder);
    }

    /**
     * 修改公开课课件设计目录
     *
     * @param moocCoursewareDesignFolder 公开课课件设计目录
     * @return 结果
     */
    @Override
    public boolean updateMoocCoursewareDesignFolder(MoocCoursewareDesignFolder moocCoursewareDesignFolder) {
        return this.updateById(moocCoursewareDesignFolder);
    }

    /**
     * 批量删除公开课课件设计目录
     *
     * @param chapterIds 需要删除的公开课课件设计目录主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocCoursewareDesignFolderByChapterIds(List<Long> chapterIds) {
        return this.removeByIds(chapterIds);
    }

}

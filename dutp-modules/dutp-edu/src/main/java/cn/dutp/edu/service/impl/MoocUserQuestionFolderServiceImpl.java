package cn.dutp.edu.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.edu.mapper.MoocUserQuestionFolderMapper;
import cn.dutp.domain.MoocUserQuestionFolder;
import cn.dutp.edu.service.IMoocUserQuestionFolderService;
import cn.hutool.core.util.ObjectUtil;
import cn.dutp.common.core.utils.TreeUtil;

/**
 * 题库目录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Service
public class MoocUserQuestionFolderServiceImpl extends ServiceImpl<MoocUserQuestionFolderMapper, MoocUserQuestionFolder> implements IMoocUserQuestionFolderService
{
    @Autowired
    private MoocUserQuestionFolderMapper moocUserQuestionFolderMapper;

    /**
     * 查询题库目录
     *
     * @param folderId 题库目录主键
     * @return 题库目录
     */
    @Override
    public MoocUserQuestionFolder selectMoocUserQuestionFolderByFolderId(Long folderId)
    {
        return this.getById(folderId);
    }

    /**
     * 查询题库目录列表
     *
     * @param moocUserQuestionFolder 题库目录
     * @return 题库目录
     */
    @Override
    public List<MoocUserQuestionFolder> selectMoocUserQuestionFolderList(MoocUserQuestionFolder moocUserQuestionFolder)
    {
        LambdaQueryWrapper<MoocUserQuestionFolder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(moocUserQuestionFolder.getFolderName())) {
                lambdaQueryWrapper.like(MoocUserQuestionFolder::getFolderName
                ,moocUserQuestionFolder.getFolderName());
            }
                if(ObjectUtil.isNotEmpty(moocUserQuestionFolder.getUserId())) {
                lambdaQueryWrapper.eq(MoocUserQuestionFolder::getUserId
                ,moocUserQuestionFolder.getUserId());
            }
                if(ObjectUtil.isNotEmpty(moocUserQuestionFolder.getParentId())) {
                lambdaQueryWrapper.eq(MoocUserQuestionFolder::getParentId
                ,moocUserQuestionFolder.getParentId());
            }
                if(ObjectUtil.isNotEmpty(moocUserQuestionFolder.getDefaultType())) {
                lambdaQueryWrapper.eq(MoocUserQuestionFolder::getDefaultType
                ,moocUserQuestionFolder.getDefaultType());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增题库目录
     *
     * @param moocUserQuestionFolder 题库目录
     * @return 结果
     */
    @Override
    public boolean insertMoocUserQuestionFolder(MoocUserQuestionFolder moocUserQuestionFolder)
    {
        return this.save(moocUserQuestionFolder);
    }

    /**
     * 修改题库目录
     *
     * @param moocUserQuestionFolder 题库目录
     * @return 结果
     */
    @Override
    public boolean updateMoocUserQuestionFolder(MoocUserQuestionFolder moocUserQuestionFolder)
    {
        return this.updateById(moocUserQuestionFolder);
    }

    /**
     * 批量删除题库目录
     *
     * @param folderIds 需要删除的题库目录主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocUserQuestionFolderByFolderIds(List<Long> folderIds)
    {
        return this.removeByIds(folderIds);
    }

    /**
     * 查询题库目录树
     *
     * @param moocUserQuestionFolder 题库目录
     * @return 题库目录树结构
     */
    @Override
    public List<MoocUserQuestionFolder> selectMoocUserQuestionFolderTree(MoocUserQuestionFolder moocUserQuestionFolder) {
        List<MoocUserQuestionFolder> folderList = selectMoocUserQuestionFolderList(moocUserQuestionFolder);
        
        return TreeUtil.<MoocUserQuestionFolder>makeTree(
            folderList, 
            // 根节点条件：parentId为0或null
            item -> item.getParentId() == null || item.getParentId() == 0L,
            // 父子关系判断
            (parent, child) -> parent.getFolderId().equals(child.getParentId()),
            // 设置子节点
            (parent, children) -> parent.setChildren(children)
        );
    }

}

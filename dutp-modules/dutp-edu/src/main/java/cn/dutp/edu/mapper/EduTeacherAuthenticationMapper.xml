<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.EduTeacherAuthenticationMapper">

    <resultMap type="EduTeacherAuthentication" id="EduTeacherAuthenticationResult">
        <result property="authId"    column="auth_id"    />
        <result property="userId"    column="user_Id"    />
        <result property="userName"    column="user_name"    />
        <result property="nickName"    column="nick_name"    />
        <result property="realName"    column="real_name"    />
        <result property="phone"    column="phone"    />
        <result property="academyId"    column="academy_id"    />
        <result property="workNo"    column="work_no"    />
        <result property="email"    column="email"    />
        <result property="schoolId"    column="school_id"    />
        <result property="specialityId"    column="speciality_id"    />
        <result property="authState"    column="auth_state"    />
        <result property="positionName"    column="position_name"    />
        <result property="titleName"    column="title_name"    />
        <result property="dutyName"    column="duty_name"    />
        <result property="courseName"    column="course_name"    />
        <result property="classmateTotal"    column="classmate_total"    />
        <result property="termBegin"    column="term_begin"    />
        <result property="termEnd"    column="term_end"    />
        <result property="bookPreference"    column="book_preference"    />
        <result property="authenticationImage"    column="authentication_image"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="schoolName"    column="school_name"    />
        <result property="specialityName" column="speciality_name"/>
        <result property="rejectReason" column="reject_reason"/>
    </resultMap>

    <sql id="selectEduTeacherAuthenticationVo">
        select eta.auth_id, eta.user_Id, eta.user_name, eta.nick_name, eta.work_no, eta.email, eta.real_name, eta.phone, eta.speciality_id, eta.academy_id, school.school_id, speciality.school_id, eta.auth_state, eta.position_name, eta.title_name, eta.duty_name, eta.course_name, eta.classmate_total, eta.term_begin, eta.term_end, eta.book_preference, eta.authentication_image, eta.del_flag, eta.create_by, eta.create_time, eta.update_by, eta.update_time,school.school_name,college.school_name as college_name,speciality.school_name as speciality_name, eta.reject_reason
        from edu_teacher_authentication eta
                 left join dutp_school school on eta.school_id = school.school_id
                 left join dutp_school college on eta.academy_id = college.school_id
                 left join dutp_school speciality on eta.speciality_id = speciality.school_id
    </sql>

    <select id="selectEduTeacherAuthenticationList" parameterType="EduTeacherAuthentication" resultMap="EduTeacherAuthenticationResult">
        <include refid="selectEduTeacherAuthenticationVo"/>
        <where>
            <if test="userName != null  and userName != ''"> and user_name = #{userName}</if>
             <if test="realName != null  and realName != ''"> and real_name like concat('%', #{realName}, '%')</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%') or work_no = #{nickName}</if>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="workNo != null "> and work_no = #{workNo}</if>
            <if test="email != null  and email != ''"> and email = #{email}</if>
            <if test="schoolId != null "> and school.school_id = #{schoolId}</if>

            <if test="academyId != null "> and eta.academy_id = #{academyId}</if>
            <if test="specialityId != null "> and eta.speciality_id = #{specialityId}</if>
            <if test="authState != null "> and auth_state = #{authState}</if>
            <if test="positionName != null  and positionName != ''"> and position_name like concat('%', #{positionName}, '%')</if>
            <if test="titleName != null  and titleName != ''"> and title_name like concat('%', #{titleName}, '%')</if>
            <if test="dutyName != null  and dutyName != ''"> and duty_name like concat('%', #{dutyName}, '%')</if>
            <if test="courseName != null  and courseName != ''"> and course_name like concat('%', #{courseName}, '%')</if>
            <if test="classmateTotal != null "> and classmate_total = #{classmateTotal}</if>
            <if test="termBegin != null "> and term_begin = #{termBegin}</if>
            <if test="termEnd != null "> and term_end = #{termEnd}</if>
            <if test="bookPreference != null  and bookPreference != ''"> and book_preference = #{bookPreference}</if>
            <if test="authenticationImage != null  and authenticationImage != ''"> and authentication_image = #{authenticationImage}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectEduTeacherAuthenticationById" parameterType="Long" resultMap="EduTeacherAuthenticationResult">
        <include refid="selectEduTeacherAuthenticationVo"/>
        where auth_id = #{id}
        order by create_time desc
    </select>

    <insert id="insertEduTeacherAuthentication" parameterType="EduTeacherAuthentication" useGeneratedKeys="true" keyProperty="Id">
        insert into edu_teacher_authentication
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="userId != null and userName != ''">user_id,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="realName != null">real_name,</if>
            <if test="phone != null">phone,</if>
            <if test="academyId != null">academy_id,</if>
            <if test="workNo != null">work_no,</if>
            <if test="email != null">email,</if>
            <if test="schoolId != null">school_id,</if>
            <if test="specialityId != null">speciality_id,</if>
            <if test="authState != null">auth_state,</if>
            <if test="positionName != null">position_name,</if>
            <if test="titleName != null">title_name,</if>
            <if test="dutyName != null">duty_name,</if>
            <if test="courseName != null">course_name,</if>
            <if test="classmateTotal != null">classmate_total,</if>
            <if test="termBegin != null">term_begin,</if>
            <if test="termEnd != null">term_end,</if>
            <if test="bookPreference != null">book_preference,</if>
            <if test="authenticationImage != null">authentication_image,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null and userName != ''">#{userId},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="realName != null">real_name,</if>
            <if test="phone != null">phone,</if>
            <if test="academyId != null">academy_id,</if>
            <if test="workNo != null">#{workNo},</if>
            <if test="email != null">#{email},</if>
            <if test="schoolId != null">#{schoolId},</if>
            <if test="specialityId != null">#{specialityId},</if>
            <if test="authState != null">#{authState},</if>
            <if test="positionName != null">#{positionName},</if>
            <if test="titleName != null">#{titleName},</if>
            <if test="dutyName != null">#{dutyName},</if>
            <if test="courseName != null">#{courseName},</if>
            <if test="classmateTotal != null">#{classmateTotal},</if>
            <if test="termBegin != null">#{termBegin},</if>
            <if test="termEnd != null">#{termEnd},</if>
            <if test="bookPreference != null">#{bookPreference},</if>
            <if test="authenticationImage != null">#{authenticationImage},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateEduTeacherAuthentication" parameterType="EduTeacherAuthentication">
        update edu_teacher_authentication
        <trim prefix="SET" suffixOverrides=",">
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="userId != null and userName != ''">user_id,</if>
            <if test="realName != null">real_name,</if>
            <if test="phone != null">phone,</if>
            <if test="academyId != null">academy_id,</if>
            <if test="workNo != null">work_no = #{workNo},</if>
            <if test="email != null">email = #{email},</if>
            <if test="schoolId != null">school_id = #{schoolId},</if>
            <if test="specialityId != null">speciality_id = #{specialityId},</if>
            <if test="authState != null">auth_state = #{authState},</if>
            <if test="positionName != null">position_name = #{positionName},</if>
            <if test="titleName != null">title_name = #{titleName},</if>
            <if test="dutyName != null">duty_name = #{dutyName},</if>
            <if test="courseName != null">course_name = #{courseName},</if>
            <if test="classmateTotal != null">classmate_total = #{classmateTotal},</if>
            <if test="termBegin != null">term_begin = #{termBegin},</if>
            <if test="termEnd != null">term_end = #{termEnd},</if>
            <if test="bookPreference != null">book_preference = #{bookPreference},</if>
            <if test="authenticationImage != null">authentication_image = #{authenticationImage},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where Id = #{Id}
    </update>

    <delete id="deleteEduTeacherAuthenticationById" parameterType="Long">
        delete from edu_teacher_authentication where auth_id = #{authId}
    </delete>

    <delete id="deleteEduTeacherAuthenticationByIds" parameterType="String">
        delete from edu_teacher_authentication where Id in
        <foreach item="Id" collection="array" open="(" separator="," close=")">
            #{Id}
        </foreach>
    </delete>
    <select id="getSuperUser"  resultType="Long">
        SELECT
            sys_user_role.user_id
        FROM
            sys_user_role
                LEFT JOIN
            sys_role
            ON
                sys_user_role.role_id = sys_role.role_id
        WHERE
            sys_role.role_key = 'super'
    </select>
</mapper>
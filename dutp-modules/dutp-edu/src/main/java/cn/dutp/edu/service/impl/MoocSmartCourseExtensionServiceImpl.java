package cn.dutp.edu.service.impl;

import java.util.List;

import cn.dutp.edu.domain.MoocSmartCourseBrainstorm;
import cn.dutp.edu.domain.MoocSmartCourseExtensionRecord;
import cn.dutp.edu.mapper.MoocSmartCourseBrainstormMapper;
import cn.dutp.edu.mapper.MoocSmartCourseExtensionRecordMapper;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.edu.mapper.MoocSmartCourseExtensionMapper;
import cn.dutp.edu.domain.MoocSmartCourseExtension;
import cn.dutp.edu.service.IMoocSmartCourseExtensionService;

/**
 * 互动课堂的拓展内容Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class MoocSmartCourseExtensionServiceImpl extends ServiceImpl<MoocSmartCourseExtensionMapper, MoocSmartCourseExtension> implements IMoocSmartCourseExtensionService
{
    @Autowired
    private MoocSmartCourseExtensionMapper moocSmartCourseExtensionMapper;

    @Autowired
    private MoocSmartCourseBrainstormMapper moocSmartCourseBrainstormMapper;

    @Autowired
    private MoocSmartCourseExtensionRecordMapper moocSmartCourseExtensionRecordMapper;

    /**
     * 查询互动课堂的拓展内容
     *
     * @param extensionId 互动课堂的拓展内容主键
     * @return 互动课堂的拓展内容
     */
    @Override
    public MoocSmartCourseExtension selectMoocSmartCourseExtensionByExtensionId(Long extensionId)
    {
        MoocSmartCourseExtension res = this.getById(extensionId);
        res.setFileList(moocSmartCourseBrainstormMapper.getDutpUserCommonFile(extensionId));

        // 查询学生记录
        List<MoocSmartCourseExtensionRecord> records = moocSmartCourseExtensionRecordMapper.selectRecords(extensionId);
        return res;
    }

    /**
     * 查询互动课堂的拓展内容列表
     *
     * @param moocSmartCourseExtension 互动课堂的拓展内容
     * @return 互动课堂的拓展内容
     */
    @Override
    public List<MoocSmartCourseExtension> selectMoocSmartCourseExtensionList(MoocSmartCourseExtension moocSmartCourseExtension)
    {
        LambdaQueryWrapper<MoocSmartCourseExtension> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(moocSmartCourseExtension.getCourseId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseExtension::getCourseId
                ,moocSmartCourseExtension.getCourseId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseExtension.getExtensionName())) {
                lambdaQueryWrapper.like(MoocSmartCourseExtension::getExtensionName
                ,moocSmartCourseExtension.getExtensionName());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseExtension.getExtensionContent())) {
                lambdaQueryWrapper.eq(MoocSmartCourseExtension::getExtensionContent
                ,moocSmartCourseExtension.getExtensionContent());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseExtension.getExtensionCreatorId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseExtension::getExtensionCreatorId
                ,moocSmartCourseExtension.getExtensionCreatorId());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增互动课堂的拓展内容
     *
     * @param moocSmartCourseExtension 互动课堂的拓展内容
     * @return 结果
     */
    @Override
    public boolean insertMoocSmartCourseExtension(MoocSmartCourseExtension moocSmartCourseExtension)
    {
        return this.save(moocSmartCourseExtension);
    }

    /**
     * 修改互动课堂的拓展内容
     *
     * @param moocSmartCourseExtension 互动课堂的拓展内容
     * @return 结果
     */
    @Override
    public boolean updateMoocSmartCourseExtension(MoocSmartCourseExtension moocSmartCourseExtension)
    {
        return this.updateById(moocSmartCourseExtension);
    }

    /**
     * 批量删除互动课堂的拓展内容
     *
     * @param extensionIds 需要删除的互动课堂的拓展内容主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocSmartCourseExtensionByExtensionIds(List<Long> extensionIds)
    {
        return this.removeByIds(extensionIds);
    }

}

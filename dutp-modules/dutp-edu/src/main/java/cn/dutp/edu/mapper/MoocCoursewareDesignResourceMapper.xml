<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocCoursewareDesignResourceMapper">
    
    <resultMap type="MoocCoursewareDesignResource" id="MoocCoursewareDesignResourceResult">
        <result property="chapterResourceId"    column="chapter_resource_id"    />
        <result property="chapterId"    column="chapter_id"    />
        <result property="coursewareDesignId"    column="courseware_design_id"    />
        <result property="resourceTitle"    column="resource_title"    />
        <result property="resourceContent"    column="resource_content"    />
        <result property="resourceType"    column="resource_type"    />
        <result property="resourceSort"    column="resource_sort"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMoocCoursewareDesignResourceVo">
        select chapter_resource_id, chapter_id, courseware_design_id, resource_title, resource_content, resource_type, resource_sort, del_flag, create_by, create_time, update_by, update_time from mooc_courseware_design_resource
    </sql>

    <select id="selectMoocCoursewareDesignResourceList" parameterType="MoocCoursewareDesignResource" resultMap="MoocCoursewareDesignResourceResult">
        <include refid="selectMoocCoursewareDesignResourceVo"/>
        <where>  
            <if test="chapterId != null "> and chapter_id = #{chapterId}</if>
            <if test="coursewareDesignId != null "> and courseware_design_id = #{coursewareDesignId}</if>
            <if test="resourceTitle != null  and resourceTitle != ''"> and resource_title = #{resourceTitle}</if>
            <if test="resourceContent != null  and resourceContent != ''"> and resource_content = #{resourceContent}</if>
            <if test="resourceType != null  and resourceType != ''"> and resource_type = #{resourceType}</if>
            <if test="resourceSort != null "> and resource_sort = #{resourceSort}</if>
        </where>
    </select>
    
    <select id="selectMoocCoursewareDesignResourceByChapterResourceId" parameterType="Long" resultMap="MoocCoursewareDesignResourceResult">
        <include refid="selectMoocCoursewareDesignResourceVo"/>
        where chapter_resource_id = #{chapterResourceId}
    </select>

    <insert id="insertMoocCoursewareDesignResource" parameterType="MoocCoursewareDesignResource" useGeneratedKeys="true" keyProperty="chapterResourceId">
        insert into mooc_courseware_design_resource
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="chapterId != null">chapter_id,</if>
            <if test="coursewareDesignId != null">courseware_design_id,</if>
            <if test="resourceTitle != null">resource_title,</if>
            <if test="resourceContent != null">resource_content,</if>
            <if test="resourceType != null and resourceType != ''">resource_type,</if>
            <if test="resourceSort != null">resource_sort,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="chapterId != null">#{chapterId},</if>
            <if test="coursewareDesignId != null">#{coursewareDesignId},</if>
            <if test="resourceTitle != null">#{resourceTitle},</if>
            <if test="resourceContent != null">#{resourceContent},</if>
            <if test="resourceType != null and resourceType != ''">#{resourceType},</if>
            <if test="resourceSort != null">#{resourceSort},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMoocCoursewareDesignResource" parameterType="MoocCoursewareDesignResource">
        update mooc_courseware_design_resource
        <trim prefix="SET" suffixOverrides=",">
            <if test="chapterId != null">chapter_id = #{chapterId},</if>
            <if test="coursewareDesignId != null">courseware_design_id = #{coursewareDesignId},</if>
            <if test="resourceTitle != null">resource_title = #{resourceTitle},</if>
            <if test="resourceContent != null">resource_content = #{resourceContent},</if>
            <if test="resourceType != null and resourceType != ''">resource_type = #{resourceType},</if>
            <if test="resourceSort != null">resource_sort = #{resourceSort},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where chapter_resource_id = #{chapterResourceId}
    </update>

    <delete id="deleteMoocCoursewareDesignResourceByChapterResourceId" parameterType="Long">
        delete from mooc_courseware_design_resource where chapter_resource_id = #{chapterResourceId}
    </delete>

    <delete id="deleteMoocCoursewareDesignResourceByChapterResourceIds" parameterType="String">
        delete from mooc_courseware_design_resource where chapter_resource_id in 
        <foreach item="chapterResourceId" collection="array" open="(" separator="," close=")">
            #{chapterResourceId}
        </foreach>
    </delete>
</mapper>
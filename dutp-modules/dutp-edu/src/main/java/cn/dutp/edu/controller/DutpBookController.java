package cn.dutp.edu.controller;

import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.domain.DutpSubject;
import cn.dutp.edu.domain.dto.BookDto;
import cn.dutp.edu.domain.dto.DtbUserBookDto;
import cn.dutp.edu.domain.vo.*;
import cn.dutp.edu.domian.DutpUser;
import cn.dutp.edu.service.DtbBookService;
import cn.hutool.core.lang.tree.Tree;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: dutp
 * @date: 2025/1/16
 */
@RestController
@RequestMapping("/book")
public class DutpBookController extends BaseController {

    @Autowired
    DtbBookService bookService;

    /**
     * 查询学科信息列表(树形结构)
     * 教务端教材管理用
     */
    @GetMapping("/selectSubjectList")
    public AjaxResult selectSubjectList(DutpSubject dutpSubject) {
        List<Tree<String>> list = bookService.selectSubjectList(dutpSubject);
        return AjaxResult.success(list);
    }

    /**
     * 教务端教材管理-查询公开教材列表
     */
    @GetMapping("/selectBookList")
    public TableDataInfo selectBookList(BookDto dto) {
        List<BookVo> list = bookService.selectBookList(dto);
        return getDataTable(list);
    }

    /**
     * 教务端教材管理-查询校本教材列表
     */
    @GetMapping("/selectEduBookList")
    public TableDataInfo selectEduBookList(BookDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        TableDataInfo info = bookService.selectEduBookList(dto);
        return info;
    }

    /**
     * 教务端教材商城-查询教材列表
     */
    @GetMapping("/selectEducationalBookList")
    public AjaxResult selectEducationalBookList(BookDto dto) {
        List<BookVo> list = bookService.selectEducationalBookList(dto);
        return AjaxResult.success(list);
    }

    /**
     * 教务端教材管理-查询教材详情
     */
    @GetMapping(value = "/getBookDetail")
    public AjaxResult getBookDetail(BookDto dto) {
        return success(bookService.getBookDetail(dto));
    }

    /**
     * 教务端教材管理-同步
     */
    @GetMapping(value = "/pushBook")
    public AjaxResult pushBook(BookDto dto) {
        return success(bookService.pushBook(dto));
    }

    /**
     * 教务端教材详情-查询用户
     */
    @GetMapping(value = "/selectBookUserList")
    public TableDataInfo selectBookUserList(BookDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<DutpUser> userList = bookService.selectBookUserList(dto);
        return getDataTable(userList);
    }

    /**
     * 教务端教材详情-批量添加教材使用用户
     */
    @Log(title = "教材详情批量导入教材使用用户", businessType = BusinessType.IMPORT)
    @PostMapping(value = "/importBookUser")
    public AjaxResult importBookUser(@RequestBody BookDto dto){
        return bookService.importBookUser(dto);
    }

    /**
     * 教务端教材详情-添加教材使用用户
     */
    @Log(title = "教材详情添加教材使用用户", businessType = BusinessType.INSERT)
    @PostMapping(value = "/addBookUser")
    public AjaxResult addBookUser(@RequestBody DtbUserBookDto dto){
        return bookService.addBookUser(dto);
    }

    /**
     * 教务端教材详情-下载导入失败的用户
     */
    @Log(title = "教材详情下载导入失败的用户", businessType = BusinessType.EXPORT)
    @PostMapping(value = "/downloadErrorList")
    public void downloadErrorList(@RequestBody BookDto dto,HttpServletResponse response){
        List<DtbBookUserExport> errorUserBookList = dto.getErrorUserBookList();
        bookService.downloadErrorList(errorUserBookList,response);
    }


    /**
     * 教务端-导出名单
     */
    @PostMapping(value = "/exportBookUser")
    @Log(title = "导出校本教材详情", businessType = BusinessType.EXPORT)
    public void exportBookUser(HttpServletResponse response, BookDto dto){
        List<DutpUser> userList = bookService.exportBookUser(dto);
        List<DtbBookUserExport> exportList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(userList)) {
            userList.stream().forEach(e->{
                DtbBookUserExport dt = new DtbBookUserExport();
                BeanUtils.copyProperties(e,dt);
                exportList.add(dt);
            });
            ExcelUtil<DtbBookUserExport> util = new ExcelUtil<DtbBookUserExport>(DtbBookUserExport.class);
            util.exportExcel(response, exportList, "校本教材详情");
        }
    }

    /**
     * 教务端-删除名单
     */
    @PostMapping(value = "/removeBookUser")
    @Log(title = "校本教材移除绑定人员", businessType = BusinessType.DELETE)
    public AjaxResult removeBookUser(@RequestBody BookDto dto){
        bookService.removeBookUser(dto);
        return AjaxResult.success();
    }

    /**
     * 教务端-订单列表
     */
    @GetMapping(value = "/getOrderBySchoolList")
    public TableDataInfo getOrderBySchoolList(BookDto dto){
        startPage();
        return getDataTable(bookService.getOrderBySchoolList(dto));
    }

    /**
     * 教务端-订单详情
     */
    @PostMapping(value = "/getOrderDetailBySchool")
    public DutpEduOrderDetailVo getOrderDetailBySchool(@RequestBody BookDto dto){
        return bookService.getOrderDetailBySchool(dto);
    }

    /**
     * 教务端-查询教材详情
     */
    @GetMapping(value = "/getRemoteBookDetail/{bookId}")
    public AjaxResult getRemoteBookDetail(@PathVariable("bookId") String bookId) {
        return success(bookService.getRemoteBookDetail(bookId));
    }

    /**
     * 查询教材列表(教材使用统计教材列表）
     */
    @GetMapping("/getBookListForSchool")
    public TableDataInfo getBookListForSchool(BookDto dto) {
        startPage();
        List<BookVo> list = bookService.selectBookListForSchool(dto);
        return getDataTable(list);
    }

    /**
     * 教务端教材详情-资源包下载
     */
    @PostMapping(value = "/downLoadBookFile")
    public void downLoadBookFile(HttpServletResponse response, @RequestBody BookDto dto) throws IOException {
        bookService.downLoadFile(response,dto);
    }

    /**
     * 教务端-获取教材数据总览
     */
    @GetMapping("/dataOverview")
    public AjaxResult dataOverview(BookDataOverviewVo dto) {
        BookDataOverviewVo data = bookService.dataOverview(dto);
        return success(data);
    }

    @GetMapping("/getChapterDataList")
    public AjaxResult getChapterDataList(BookDataOverviewVo dto) {
        List<BookDataOverviewVo> data = bookService.getChapterDataList(dto);
        return success(data);
    }


    /**
     * 教务端-导出数据总览
     */
    @PostMapping("/exportDataOverview")
    public void exportDataOverview(HttpServletResponse response, BookDto dto) {
        bookService.exportDataOverview(response, dto);
    }

    /**
     * 教务端-导出应用数据
     */
    @PostMapping("/exportAppData")
    public void exportAppData(HttpServletResponse response, BookDto dto) {
        bookService.exportAppData(response, dto);
    }


    @GetMapping("/chapterListForSelect")
    public AjaxResult listForSelect( DtbBookChapter dto) {
        List<DtbBookChapter> list = bookService.chapterListForSelect(dto);
        return success(list);
    }

    /**
     * 查询教材应用用户数据
     */
    @GetMapping("/selectBookApplicationUserData")
    public AjaxResult selectBookApplicationUserData(DtbBookApplicationUserDataVo dto) {
        return success(bookService.selectBookApplicationUserData(dto));
    }

    /**
     * 根据教材id和版本id获取教材目录
     */
    @GetMapping("/selectChapterByBookId")
    public AjaxResult selectChapterByBookId(DtbBookApplicationUserDataVo dto) {
        return success(bookService.selectChapterByBookId(dto));
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocCoursewareDesignFolderMapper">
    
    <resultMap type="MoocCoursewareDesignFolder" id="MoocCoursewareDesignFolderResult">
        <result property="chapterId"    column="chapter_id"    />
        <result property="chapterName"    column="chapter_name"    />
        <result property="coursewareDesignId"    column="courseware_design_id"    />
        <result property="sort"    column="sort"    />
        <result property="parentId"    column="parent_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMoocCoursewareDesignFolderVo">
        select chapter_id, chapter_name, courseware_design_id, sort, parent_id, del_flag, create_by, create_time, update_by, update_time from mooc_courseware_design_folder
    </sql>

    <select id="selectMoocCoursewareDesignFolderList" parameterType="MoocCoursewareDesignFolder" resultMap="MoocCoursewareDesignFolderResult">
        <include refid="selectMoocCoursewareDesignFolderVo"/>
        <where>  
            <if test="chapterName != null  and chapterName != ''"> and chapter_name like concat('%', #{chapterName}, '%')</if>
            <if test="coursewareDesignId != null "> and courseware_design_id = #{coursewareDesignId}</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
        </where>
    </select>
    
    <select id="selectMoocCoursewareDesignFolderByChapterId" parameterType="Long" resultMap="MoocCoursewareDesignFolderResult">
        <include refid="selectMoocCoursewareDesignFolderVo"/>
        where chapter_id = #{chapterId}
    </select>

    <insert id="insertMoocCoursewareDesignFolder" parameterType="MoocCoursewareDesignFolder" useGeneratedKeys="true" keyProperty="chapterId">
        insert into mooc_courseware_design_folder
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="chapterName != null and chapterName != ''">chapter_name,</if>
            <if test="coursewareDesignId != null">courseware_design_id,</if>
            <if test="sort != null">sort,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="chapterName != null and chapterName != ''">#{chapterName},</if>
            <if test="coursewareDesignId != null">#{coursewareDesignId},</if>
            <if test="sort != null">#{sort},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMoocCoursewareDesignFolder" parameterType="MoocCoursewareDesignFolder">
        update mooc_courseware_design_folder
        <trim prefix="SET" suffixOverrides=",">
            <if test="chapterName != null and chapterName != ''">chapter_name = #{chapterName},</if>
            <if test="coursewareDesignId != null">courseware_design_id = #{coursewareDesignId},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where chapter_id = #{chapterId}
    </update>

    <delete id="deleteMoocCoursewareDesignFolderByChapterId" parameterType="Long">
        delete from mooc_courseware_design_folder where chapter_id = #{chapterId}
    </delete>

    <delete id="deleteMoocCoursewareDesignFolderByChapterIds" parameterType="String">
        delete from mooc_courseware_design_folder where chapter_id in 
        <foreach item="chapterId" collection="array" open="(" separator="," close=")">
            #{chapterId}
        </foreach>
    </delete>
</mapper>
package cn.dutp.edu.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.edu.mapper.MoocSmartCourseTestPaperQuestionMapper;
import cn.dutp.edu.domain.MoocSmartCourseTestPaperQuestion;
import cn.dutp.edu.service.IMoocSmartCourseTestPaperQuestionService;

/**
 * 互动课堂试卷小题Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class MoocSmartCourseTestPaperQuestionServiceImpl extends ServiceImpl<MoocSmartCourseTestPaperQuestionMapper, MoocSmartCourseTestPaperQuestion> implements IMoocSmartCourseTestPaperQuestionService
{
    @Autowired
    private MoocSmartCourseTestPaperQuestionMapper moocSmartCourseTestPaperQuestionMapper;

    /**
     * 查询互动课堂试卷小题
     *
     * @param paperQuestionId 互动课堂试卷小题主键
     * @return 互动课堂试卷小题
     */
    @Override
    public MoocSmartCourseTestPaperQuestion selectMoocSmartCourseTestPaperQuestionByPaperQuestionId(Long paperQuestionId)
    {
        return this.getById(paperQuestionId);
    }

    /**
     * 查询互动课堂试卷小题列表
     *
     * @param moocSmartCourseTestPaperQuestion 互动课堂试卷小题
     * @return 互动课堂试卷小题
     */
    @Override
    public List<MoocSmartCourseTestPaperQuestion> selectMoocSmartCourseTestPaperQuestionList(MoocSmartCourseTestPaperQuestion moocSmartCourseTestPaperQuestion)
    {
        LambdaQueryWrapper<MoocSmartCourseTestPaperQuestion> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(moocSmartCourseTestPaperQuestion.getPaperId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseTestPaperQuestion::getPaperId
                ,moocSmartCourseTestPaperQuestion.getPaperId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseTestPaperQuestion.getCollectionId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseTestPaperQuestion::getCollectionId
                ,moocSmartCourseTestPaperQuestion.getCollectionId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseTestPaperQuestion.getQuestionId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseTestPaperQuestion::getQuestionId
                ,moocSmartCourseTestPaperQuestion.getQuestionId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseTestPaperQuestion.getSort())) {
                lambdaQueryWrapper.eq(MoocSmartCourseTestPaperQuestion::getSort
                ,moocSmartCourseTestPaperQuestion.getSort());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseTestPaperQuestion.getQuestionScore())) {
                lambdaQueryWrapper.eq(MoocSmartCourseTestPaperQuestion::getQuestionScore
                ,moocSmartCourseTestPaperQuestion.getQuestionScore());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增互动课堂试卷小题
     *
     * @param moocSmartCourseTestPaperQuestion 互动课堂试卷小题
     * @return 结果
     */
    @Override
    public boolean insertMoocSmartCourseTestPaperQuestion(MoocSmartCourseTestPaperQuestion moocSmartCourseTestPaperQuestion)
    {
        return this.save(moocSmartCourseTestPaperQuestion);
    }

    /**
     * 修改互动课堂试卷小题
     *
     * @param moocSmartCourseTestPaperQuestion 互动课堂试卷小题
     * @return 结果
     */
    @Override
    public boolean updateMoocSmartCourseTestPaperQuestion(MoocSmartCourseTestPaperQuestion moocSmartCourseTestPaperQuestion)
    {
        return this.updateById(moocSmartCourseTestPaperQuestion);
    }

    /**
     * 批量删除互动课堂试卷小题
     *
     * @param paperQuestionIds 需要删除的互动课堂试卷小题主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocSmartCourseTestPaperQuestionByPaperQuestionIds(List<Long> paperQuestionIds)
    {
        return this.removeByIds(paperQuestionIds);
    }

}

package cn.dutp.edu.service;

import cn.dutp.book.domain.DtbUserBook;
import cn.dutp.common.core.constant.ServiceNameConstants;
import cn.dutp.common.core.domain.R;
import cn.dutp.domain.DtbBook;
import cn.dutp.domain.DutpSubject;
import cn.dutp.edu.domain.dto.BookDto;
import cn.dutp.edu.domain.vo.*;
import feign.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;


import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Collection;

@FeignClient(name = ServiceNameConstants.BOOK_SERVICE, contextId = "dtbBook")
public interface DtbBookFeignClient {

    @GetMapping("/book/subject/list")
    R<List<DutpSubject>> selectSubjectList(@RequestBody DutpSubject dutpSubject);

    @PostMapping("/book/list")
    List<BookVo> selectBookList(@RequestBody BookDto dto);

    @PostMapping("/book/school/list")
    List<BookVo> selectBookListForSchool(@RequestBody BookDto dto);

    @GetMapping("/book/{bookId}")
    @ResponseBody
    R<DtbBook> getBookDetail(@PathVariable("bookId") String bookId);




    @GetMapping("/chapterData/detail")
    @ResponseBody
    R<BookVo> getRemoteBookDetail(@RequestBody BookDto dto);


    @GetMapping("/chapter/listForSelect")
    R<List<DtbBookChapter>> listForSelect(@RequestParam ("bookId") Long bookId);

    @GetMapping("/chapterData/list")
    R<List<BookDataOverviewVo>> getChapterDataList(@SpringQueryMap BookDataOverviewVo dtbBookChapterData);

    @GetMapping("/chapterData/dataOverview")
    R<BookDataOverviewVo> dataOverview(@SpringQueryMap BookDataOverviewVo dtbBookChapterData);

    @PostMapping(value = "/chapterData/inner/exportDataOverview", produces = "application/octet-stream")
    Response exportDataOverview(@RequestBody BookDto dto);


    @PostMapping(value = "/chapterData/inner/exportAppData", produces = "application/octet-stream")
    Response exportAppData( @RequestBody BookDto dto);

    @PostMapping("/chapterData/inner/selectBookApplicationUserData")
    R<DtbBookApplicationUserDataVo> selectBookApplicationUserData(@RequestBody DtbBookApplicationUserDataVo dto);

    @PostMapping("/userBook")
    R<Boolean> addDtbUserBook(@RequestBody DtbUserBook dtbUserBook);

    @PostMapping("/userBook/addBatchUserBook")
    Response addBatchUserBook(List<DtbUserBook> insList);
}


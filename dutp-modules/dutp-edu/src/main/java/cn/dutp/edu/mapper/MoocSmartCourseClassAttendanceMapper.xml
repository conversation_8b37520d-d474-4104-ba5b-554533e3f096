<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocSmartCourseClassAttendanceMapper">
    
    <resultMap type="MoocSmartCourseClassAttendance" id="MoocSmartCourseClassAttendanceResult">
        <result property="attendanceActivityId"    column="attendance_activity_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="classId"    column="class_id"    />
        <result property="lessonId"    column="lesson_id"    />
        <result property="title"    column="title"    />
        <result property="attendanceType"    column="attendance_type"    />
        <result property="endMethod"    column="end_method"    />
        <result property="durationMinutes"    column="duration_minutes"    />
        <result property="signInCode"    column="sign_in_code"    />
        <result property="status"    column="status"    />
        <result property="activityStartTime"    column="activity_start_time"    />
        <result property="activityEndTime"    column="activity_end_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectMoocSmartCourseClassAttendanceVo">
        select attendance_activity_id, course_id, class_id, lesson_id, title, attendance_type, end_method, duration_minutes, sign_in_code, status, activity_start_time, activity_end_time, create_by, create_time, update_by, update_time, del_flag from mooc_smart_course_class_attendance
    </sql>

    <select id="selectMoocSmartCourseClassAttendanceList" parameterType="MoocSmartCourseClassAttendance" resultMap="MoocSmartCourseClassAttendanceResult">
        <include refid="selectMoocSmartCourseClassAttendanceVo"/>
        <where>  
            <if test="courseId != null "> and course_id = #{courseId}</if>
            <if test="classId != null "> and class_id = #{classId}</if>
            <if test="lessonId != null "> and lesson_id = #{lessonId}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="attendanceType != null "> and attendance_type = #{attendanceType}</if>
            <if test="endMethod != null "> and end_method = #{endMethod}</if>
            <if test="durationMinutes != null "> and duration_minutes = #{durationMinutes}</if>
            <if test="signInCode != null  and signInCode != ''"> and sign_in_code = #{signInCode}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="activityStartTime != null "> and activity_start_time = #{activityStartTime}</if>
            <if test="activityEndTime != null "> and activity_end_time = #{activityEndTime}</if>
        </where>
    </select>
    
    <select id="selectMoocSmartCourseClassAttendanceByAttendanceActivityId" parameterType="Long" resultMap="MoocSmartCourseClassAttendanceResult">
        <include refid="selectMoocSmartCourseClassAttendanceVo"/>
        where attendance_activity_id = #{attendanceActivityId}
    </select>

    <insert id="insertMoocSmartCourseClassAttendance" parameterType="MoocSmartCourseClassAttendance" useGeneratedKeys="true" keyProperty="attendanceActivityId">
        insert into mooc_smart_course_class_attendance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="courseId != null">course_id,</if>
            <if test="classId != null">class_id,</if>
            <if test="lessonId != null">lesson_id,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="attendanceType != null">attendance_type,</if>
            <if test="endMethod != null">end_method,</if>
            <if test="durationMinutes != null">duration_minutes,</if>
            <if test="signInCode != null">sign_in_code,</if>
            <if test="status != null">status,</if>
            <if test="activityStartTime != null">activity_start_time,</if>
            <if test="activityEndTime != null">activity_end_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="courseId != null">#{courseId},</if>
            <if test="classId != null">#{classId},</if>
            <if test="lessonId != null">#{lessonId},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="attendanceType != null">#{attendanceType},</if>
            <if test="endMethod != null">#{endMethod},</if>
            <if test="durationMinutes != null">#{durationMinutes},</if>
            <if test="signInCode != null">#{signInCode},</if>
            <if test="status != null">#{status},</if>
            <if test="activityStartTime != null">#{activityStartTime},</if>
            <if test="activityEndTime != null">#{activityEndTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateMoocSmartCourseClassAttendance" parameterType="MoocSmartCourseClassAttendance">
        update mooc_smart_course_class_attendance
        <trim prefix="SET" suffixOverrides=",">
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="classId != null">class_id = #{classId},</if>
            <if test="lessonId != null">lesson_id = #{lessonId},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="attendanceType != null">attendance_type = #{attendanceType},</if>
            <if test="endMethod != null">end_method = #{endMethod},</if>
            <if test="durationMinutes != null">duration_minutes = #{durationMinutes},</if>
            <if test="signInCode != null">sign_in_code = #{signInCode},</if>
            <if test="status != null">status = #{status},</if>
            <if test="activityStartTime != null">activity_start_time = #{activityStartTime},</if>
            <if test="activityEndTime != null">activity_end_time = #{activityEndTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where attendance_activity_id = #{attendanceActivityId}
    </update>

    <delete id="deleteMoocSmartCourseClassAttendanceByAttendanceActivityId" parameterType="Long">
        delete from mooc_smart_course_class_attendance where attendance_activity_id = #{attendanceActivityId}
    </delete>

    <delete id="deleteMoocSmartCourseClassAttendanceByAttendanceActivityIds" parameterType="String">
        delete from mooc_smart_course_class_attendance where attendance_activity_id in 
        <foreach item="attendanceActivityId" collection="array" open="(" separator="," close=")">
            #{attendanceActivityId}
        </foreach>
    </delete>
</mapper>
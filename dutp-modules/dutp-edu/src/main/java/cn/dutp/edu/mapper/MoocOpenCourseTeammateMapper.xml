<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocOpenCourseTeammateMapper">
    
    <resultMap type="MoocOpenCourseTeammate" id="MoocOpenCourseTeammateResult">
        <result property="teammateId"    column="teammate_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="avatar"    column="avatar"    />
        <result property="sort"    column="sort"    />
        <result property="name"    column="name"    />
        <result property="schoolName"    column="school_name"    />
        <result property="title"    column="title"    />
        <result property="description"    column="description"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMoocOpenCourseTeammateVo">
        select teammate_id, course_id, avatar, sort, name, school_name, title, description, del_flag, create_by, create_time, update_by, update_time from mooc_open_course_teammate
    </sql>

    <select id="selectMoocOpenCourseTeammateList" parameterType="MoocOpenCourseTeammate" resultMap="MoocOpenCourseTeammateResult">
        <include refid="selectMoocOpenCourseTeammateVo"/>
        <where>  
            <if test="courseId != null "> and course_id = #{courseId}</if>
            <if test="avatar != null  and avatar != ''"> and avatar = #{avatar}</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="schoolName != null  and schoolName != ''"> and school_name like concat('%', #{schoolName}, '%')</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
        </where>
    </select>
    
    <select id="selectMoocOpenCourseTeammateByTeammateId" parameterType="Long" resultMap="MoocOpenCourseTeammateResult">
        <include refid="selectMoocOpenCourseTeammateVo"/>
        where teammate_id = #{teammateId}
    </select>

    <insert id="insertMoocOpenCourseTeammate" parameterType="MoocOpenCourseTeammate">
        insert into mooc_open_course_teammate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teammateId != null">teammate_id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="avatar != null">avatar,</if>
            <if test="sort != null">sort,</if>
            <if test="name != null">name,</if>
            <if test="schoolName != null">school_name,</if>
            <if test="title != null">title,</if>
            <if test="description != null">description,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teammateId != null">#{teammateId},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="sort != null">#{sort},</if>
            <if test="name != null">#{name},</if>
            <if test="schoolName != null">#{schoolName},</if>
            <if test="title != null">#{title},</if>
            <if test="description != null">#{description},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMoocOpenCourseTeammate" parameterType="MoocOpenCourseTeammate">
        update mooc_open_course_teammate
        <trim prefix="SET" suffixOverrides=",">
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="name != null">name = #{name},</if>
            <if test="schoolName != null">school_name = #{schoolName},</if>
            <if test="title != null">title = #{title},</if>
            <if test="description != null">description = #{description},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where teammate_id = #{teammateId}
    </update>

    <delete id="deleteMoocOpenCourseTeammateByTeammateId" parameterType="Long">
        delete from mooc_open_course_teammate where teammate_id = #{teammateId}
    </delete>

    <delete id="deleteMoocOpenCourseTeammateByTeammateIds" parameterType="String">
        delete from mooc_open_course_teammate where teammate_id in 
        <foreach item="teammateId" collection="array" open="(" separator="," close=")">
            #{teammateId}
        </foreach>
    </delete>
</mapper>
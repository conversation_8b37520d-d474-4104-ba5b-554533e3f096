<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocOpenCourseApplyMapper">
    
    <resultMap type="MoocOpenCourseApply" id="MoocOpenCourseApplyResult">
        <result property="applyId"    column="apply_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="userId"    column="user_id"    />
        <result property="auditUserId"    column="audit_user_id"    />
        <result property="applyStatus"    column="apply_status"    />
        <result property="applyRemark"    column="apply_remark"    />
        <result property="step"    column="step"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMoocOpenCourseApplyVo">
        select apply_id, course_id, user_id, audit_user_id, apply_status, apply_remark, step, del_flag, create_by, create_time, update_by, update_time from mooc_open_course_apply
    </sql>

    <select id="selectMoocOpenCourseApplyList" parameterType="MoocOpenCourseApply" resultMap="MoocOpenCourseApplyResult">
        <include refid="selectMoocOpenCourseApplyVo"/>
        <where>  
            <if test="courseId != null "> and course_id = #{courseId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="auditUserId != null "> and audit_user_id = #{auditUserId}</if>
            <if test="applyStatus != null  and applyStatus != ''"> and apply_status = #{applyStatus}</if>
            <if test="applyRemark != null  and applyRemark != ''"> and apply_remark = #{applyRemark}</if>
            <if test="step != null  and step != ''"> and step = #{step}</if>
        </where>
    </select>
    
    <select id="selectMoocOpenCourseApplyByApplyId" parameterType="Long" resultMap="MoocOpenCourseApplyResult">
        <include refid="selectMoocOpenCourseApplyVo"/>
        where apply_id = #{applyId}
    </select>

    <insert id="insertMoocOpenCourseApply" parameterType="MoocOpenCourseApply">
        insert into mooc_open_course_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applyId != null">apply_id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="auditUserId != null">audit_user_id,</if>
            <if test="applyStatus != null">apply_status,</if>
            <if test="applyRemark != null">apply_remark,</if>
            <if test="step != null">step,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applyId != null">#{applyId},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="auditUserId != null">#{auditUserId},</if>
            <if test="applyStatus != null">#{applyStatus},</if>
            <if test="applyRemark != null">#{applyRemark},</if>
            <if test="step != null">#{step},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMoocOpenCourseApply" parameterType="MoocOpenCourseApply">
        update mooc_open_course_apply
        <trim prefix="SET" suffixOverrides=",">
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="auditUserId != null">audit_user_id = #{auditUserId},</if>
            <if test="applyStatus != null">apply_status = #{applyStatus},</if>
            <if test="applyRemark != null">apply_remark = #{applyRemark},</if>
            <if test="step != null">step = #{step},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where apply_id = #{applyId}
    </update>

    <delete id="deleteMoocOpenCourseApplyByApplyId" parameterType="Long">
        delete from mooc_open_course_apply where apply_id = #{applyId}
    </delete>

    <delete id="deleteMoocOpenCourseApplyByApplyIds" parameterType="String">
        delete from mooc_open_course_apply where apply_id in 
        <foreach item="applyId" collection="array" open="(" separator="," close=")">
            #{applyId}
        </foreach>
    </delete>
</mapper>
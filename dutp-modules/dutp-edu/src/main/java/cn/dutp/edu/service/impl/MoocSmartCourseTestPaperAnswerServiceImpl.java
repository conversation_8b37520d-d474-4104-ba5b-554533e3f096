package cn.dutp.edu.service.impl;

import java.util.Collections;
import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.edu.mapper.MoocSmartCourseTestPaperAnswerMapper;
import cn.dutp.edu.domain.MoocSmartCourseTestPaperAnswer;
import cn.dutp.edu.service.IMoocSmartCourseTestPaperAnswerService;

/**
 * 互动课堂学生考试/作业结果Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class MoocSmartCourseTestPaperAnswerServiceImpl extends ServiceImpl<MoocSmartCourseTestPaperAnswerMapper, MoocSmartCourseTestPaperAnswer> implements IMoocSmartCourseTestPaperAnswerService
{
    @Autowired
    private MoocSmartCourseTestPaperAnswerMapper moocSmartCourseTestPaperAnswerMapper;

    /**
     * 查询互动课堂学生考试/作业结果
     *
     * @param answertId 互动课堂学生考试/作业结果主键
     * @return 互动课堂学生考试/作业结果
     */
    @Override
    public MoocSmartCourseTestPaperAnswer selectMoocSmartCourseTestPaperAnswerByAnswertId(Long answertId)
    {
        return this.getById(answertId);
    }

    /**
     * 查询互动课堂学生考试/作业结果列表
     *
     * @param moocSmartCourseTestPaperAnswer 互动课堂学生考试/作业结果
     * @return 互动课堂学生考试/作业结果
     */
    @Override
    public List<MoocSmartCourseTestPaperAnswer> selectMoocSmartCourseTestPaperAnswerList(MoocSmartCourseTestPaperAnswer moocSmartCourseTestPaperAnswer)
    {
        LambdaQueryWrapper<MoocSmartCourseTestPaperAnswer> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(moocSmartCourseTestPaperAnswer.getAssignmentId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseTestPaperAnswer::getAssignmentId
                ,moocSmartCourseTestPaperAnswer.getAssignmentId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseTestPaperAnswer.getHomeworkShowId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseTestPaperAnswer::getHomeworkShowId
                ,moocSmartCourseTestPaperAnswer.getHomeworkShowId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseTestPaperAnswer.getUserId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseTestPaperAnswer::getUserId
                ,moocSmartCourseTestPaperAnswer.getUserId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseTestPaperAnswer.getAnswerContent())) {
                lambdaQueryWrapper.eq(MoocSmartCourseTestPaperAnswer::getAnswerContent
                ,moocSmartCourseTestPaperAnswer.getAnswerContent());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseTestPaperAnswer.getScore())) {
                lambdaQueryWrapper.eq(MoocSmartCourseTestPaperAnswer::getScore
                ,moocSmartCourseTestPaperAnswer.getScore());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseTestPaperAnswer.getAnswerType())) {
                lambdaQueryWrapper.eq(MoocSmartCourseTestPaperAnswer::getAnswerType
                ,moocSmartCourseTestPaperAnswer.getAnswerType());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增互动课堂学生考试/作业结果
     *
     * @param moocSmartCourseTestPaperAnswer 互动课堂学生考试/作业结果
     * @return 结果
     */
    @Override
    public boolean insertMoocSmartCourseTestPaperAnswer(MoocSmartCourseTestPaperAnswer moocSmartCourseTestPaperAnswer)
    {
        return this.save(moocSmartCourseTestPaperAnswer);
    }

    /**
     * 修改互动课堂学生考试/作业结果
     *
     * @param moocSmartCourseTestPaperAnswer 互动课堂学生考试/作业结果
     * @return 结果
     */
    @Override
    public boolean updateMoocSmartCourseTestPaperAnswer(MoocSmartCourseTestPaperAnswer moocSmartCourseTestPaperAnswer)
    {
        return this.updateById(moocSmartCourseTestPaperAnswer);
    }

    /**
     * 批量删除互动课堂学生考试/作业结果
     *
     * @param answertIds 需要删除的互动课堂学生考试/作业结果主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocSmartCourseTestPaperAnswerByAnswertIds(List<Long> answertIds)
    {
        return this.removeByIds(answertIds);
    }

    @Override
    public MoocSmartCourseTestPaperAnswer selectUserAnswerByTestPaperId(Long testPaperId, Long userId) {
        LambdaQueryWrapper<MoocSmartCourseTestPaperAnswer> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MoocSmartCourseTestPaperAnswer::getAssignmentId, testPaperId);
        lambdaQueryWrapper.eq(MoocSmartCourseTestPaperAnswer::getUserId, userId);
        MoocSmartCourseTestPaperAnswer one = this.getOne(lambdaQueryWrapper);
        return one ;
    }

    @Override
    public List<MoocSmartCourseTestPaperAnswer> getUserAnswerByAssignmentId(MoocSmartCourseTestPaperAnswer moocSmartCourseTestPaperAnswer) {
        return moocSmartCourseTestPaperAnswerMapper.getUserAnswerByAssignmentId(moocSmartCourseTestPaperAnswer);
    }

}

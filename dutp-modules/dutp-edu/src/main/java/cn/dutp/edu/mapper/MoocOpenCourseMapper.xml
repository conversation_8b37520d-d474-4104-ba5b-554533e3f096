<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocOpenCourseMapper">
    
    <resultMap type="MoocOpenCourse" id="MoocOpenCourseResult">
        <result property="courseId"    column="course_id"    />
        <result property="courseCode"    column="course_code"    />
        <result property="courseName"    column="course_name"    />
        <result property="courseDescription"    column="course_description"    />
        <result property="courseImage"    column="course_image"    />
        <result property="courseCover"    column="course_cover"    />
        <result property="courseDesignId"    column="course_design_id"    />
        <result property="subjectId"    column="subject_id"    />
        <result property="secondSubjectId"    column="second_subject_id"    />
        <result property="thirdSubjectId"    column="third_subject_id"    />
        <result property="courseStatus"    column="course_status"    />
        <result property="approvalStatus"    column="approval_status"    />
        <result property="reviewStatus"    column="review_status"    />
        <result property="applyTime"    column="apply_time"    />
        <result property="allowJoinFlag"    column="allow_join_flag"    />
        <result property="openJoinFlag"    column="open_join_flag"    />
        <result property="openTime"    column="open_time"    />
        <result property="userId"    column="user_id"    />
        <result property="sort"    column="sort"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="learnersNum"    column="learners_num"    />
        <!-- Collection mapping for teammates -->
        <collection property="teammates" ofType="cn.dutp.domain.MoocOpenCourseTeammate"
                    select="selectTeammatesByCourseId" column="course_id"/>
    </resultMap>

    <resultMap type="cn.dutp.domain.MoocOpenCourseTeammate" id="MoocOpenCourseTeammateResult">
        <id     property="teammateId"  column="teammate_id"  />
        <result property="courseId"    column="course_id"    />
        <result property="avatar"      column="avatar"       />
        <result property="sort"        column="sort"         />
        <result property="name"        column="name"         />
        <result property="schoolName"  column="school_name"  />
        <result property="title"       column="title"        />
        <result property="description" column="description"  />
        <result property="delFlag"     column="del_flag"     />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"  column="create_time"  />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"  column="update_time"  />

    </resultMap>

    <sql id="selectMoocOpenCourseVo">
        select course_id, course_code, course_name, course_description, course_image, course_cover, course_design_id, subject_id, second_subject_id, third_subject_id, course_status, approval_status, review_status, apply_time, allow_join_flag, open_join_flag, open_time, user_id, sort, del_flag, create_by, create_time, update_by, update_time from mooc_open_course
    </sql>

    <select id="selectMoocOpenCourseList" resultType="cn.dutp.domain.MoocOpenCourse">
        SELECT
            moc.course_id,
            moc.course_code,
            moc.course_name,
            moc.course_description,
            moc.course_cover,
            moc.course_design_id,
            moc.course_status,
            moc.approval_status,
            moc.sort,
            moc.create_time,
            du.real_name AS realName,
            count(mocs.user_id) AS learnersNum,
            sub1.step,
            sub1.apply_status
        FROM mooc_open_course moc
        LEFT JOIN mooc_open_course_student mocs ON moc.course_id = mocs.course_id AND mocs.del_flag = 0
        LEFT JOIN dutp_user du ON moc.user_id = du.user_id
        LEFT JOIN (
            SELECT
                moca.course_id,
                moca.step,
                moca.apply_status,
                ROW_NUMBER() OVER (PARTITION BY moca.course_id ORDER BY moca.create_time DESC) as rn
            FROM mooc_open_course_apply moca
            WHERE moca.del_flag = 0
        ) sub1 ON moc.course_id = sub1.course_id AND sub1.rn = 1
        WHERE moc.user_id = #{param.userId} AND moc.del_flag = 0
        <if test="param.applyStatus != null and param.applyStatus != '' and param.step != null and param.step != ''">
            AND sub1.step = #{param.step} AND sub1.apply_status = #{param.applyStatus}
        </if>
        GROUP BY moc.course_id, moc.course_code, moc.course_name, moc.course_description,
                 moc.course_cover, moc.course_design_id, moc.course_status, moc.approval_status,
                 moc.sort, moc.create_time, du.real_name, sub1.step, sub1.apply_status
        ORDER BY moc.create_time DESC
    </select>

    <select id="selectMoocOpenCourseById" resultMap="MoocOpenCourseResult">
        SELECT
            moc.course_id,
            moc.course_code,
            moc.course_name,
            moc.course_description,
            moc.course_image,
            moc.course_cover,
            moc.course_design_id,
            moc.subject_id,
            moc.second_subject_id,
            moc.third_subject_id,
            moc.course_status,
            moc.approval_status,
            moc.review_status,
            moc.apply_time,
            moc.allow_join_flag,
            moc.open_join_flag,
            moc.open_time,
            moc.user_id,
            moc.sort,
            moc.del_flag,
            moc.create_by,
            moc.create_time,
            moc.update_by,
            moc.update_time,
            count(distinct mocs.user_id) learners_num
        FROM
            mooc_open_course moc
        LEFT JOIN
            mooc_open_course_student mocs ON moc.course_id = mocs.course_id AND mocs.del_flag = 0
        WHERE
            moc.course_id = #{courseId} AND moc.del_flag = '0'
        GROUP BY
            moc.course_id
    </select>

    <select id="selectMyOpenCourses" resultMap="MoocOpenCourseResult">
        SELECT
            moc.course_id,
            moc.course_code,
            moc.course_name,
            moc.course_description,
            moc.course_image,
            moc.course_cover,
            moc.course_design_id,
            moc.subject_id,
            moc.second_subject_id,
            moc.third_subject_id,
            moc.course_status,
            moc.approval_status,
            moc.review_status,
            moc.apply_time,
            moc.allow_join_flag,
            moc.open_join_flag,
            moc.open_time,
            moc.user_id, -- Course creator
            moc.sort,
            moc.del_flag,
            moc.create_by,
            moc.create_time,
            moc.update_by,
            moc.update_time,
            count(distinct mocs2.user_id) learners_num
        FROM
            mooc_open_course moc
        INNER JOIN
            mooc_open_course_student mocs ON moc.course_id = mocs.course_id AND mocs.user_id = #{userId}
        left JOIN
            mooc_open_course_student mocs2 ON moc.course_id = mocs2.course_id AND mocs2.del_flag = 0
        WHERE
           mocs.del_flag = '0'
            AND moc.del_flag = '0'
            <if test="courseFilters != null">
                <if test="courseFilters.courseName != null and courseFilters.courseName != ''">
                    AND moc.course_name LIKE CONCAT('%', #{courseFilters.courseName}, '%')
                </if>
                <if test="courseFilters.courseStatus != null and courseFilters.courseStatus != ''">
                    AND moc.course_status = #{courseFilters.courseStatus}
                </if>
                <if test="courseFilters.subjectId != null">
                    AND moc.subject_id = #{courseFilters.subjectId}
                </if>
                <if test="courseFilters.secondSubjectId != null">
                    AND moc.second_subject_id = #{courseFilters.secondSubjectId}
                </if>
                <if test="courseFilters.thirdSubjectId != null">
                    AND moc.third_subject_id = #{courseFilters.thirdSubjectId}
                </if>
                <if test="courseFilters.approvalStatus != null and courseFilters.approvalStatus != ''">
                    AND moc.approval_status = #{courseFilters.approvalStatus}
                </if>
                <if test="courseFilters.reviewStatus != null and courseFilters.reviewStatus != ''">
                    AND moc.review_status = #{courseFilters.reviewStatus}
                </if>
                <if test="courseFilters.courseCode != null and courseFilters.courseCode != ''">
                    AND moc.course_code = #{courseFilters.courseCode}
                </if>
            </if>
        GROUP BY
            moc.course_id
        ORDER BY
            moc.create_time DESC
    </select>

    <select id="selectTeammatesByCourseId" resultMap="MoocOpenCourseTeammateResult">
        SELECT
            teammate_id,
            course_id,
            avatar,
            sort,
            name,
            school_name,
            title,
            description,
            del_flag,
            create_by,
            create_time,
            update_by,
            update_time
        FROM
            mooc_open_course_teammate
        WHERE
            course_id = #{courseId}
            AND del_flag = '0'
        ORDER BY
            sort ASC, create_time ASC
    </select>

</mapper>
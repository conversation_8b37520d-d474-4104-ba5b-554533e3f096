package cn.dutp.edu.service;

import java.util.List;
import cn.dutp.edu.domain.MoocSmartCourseStatic;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 互动课堂统计数据快照Service接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IMoocSmartCourseStaticService extends IService<MoocSmartCourseStatic>
{
    /**
     * 查询互动课堂统计数据快照
     *
     * @param staticId 互动课堂统计数据快照主键
     * @return 互动课堂统计数据快照
     */
    public MoocSmartCourseStatic selectMoocSmartCourseStaticByStaticId(Long staticId);

    /**
     * 查询互动课堂统计数据快照列表
     *
     * @param moocSmartCourseStatic 互动课堂统计数据快照
     * @return 互动课堂统计数据快照集合
     */
    public List<MoocSmartCourseStatic> selectMoocSmartCourseStaticList(MoocSmartCourseStatic moocSmartCourseStatic);

    /**
     * 新增互动课堂统计数据快照
     *
     * @param moocSmartCourseStatic 互动课堂统计数据快照
     * @return 结果
     */
    public boolean insertMoocSmartCourseStatic(MoocSmartCourseStatic moocSmartCourseStatic);

    /**
     * 修改互动课堂统计数据快照
     *
     * @param moocSmartCourseStatic 互动课堂统计数据快照
     * @return 结果
     */
    public boolean updateMoocSmartCourseStatic(MoocSmartCourseStatic moocSmartCourseStatic);

    /**
     * 批量删除互动课堂统计数据快照
     *
     * @param staticIds 需要删除的互动课堂统计数据快照主键集合
     * @return 结果
     */
    public boolean deleteMoocSmartCourseStaticByStaticIds(List<Long> staticIds);

}

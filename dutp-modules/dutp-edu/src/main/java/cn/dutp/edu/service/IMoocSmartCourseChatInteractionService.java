package cn.dutp.edu.service;

import java.util.List;
import cn.dutp.edu.domain.MoocSmartCourseChatInteraction;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 互动课堂的课上互动讨论收藏/解答/点赞记录Service接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IMoocSmartCourseChatInteractionService extends IService<MoocSmartCourseChatInteraction>
{
    /**
     * 查询互动课堂的课上互动讨论收藏/解答/点赞记录
     *
     * @param interactionId 互动课堂的课上互动讨论收藏/解答/点赞记录主键
     * @return 互动课堂的课上互动讨论收藏/解答/点赞记录
     */
    public MoocSmartCourseChatInteraction selectMoocSmartCourseChatInteractionByInteractionId(Long interactionId);

    /**
     * 查询互动课堂的课上互动讨论收藏/解答/点赞记录列表
     *
     * @param moocSmartCourseChatInteraction 互动课堂的课上互动讨论收藏/解答/点赞记录
     * @return 互动课堂的课上互动讨论收藏/解答/点赞记录集合
     */
    public List<MoocSmartCourseChatInteraction> selectMoocSmartCourseChatInteractionList(MoocSmartCourseChatInteraction moocSmartCourseChatInteraction);

    /**
     * 新增互动课堂的课上互动讨论收藏/解答/点赞记录
     *
     * @param moocSmartCourseChatInteraction 互动课堂的课上互动讨论收藏/解答/点赞记录
     * @return 结果
     */
    public boolean insertMoocSmartCourseChatInteraction(MoocSmartCourseChatInteraction moocSmartCourseChatInteraction);

    /**
     * 修改互动课堂的课上互动讨论收藏/解答/点赞记录
     *
     * @param moocSmartCourseChatInteraction 互动课堂的课上互动讨论收藏/解答/点赞记录
     * @return 结果
     */
    public boolean updateMoocSmartCourseChatInteraction(MoocSmartCourseChatInteraction moocSmartCourseChatInteraction);

    /**
     * 批量删除互动课堂的课上互动讨论收藏/解答/点赞记录
     *
     * @param interactionIds 需要删除的互动课堂的课上互动讨论收藏/解答/点赞记录主键集合
     * @return 结果
     */
    public boolean deleteMoocSmartCourseChatInteractionByInteractionIds(List<Long> interactionIds);

}

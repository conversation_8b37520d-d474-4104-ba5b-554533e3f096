<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocUserQuestionMapper">
    
    <resultMap type="MoocUserQuestion" id="MoocUserQuestionResult">
        <result property="questionId"    column="question_id"    />
        <result property="questionType"    column="question_type"    />
        <result property="questionContent"    column="question_content"    />
        <result property="rightAnswer"    column="right_answer"    />
        <result property="analysis"    column="analysis"    />
        <result property="userId"    column="user_id"    />
        <result property="disorder"    column="disorder"    />
        <result property="sort"    column="sort"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="folderId"    column="folder_id"    />
        <result property="codeContent" column="code_content"/>
        <result property="folderName"    column="folder_name"    />
        <result property="questionRemark" column="question_remark"/>
        <result property="createSource" column="create_source"/>
    </resultMap>

    <resultMap type="MoocUserQuestion" id="MoocUserQuestionWithOptionsResult" extends="MoocUserQuestionResult">
        <collection property="options" 
                    column="question_id" 
                    select="selectQuestionOptions"
                    fetchType="eager"/>
    </resultMap>

    <resultMap id="QuestionOptionResult" type="MoocUserQuestionOption">
        <result property="optionId" column="option_id"/>
        <result property="optionContent" column="option_content"/>
        <result property="questionId" column="question_id"/>
        <result property="rightFlag" column="right_flag"/>
        <result property="optionPosition" column="option_position"/>
        <result property="sort" column="sort"/>
    </resultMap>

    <sql id="selectMoocUserQuestionVo">
        select question_id, question_type, question_content, right_answer, analysis, user_id, 
               disorder, sort, create_by, create_time, update_by, update_time, del_flag, 
               folder_id, code_content, create_source, question_remark
        from mooc_user_question
    </sql>

    <select id="selectQuestionOptions" resultMap="QuestionOptionResult">
        SELECT 
            option_id, option_content, question_id, right_flag, 
            option_position, sort
        FROM mooc_user_question_option
        WHERE question_id = #{question_id}
        AND (del_flag = '0' OR del_flag IS NULL)
        ORDER BY sort
    </select>

    <select id="selectMoocUserQuestionListWithOptions" parameterType="MoocUserQuestion" resultMap="MoocUserQuestionWithOptionsResult">
        SELECT 
            q.question_id, q.question_type, q.question_content, q.right_answer, 
            q.analysis, q.user_id, q.disorder, q.sort,
            q.create_by, q.create_time, q.update_by, q.update_time, 
            q.del_flag, q.folder_id, q.code_content, q.question_remark, q.create_source
        FROM mooc_user_question q
        <where>
            <if test="questionType != null">AND q.question_type = #{questionType}</if>
            <if test="questionContent != null and questionContent != ''">AND q.question_content like concat('%', #{questionContent}, '%')</if>
            <if test="userId != null">AND q.user_id = #{userId}</if>
            <if test="disorder != null">AND q.disorder = #{disorder}</if>
            <if test="folderId != null">AND q.folder_id = #{folderId}</if>
            AND q.del_flag = '0'
        </where>
        ORDER BY q.sort desc, q.create_time desc
    </select>

    <!-- 移入回收站 -->
    <update id="moveToRecycleBin">
        update mooc_user_question set del_flag = '1', update_time = sysdate(), update_by=#{username}
        where question_id in
        <foreach collection="questionIds" item="questionId" open="(" separator="," close=")">
            #{questionId}
        </foreach>
    </update>

    <!-- 从回收站恢复 -->
    <update id="restoreByIds">
        update mooc_user_question set del_flag = '0', update_time = sysdate(), update_by=#{username}
        where question_id in
        <foreach collection="questionIds" item="questionId" open="(" separator="," close=")">
            #{questionId}
        </foreach>
    </update>

    <!-- 查询回收站列表 -->
    <select id="recycleBinList" resultMap="MoocUserQuestionResult">
        select q.*, folder.folder_name from mooc_user_question q
               left join mooc_user_question_folder folder on q.folder_id = folder.folder_id
        where q.del_flag = '1'
        <if test="folderId != null">
            and q.folder_id = #{folderId}
        </if>
        <if test="userId != null">
            and q.user_id = #{userId}
        </if>
        <if test="questionType != null and questionType != ''">
            and q.question_type = #{questionType}
        </if>
        order by q.create_time desc
    </select>

    <select id="selectMoocUserQuestionBatch" resultMap="MoocUserQuestionResult">
        <include refid="selectMoocUserQuestionVo"/>
        where question_id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="checkPaperReference" parameterType="Long[]" resultType="int">
        SELECT COUNT(1)
        FROM mooc_smart_course_test_paper_question
        LEFT JOIN mooc_smart_course_test_paper ON mooc_smart_course_test_paper_question.paper_id = mooc_smart_course_test_paper.paper_id
        WHERE mooc_smart_course_test_paper_question.question_id in
        <foreach collection="questionIds" item="questionId" open="(" separator="," close=")">
            #{questionId}
        </foreach>
        AND mooc_smart_course_test_paper.del_flag = '0'
    </select>
</mapper> 
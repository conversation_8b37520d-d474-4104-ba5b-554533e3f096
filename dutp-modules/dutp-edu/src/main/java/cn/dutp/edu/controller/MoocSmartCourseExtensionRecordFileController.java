package cn.dutp.edu.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.edu.domain.MoocSmartCourseExtensionRecordFile;
import cn.dutp.edu.service.IMoocSmartCourseExtensionRecordFileService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 拓展内容提交文件Controller
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@RestController
@RequestMapping("/extensionRecordFile")
public class MoocSmartCourseExtensionRecordFileController extends BaseController
{
    @Autowired
    private IMoocSmartCourseExtensionRecordFileService moocSmartCourseExtensionRecordFileService;

    /**
     * 查询拓展内容提交文件列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocSmartCourseExtensionRecordFile moocSmartCourseExtensionRecordFile)
    {
        startPage();
        List<MoocSmartCourseExtensionRecordFile> list = moocSmartCourseExtensionRecordFileService.selectMoocSmartCourseExtensionRecordFileList(moocSmartCourseExtensionRecordFile);
        return getDataTable(list);
    }

    /**
     * 导出拓展内容提交文件列表
     */
    @RequiresPermissions("edu:extensionRecordFile:export")
    @Log(title = "导出拓展内容提交文件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocSmartCourseExtensionRecordFile moocSmartCourseExtensionRecordFile)
    {
        List<MoocSmartCourseExtensionRecordFile> list = moocSmartCourseExtensionRecordFileService.selectMoocSmartCourseExtensionRecordFileList(moocSmartCourseExtensionRecordFile);
        ExcelUtil<MoocSmartCourseExtensionRecordFile> util = new ExcelUtil<MoocSmartCourseExtensionRecordFile>(MoocSmartCourseExtensionRecordFile.class);
        util.exportExcel(response, list, "拓展内容提交文件数据");
    }

    /**
     * 获取拓展内容提交文件详细信息
     */
    @RequiresPermissions("edu:extensionRecordFile:query")
    @GetMapping(value = "/{fileId}")
    public AjaxResult getInfo(@PathVariable("fileId") Long fileId)
    {
        return success(moocSmartCourseExtensionRecordFileService.selectMoocSmartCourseExtensionRecordFileByFileId(fileId));
    }

    /**
     * 新增拓展内容提交文件
     */
    @RequiresPermissions("edu:extensionRecordFile:add")
    @Log(title = "新增拓展内容提交文件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocSmartCourseExtensionRecordFile moocSmartCourseExtensionRecordFile)
    {
       
        return toAjax(moocSmartCourseExtensionRecordFileService.insertMoocSmartCourseExtensionRecordFile(moocSmartCourseExtensionRecordFile));
    }

    /**
     * 修改拓展内容提交文件
     */
    @RequiresPermissions("edu:extensionRecordFile:edit")
    @Log(title = "修改拓展内容提交文件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocSmartCourseExtensionRecordFile moocSmartCourseExtensionRecordFile)
    {
        return toAjax(moocSmartCourseExtensionRecordFileService.updateMoocSmartCourseExtensionRecordFile(moocSmartCourseExtensionRecordFile));
    }

    /**
     * 删除拓展内容提交文件
     */
    @RequiresPermissions("edu:extensionRecordFile:remove")
    @Log(title = "删除拓展内容提交文件", businessType = BusinessType.DELETE)
    @DeleteMapping("/{fileIds}")
    public AjaxResult remove(@PathVariable Long[] fileIds)
    {
        return toAjax(moocSmartCourseExtensionRecordFileService.deleteMoocSmartCourseExtensionRecordFileByFileIds(Arrays.asList(fileIds)));
    }
}

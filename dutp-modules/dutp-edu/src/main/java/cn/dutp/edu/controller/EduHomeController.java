package cn.dutp.edu.controller;

import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBookOrderVo;
import cn.dutp.domain.DtbBookVo;
import cn.dutp.domain.vo.DtbEduBookOrderDetailVo;
import cn.dutp.domain.vo.DtbEduBookOrderVo;
import cn.dutp.edu.domain.vo.DtbBookRefundOrderManagementVo;
import cn.dutp.edu.domain.vo.DutpUserVo;
import cn.dutp.edu.service.DtbBookService;
import cn.dutp.edu.service.DutpUserService;
import cn.dutp.edu.service.EduHomeService;
import cn.dutp.message.api.domain.DutpUserMessage;
import cn.hutool.core.util.ObjectUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author  YJP
 * Date  2025/1/13 下午2:14
 * Description:
 */
@RestController
@RequestMapping("/eduHome")
public class EduHomeController extends BaseController {
    @Autowired
    private DutpUserService dutpUserService;
    @Autowired
    private EduHomeService eduHomeService;
    @Autowired
    private DtbBookService bookService;

    /**
     * 消息中心列表
     *
     * @return 消息列表
     */
    @GetMapping("/messageInfo")
    public AjaxResult messageInfo() {
        // 根据登陆用户id获取消息列表
        List<DutpUserMessage> dutpUserMessages = eduHomeService.messageList(SecurityUtils.getUserId());
        return success(dutpUserMessages);
    }

    /**
     * 教学基本信息
     *
     * @return 院系数量 专业数量 学生数量 教师数量
     */
    @GetMapping("/basicTeachingInformation")
    public AjaxResult basicTeachingInformation() {
        Map<String, Object> entity = new HashMap<>();
        // 根据登陆用户的学校id获取学校 院系数量
        List<Long> departmentList = eduHomeService.selectDepartmentIdBySchoolId(SecurityUtils.getLoginUser().getSchoolId());
        // 专业数量默认为0
        Integer majorNumber = 0;
        // 学校存在院系才会有专业,没有院系的时候不去查专业
        if (!departmentList.isEmpty()) {
            // 根据院系id的List去获取所有专业数量
            majorNumber = eduHomeService.countMajorByDepartmentList(departmentList);
        }
        // 根据登陆用户的学校id获取学生数量和教师数量
        DutpUserVo dutpUserVo = dutpUserService.countTeacherAndStudentBySchoolId(SecurityUtils.getLoginUser().getSchoolId());
        entity.put("departmentNumber", departmentList.size());// 院系数量
        entity.put("majorNumber", majorNumber);// 专业数量
        entity.put("studentNumber", 0);// 学生数量
        entity.put("teacherNumber", 0);// 教师数量
        if (null != dutpUserVo) {
            if (ObjectUtil.isNotEmpty(dutpUserVo.getTeacherNumber())) {
                entity.put("teacherNumber", dutpUserVo.getTeacherNumber());// 教师数量
            }
            if (ObjectUtil.isNotEmpty(dutpUserVo.getStudentNumber())) {
                entity.put("studentNumber", dutpUserVo.getStudentNumber());// 学生数量
            }
        }
        return success(entity);
    }

    /**
     * 教材总览
     *
     * @return 教材总数 已出版教材数量 制作中教材数量 已上架教材数量
     */
    @GetMapping("/overviewOfTextbooks")
    public AjaxResult overviewOfTextbooks() {
        // 根据登陆用户的学校id获取教材信息
        DtbBookVo dtbBookVo = eduHomeService.overviewOfTextbooks(SecurityUtils.getLoginUser().getSchoolId());
        // 当学校没有教材的情况下数据默认值为0
        if (dtbBookVo.getTextbooksNumber() == 0) {
            dtbBookVo.setInProductionNumber(0);
            dtbBookVo.setShelvesNumber(0);
            dtbBookVo.setPublishedNumber(0);
        }
        return success(dtbBookVo);
    }

    /**
     * 订单管理
     *
     * @return 订单总数 书商订单，代采订单
     */
    @GetMapping("/orderManagement/{limitNum}")
    public AjaxResult orderManagement(@PathVariable("limitNum") Integer limitNum) {
        Long schoolId = SecurityUtils.getLoginUser().getSchoolId();
        // 根据登陆用户的学校id获取不同类型订单的数量
        DtbBookOrderVo dtbBookOrderVo = eduHomeService.countOrderNumber(schoolId);
        // 获取登陆用户的学校id的主订单列表
        List<DtbEduBookOrderVo> dtbEduBookOrderList = eduHomeService.selectOrderInfoBySchoolId(schoolId, limitNum);
        for (DtbEduBookOrderVo dtbEduBookOrderVo : dtbEduBookOrderList) {
            // 查询子订单详情
            List<DtbEduBookOrderDetailVo> dtbEduBookOrderDetailList = eduHomeService.selectOrderDetailById(dtbEduBookOrderVo.getOrderId());
            dtbEduBookOrderVo.setDtbEduBookOrderDetailList(dtbEduBookOrderDetailList);
        }
        // 没有订单的情况下数据默认值为0
        if (!ObjectUtil.isNotEmpty(dtbBookOrderVo)) {
            dtbBookOrderVo = new DtbBookOrderVo();
            dtbBookOrderVo.setTotalOrderNumber(0);
            dtbBookOrderVo.setSellerOrderNumber(0);
            dtbBookOrderVo.setProxyOrderNumber(0);
            dtbBookOrderVo.setOrderBookList(null);
        } else {
            dtbBookOrderVo.setTotalOrderNumber(dtbBookOrderVo.getSellerOrderNumber() + dtbBookOrderVo.getProxyOrderNumber());
            dtbBookOrderVo.setOrderBookList(dtbEduBookOrderList);
        }
        return success(dtbBookOrderVo);
    }

    /**
     * 售后管理
     *
     * @return 售后订单总数 退款中订单数量 售后处理已完成的订单数量
     */
    @GetMapping("/afterSalesManagement")
    public AjaxResult afterSalesManagement() {
        // 根据登陆用户的学校id获取售后订单信息
        DtbBookRefundOrderManagementVo dtbBookRefundOrderVo = eduHomeService.countRefundOrderNumber(SecurityUtils.getLoginUser().getSchoolId());
        // 没有售后订单的情况下数据默认值为0
        if (dtbBookRefundOrderVo.getRefundTotalOrderNumber() == 0) {
            dtbBookRefundOrderVo.setCompletedNumber(0);
            dtbBookRefundOrderVo.setRefundInProgressNumber(0);
        }
        return success(dtbBookRefundOrderVo);
    }


}

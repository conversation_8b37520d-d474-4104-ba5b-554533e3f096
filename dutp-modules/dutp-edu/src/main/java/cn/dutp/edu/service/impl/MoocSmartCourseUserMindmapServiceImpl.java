package cn.dutp.edu.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.edu.mapper.MoocSmartCourseUserMindmapMapper;
import cn.dutp.edu.domain.MoocSmartCourseUserMindmap;
import cn.dutp.edu.service.IMoocSmartCourseUserMindmapService;
import javax.annotation.Resource;

/**
 * 用户书籍思维导图数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class MoocSmartCourseUserMindmapServiceImpl extends ServiceImpl<MoocSmartCourseUserMindmapMapper, MoocSmartCourseUserMindmap> implements IMoocSmartCourseUserMindmapService
{
    @Resource
    private MoocSmartCourseUserMindmapMapper moocSmartCourseUserMindmapMapper;

    @Override
    public MoocSmartCourseUserMindmap selectByUserIdAndBookId(Long userId, Long bookId) {
        return moocSmartCourseUserMindmapMapper.selectByUserIdAndBookId(userId, bookId);
    }

    /**
     * 查询用户书籍思维导图数据
     *
     * @param mindmapId 用户书籍思维导图数据主键
     * @return 用户书籍思维导图数据
     */
    @Override
    public MoocSmartCourseUserMindmap selectMoocSmartCourseUserMindmapByMindmapId(Long mindmapId)
    {
        return this.getById(mindmapId);
    }

    /**
     * 查询用户书籍思维导图数据列表
     *
     * @param moocSmartCourseUserMindmap 用户书籍思维导图数据
     * @return 用户书籍思维导图数据
     */
    @Override
    public List<MoocSmartCourseUserMindmap> selectMoocSmartCourseUserMindmapList(MoocSmartCourseUserMindmap moocSmartCourseUserMindmap)
    {
        LambdaQueryWrapper<MoocSmartCourseUserMindmap> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(moocSmartCourseUserMindmap.getUserId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseUserMindmap::getUserId
                ,moocSmartCourseUserMindmap.getUserId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseUserMindmap.getBookId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseUserMindmap::getBookId
                ,moocSmartCourseUserMindmap.getBookId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseUserMindmap.getMindMapData())) {
                lambdaQueryWrapper.eq(MoocSmartCourseUserMindmap::getMindMapData
                ,moocSmartCourseUserMindmap.getMindMapData());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增用户书籍思维导图数据
     *
     * @param moocSmartCourseUserMindmap 用户书籍思维导图数据
     * @return 结果
     */
    @Override
    public boolean insertMoocSmartCourseUserMindmap(MoocSmartCourseUserMindmap moocSmartCourseUserMindmap)
    {
        return this.save(moocSmartCourseUserMindmap);
    }

    /**
     * 修改用户书籍思维导图数据
     *
     * @param moocSmartCourseUserMindmap 用户书籍思维导图数据
     * @return 结果
     */
    @Override
    public boolean updateMoocSmartCourseUserMindmap(MoocSmartCourseUserMindmap moocSmartCourseUserMindmap)
    {
        return this.updateById(moocSmartCourseUserMindmap);
    }

    /**
     * 批量删除用户书籍思维导图数据
     *
     * @param mindmapIds 需要删除的用户书籍思维导图数据主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocSmartCourseUserMindmapByMindmapIds(List<Long> mindmapIds)
    {
        return this.removeByIds(mindmapIds);
    }

}

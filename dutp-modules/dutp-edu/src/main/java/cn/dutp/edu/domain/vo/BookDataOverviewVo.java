package cn.dutp.edu.domain.vo;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class BookDataOverviewVo {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long dataId;

    /**
     * 教材ID
     */
    @Excel(name = "教材ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 章节ID
     */
    @Excel(name = "章节ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    /**
     * 文字数量
     */
    @Excel(name = "文字数量")
    private Long wordQuantity;

    /**
     * 图片数量
     */
    @Excel(name = "图片数量")
    private Long imageQuanity;

    /**
     * 气泡数量
     */
    @Excel(name = "气泡数量")
    private Long bubbleQuantity;

    /**
     * 链接数量
     */
    @Excel(name = "链接数量")
    private Long outsideLinkQuantity;

    /**
     * 公式数量
     */
    @Excel(name = "公式数量")
    private Long formulaQuantity;

    /**
     * 3D数量
     */
    @Excel(name = "3D数量")
    private Long threeDimenQuantity;

    /**
     * AR/VR数量
     */
    @Excel(name = "AR/VR数量")
    private Long avrQuantity;

    /**
     * 仿真数量
     */
    @Excel(name = "仿真数量")
    private Long simulationQuantity;

    /**
     * 试题数量
     */
    @Excel(name = "试题数量")
    private Long questionQuantity;

    /**
     * 游戏数量
     */
    @Excel(name = "游戏数量")
    private Long gameQuantity;

    /**
     * 脚注数量
     */
    @Excel(name = "脚注数量")
    private Long footnoteQuantity;

    /**
     * 教学资源数量
     */
    @Excel(name = "教学资源数量")
    private Long resouceQuantity;

    /**
     * 音频数量
     */
    @Excel(name = "音频数量")
    private Long audioQuantity;

    /**
     * 视频数量
     */
    @Excel(name = "视频数量")
    private Long videoQuantity;

    /**
     * 拓展阅读数量
     */
    @Excel(name = "拓展阅读数量")
    private Long extQuantity;

    /**
     * 代码块数量
     */
    @Excel(name = "代码块数量")
    private Long codeQuantity;

    /**
     * 音频总时长-秒
     */
    @Excel(name = "音频总时长-秒")
    private Long audioTotalDuration;

    /**
     * 互动投票数量
     */
    @Excel(name = "互动投票数量")
    private Long interactionVoteQuantity;

    /**
     * 视频总时长
     */
    @Excel(name = "视频总时长")
    private Long videoTotalDuration;

    /**
     * 互动词云数量
     */
    @Excel(name = "互动词云数量")
    private Long interactionWordCloudQuantity;

    /**
     * 互动讨论数量
     */
    @Excel(name = "互动讨论数量")
    private Long interactionDiscussQuantity;

    /**
     * 互动图片瀑布流数量
     */
    @Excel(name = "互动图片瀑布流数量")
    private Long interactionImageWaterfallQuantity;

    /**
     * 内部链接数量
     */
    @Excel(name = "内部链接数量")
    private Long insideLinkQuantity;

    /**
     * 学习总时长
     */
    @Excel(name = "学习总时长")
    private Long studyTotalTime;

    /**
     * 视频总时长
     */
    @Excel(name = "视频总时长")
    private Long studyVideoTime;

    /**
     * 笔记总数
     */
    @Excel(name = "笔记总数")
    private Long studyNoteQuantity;

    /**
     * 高亮总数
     */
    @Excel(name = "高亮总数")
    private Long studyHighlightQuantity;

    /**
     * 讨论次数
     */
    @Excel(name = "讨论次数")
    private Long studyDiscussQuantity;

    /**
     * 学习人数
     */
    @Excel(name = "学习人数")
    private Long studyUserQuantity;

    /**
     * 试题练习总次数
     */
    @Excel(name = "试题练习总次数")
    private Long studyQuestionQuantity;

    /**
     * 试题平均正确率
     */
    @Excel(name = "试题平均正确率")
    private BigDecimal studyQuestionRate;


    @Excel(name = "学习学校数")
    private Long studySchoolQuantity;

    /**
     * 章节名称
     */
    @TableField(exist = false)
    private String chapterName;

    /**
     * 题型列表
     */
    @TableField(exist = false)
    private List<Long> questionList;
} 
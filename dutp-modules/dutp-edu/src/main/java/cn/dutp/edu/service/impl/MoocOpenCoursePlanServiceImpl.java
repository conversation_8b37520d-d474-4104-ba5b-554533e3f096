package cn.dutp.edu.service.impl;

import cn.dutp.domain.MoocOpenCoursePlan;
import cn.dutp.edu.mapper.MoocOpenCoursePlanMapper;
import cn.dutp.edu.service.IMoocOpenCoursePlanService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 公开课开课计划Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Service
public class MoocOpenCoursePlanServiceImpl extends ServiceImpl<MoocOpenCoursePlanMapper, MoocOpenCoursePlan> implements IMoocOpenCoursePlanService {
    @Autowired
    private MoocOpenCoursePlanMapper moocOpenCoursePlanMapper;

    /**
     * 查询公开课开课计划
     *
     * @param planId 公开课开课计划主键
     * @return 公开课开课计划
     */
    @Override
    public MoocOpenCoursePlan selectMoocOpenCoursePlanByPlanId(Long planId) {
        return this.getById(planId);
    }

    /**
     * 查询公开课开课计划列表
     *
     * @param moocOpenCoursePlan 公开课开课计划
     * @return 公开课开课计划
     */
    @Override
    public List<MoocOpenCoursePlan> selectMoocOpenCoursePlanList(MoocOpenCoursePlan moocOpenCoursePlan) {
        LambdaQueryWrapper<MoocOpenCoursePlan> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotEmpty(moocOpenCoursePlan.getCourseId())) {
            lambdaQueryWrapper.eq(MoocOpenCoursePlan::getCourseId
                    , moocOpenCoursePlan.getCourseId());
        }
        if (ObjectUtil.isNotEmpty(moocOpenCoursePlan.getName())) {
            lambdaQueryWrapper.like(MoocOpenCoursePlan::getName
                    , moocOpenCoursePlan.getName());
        }
        if (ObjectUtil.isNotEmpty(moocOpenCoursePlan.getStartTime())) {
            lambdaQueryWrapper.eq(MoocOpenCoursePlan::getStartTime
                    , moocOpenCoursePlan.getStartTime());
        }
        if (ObjectUtil.isNotEmpty(moocOpenCoursePlan.getEndTime())) {
            lambdaQueryWrapper.eq(MoocOpenCoursePlan::getEndTime
                    , moocOpenCoursePlan.getEndTime());
        }
        if (ObjectUtil.isNotEmpty(moocOpenCoursePlan.getSort())) {
            lambdaQueryWrapper.eq(MoocOpenCoursePlan::getSort
                    , moocOpenCoursePlan.getSort());
        }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增公开课开课计划
     *
     * @param moocOpenCoursePlan 公开课开课计划
     * @return 结果
     */
    @Override
    public boolean insertMoocOpenCoursePlan(MoocOpenCoursePlan moocOpenCoursePlan) {
        return this.save(moocOpenCoursePlan);
    }

    /**
     * 修改公开课开课计划
     *
     * @param moocOpenCoursePlan 公开课开课计划
     * @return 结果
     */
    @Override
    public boolean updateMoocOpenCoursePlan(MoocOpenCoursePlan moocOpenCoursePlan) {
        return this.updateById(moocOpenCoursePlan);
    }

    /**
     * 批量删除公开课开课计划
     *
     * @param planIds 需要删除的公开课开课计划主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocOpenCoursePlanByPlanIds(List<Long> planIds) {
        return this.removeByIds(planIds);
    }

}

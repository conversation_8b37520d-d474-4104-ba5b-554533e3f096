package cn.dutp.edu.service;

import java.util.List;
import cn.dutp.edu.domain.MoocSmartCourseClassAttendance;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 互动课堂班级签到活动Service接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IMoocSmartCourseClassAttendanceService extends IService<MoocSmartCourseClassAttendance>
{
    /**
     * 查询互动课堂班级签到活动
     *
     * @param attendanceActivityId 互动课堂班级签到活动主键
     * @return 互动课堂班级签到活动
     */
    public MoocSmartCourseClassAttendance selectMoocSmartCourseClassAttendanceByAttendanceActivityId(Long attendanceActivityId);

    /**
     * 查询互动课堂班级签到活动列表
     *
     * @param moocSmartCourseClassAttendance 互动课堂班级签到活动
     * @return 互动课堂班级签到活动集合
     */
    public List<MoocSmartCourseClassAttendance> selectMoocSmartCourseClassAttendanceList(MoocSmartCourseClassAttendance moocSmartCourseClassAttendance);

    /**
     * 新增互动课堂班级签到活动
     *
     * @param moocSmartCourseClassAttendance 互动课堂班级签到活动
     * @return 结果
     */
    public boolean insertMoocSmartCourseClassAttendance(MoocSmartCourseClassAttendance moocSmartCourseClassAttendance);

    /**
     * 修改互动课堂班级签到活动
     *
     * @param moocSmartCourseClassAttendance 互动课堂班级签到活动
     * @return 结果
     */
    public boolean updateMoocSmartCourseClassAttendance(MoocSmartCourseClassAttendance moocSmartCourseClassAttendance);

    /**
     * 批量删除互动课堂班级签到活动
     *
     * @param attendanceActivityIds 需要删除的互动课堂班级签到活动主键集合
     * @return 结果
     */
    public boolean deleteMoocSmartCourseClassAttendanceByAttendanceActivityIds(List<Long> attendanceActivityIds);

}

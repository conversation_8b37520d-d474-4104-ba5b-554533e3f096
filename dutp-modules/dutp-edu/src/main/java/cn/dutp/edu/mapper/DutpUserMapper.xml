<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.DutpUserMapper">

    <select id="selectAll" resultType="cn.dutp.edu.domian.DutpUser" flushCache="true" useCache="false">
        SELECT
        u.*,
        (SELECT school_name FROM dutp_school AS s WHERE s.school_id = u.school_id AND data_type = 0 AND del_flag = 0) AS school_name,
        (SELECT school_name FROM dutp_school AS s WHERE s.school_id = u.academy_id AND data_type = 1 AND del_flag = 0) AS academy_name,
        (SELECT school_name FROM dutp_school AS s WHERE s.school_id = u.speciality_id AND data_type = 2 AND del_flag = 0) AS subject_name,
        (SELECT COUNT(*) FROM dtb_user_book AS ub WHERE ub.user_id = u.user_id AND add_way = 2 AND del_flag = 0) AS schoolAdd,
        (SELECT COUNT(*) FROM dtb_user_book AS ub WHERE ub.user_id = u.user_id AND add_way = 1 AND del_flag = 0) AS personAdd
        FROM
        dutp_user AS u
        <where>
            <if test="nickName != null and nickName != ''">AND u.nick_name LIKE CONCAT('%', #{nickName}, '%')</if>
            <if test="realName != null and realName != ''">AND u.real_name LIKE CONCAT('%', #{realName}, '%')</if>
            <if test="userNo != null and userNo != ''">AND u.user_no LIKE CONCAT('%', #{userNo}, '%')</if>
            <if test="userType != null and userType != ''">AND u.user_type LIKE CONCAT('%', #{userType}, '%')</if>
            <if test="schoolId != null">AND u.school_id = #{schoolId}</if>
            AND u.del_flag = 0
        </where>
        ORDER BY u.create_time DESC
    </select>
    <select id="selectStudent" resultType="cn.dutp.edu.domian.DutpUser">
        SELECT
        u.* ,s.school_name,a.academy_name
        FROM
        dutp_user AS u
        LEFT JOIN dutp_school AS s ON u.school_id = s.school_id AND s.del_flag = 0
        LEFT JOIN dutp_academy as a on u.academy_id = a.academy_id and a.del_flag = 0
        <where>
            <if test="nickName != null and nickName != ''">and u.nick_name like concat('%' ,#{nickName},'%')</if>
            <if test="userNo != null and userNo != ''">and u.user_no like concat('%' ,#{userNo},'%')</if>
            <if test="schoolId != null">and s.school_id = #{schoolId}</if>
            and
            u.del_flag = 0
            and
            u.user_type = 1
        </where>
    </select>
    <select id="selectTeacher" resultType="cn.dutp.edu.domian.DutpUser">
        SELECT
        u.* ,s.school_name,a.academy_name
        FROM
        dutp_user AS u
        LEFT JOIN dutp_school AS s ON u.school_id = s.school_id AND s.del_flag = 0
        LEFT JOIN dutp_academy as a on u.academy_id = a.academy_id and a.del_flag = 0
        <where>
            <if test="nickName != null and nickName != ''">and u.nick_name like concat('%' ,#{nickName},'%')</if>
            <if test="userNo != null and userNo != ''">and u.user_no like concat('%' ,#{userNo},'%')</if>
            <if test="schoolId != null">and s.school_id = #{schoolId}</if>
            and
            u.del_flag = 0
            and
            u.user_type = 2
        </where>
    </select>

    <!--根据登陆用户的学校id获取 学生数量 教师数量-->
    <select id="countTeacherAndStudentBySchoolId" resultType="cn.dutp.edu.domain.vo.DutpUserVo">
        SELECT SUM(CASE WHEN user_type = 1 THEN 1 ELSE 0 END) AS studentNumber,
               SUM(CASE WHEN user_type = 2 THEN 1 ELSE 0 END) AS teacherNumber
        FROM dutp_user
        WHERE school_id = #{schoolId}
          AND del_flag = 0
    </select>

    <select id="selectBookUserList" parameterType="cn.dutp.edu.domain.dto.BookDto" resultType="cn.dutp.edu.domian.DutpUser">
        select
            du.user_no,
            dub.user_book_id,
            du.user_type,
            du.nick_name,
            du.phonenumber,
            du.speciality_id,
            du.academy_id,
            du.user_id,
            ds2.school_name as academyName,
            ds1.school_name as specialityName
        from
            dtb_user_book dub
        left join
            dutp_user du on du.user_id = dub.user_id
        left join
            dutp_school ds1 on ds1.school_id = du.speciality_id
        left join
            dutp_school ds2 on ds2.school_id = du.academy_id
        <where>
            du.del_flag = 0 and du.status = 0 and dub.book_id = #{bookId}
            <if test="nickName != null and nickName != ''">and du.nick_name like concat('%' ,#{nickName},'%')</if>
            <if test="userNo != null and userNo != ''">and du.user_no like concat('%' ,#{userNo},'%')</if>
            <if test="schoolId != null">and du.school_id = #{schoolId}</if>
            <if test="specialityId != null">and du.speciality_id = #{specialityId}</if>
            <if test="academyId != null">and du.academy_id = #{academyId}</if>
        </where>
    </select>

    <select id="removeBookUser" parameterType="cn.dutp.edu.domain.dto.BookDto">
        delete from dtb_user_book
        <where>
            user_book_id =#{userBookId}
        </where>
    </select>
    <select id="selectExportList" resultType="cn.dutp.edu.domian.DutpUser">
        SELECT
            u.user_name,real_name,u.user_type,u.user_no,u.email,
            (SELECT school_name FROM dutp_school AS s WHERE s.school_id = u.school_id AND data_type = 0 AND del_flag = 0) AS school_name,
            (SELECT school_name FROM dutp_school AS s WHERE s.school_id = u.academy_id AND data_type = 1 AND del_flag = 0) AS academy_name,
            (SELECT school_name FROM dutp_school AS s WHERE s.school_id = u.speciality_id AND data_type = 2 AND del_flag = 0) AS subject_name,
            (SELECT COUNT(*) FROM dtb_user_book AS ub WHERE ub.user_id = u.user_id AND add_way = 2 AND del_flag = 0) AS schoolAdd,
            (SELECT COUNT(*) FROM dtb_user_book AS ub WHERE ub.user_id = u.user_id AND add_way = 1 AND del_flag = 0) AS personAdd
        FROM
            dutp_user AS u
        ORDER BY u.create_time DESC
    </select>
</mapper>
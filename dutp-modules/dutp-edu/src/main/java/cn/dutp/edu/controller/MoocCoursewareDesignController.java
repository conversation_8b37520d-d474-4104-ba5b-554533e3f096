package cn.dutp.edu.controller;

import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.domain.MoocCoursewareDesign;
import cn.dutp.edu.service.IMoocCoursewareDesignService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 公开课课件设计Controller
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@RestController
@RequestMapping("/coursewareDesign")
public class MoocCoursewareDesignController extends BaseController {
    @Autowired
    private IMoocCoursewareDesignService moocCoursewareDesignService;

    /**
     * 查询公开课课件设计列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocCoursewareDesign moocCoursewareDesign) {
        startPage();
        List<MoocCoursewareDesign> list = moocCoursewareDesignService.selectMoocCoursewareDesignList(moocCoursewareDesign);
        return getDataTable(list);
    }

    /**
     * 导出公开课课件设计列表
     */
    @Log(title = "导出公开课课件设计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocCoursewareDesign moocCoursewareDesign) {
        List<MoocCoursewareDesign> list = moocCoursewareDesignService.selectMoocCoursewareDesignList(moocCoursewareDesign);
        ExcelUtil<MoocCoursewareDesign> util = new ExcelUtil<MoocCoursewareDesign>(MoocCoursewareDesign.class);
        util.exportExcel(response, list, "公开课课件设计数据");
    }

    /**
     * 获取公开课课件设计详细信息
     */
    @GetMapping(value = "/{coursewareDesignId}")
    public AjaxResult getInfo(@PathVariable("coursewareDesignId") Long coursewareDesignId) {
        return success(moocCoursewareDesignService.selectMoocCoursewareDesignByCoursewareDesignId(coursewareDesignId));
    }
    
    /**
     * 获取公开课课件设计树状结构
     */
    @GetMapping(value = "/content/{coursewareDesignId}")
    public AjaxResult getDesignContent(@PathVariable("coursewareDesignId") Long coursewareDesignId) {
        return success(moocCoursewareDesignService.getDesignContentByCoursewareDesignId(coursewareDesignId));
    }

    /**
     * 新增公开课课件设计
     */
    @Log(title = "新增公开课课件设计", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocCoursewareDesign moocCoursewareDesign) {
        return toAjax(moocCoursewareDesignService.insertMoocCoursewareDesign(moocCoursewareDesign));
    }

    /**
     * 修改公开课课件设计
     */
    @Log(title = "修改公开课课件设计", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocCoursewareDesign moocCoursewareDesign) {
        return toAjax(moocCoursewareDesignService.updateMoocCoursewareDesign(moocCoursewareDesign));
    }

    /**
     * 删除公开课课件设计
     */
    @Log(title = "删除公开课课件设计", businessType = BusinessType.DELETE)
    @DeleteMapping("/{coursewareDesignIds}")
    public AjaxResult remove(@PathVariable Long[] coursewareDesignIds) {
        return toAjax(moocCoursewareDesignService.deleteMoocCoursewareDesignByCoursewareDesignIds(Arrays.asList(coursewareDesignIds)));
    }

    /**
     * 查询公开课课件设计列表不分页
     */
    @GetMapping("/listNoPage")
    public AjaxResult listNoPage(MoocCoursewareDesign moocCoursewareDesign) {
        return success(moocCoursewareDesignService.listNoPage(moocCoursewareDesign));
    }

    /**
     * 获取公开课课件设计详细信息
     */
    @GetMapping(value = "/getcoursewareDesign/{coursewareDesignId}")
    public AjaxResult getCoursewareDesignInfo(@PathVariable("coursewareDesignId") Long coursewareDesignId) {
        return success(moocCoursewareDesignService.getCoursewareDesignInfo(coursewareDesignId));
    }
}

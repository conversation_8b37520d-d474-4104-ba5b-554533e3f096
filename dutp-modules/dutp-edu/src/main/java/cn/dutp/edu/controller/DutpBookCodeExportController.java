package cn.dutp.edu.controller;

import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBookCodeExportInfo;
import cn.dutp.domain.DtbBookCodeExportItem;
import cn.dutp.domain.dto.DtbBookPurchaseCodeDto;
import cn.dutp.domain.vo.DtbBookPurchaseCodeVO;
import cn.dutp.edu.domain.dto.BookDto;
import cn.dutp.edu.domain.vo.BookVo;
import cn.dutp.edu.domain.vo.DtbBookPurchaseCodeExport;
import cn.dutp.edu.domain.vo.DtbBookdtbPurchaseCodeUnboundExport;
import cn.dutp.edu.service.DtbBookCodeExportInfoService;
import cn.dutp.edu.service.DtbBookCodeExportItemService;
import cn.dutp.edu.service.DtbBookCodeExportService;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: YJP
 * @date: 2025/2/24
 */
@RestController
@RequestMapping("/bookCodeExport")
public class DutpBookCodeExportController extends BaseController {

    @Autowired
    private DtbBookCodeExportInfoService dtbBookCodeExportInfoService;

    @Autowired
    private DtbBookCodeExportItemService dtbBookCodeExportItemService;

    @Autowired
    private DtbBookCodeExportService bookCodeExportService;

    /**
     * 根据订单id获取 订单的书籍集合
     *
     * @return 书籍集合
     */
    @GetMapping("/selectBookListByOrderId/{orderId}")
    public AjaxResult selectBookListByOrderId(@PathVariable("orderId") Long orderId) {
        // 根据订单id获取订单的书籍集合
        List<BookVo> bookVos = bookCodeExportService.selectBookListByOrderId(orderId);
        return success(bookVos);
    }


    /**
     * 查询登录人学校名下当前书籍下已兌換的购书码信息列表
     *
     * @param bookDto 信息
     * @return 已兌換的购书码信息列表
     */
    @RequiresPermissions("edu:bookCodeRecord:AlreadyBoundList")
    @PostMapping("/alreadyBoundCode")
    public TableDataInfo alreadyBoundCode(@RequestBody BookDto bookDto) {
        PageHelper.startPage(bookDto.getPageNum(), bookDto.getPageSize());
        bookDto.setSchoolId(SecurityUtils.getLoginUser().getSchoolId());
        List<DtbBookPurchaseCodeVO> dtbBookPurchaseCodeVO = bookCodeExportService.alreadyBoundCode(bookDto);
        return getDataTable(dtbBookPurchaseCodeVO);
    }

    /**
     * 查询登录人学校名下当前书籍下的未兌換的购书码
     *
     * @param bookDto 信息
     * @return 当前书籍下的未兌換的购书码
     */
    @RequiresPermissions("edu:bookCodeRecord:unBoundList")
    @PostMapping("/unboundCode")
    public TableDataInfo getUnboundCodeList(@RequestBody BookDto bookDto) {
        PageHelper.startPage(bookDto.getPageNum(), bookDto.getPageSize());
        bookDto.setSchoolId(SecurityUtils.getLoginUser().getSchoolId());
        List<DtbBookPurchaseCodeVO> unboundCodeList = bookCodeExportService.getUnboundCodeList(bookDto);
        return getDataTable(unboundCodeList);
    }

    /**
     * 获取持有当前书籍购书码的学院集合
     *
     * @param bookDto 信息
     * @return 学院集合
     */
    @PostMapping("/exportCollegeList")
    public AjaxResult getExportCollegeList(@RequestBody BookDto bookDto) {
        bookDto.setSchoolId(SecurityUtils.getLoginUser().getSchoolId());
        return success(bookCodeExportService.getExportCollegeList(bookDto));
    }

    /**
     * 获取导出记录列表
     *
     * @param bookDto 信息
     * @return 导出记录列表
     */
    @RequiresPermissions("edu:bookCodeRecord:exportRecordList")
    @PostMapping("/exportRecordList")
    public TableDataInfo getExportRecordList(@RequestBody BookDto bookDto) {
        PageHelper.startPage(bookDto.getPageNum(), bookDto.getPageSize());
        return getDataTable(bookCodeExportService.getExportRecordList(bookDto));
    }

    /**
     * 解绑购书码，以及更新订单下的购书码表
     *
     * @param dtbBookPurchaseCodeDto 信息
     * @return 结果
     */
    @Log(title = "教务端解绑购书码", businessType = BusinessType.UPDATE)
    @RequiresPermissions("edu:bookCodeRecord:unBind")
    @PostMapping("/unBindCode")
    public AjaxResult unBindCode(@RequestBody DtbBookPurchaseCodeDto dtbBookPurchaseCodeDto) {
        return success(bookCodeExportService.unBindCode(dtbBookPurchaseCodeDto));
    }

    /**
     * 未兌換的购书码导出
     */
    @Log(title = "未兌換的购书码导出", businessType = BusinessType.EXPORT)
    @RequiresPermissions("edu:bookCodeRecord:unBoundExport")
    @Transactional
    @PostMapping("/unboundExport")
    public void unboundExport(HttpServletResponse response, BookDto bookDto) {
        bookDto.setSchoolId(SecurityUtils.getLoginUser().getSchoolId());
        // 获取要导出的购书码信息
        List<DtbBookdtbPurchaseCodeUnboundExport> unboundCodeList = bookCodeExportService.getUnboundExport(bookDto);
        // 订单下的购书码表的id集合
        List<Long> orderItemList = unboundCodeList.stream().limit(bookDto.getExportQuantity()).map(DtbBookdtbPurchaseCodeUnboundExport::getOrderCodeId).collect(Collectors.toList());
        boolean isResult = true;
        // 更新订单下的购书码表的导出次数+1
        isResult = bookCodeExportService.updateBookCodeExportQuantity(orderItemList);
        // 购书码表的id集合
        List<Long> codeIdList = unboundCodeList.stream().limit(bookDto.getExportQuantity()).map(DtbBookdtbPurchaseCodeUnboundExport::getCodeId).collect(Collectors.toList());
        DtbBookCodeExportInfo dtbBookCodeExportInfo = new DtbBookCodeExportInfo();
        dtbBookCodeExportInfo.setBookId(bookDto.getBookId());
        dtbBookCodeExportInfo.setExportUserId(SecurityUtils.getUserId());
        dtbBookCodeExportInfo.setExportDate(new Date());
        dtbBookCodeExportInfo.setCodeQuantity(bookDto.getExportQuantity());
        // 购书码导出记录
        isResult = dtbBookCodeExportInfoService.addExportInfo(dtbBookCodeExportInfo);
        for (Long item : codeIdList) {
            DtbBookCodeExportItem dtbBookCodeExportItem = new DtbBookCodeExportItem();
            dtbBookCodeExportItem.setExportDataId(dtbBookCodeExportInfo.getExportDataId());
            dtbBookCodeExportItem.setCodeId(item);
            dtbBookCodeExportItem.setBookId(bookDto.getBookId());
            // 导出购书码明细
            isResult = dtbBookCodeExportItemService.addExportItem(dtbBookCodeExportItem);
        }
        if (!isResult) {
            throw new RuntimeException("导出失败");
        }
        ExcelUtil<DtbBookdtbPurchaseCodeUnboundExport> util = new ExcelUtil<>(DtbBookdtbPurchaseCodeUnboundExport.class);
        util.exportExcel(response, unboundCodeList, "购书码");
    }

    /**
     * 已兌換的购书码导出
     *
     * @param response 导出
     * @param bookDto  信息
     * @throws IOException 异常
     */
    @Log(title = "已兌換的购书码导出", businessType = BusinessType.EXPORT)
    @RequiresPermissions("edu:bookCodeRecord:AlreadyBoundExport")
    @Transactional
    @PostMapping("/alreadyBound")
    public void alreadyBoundExport(HttpServletResponse response, BookDto bookDto) throws IOException {
        bookDto.setSchoolId(SecurityUtils.getLoginUser().getSchoolId());
        // 获取要导出的已兌換购书码信息
        List<DtbBookPurchaseCodeExport> dtbBookPurchaseCodeList = bookCodeExportService.alreadyBoundExport(bookDto);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码
        String fileName = URLEncoder.encode("教务的购书码数据", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
        // 按 学院Id 分组
        Map<String, List<DtbBookPurchaseCodeExport>> groupedData = groupDataById(dtbBookPurchaseCodeList);
        // 获取输出流
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build()) {
            // sheet角标
            int sheetIndex = 0;
            for (Map.Entry<String, List<DtbBookPurchaseCodeExport>> entry : groupedData.entrySet()) {
                DtbBookCodeExportInfo dtbBookCodeExportInfo = new DtbBookCodeExportInfo();
                dtbBookCodeExportInfo.setBookId(entry.getValue().get(0).getBookId());
                dtbBookCodeExportInfo.setExportUserId(SecurityUtils.getUserId());
                dtbBookCodeExportInfo.setExportDate(new Date());
                dtbBookCodeExportInfo.setCodeQuantity(entry.getValue().size());
                // 购书码导出记录
                dtbBookCodeExportInfoService.addExportInfo(dtbBookCodeExportInfo);
                for (DtbBookPurchaseCodeExport dtbBookPurchaseCodeExport : entry.getValue()) {
                    DtbBookCodeExportItem dtbBookCodeExportItem = new DtbBookCodeExportItem();
                    dtbBookCodeExportItem.setExportDataId(dtbBookCodeExportInfo.getExportDataId());
                    dtbBookCodeExportItem.setCodeId(dtbBookPurchaseCodeExport.getCodeId());
                    dtbBookCodeExportItem.setBookId(dtbBookPurchaseCodeExport.getBookId());
                    // 导出购书码明细
                    dtbBookCodeExportItemService.addExportItem(dtbBookCodeExportItem);
                }
                // Sheet 名称
                String sheetName = entry.getValue().get(0).getSchoolName() + "已兑换的购书码";
                // 设置表头
                WriteSheet writeSheet = EasyExcel.writerSheet(sheetIndex++, sheetName)
                        .head(DtbBookPurchaseCodeExport.class)
                        .build();
                // 写入数据
                excelWriter.write(entry.getValue(), writeSheet);
            }
        }
    }

    /**
     * 按 学院ID将list 分组
     */
    private Map<String, List<DtbBookPurchaseCodeExport>> groupDataById(List<DtbBookPurchaseCodeExport> dataList) {
        Map<String, List<DtbBookPurchaseCodeExport>> groupedData = new HashMap<>();
        for (DtbBookPurchaseCodeExport data : dataList) {
            groupedData.computeIfAbsent(data.getSchoolId().toString(), k -> new ArrayList<>()).add(data);
        }
        return groupedData;
    }
}

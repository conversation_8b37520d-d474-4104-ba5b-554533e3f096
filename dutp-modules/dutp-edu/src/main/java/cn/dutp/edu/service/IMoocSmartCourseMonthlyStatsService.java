package cn.dutp.edu.service;

import java.util.List;
import cn.dutp.edu.domain.MoocSmartCourseMonthlyStats;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 班级月度综合统计 (出勤与学习进度)Service接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IMoocSmartCourseMonthlyStatsService extends IService<MoocSmartCourseMonthlyStats>
{
    /**
     * 查询班级月度综合统计 (出勤与学习进度)
     *
     * @param statId 班级月度综合统计 (出勤与学习进度)主键
     * @return 班级月度综合统计 (出勤与学习进度)
     */
    public MoocSmartCourseMonthlyStats selectMoocSmartCourseMonthlyStatsByStatId(Long statId);

    /**
     * 查询班级月度综合统计 (出勤与学习进度)列表
     *
     * @param moocSmartCourseMonthlyStats 班级月度综合统计 (出勤与学习进度)
     * @return 班级月度综合统计 (出勤与学习进度)集合
     */
    public List<MoocSmartCourseMonthlyStats> selectMoocSmartCourseMonthlyStatsList(MoocSmartCourseMonthlyStats moocSmartCourseMonthlyStats);

    /**
     * 新增班级月度综合统计 (出勤与学习进度)
     *
     * @param moocSmartCourseMonthlyStats 班级月度综合统计 (出勤与学习进度)
     * @return 结果
     */
    public boolean insertMoocSmartCourseMonthlyStats(MoocSmartCourseMonthlyStats moocSmartCourseMonthlyStats);

    /**
     * 修改班级月度综合统计 (出勤与学习进度)
     *
     * @param moocSmartCourseMonthlyStats 班级月度综合统计 (出勤与学习进度)
     * @return 结果
     */
    public boolean updateMoocSmartCourseMonthlyStats(MoocSmartCourseMonthlyStats moocSmartCourseMonthlyStats);

    /**
     * 批量删除班级月度综合统计 (出勤与学习进度)
     *
     * @param statIds 需要删除的班级月度综合统计 (出勤与学习进度)主键集合
     * @return 结果
     */
    public boolean deleteMoocSmartCourseMonthlyStatsByStatIds(List<Long> statIds);

}

package cn.dutp.edu.service;

import cn.dutp.edu.domain.vo.TeacherVo;
import cn.dutp.edu.domian.DutpUser;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2025/7/18 10:04
 */
public interface IEduTeacherService  extends IService<DutpUser> {

    /**
     * 教务：查询当前学校下的教师
     *
     * @param teacher
     * @return
     */
    public List<DutpUser> selectTeacherList(TeacherVo teacher);

    /**
     * 添加教师信息
     * @param teacher 教师
     * @return 结果
     */
    boolean addTeacher(DutpUser teacher);

    /**
     * 修改教师信息
     * @param teacher 教师
     * @return 结果
     */
    boolean editTeacher(DutpUser teacher);

    /**
     * 删除教师信息
     * @param userId
     * @return
     */
    boolean deleteTeacher(List<Long> userId);

    TeacherVo getByUserId(Long userId);
}

package cn.dutp.edu.service.impl;

import cn.dutp.book.domain.DtbBookChapterCatalog;
import cn.dutp.book.domain.DtbBookFile;
import cn.dutp.book.domain.DtbUserBook;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.utils.AliyunOssStsUtil;
import cn.dutp.common.core.utils.StringUtils;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBook;
import cn.dutp.domain.DtbBookOrder;
import cn.dutp.domain.DutpSubject;
import cn.dutp.edu.domain.DutpSchool;
import cn.dutp.edu.domain.EduSchoolBook;
import cn.dutp.edu.domain.dto.BookDto;
import cn.dutp.edu.domain.dto.DtbUserBookDto;
import cn.dutp.edu.domain.vo.*;
import cn.dutp.edu.domian.DutpUser;
import cn.dutp.edu.mapper.*;
import cn.dutp.edu.service.DtbBookFeignClient;
import cn.dutp.edu.service.DtbBookService;
import cn.dutp.edu.service.DutpUserService;
import cn.dutp.system.api.RemoteUserService;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import feign.Response;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

;

/**
 * <AUTHOR>
 * @description 针对表【dtb_book(DUTP-DTB_002数字教材)】的数据库操作Service实现
 * @createDate 2025-01-14 09:22:08
 */
@Service
public class DtbBookServiceImpl extends ServiceImpl<DtbBookMapper, DtbBook>
        implements DtbBookService {
    @Autowired
    private DtbBookMapper dtbBookMapper;

    @Autowired
    DutpShopSubjectMapper dutpSubjectMapper;

    @Autowired
    DtbBookChapterCatalogMapper dtbBookChapterCatalogMapper;

    @Autowired
    DtbBookFileMapper dtbBookFileMapper;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private DtbBookFeignClient dtbBookFeignClient;

    @Autowired
    private DutpUserMapper dutpUserMapper;

    @Autowired
    private DutpUserService dutpUserService;
    @Autowired
    private AliyunOssStsUtil aliyunOssStsUtil;
    @Autowired
    private DtbUserBookMapper dtbUserBookMapper;
    @Autowired
    private DtbBookOrderMapper dtbBookOrderMapper;
    @Autowired
    private DtbBookChapterMapper dtbBookChapterMapper;
    @Autowired
    private DtbBookChapterCatalogMapper dtbBookChapter;
    @Autowired
    private EduSchoolBookMapper eduSchoolBookMapper;

    @Override
    public List<Tree<String>> selectSubjectList(DutpSubject dutpSubject) {
        LambdaQueryWrapper<DutpSubject> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotEmpty(dutpSubject.getSubjectName())) {
            lambdaQueryWrapper.like(DutpSubject::getSubjectName, dutpSubject.getSubjectName());
        }
        if (dutpSubject.getParentId() != null) {
            lambdaQueryWrapper.eq(DutpSubject::getParentId, dutpSubject.getParentId());
        }
        if (dutpSubject.getSubjectLevel() != null) {
            lambdaQueryWrapper.eq(DutpSubject::getSubjectLevel, dutpSubject.getSubjectLevel());
        }

        List<DutpSubject> list = dutpSubjectMapper.selectList(lambdaQueryWrapper);
        return getSubjectTreeNode(list);
    }

    private List<Tree<String>> getSubjectTreeNode(List<DutpSubject> list) {
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setChildrenKey("children");

        List<Tree<String>> treeNodes = TreeUtil.build(list,"0",treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getSubjectId().toString());
                    if (treeNode.getParentId() != null) {
                        tree.setParentId(treeNode.getParentId().toString());
                    } else {
                        tree.setParentId(null);
                    }
                    tree.setName(treeNode.getSubjectName());
                    tree.setWeight(treeNode.getSort());

                    tree.putExtra("subjectId", treeNode.getSubjectId().toString());
                    tree.putExtra("subjectName", treeNode.getSubjectName());
                    tree.putExtra("subjectLevel", treeNode.getSubjectLevel());
                });
        return treeNodes;
    }

    @Override
    public List<BookVo> selectEducationalBookList(BookDto dto) {
        dto.setSchoolId(SecurityUtils.getLoginUser().getSchoolId());
        return dtbBookMapper.selectOpenBookList(dto);
    }
    @Override
    public List<BookVo> selectBookList(BookDto dto) {
        // 公开教材
        QueryWrapper<DtbBookOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DtbBookOrder::getSchoolId, SecurityUtils.getLoginUser().getSchoolId())
                .eq(DtbBookOrder::getDeleted, 1)
                .in(DtbBookOrder::getOrderStatus, "settlement","completed")
                .in(DtbBookOrder::getOrderType, 3,5);
        List<DtbBookOrder> orderList = dtbBookOrderMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(orderList)) {
            List<Long> orderIds = orderList.stream().map(DtbBookOrder::getOrderId).collect(Collectors.toList());
            dto.setSchoolId(SecurityUtils.getLoginUser().getSchoolId());
            PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
            return dtbBookMapper.selectBookList(dto,orderIds);
        }
        return null;
    }

    @Override
    @Transactional
    public AjaxResult importBookUser(BookDto dto) {
        Map<String, Object> res = new HashMap<>();
        //字段缺失数据数量
        Integer lackCount = 0;
        //手机号/姓名/学号不匹配
        Integer errorSchoolCount = 0;
        //手机号有误数量
        Integer errorPhoneCount = 0;
        // 工号重复数量
        Integer repeatUserNoCount = 0;
        List<DtbBookUserExport> errorUserBookList = new ArrayList<>();
        List<DtbUserBook> insList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(dto.getUserBookList())) {
            // 查询书籍下绑定的人员
            dto.setSchoolId(SecurityUtils.getLoginUser().getSchoolId());
            List<DutpUser> userList = dutpUserMapper.selectBookUserList(dto);
            // 查询当前学校下的所有学院和专业
            List<DutpSchool> schoolList = dtbBookMapper.selectSchoolById(SecurityUtils.getLoginUser().getSchoolId());
            DtbUserBookDto bookDto = dtbBookMapper.selectSchoolBookBySchoolBookId(dto.getSchoolBookId());
            for (DtbBookUserExport userBook : dto.getUserBookList()) {
                // 验证添加的人员中是否有重复数据
                List<DtbBookUserExport> usList = dto.getUserBookList().stream().filter(e -> e.getUserNo().equals(userBook.getUserNo())).collect(Collectors.toList());
                if (usList.size() > 1) {
                    repeatUserNoCount++;
                    errorUserBookList.add(userBook);
                    continue;
                }
                // 验证书籍下已添加的人员是否有重复
                DutpUser user = userList.stream().filter(e -> e.getUserNo().equals(userBook.getUserNo())).findFirst().orElse(null);
                if (!ObjectUtil.isEmpty(user)) {
                    repeatUserNoCount++;
                    errorUserBookList.add(userBook);
                    continue;
                }
                if (hasNullFields(userBook)) {
                    // 字段缺失
                    lackCount++;
                    errorUserBookList.add(userBook);
                    continue;
                } else {
                    if (userBook.getPhonenumber().length() != 11) {
                        //手机号有误数量
                        errorPhoneCount++;
                        errorUserBookList.add(userBook);
                        continue;
                    }
                    // 学号有误数量
                    QueryWrapper<DutpUser> queryWrapper = new QueryWrapper<>();
                    queryWrapper.lambda().eq(DutpUser::getRealName,userBook.getNickName())
                            .eq(DutpUser::getPhonenumber,userBook.getPhonenumber())
                            .eq(DutpUser::getSchoolId,SecurityUtils.getLoginUser().getSchoolId())
                            .eq(DutpUser::getDelFlag,0);
                    DutpUser dutpUser = dutpUserMapper.selectOne(queryWrapper);
                    if (ObjectUtil.isEmpty(dutpUser)) {
                        //手机号姓名未找到人员
                        errorSchoolCount++;
                        errorUserBookList.add(userBook);
                        continue;
                    } else {
                        if (StringUtils.isNotBlank(userBook.getUserNo()) && !userBook.getUserNo().equals(userBook.getUserNo())) {
                            errorSchoolCount++;
                            errorUserBookList.add(userBook);
                            continue;
                        } else if (StringUtils.isBlank(userBook.getUserNo())) {
                            // 验证学号是否重复，更新学号
                            QueryWrapper<DutpUser> userNoWapper = new QueryWrapper<>();
                            userNoWapper.lambda().eq(DutpUser::getUserNo,userBook.getUserNo())
                                    .eq(DutpUser::getSchoolId,SecurityUtils.getLoginUser().getSchoolId())
                                    .eq(DutpUser::getDelFlag,0);
                            DutpUser userNo = dutpUserMapper.selectOne(userNoWapper);
                            if (ObjectUtil.isEmpty(userNo)) {
                                // 没有学号更新学号信息
                                UpdateWrapper<DutpUser> updateWrapper = new UpdateWrapper<>();
                                updateWrapper.lambda().eq(DutpUser::getUserId,dutpUser.getUserId())
                                        .set(DutpUser::getUserNo,userBook.getUserNo())
                                        .set(DutpUser::getUpdateTime,new Date())
                                        .set(DutpUser::getUpdateBy,SecurityUtils.getUsername());
                                dutpUserMapper.update(null,updateWrapper);
                            } else {
                                repeatUserNoCount++;
                                errorUserBookList.add(userBook);
                                continue;
                            }
                        }
                        DtbUserBookDto dbto = new DtbUserBookDto();
                        dbto.setSchoolBookId(dto.getSchoolBookId());
                        dbto.setBookId(dto.getBookId());
                        saveUserBook(dutpUser,dbto);
                    }
                }
            }
            if (!CollectionUtils.isEmpty(insList)) {
                dtbBookFeignClient.addBatchUserBook(insList);
            }
            if (!CollectionUtils.isEmpty(errorUserBookList)) {
                res.put("code", 500);
                res.put("lackCount", lackCount);
                res.put("successCount", dto.getUserBookList().size() - errorUserBookList.size());
                res.put("errorCount", errorUserBookList.size());
                res.put("errorSchoolCount", errorSchoolCount);
                res.put("errorPhoneCount", errorPhoneCount);
                res.put("repeatUserNoCount", repeatUserNoCount);
                res.put("errList", errorUserBookList);
            } else {
                res.put("code", 200);
                res.put("count",dto.getUserBookList().size());
            }
            return AjaxResult.success(res);
        } else {
            res.put("code", 201);
            res.put("message","导入失败！文件为空或未包含有效数据，请检查后重新上传");
            return AjaxResult.success(res);
        }
    }

    public static boolean hasNullFields(Object obj) {
        if (obj == null) {
            return true; // 如果对象本身为 null，返回 true
        }

        List<String> fieldsToCheck = Arrays.asList("userNo", "nickName", "phonenumber"); // 需要判断的字段名列表

        Class<?> clazz = obj.getClass();

        for (String fieldName : fieldsToCheck) {
            try {
                Field field = clazz.getDeclaredField(fieldName);
                field.setAccessible(true);

                Object value = field.get(obj);
                if (value == null) {
                    return true; // 该字段为 null，返回 true
                }
            } catch (NoSuchFieldException e) {
                // 如果对象没有该字段，可选择忽略或直接返回 true 这里我选择忽略继续检查其他字段
                // 也可以根据需求改为 return true;
                e.printStackTrace();
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }

        return false; // 所有指定字段不为 null，返回 false
    }

    @Override
    public void removeBookUser(BookDto dto) {
        dutpUserMapper.removeBookUser(dto);
    }

    @Override
    public List<DutpUser> exportBookUser(BookDto dto) {
        List<DutpUser> userList = dutpUserMapper.selectBookUserList(dto);
        return userList;
    }

    @Override
    public List<DutpUser> selectBookUserList(BookDto dto) {
        dto.setSchoolId(SecurityUtils.getLoginUser().getSchoolId());
        if (!CollectionUtils.isEmpty(dto.getSchoolIdList())) {
            if (dto.getSchoolIdList().size() == 1) {
                dto.setAcademyId(dto.getSchoolIdList().get(0));
            } else if (dto.getSchoolIdList().size() == 2) {
                dto.setAcademyId(dto.getSchoolIdList().get(0));
                dto.setSpecialityId(dto.getSchoolIdList().get(1));
            }
        }
        List<DutpUser> userList = dutpUserMapper.selectBookUserList(dto);
        return userList;
    }

    @Override
    public List<DutpEduOrderVo> getOrderBySchoolList(BookDto dto) {
        Long schoolId = SecurityUtils.getLoginUser().getSchoolId();
        dto.setSchoolId(schoolId);
        // 根据学校id和订单类型，查询订单的列表
        List<DutpEduOrderVo> list = dtbBookMapper.selectOrderListBySchoolAndType(dto);
        if (!list.isEmpty()) {
            list.forEach(item -> {
                // 根据学校id和主订单id，查询子订单的列表
                List<DutpEduOrderDetailVo> orderBySchoolList = dtbBookMapper.selectItemListBySchoolAndOrder(item.getOrderId(), schoolId);
                item.setItemList(orderBySchoolList);
            });
        }
        return list;
    }

    @Override
    public DutpEduOrderDetailVo getOrderDetailBySchool(BookDto dto) {
        DutpEduOrderDetailVo detailVo = dtbBookMapper.getBookDetailByItemId(dto.getOrderItemId());
        return detailVo;
    }

    @Override
    @Transactional
    public AjaxResult addBookUser(DtbUserBookDto dto) {
        /** 手机号姓名必填，学号如果没有需要更新，有需加入验证  学号姓名电话作为验证条件，如果没有学号需要更新输入的学号并且不能和现有的重复，如果有学号，共同验证是否存在用户*/
        QueryWrapper<DutpUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DutpUser::getRealName,dto.getUserName())
                .eq(DutpUser::getPhonenumber,dto.getPhonenumber())
                .eq(DutpUser::getSchoolId,SecurityUtils.getLoginUser().getSchoolId())
                .eq(DutpUser::getDelFlag,0);
        DutpUser user = dutpUserMapper.selectOne(queryWrapper);
        if (ObjectUtil.isEmpty(user)) {
            return AjaxResult.error("未找到该用户");
        } else {
            if (StringUtils.isNotBlank(user.getUserNo()) && !dto.getUserNo().equals(user.getUserNo())) {
                return AjaxResult.error("学号有误，请重新输入");
            } else if (StringUtils.isBlank(user.getUserNo())) {
                // 验证学号是否重复，更新学号
                QueryWrapper<DutpUser> userNoWapper = new QueryWrapper<>();
                userNoWapper.lambda().eq(DutpUser::getUserNo,dto.getUserNo())
                        .eq(DutpUser::getSchoolId,SecurityUtils.getLoginUser().getSchoolId())
                        .eq(DutpUser::getDelFlag,0);
                DutpUser userNo = dutpUserMapper.selectOne(userNoWapper);
                if (ObjectUtil.isEmpty(userNo)) {
                    // 没有学号更新学号信息
                    UpdateWrapper<DutpUser> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.lambda().eq(DutpUser::getUserId,user.getUserId())
                            .set(DutpUser::getUserNo,dto.getUserNo())
                            .set(DutpUser::getUpdateTime,new Date())
                            .set(DutpUser::getUpdateBy,SecurityUtils.getUsername());
                    dutpUserMapper.update(null,updateWrapper);
                } else {
                    return AjaxResult.error("学号已存在，请重新输入");
                }
            }
            return saveUserBook(user,dto);
        }
    }

    public AjaxResult  saveUserBook(DutpUser user,DtbUserBookDto dto) {
        // 添加用户到书籍下
        QueryWrapper<DtbUserBook> userBookQueryWrapper = new QueryWrapper<>();
        userBookQueryWrapper.lambda().eq(DtbUserBook::getUserId,user.getUserId())
                .eq(DtbUserBook::getBookId,dto.getBookId());
        DtbUserBook userBook = dtbUserBookMapper.selectOne(userBookQueryWrapper);
        if (ObjectUtil.isEmpty(userBook)) {
            DtbUserBook dtbUserBook = new DtbUserBook();
            dtbUserBook.setBookId(dto.getBookId());
            dtbUserBook.setUserId(user.getUserId());
            dtbUserBook.setAddWay(2);
            // 获取书籍当前版本和当前书籍有效期
            dto.setSchoolId(SecurityUtils.getLoginUser().getSchoolId());
            DtbUserBookDto bookDto = dtbBookMapper.selectSchoolBookBySchoolBookId(dto.getSchoolBookId());
            dtbUserBook.setVersionId(bookDto.getVersionId());
            dtbUserBook.setExpireDate(bookDto.getExpireDate());

            // 新建学生书籍关系
            dtbBookFeignClient.addDtbUserBook(dtbUserBook);
            return AjaxResult.success();
        } else {
            return AjaxResult.error("该用户已添加到该书籍下，无需重复添加");
        }
    }

    @Override
    public void downLoadFile(HttpServletResponse response, BookDto dto) throws IOException {
        if (dto.getDownloadType() == 1) {
            QueryWrapper<DtbBookFile> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(DtbBookFile::getBookFileId,dto.getBookFileId())
                    .eq(DtbBookFile::getDownload,1).eq(DtbBookFile::getDelFlag,0);
            DtbBookFile dtbBookFile = dtbBookFileMapper.selectOne(queryWrapper);
            if (ObjectUtil.isEmpty(dtbBookFile)) {
                throw new ServiceException("未找到该文件或该文件不允许下载");
            } else {
                downloadBookFile(dtbBookFile,response);
            }
        } else {
            QueryWrapper<DtbBookFile> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(DtbBookFile::getBookId,dto.getBookId())
                    .eq(DtbBookFile::getDownload,1).eq(DtbBookFile::getDelFlag,0);
            List<DtbBookFile> dtbBookFile = dtbBookFileMapper.selectList(queryWrapper);
            if (ObjectUtil.isEmpty(dtbBookFile)) {
                throw new ServiceException("未找到该教材下的资源或该教材下的资源不可下载");
            } else {
                try (ZipOutputStream zipOutputStream = new ZipOutputStream(response.getOutputStream())) {
                    for(DtbBookFile dtbBookFileItem : dtbBookFile) {
                        downloadAndZipFile(dtbBookFileItem.getFileUrl(),zipOutputStream);
                    }
                } catch (Exception e) {
                    response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Error creating ZIP file.");
                }

            }
        }
    }

    @Override
    public void downloadErrorList(List<DtbBookUserExport> errorUserBookList, HttpServletResponse response) {
        errorUserBookList.stream().forEach(errorUserBook -> {
            errorUserBook.setUserType(StringUtils.isNotBlank(errorUserBook.getUserType())?(errorUserBook.getUserType().equals("学生")?"1":"2"):"");
        });
        ExcelUtil<DtbBookUserExport> util = new ExcelUtil<DtbBookUserExport>(DtbBookUserExport.class);
        util.exportExcel(response, errorUserBookList, "校本教材详情");
    }

    @Override
    public List<Tree<String>> selectChapterByBookId(DtbBookApplicationUserDataVo dto) {
        // 获取版本号
        QueryWrapper<DtbBook> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DtbBook::getBookId,dto.getBookId()).eq(DtbBook::getDelFlag, 0);
        DtbBook dtbBook = dtbBookMapper.selectOne(queryWrapper);
        if (ObjectUtil.isEmpty(dtbBook)) {
            throw new ServiceException("未找到该教材");
        } else {
            Long versionId = dtbBook.getCurrentVersionId();
            QueryWrapper<DtbBookChapterCatalog> queryCataWrapper = new QueryWrapper<>();
            queryCataWrapper.lambda().eq(DtbBookChapterCatalog::getBookId,dto.getBookId())
                    .eq(DtbBookChapterCatalog::getVersionId,versionId);
            List<DtbBookChapterCatalog> catalogList = dtbBookChapterCatalogMapper.selectList(queryCataWrapper);
            if (CollectionUtils.isEmpty(catalogList)) {
                return Collections.emptyList();
            } else {
                return getTreeNode(catalogList);
            }
        }
    }

    @Override
    public Integer pushBook(BookDto dto) {
        UpdateWrapper<EduSchoolBook> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(EduSchoolBook::getSchoolBookId,dto.getSchoolBookId())
                .set(EduSchoolBook::getPushFlag,1)
                .set(EduSchoolBook::getUpdateTime,new Date())
                .set(EduSchoolBook::getUpdateBy,SecurityUtils.getUsername());
        eduSchoolBookMapper.update(null, updateWrapper);
        // 更新学生书架
        UpdateWrapper<DtbUserBook> updateUserBookWrapper = new UpdateWrapper<>();
        updateUserBookWrapper.lambda().eq(DtbUserBook::getBookId,dto.getBookId())
                .set(DtbUserBook::getExpireDate,dto.getExpireDate())
                .set(DtbUserBook::getUpdateTime,new Date())
                .set(DtbUserBook::getUpdateBy,SecurityUtils.getUsername());
        return dtbUserBookMapper.update(null, updateUserBookWrapper);
    }

    @Override
    public TableDataInfo selectEduBookList(BookDto dto) {
        TableDataInfo info = new TableDataInfo();
        dto.setSchoolId(SecurityUtils.getLoginUser().getSchoolId());
        List<BookVo> list = dtbBookMapper.selectEduSchoolBookList(dto);
        info.setTotal(new PageInfo(list).getTotal());
        if (!ObjectUtil.isEmpty(list)) {
            List<Long> ids = list.stream().map(BookVo::getBookId).collect(Collectors.toList());
            QueryWrapper<DtbBook> childQueryWrapper = new QueryWrapper<>();
            childQueryWrapper.lambda().in(DtbBook::getMasterBookId,ids)
                    .eq(DtbBook::getDelFlag,0)
                    .eq(DtbBook::getMasterFlag,3)
                    .eq(DtbBook::getPublishStatus,2);
            List<DtbBook> childBookList = dtbBookMapper.selectList(childQueryWrapper);
            if (!ObjectUtil.isEmpty(childBookList)) {
                childBookList.forEach(e -> {
                    BookVo bookVo = new BookVo();
                    bookVo.setBookId(e.getBookId());
                    bookVo.setBookName(e.getBookName());
                    bookVo.setMasterBookId(e.getMasterBookId());
                    bookVo.setBookNo(e.getBookNo());
                    bookVo.setCover(e.getCover());
                    bookVo.setShelfTime(e.getShelfTime());
                    bookVo.setReadQuantity(e.getReadQuantity().intValue());
                    bookVo.setMasterFlag(e.getMasterFlag());
                    BookVo parentBookVo = list.stream().filter(b -> b.getBookId().equals(e.getMasterBookId())).findFirst().get();
                    if (!ObjectUtil.isEmpty(parentBookVo)) {
                        bookVo.setExpireDate(parentBookVo.getExpireDate());
                        bookVo.setCreateTime(parentBookVo.getCreateTime());
                        bookVo.setIsLastFlag(parentBookVo.getIsLastFlag());
                        bookVo.setIsExpireFlag(parentBookVo.getIsExpireFlag());
                    }
                    list.add(bookVo);
                });
            }
            List<Tree<String>> res = getEduTreeNode(list);
            info.setRows(res);
        }
        return info;
    }

    private List<Tree<String>> getEduTreeNode(List<BookVo> list) {
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setChildrenKey("children");
        try {
            List<Tree<String>> treeNodes = TreeUtil.build(list,"0",treeNodeConfig,
                    (treeNode, tree) -> {
                        tree.setId(treeNode.getBookId().toString());
                        tree.setParentId(treeNode.getMasterBookId() == null ? "0" : treeNode.getMasterBookId().toString());
                        tree.setName(treeNode.getBookName());
                        tree.setWeight(treeNode.getCreateTime());
                        tree.putExtra("bookId", treeNode.getBookId().toString());
                        tree.putExtra("schoolBookId", treeNode.getSchoolBookId() == null ? "" : treeNode.getSchoolBookId().toString());
                        tree.putExtra("bookName", treeNode.getBookName());
                        tree.putExtra("masterBookId", treeNode.getMasterBookId() == null? "" : treeNode.getMasterBookId().toString());
                        tree.putExtra("bookNo", treeNode.getBookNo());
                        tree.putExtra("cover", treeNode.getCover());
                        tree.putExtra("masterFlag", treeNode.getMasterFlag());
                        tree.putExtra("shelfTime", treeNode.getShelfTime());
                        tree.putExtra("readQuantity", treeNode.getReadQuantity());
                        tree.putExtra("isLastFlag", treeNode.getIsLastFlag());
                        tree.putExtra("isExpireFlag", treeNode.getIsExpireFlag());
                        tree.putExtra("expireDate", treeNode.getExpireDate());
                        tree.putExtra("createTime", treeNode.getCreateTime());
                        tree.putExtra("pushFlag", treeNode.getPushFlag());
                    });
            return treeNodes;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private void downloadAndZipFile(String fileUrl, ZipOutputStream zipOutputStream) throws IOException {
        HttpURLConnection urlConnection = null;
        InputStream inputStream = null;
        try {
            // 创建 URL 对象
            URL url = new URL(fileUrl);
            urlConnection = (HttpURLConnection) url.openConnection();
            urlConnection.setRequestMethod("GET");
            urlConnection.connect();

            // 检查响应代码
            if (urlConnection.getResponseCode() != HttpURLConnection.HTTP_OK) {
                throw new IOException("File not found: " + fileUrl);
            }

            // 获取文件的 MIME 类型
            String mimeType = urlConnection.getContentType();
            if (mimeType == null) {
                mimeType = "application/octet-stream"; // 默认类型
            }

            // 创建 ZIP 条目
            String fileName = fileUrl.substring(fileUrl.lastIndexOf('/') + 1);
            ZipEntry zipEntry = new ZipEntry(fileName);
            zipOutputStream.putNextEntry(zipEntry);

            // 读取远程文件并写入 ZIP 输出流
            inputStream = urlConnection.getInputStream();
            byte[] buffer = new byte[4096]; // 4KB缓冲区
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                zipOutputStream.write(buffer, 0, bytesRead);
            }
            zipOutputStream.closeEntry(); // 关闭当前 ZIP 条目
        } finally {
            // 关闭流
            if (inputStream != null) {
                inputStream.close();
            }
            if (urlConnection != null) {
                urlConnection.disconnect();
            }
        }
    }

    private void downloadBookFile(DtbBookFile dtbBookFile,HttpServletResponse response) throws IOException {
        HttpURLConnection urlConnection = null;
        InputStream inputStream = null;
        try {
            // 创建 URL 对象
            URL url = new URL(dtbBookFile.getFileUrl());
            urlConnection = (HttpURLConnection) url.openConnection();
            urlConnection.setRequestMethod("GET");
            urlConnection.connect();

            // 检查响应代码
            if (urlConnection.getResponseCode() != HttpURLConnection.HTTP_OK) {
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "File not found.");
                return;
            }
            // 获取文件的 MIME 类型
            String mimeType = urlConnection.getContentType();
            if (mimeType == null) {
                mimeType = "application/octet-stream"; // 默认类型
            }
            response.setContentType(mimeType);

            // 设置响应头，指示浏览器下载文件
            String fileName = dtbBookFile.getFileUrl().substring(dtbBookFile.getFileUrl().lastIndexOf('/') + 1);
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
            response.setContentLength(urlConnection.getContentLength()); // 设置文件大小

            // 读取远程文件并写入响应输出流
            inputStream = urlConnection.getInputStream();
            OutputStream outputStream = response.getOutputStream();
            byte[] buffer = new byte[4096]; // 4KB缓冲区
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        } finally {
            // 关闭流
            if (inputStream != null) {
                inputStream.close();
            }
            if (urlConnection != null) {
                urlConnection.disconnect();
            }
        }
    }

    @Override
    public List<BookVo> selectBookListForSchool(BookDto dto) {
        long schoolId = SecurityUtils.getLoginUser().getSchoolId();
        if(ObjectUtil.isNotEmpty(schoolId)) {
            dto.setSchoolId(schoolId);
            return dtbBookMapper.selectBookListForSchool(dto);
        }
        return null;
    }

    @Override
    public BookVo getBookDetail(BookDto dto) {
        BookVo result = new BookVo();
        if (dto.getBookOrganize() == 1) {
            result = dtbBookMapper.getOpenBookDetail(dto.getBookId(),SecurityUtils.getLoginUser().getSchoolId());
            // 公开教材
            QueryWrapper<DtbBook> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(DtbBook::getMasterBookId,dto.getBookId())
                    .eq(DtbBook::getMasterFlag,3).eq(DtbBook::getPublishStatus, 2)
                    .eq(DtbBook::getDelFlag,0);
            List<DtbBook> childList = dtbBookMapper.selectList(queryWrapper);
            result.setChildrenList(childList);

            //获取目录
            List<DtbBookChapter> catalogList = getCatalogByBookId(dto.getBookId(),result.getCurrentVersionId());
            result.setCatalogTreeNodeList(catalogList);
            //获取资源包
            QueryWrapper<DtbBookFile> queryFileWapper = new QueryWrapper<>();
            queryFileWapper.lambda().eq(DtbBookFile::getBookId,dto.getBookId())
                    .eq(DtbBookFile::getDelFlag,0);
            List<DtbBookFile> dtbBookFileList = dtbBookFileMapper.selectList(queryFileWapper);
            if (!CollectionUtils.isEmpty(dtbBookFileList)) {

                List<Long> bookFileId = dtbBookFileList.stream().map(DtbBookFile::getBookFileId).collect(Collectors.toList());
                // 是否允许下载
                QueryWrapper<DtbBookFile> query = new QueryWrapper<>();
                query.lambda().in(DtbBookFile::getBookFileId,bookFileId)
                        .eq(DtbBookFile::getDelFlag,0);
                List<DtbBookFile> dtbBookFiles = dtbBookFileMapper.selectList(query);
                for (DtbBookFile dtbBookFile : dtbBookFileList) {
                    DtbBookFile file = dtbBookFiles.stream().filter(e -> e.getBookFileId().equals(dtbBookFile.getBookFileId())).findFirst().orElse(null);
                    if (ObjectUtil.isNotEmpty(file)) {
                        dtbBookFile.setDownload(file.getDownload());
                    }
                }
                result.setDtbBookFileList(dtbBookFileList);
            }
        } else {
            // 校本教材
            result = dtbBookMapper.getSchoolBookDetail(dto.getBookId());
            // 获取教材下绑定的人员
            DutpUser user = new DutpUser();
            user.setSchoolId(SecurityUtils.getLoginUser().getSchoolId());
            user.setBookId(dto.getBookId());
        }
        return result;
    }

    private List<DtbBookChapter> getCatalogByBookId(Long bookId, Long currentVersionId) {
        if (ObjectUtil.isEmpty(bookId)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<DtbBookChapter> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DtbBookChapter::getBookId, bookId)
                .eq(DtbBookChapter::getDelFlag, 0)
                .orderByAsc(DtbBookChapter::getSort);
        List<DtbBookChapter> dtbBookChapterTreeVOList = dtbBookChapterMapper.queryBookChapterDataList(bookId,currentVersionId);
        if (ObjectUtil.isEmpty(dtbBookChapterTreeVOList)) {
            return new ArrayList<>();
        }
        dtbBookChapterTreeVOList.forEach(item -> {
            LambdaQueryWrapper<DtbBookChapterCatalog> catalogLambdaQueryWrapper = new LambdaQueryWrapper<>();
            catalogLambdaQueryWrapper.eq(DtbBookChapterCatalog::getChapterId, item.getChapterId());
            List<DtbBookChapterCatalog> dtbBookChapterCatalogList = dtbBookChapter.selectList(catalogLambdaQueryWrapper);
            dtbBookChapterCatalogList.forEach(catalogItem -> {
                catalogItem.setFree(item.getFree());
                catalogItem.setChapterName(catalogItem.getTitle());
            });
            item.setChildren(dtbBookChapterCatalogList);
        });
        return dtbBookChapterTreeVOList;
    }

    private List<Tree<String>> getTreeNode(List<DtbBookChapterCatalog> catalogList) {
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setChildrenKey("children");

        List<Tree<String>> treeNodes = TreeUtil.build(catalogList,"0",treeNodeConfig,
                (treeNode, tree) -> {
            tree.setId(treeNode.getCatalogId().toString());
            if (treeNode.getParentId() != null) {
                tree.setParentId(treeNode.getParentId().toString());
            } else {
                tree.setParentId(null);
            }
            tree.setName(treeNode.getChapterName());
            tree.setWeight(treeNode.getCreateTime());

                tree.putExtra("catalogId", treeNode.getCatalogId());
                tree.putExtra("title", treeNode.getTitle());
            });
        return treeNodes;
    }

    @Override
    public BookDataOverviewVo dataOverview(BookDataOverviewVo dto) {
        return dtbBookFeignClient.dataOverview(dto).getData();
    }

    /**
     * 通用导出处理方法
     * @param response HTTP响应对象
     * @param feignResponse Feign响应对象
     * @param defaultFileName 默认文件名
     */
    private void handleExport(HttpServletResponse response, Response feignResponse, String defaultFileName) {
        try {
            response.reset();
            String filename = defaultFileName;

            // 从feign响应中获取文件名
            Collection<String> contentDisposition = feignResponse.headers().get("Content-Disposition");
            if (contentDisposition != null && !contentDisposition.isEmpty()) {
                String disposition = contentDisposition.iterator().next();
                if (disposition.contains("filename=")) {
                    filename = disposition.substring(disposition.indexOf("filename=") + 9).replace("\"", "");
                    try {
                        filename = java.net.URLDecoder.decode(filename, "UTF-8");
                    } catch (Exception e) {
                        // 使用默认文件名
                    }
                }
            }

            String encodedFilename = new String(filename.getBytes("UTF-8"), "ISO-8859-1");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFilename + "\"");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");

            try (InputStream inputStream = feignResponse.body().asInputStream();
                 OutputStream outputStream = response.getOutputStream()) {
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                outputStream.flush();
            }
        } catch (Exception e) {
            throw new ServiceException("导出失败：" + e.getMessage());
        }
    }

    @Override
    public void exportDataOverview(HttpServletResponse response, BookDto dto) {
        try {
            Response feignResponse = dtbBookFeignClient.exportDataOverview(dto);
            handleExport(response, feignResponse, "数据总览.xlsx");
        } catch (Exception e) {
            throw new ServiceException("导出数据总览失败：" + e.getMessage());
        }
    }

    @Override
    public void exportAppData(HttpServletResponse response, BookDto dto) {
        try {
            Response feignResponse = dtbBookFeignClient.exportAppData(dto);
            handleExport(response, feignResponse, "应用数据.xlsx");
        } catch (Exception e) {
            throw new ServiceException("导出应用数据失败：" + e.getMessage());
        }
    }

    @Override
    public DtbBook getRemoteBookDetail(String bookId) {
        return dtbBookFeignClient.getBookDetail(bookId).getData();
    }

    @Override
    public List<BookDataOverviewVo> getChapterDataList(BookDataOverviewVo dto) {
        return dtbBookFeignClient.getChapterDataList(dto).getData();
    }

    @Override
    public List<DtbBookChapter> chapterListForSelect(DtbBookChapter dto) {
        return dtbBookFeignClient.listForSelect(dto.getBookId()).getData();
    }

    @Override
    public DtbBookApplicationUserDataVo selectBookApplicationUserData(DtbBookApplicationUserDataVo dto) {
        return dtbBookFeignClient.selectBookApplicationUserData(dto).getData();
    }


}





package cn.dutp.edu.mapper;

import cn.dutp.edu.domain.dto.BookDto;
import cn.dutp.edu.domain.vo.DutpUserVo;
import cn.dutp.edu.domian.DutpUser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 用户信息
 * @date 2024/11/5 9:47
 */
@Repository
public interface DutpUserMapper extends BaseMapper<DutpUser> {

   List<DutpUser> selectExportList(Long userId);

    List<DutpUser> selectStudent(DutpUser dutpUser);

    List<DutpUser> selectAll(DutpUser dutpUser);

    List<DutpUser> selectTeacher(DutpUser dutpUser);

    @Select("SELECT\n" +
            "\tu.* ,s.school_name,s.data_type,a.school_name as academyName,sp.school_name as specialityName\n" +
            "FROM\n" +
            "\tdutp_user AS u\n" +
            "\tLEFT JOIN dutp_school AS s ON u.school_id = s.school_id AND s.del_flag = 0\n" +
            "\tLEFT JOIN dutp_school AS a ON u.academy_id = a.school_id AND a.del_flag = 0\n" +
            "\tLEFT JOIN dutp_school AS sp ON u.speciality_id = sp.school_id AND sp.del_flag = 0\n" +
            "WHERE u.del_flag = 0 and u.user_id = #{userId}")
    DutpUser getUserInfo(Long userId);

    @Update("update dutp_user set status = 1 where user_id = #{userId} ")
    boolean changeStatus(Long userId);

    @Update("update dutp_user set status = 0 where user_id = #{userId} ")
    boolean openStatus(Long userId);

    @Select("SELECT school_id from dutp_school where school_name = #{schoolName} and data_type = 0")
    Long selectIdBySchool(String schoolName);

    @Select("SELECT school_name from dutp_school where  parent_id = #{schoolId} and data_type = 1 ")
    List<String> selectIdByacademyName( Long schoolId);

    @Select("select * from dutp_user where ids = #{ids}")
    List<DutpUser> selectByIds(List<Long> ids);

    /**
     * 根据登陆用户的学校id获取 学生数量 教师数量
     *
     * @param schoolId 学校id
     * @return DutpUserVo
     */
    DutpUserVo countTeacherAndStudentBySchoolId(long schoolId);

    List<DutpUser> selectBookUserList(BookDto dto);

    void removeBookUser(BookDto dto);

    @Select("SELECT school_id from dutp_school where data_type = 1 and school_name = #{schoolName} and parent_id = #{schoolId} and del_flag = 0")
    Long selectAcademyId(@Param("schoolName") String schoolName, @Param("schoolId") Long schoolId);

    @Select("SELECT school_id from dutp_school where data_type = 2 and school_name = #{schoolName} and parent_id = #{schoolId} and del_flag = 0")
    Long selectSubjectId(@Param("schoolName") String schoolName, @Param("schoolId") Long schoolId);

    @Select("SELECT school_name from dutp_school where  parent_id = #{schoolId} and data_type = 2")
    List<String> selectSubjectNameById(Long schoolId);

    boolean resetPassword(DutpUser user);
}

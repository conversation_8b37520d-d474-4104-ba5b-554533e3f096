<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocSmartCourseGroupMapper">
    
    <resultMap type="MoocSmartCourseGroup" id="MoocSmartCourseGroupResult">
        <result property="groupId"    column="group_id"    />
        <result property="classId"    column="class_id"    />
        <result property="groupName"    column="group_name"    />
        <result property="groupCreatorId"    column="group_creator_id"    />
        <result property="groupQuantity"    column="group_quantity"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMoocSmartCourseGroupVo">
        select group_id, class_id, group_name, group_creator_id, group_quantity, del_flag, create_by, create_time, update_by, update_time from mooc_smart_course_group
    </sql>

    <select id="selectMoocSmartCourseGroupList" parameterType="MoocSmartCourseGroup" resultMap="MoocSmartCourseGroupResult">
        <include refid="selectMoocSmartCourseGroupVo"/>
        <where>  
            <if test="classId != null "> and class_id = #{classId}</if>
            <if test="groupName != null  and groupName != ''"> and group_name like concat('%', #{groupName}, '%')</if>
            <if test="groupCreatorId != null "> and group_creator_id = #{groupCreatorId}</if>
            <if test="groupQuantity != null "> and group_quantity = #{groupQuantity}</if>
        </where>
    </select>
    
    <select id="selectMoocSmartCourseGroupByGroupId" parameterType="Long" resultMap="MoocSmartCourseGroupResult">
        <include refid="selectMoocSmartCourseGroupVo"/>
        where group_id = #{groupId}
    </select>

    <insert id="insertMoocSmartCourseGroup" parameterType="MoocSmartCourseGroup">
        insert into mooc_smart_course_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="groupId != null">group_id,</if>
            <if test="classId != null">class_id,</if>
            <if test="groupName != null">group_name,</if>
            <if test="groupCreatorId != null">group_creator_id,</if>
            <if test="groupQuantity != null">group_quantity,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="groupId != null">#{groupId},</if>
            <if test="classId != null">#{classId},</if>
            <if test="groupName != null">#{groupName},</if>
            <if test="groupCreatorId != null">#{groupCreatorId},</if>
            <if test="groupQuantity != null">#{groupQuantity},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMoocSmartCourseGroup" parameterType="MoocSmartCourseGroup">
        update mooc_smart_course_group
        <trim prefix="SET" suffixOverrides=",">
            <if test="classId != null">class_id = #{classId},</if>
            <if test="groupName != null">group_name = #{groupName},</if>
            <if test="groupCreatorId != null">group_creator_id = #{groupCreatorId},</if>
            <if test="groupQuantity != null">group_quantity = #{groupQuantity},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where group_id = #{groupId}
    </update>

    <delete id="deleteMoocSmartCourseGroupByGroupId" parameterType="Long">
        delete from mooc_smart_course_group where group_id = #{groupId}
    </delete>

    <delete id="deleteMoocSmartCourseGroupByGroupIds" parameterType="String">
        delete from mooc_smart_course_group where group_id in 
        <foreach item="groupId" collection="array" open="(" separator="," close=")">
            #{groupId}
        </foreach>
    </delete>
</mapper>
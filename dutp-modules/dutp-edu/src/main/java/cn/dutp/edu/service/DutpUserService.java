package cn.dutp.edu.service;

import cn.dutp.edu.domain.dto.DutpUserDto;
import cn.dutp.edu.domain.vo.DutpUserVo;
import cn.dutp.edu.domian.DutpUser;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @author: dutp
 * @date: 2024/11/5 9:50
 */
public interface DutpUserService extends IService<DutpUser> {

    /**
     * 查询全部列表
     *
     * @param dutpUser 用户信息
     * @return 结果
     */
    public List<DutpUser> selectAll(DutpUser dutpUser);

    /**
     * 查询学生列表
     *
     * @param dutpUser 用户信息
     * @return 结果
     */
    public List<DutpUser> selectStudent(DutpUser dutpUser);

    /**
     * 查询教列表
     *
     * @param dutpUser 用户信息
     * @return 结果
     */
    public List<DutpUser> selectTeacher(DutpUser dutpUser);

    /**
     * 获取用户信息详情
     *
     * @return 结果
     */
    public DutpUser getUserById();

    /**
     * 新增学生
     *
     * @param dutpUser 用户信息
     * @return 结果
     */
    public boolean insertStudent(DutpUser dutpUser);

    /**
     * 新增教师
     *
     * @param dutpUser 用户信息
     * @return 结果
     */
    public boolean insertTeacher(DutpUser dutpUser);

    public boolean insertUser(DutpUser dutpUser);

    /**
     * 修改用户信息
     *
     * @param dutpUser
     * @return
     */
    public boolean updateUser(DutpUser dutpUser);

    /**
     * 禁用状态
     *
     * @param userId 用户id
     * @return 结果
     */
    public boolean changeStatus(Long userId);

    /**
     * 启用状态
     *
     * @param userId 用户ID
     * @return 结果
     */
    public boolean openStatus(Long userId);

    String importUser(DutpUserDto dutpUserDto);

    String importStudent(DutpUserDto dutpUserDto);

    String importTeacher(DutpUserDto dutpUserDto);

    /**
     * 批量删除教
     *
     * @param Ids 需要删除的主键集合
     * @return 结果
     */
    public boolean cancelAccount(List<Long> Ids);

    /**
     * 根据登陆用户的学校id获取 学生数量 教师数量
     *
     * @param schoolId 学校id
     * @return DutpUserVo
     */
    DutpUserVo countTeacherAndStudentBySchoolId(long schoolId);


    public DutpUser getDetail(DutpUser dutpUser);

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    public void checkUserAllowed(DutpUser user);

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    public void checkUserDataScope(Long userId);

    public boolean resetPwd(DutpUser user);

    Boolean cancelDutpUser(DutpUser user);

   void exportUser(HttpServletResponse response,DutpUser dutpUser);
}

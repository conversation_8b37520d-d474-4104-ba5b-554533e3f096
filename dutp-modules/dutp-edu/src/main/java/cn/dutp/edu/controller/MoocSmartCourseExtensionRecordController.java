    package cn.dutp.edu.controller;

    import java.util.List;
    import java.util.Arrays;
    import javax.servlet.http.HttpServletResponse;
    import org.springframework.beans.factory.annotation.Autowired;
    import org.springframework.web.bind.annotation.GetMapping;
    import org.springframework.web.bind.annotation.PostMapping;
    import org.springframework.web.bind.annotation.PutMapping;
    import org.springframework.web.bind.annotation.DeleteMapping;
    import org.springframework.web.bind.annotation.PathVariable;
    import org.springframework.web.bind.annotation.RequestBody;
    import org.springframework.web.bind.annotation.RequestMapping;
    import org.springframework.web.bind.annotation.RestController;
    import cn.dutp.common.log.annotation.Log;
    import cn.dutp.common.core.web.controller.BaseController;
    import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.common.core.web.domain.AjaxResult;
    import cn.dutp.common.log.enums.BusinessType;
    import cn.dutp.edu.domain.MoocSmartCourseExtensionRecord;
    import cn.dutp.edu.service.IMoocSmartCourseExtensionRecordService;
    import cn.dutp.common.core.utils.poi.ExcelUtil;
    import cn.dutp.common.core.web.page.TableDataInfo;

    /**
     * 互动课堂的拓展内容学生记录Controller
     *
     * <AUTHOR>
     * @date 2025-06-13
     */
    @RestController
    @RequestMapping("/extensionRecord")
    public class MoocSmartCourseExtensionRecordController extends BaseController
    {
        @Autowired
        private IMoocSmartCourseExtensionRecordService moocSmartCourseExtensionRecordService;

        /**
         * 查询互动课堂的拓展内容学生记录列表
         */
        @GetMapping("/list")
        public TableDataInfo list(MoocSmartCourseExtensionRecord moocSmartCourseExtensionRecord)
        {
            startPage();
            List<MoocSmartCourseExtensionRecord> list = moocSmartCourseExtensionRecordService.selectMoocSmartCourseExtensionRecordList(moocSmartCourseExtensionRecord);
            return getDataTable(list);
        }

        /**
         * 导出互动课堂的拓展内容学生记录列表
         */

        @Log(title = "导出互动课堂的拓展内容学生记录", businessType = BusinessType.EXPORT)
        @PostMapping("/export")
        public void export(HttpServletResponse response, MoocSmartCourseExtensionRecord moocSmartCourseExtensionRecord)
        {
            List<MoocSmartCourseExtensionRecord> list = moocSmartCourseExtensionRecordService.selectMoocSmartCourseExtensionRecordList(moocSmartCourseExtensionRecord);
            ExcelUtil<MoocSmartCourseExtensionRecord> util = new ExcelUtil<MoocSmartCourseExtensionRecord>(MoocSmartCourseExtensionRecord.class);
            util.exportExcel(response, list, "互动课堂的拓展内容学生记录数据");
        }

        /**
         * 获取互动课堂的拓展内容学生记录详细信息
         */

        @GetMapping(value = "/{recordId}")
        public AjaxResult getInfo(@PathVariable("recordId") Long recordId)
        {
            return success(moocSmartCourseExtensionRecordService.selectMoocSmartCourseExtensionRecordByRecordId(recordId));
        }

        /**
         * 获取自己的拓展提交记录
         */
        @GetMapping(value = "/record/{extensionId}")
        public AjaxResult getRecordByExtensionId(@PathVariable("extensionId") Long extensionId)
        {
            return success(moocSmartCourseExtensionRecordService.getRecordByExtensionId(extensionId, SecurityUtils.getUserId()));
        }

        /**
         * 新增互动课堂的拓展内容学生记录
         */

        @Log(title = "新增互动课堂的拓展内容学生记录", businessType = BusinessType.INSERT)
        @PostMapping
        public AjaxResult add(@RequestBody MoocSmartCourseExtensionRecord moocSmartCourseExtensionRecord)
        {
            moocSmartCourseExtensionRecord.setStudentId(SecurityUtils.getUserId());
            return toAjax(moocSmartCourseExtensionRecordService.insertMoocSmartCourseExtensionRecord(moocSmartCourseExtensionRecord));
        }

        /**
         * 修改互动课堂的拓展内容学生记录
         */

        @Log(title = "修改互动课堂的拓展内容学生记录", businessType = BusinessType.UPDATE)
        @PutMapping
        public AjaxResult edit(@RequestBody MoocSmartCourseExtensionRecord moocSmartCourseExtensionRecord)
        {
            return toAjax(moocSmartCourseExtensionRecordService.updateMoocSmartCourseExtensionRecord(moocSmartCourseExtensionRecord));
        }

        /**
         * 删除互动课堂的拓展内容学生记录
         */

        @Log(title = "删除互动课堂的拓展内容学生记录", businessType = BusinessType.DELETE)
        @DeleteMapping("/{recordIds}")
        public AjaxResult remove(@PathVariable Long[] recordIds)
        {
            return toAjax(moocSmartCourseExtensionRecordService.deleteMoocSmartCourseExtensionRecordByRecordIds(Arrays.asList(recordIds)));
        }
    }

package cn.dutp.edu.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.edu.domain.MoocSmartCourseClassQuestion;
import cn.dutp.edu.service.IMoocSmartCourseClassQuestionService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 互动课堂班级提问Controller
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@RestController
@RequestMapping("/classQuestion")
public class MoocSmartCourseClassQuestionController extends BaseController
{
    @Autowired
    private IMoocSmartCourseClassQuestionService moocSmartCourseClassQuestionService;

    /**
     * 查询互动课堂班级提问列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocSmartCourseClassQuestion moocSmartCourseClassQuestion)
    {
        startPage();
        List<MoocSmartCourseClassQuestion> list = moocSmartCourseClassQuestionService.selectMoocSmartCourseClassQuestionList(moocSmartCourseClassQuestion);
        return getDataTable(list);
    }

    /**
     * 导出互动课堂班级提问列表
     */
    @RequiresPermissions("edu:question:export")
    @Log(title = "导出互动课堂班级提问", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocSmartCourseClassQuestion moocSmartCourseClassQuestion)
    {
        List<MoocSmartCourseClassQuestion> list = moocSmartCourseClassQuestionService.selectMoocSmartCourseClassQuestionList(moocSmartCourseClassQuestion);
        ExcelUtil<MoocSmartCourseClassQuestion> util = new ExcelUtil<MoocSmartCourseClassQuestion>(MoocSmartCourseClassQuestion.class);
        util.exportExcel(response, list, "互动课堂班级提问数据");
    }

    /**
     * 获取互动课堂班级提问详细信息
     */
    @RequiresPermissions("edu:question:query")
    @GetMapping(value = "/{questionId}")
    public AjaxResult getInfo(@PathVariable("questionId") Long questionId)
    {
        return success(moocSmartCourseClassQuestionService.selectMoocSmartCourseClassQuestionByQuestionId(questionId));
    }

    /**
     * 新增互动课堂班级提问
     */
    @RequiresPermissions("edu:question:add")
    @Log(title = "新增互动课堂班级提问", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocSmartCourseClassQuestion moocSmartCourseClassQuestion)
    {
        return toAjax(moocSmartCourseClassQuestionService.insertMoocSmartCourseClassQuestion(moocSmartCourseClassQuestion));
    }

    /**
     * 修改互动课堂班级提问
     */
    @RequiresPermissions("edu:question:edit")
    @Log(title = "修改互动课堂班级提问", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocSmartCourseClassQuestion moocSmartCourseClassQuestion)
    {
        return toAjax(moocSmartCourseClassQuestionService.updateMoocSmartCourseClassQuestion(moocSmartCourseClassQuestion));
    }

    /**
     * 删除互动课堂班级提问
     */
    @RequiresPermissions("edu:question:remove")
    @Log(title = "删除互动课堂班级提问", businessType = BusinessType.DELETE)
    @DeleteMapping("/{questionIds}")
    public AjaxResult remove(@PathVariable Long[] questionIds)
    {
        return toAjax(moocSmartCourseClassQuestionService.deleteMoocSmartCourseClassQuestionByQuestionIds(Arrays.asList(questionIds)));
    }
}

package cn.dutp.edu.service;

import java.util.List;
import cn.dutp.edu.domain.MoocSmartCourseLesson;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 互动课堂的单次课堂Service接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IMoocSmartCourseLessonService extends IService<MoocSmartCourseLesson>
{
    /**
     * 查询互动课堂的单次课堂
     *
     * @param lessonId 互动课堂的单次课堂主键
     * @return 互动课堂的单次课堂
     */
    public MoocSmartCourseLesson selectMoocSmartCourseLessonByLessonId(Long lessonId);

    /**
     * 查询互动课堂的单次课堂列表
     *
     * @param moocSmartCourseLesson 互动课堂的单次课堂
     * @return 互动课堂的单次课堂集合
     */
    public List<MoocSmartCourseLesson> selectMoocSmartCourseLessonList(MoocSmartCourseLesson moocSmartCourseLesson);

    /**
     * 新增互动课堂的单次课堂
     *
     * @param moocSmartCourseLesson 互动课堂的单次课堂
     * @return 结果
     */
    public boolean insertMoocSmartCourseLesson(MoocSmartCourseLesson moocSmartCourseLesson);

    /**
     * 修改互动课堂的单次课堂
     *
     * @param moocSmartCourseLesson 互动课堂的单次课堂
     * @return 结果
     */
    public boolean updateMoocSmartCourseLesson(MoocSmartCourseLesson moocSmartCourseLesson);

    /**
     * 批量删除互动课堂的单次课堂
     *
     * @param lessonIds 需要删除的互动课堂的单次课堂主键集合
     * @return 结果
     */
    public boolean deleteMoocSmartCourseLessonByLessonIds(List<Long> lessonIds);

}

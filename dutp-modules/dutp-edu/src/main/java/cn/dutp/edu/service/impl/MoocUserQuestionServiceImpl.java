package cn.dutp.edu.service.impl;

import java.util.List;
import java.util.Date;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.edu.mapper.MoocUserQuestionMapper;
import cn.dutp.domain.MoocUserQuestion;
import cn.dutp.edu.service.IMoocUserQuestionService;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.utils.StringUtils;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.MoocUserQuestionOption;
import cn.dutp.domain.MoocUserQuestionFolder;
import cn.dutp.edu.mapper.MoocUserQuestionOptionMapper;
import cn.dutp.edu.mapper.MoocUserQuestionFolderMapper;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

/**
 * 智慧课堂习题Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Service
public class MoocUserQuestionServiceImpl extends ServiceImpl<MoocUserQuestionMapper, MoocUserQuestion> implements IMoocUserQuestionService
{
    @Autowired
    private MoocUserQuestionMapper moocUserQuestionMapper;
    
    @Autowired
    private MoocUserQuestionOptionMapper moocUserQuestionOptionMapper;
    
    @Autowired
    private MoocUserQuestionFolderMapper moocUserQuestionFolderMapper;

    /**
     * 查询智慧课堂习题
     *
     * @param questionId 智慧课堂习题主键
     * @return 智慧课堂习题
     */
    @Override
    public MoocUserQuestion selectMoocUserQuestionByQuestionId(Long questionId)
    {
        // 查询习题基本信息
        MoocUserQuestion question = this.getById(questionId);
        if (ObjectUtil.isNotNull(question)) {
            // 查询习题选项并设置到习题对象中
            LambdaQueryWrapper<MoocUserQuestionOption> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MoocUserQuestionOption::getQuestionId, questionId);
            question.setOptions(moocUserQuestionOptionMapper.selectList(queryWrapper));
        }
        return question;
    }

    /**
     * 查询智慧课堂习题列表
     *
     * @param moocUserQuestion 智慧课堂习题
     * @return 智慧课堂习题
     */
    @Override
    public List<MoocUserQuestion> selectMoocUserQuestionList(MoocUserQuestion moocUserQuestion)
    {
        LambdaQueryWrapper<MoocUserQuestion> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(moocUserQuestion.getQuestionRemark())) {
                lambdaQueryWrapper.eq(MoocUserQuestion::getQuestionRemark
                ,moocUserQuestion.getQuestionRemark());
            }
                if(ObjectUtil.isNotEmpty(moocUserQuestion.getQuestionType())) {
                lambdaQueryWrapper.eq(MoocUserQuestion::getQuestionType
                ,moocUserQuestion.getQuestionType());
            }
                if(ObjectUtil.isNotEmpty(moocUserQuestion.getQuestionContent())) {
                lambdaQueryWrapper.eq(MoocUserQuestion::getQuestionContent
                ,moocUserQuestion.getQuestionContent());
            }
                if(ObjectUtil.isNotEmpty(moocUserQuestion.getRightAnswer())) {
                lambdaQueryWrapper.eq(MoocUserQuestion::getRightAnswer
                ,moocUserQuestion.getRightAnswer());
            }
                if(ObjectUtil.isNotEmpty(moocUserQuestion.getAnalysis())) {
                lambdaQueryWrapper.eq(MoocUserQuestion::getAnalysis
                ,moocUserQuestion.getAnalysis());
            }
                if(ObjectUtil.isNotEmpty(moocUserQuestion.getUserId())) {
                lambdaQueryWrapper.eq(MoocUserQuestion::getUserId
                ,moocUserQuestion.getUserId());
            }
                if(ObjectUtil.isNotEmpty(moocUserQuestion.getDisorder())) {
                lambdaQueryWrapper.eq(MoocUserQuestion::getDisorder
                ,moocUserQuestion.getDisorder());
            }
                if(ObjectUtil.isNotEmpty(moocUserQuestion.getSort())) {
                lambdaQueryWrapper.eq(MoocUserQuestion::getSort
                ,moocUserQuestion.getSort());
            }
                if(ObjectUtil.isNotEmpty(moocUserQuestion.getFolderId())) {
                lambdaQueryWrapper.eq(MoocUserQuestion::getFolderId
                ,moocUserQuestion.getFolderId());
            }
                if(ObjectUtil.isNotEmpty(moocUserQuestion.getCodeContent())) {
                lambdaQueryWrapper.eq(MoocUserQuestion::getCodeContent
                ,moocUserQuestion.getCodeContent());
            }
                if(ObjectUtil.isNotEmpty(moocUserQuestion.getCreateSource())) {
                lambdaQueryWrapper.eq(MoocUserQuestion::getCreateSource
                ,moocUserQuestion.getCreateSource());
            }
        return this.list(lambdaQueryWrapper);
    }
    
    /**
     * 查询智慧课堂习题列表(包含选项)
     *
     * @param moocUserQuestion 智慧课堂习题
     * @return 智慧课堂习题
     */
    @Override
    public List<MoocUserQuestion> selectMoocUserQuestionListWithOptions(MoocUserQuestion moocUserQuestion) {
        return moocUserQuestionMapper.selectMoocUserQuestionListWithOptions(moocUserQuestion);
    }

    /**
     * 新增智慧课堂习题
     *
     * @param moocUserQuestion 智慧课堂习题
     * @return 结果
     */
    @Override
    public boolean insertMoocUserQuestion(MoocUserQuestion moocUserQuestion)
    {
        // 解码富文本内容
        decodeQuestionContent(moocUserQuestion);

        // 保存习题基本信息
        boolean result = this.save(moocUserQuestion);

        // 如果习题包含选项且保存成功，则保存选项
        if (result && ObjectUtil.isNotEmpty(moocUserQuestion.getOptions())) {
            moocUserQuestion.getOptions().forEach(option -> {
                // 解码选项内容
                decodeOptionContent(option);
                option.setQuestionId(moocUserQuestion.getQuestionId());
                moocUserQuestionOptionMapper.insert(option);
            });
        }

        return result;
    }

    /**
     * 修改智慧课堂习题
     *
     * @param moocUserQuestion 智慧课堂习题
     * @return 结果
     */
    @Override
    public boolean updateMoocUserQuestion(MoocUserQuestion moocUserQuestion)
    {
        // 解码富文本内容
        decodeQuestionContent(moocUserQuestion);
        
        // 更新习题基本信息
        boolean result = this.updateById(moocUserQuestion);
        
        // 如果更新成功且包含选项
        if (result && ObjectUtil.isNotEmpty(moocUserQuestion.getOptions())) {
            // 删除原有选项
            LambdaQueryWrapper<MoocUserQuestionOption> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MoocUserQuestionOption::getQuestionId, moocUserQuestion.getQuestionId());
            moocUserQuestionOptionMapper.delete(wrapper);
            
            // 添加新选项
            moocUserQuestion.getOptions().forEach(option -> {
                // 解码选项内容
                decodeOptionContent(option);
                option.setQuestionId(moocUserQuestion.getQuestionId());
                moocUserQuestionOptionMapper.insert(option);
            });
        }
        
        return result;
    }

    /**
     * 批量删除智慧课堂习题
     *
     * @param questionIds 需要删除的智慧课堂习题主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocUserQuestionByQuestionIds(List<Long> questionIds)
    {
        return this.removeByIds(questionIds);
    }
    
    /**
     * 将内容从URL编码格式解码
     */
    private String decodeContent(String content) {
        if (StringUtils.isEmpty(content)) {
            return content;
        }
        
        try {
            return URLDecoder.decode(content, StandardCharsets.UTF_8.name());
        } catch (Exception e) {
            log.error("内容解码警告: {}" + e.getMessage());
            // 解码失败时返回原始内容
            return content;
        }
    }
    
    /**
     * 解码题目内容
     */
    private void decodeQuestionContent(MoocUserQuestion moocUserQuestion) {
        if (ObjectUtil.isNotEmpty(moocUserQuestion)) {
            // 解码题目内容
            if (StringUtils.isNotEmpty(moocUserQuestion.getQuestionContent())) {
                moocUserQuestion.setQuestionContent(decodeContent(moocUserQuestion.getQuestionContent()));
            }
            
            // 解码解析内容
            if (StringUtils.isNotEmpty(moocUserQuestion.getAnalysis())) {
                moocUserQuestion.setAnalysis(decodeContent(moocUserQuestion.getAnalysis()));
            }
            
            // 解码代码内容
            if (StringUtils.isNotEmpty(moocUserQuestion.getCodeContent())) {
                moocUserQuestion.setCodeContent(decodeContent(moocUserQuestion.getCodeContent()));
            }
        }
    }
    
    /**
     * 解码选项内容
     */
    private void decodeOptionContent(MoocUserQuestionOption option) {
        if (ObjectUtil.isNotEmpty(option)) {
            // 解码选项内容
            if (StringUtils.isNotEmpty(option.getOptionContent())) {
                option.setOptionContent(decodeContent(option.getOptionContent()));
            }
        }
    }
    
    @Override
    public String importQuestions(List<MoocUserQuestion> moocUserQuestionList) {
        int successCount = 0;
        int failCount = 0;

        if (StringUtils.isEmpty(moocUserQuestionList)) {
            throw new ServiceException("导入数据为空");
        }

        for (MoocUserQuestion question : moocUserQuestionList) {
            //给每个题目加入userId
            question.setUserId(SecurityUtils.getUserId());
            try {
                // 解码题目内容
                decodeQuestionContent(question);

                // 保存题目基本信息
                this.save(question);

                // 保存题目选项
                if (StringUtils.isNotEmpty(question.getOptions())) {
                    for (MoocUserQuestionOption option : question.getOptions()) {
                        // 解码选项内容
                        decodeOptionContent(option);
                        option.setQuestionId(question.getQuestionId());
                        moocUserQuestionOptionMapper.insert(option);
                    }
                }
                successCount++;
            } catch (Exception e) {
                log.error("导入题目失败", e);
                failCount++;
            }
        }

        if (successCount == 0) {
            throw new ServiceException("导入失败，请检查导入模板是否正确");
        }

        return String.format("导入完成：成功%d道题目，失败%d道题目", successCount, failCount);
    }
    
    @Override
    public boolean moveToRecycleBin(List<Long> questionIds) {
        return moocUserQuestionMapper.moveToRecycleBin(questionIds, SecurityUtils.getUsername());
    }

    @Override
    public boolean restoreFromRecycleBin(List<Long> questionIds) {
        // 查询要还原的题目信息，获取它们的目录ID
        List<MoocUserQuestion> questions = moocUserQuestionMapper.selectMoocUserQuestionBatch(questionIds);
        if (StringUtils.isEmpty(questions)) {
            return false;
        }

        // 调用原有的还原方法
        boolean restoreByIds = moocUserQuestionMapper.restoreByIds(questionIds, SecurityUtils.getUsername());

        // 检查每个题目的目录是否存在且未被删除
        for (MoocUserQuestion question : questions) {
            Long folderId = question.getFolderId();
            if (folderId != null) {
                // 查询目录状态，如果目录不存在或已删除，则将题目放入默认目录
                boolean folderExists = checkFolderExists(folderId);
                if (!folderExists) {
                    // 查找defaultType为1的默认目录
                    Long defaultFolderId = getDefaultFolderId();
                    question.setFolderId(defaultFolderId);
                    // 更新题目的目录ID
                    this.updateById(question);
                }
            }
        }

        return restoreByIds;
    }
    
    /**
     * 检查题目目录是否存在且未被删除
     *
     * @param folderId 目录ID
     * @return 目录是否可用
     */
    private boolean checkFolderExists(Long folderId) {
        LambdaQueryWrapper<MoocUserQuestionFolder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MoocUserQuestionFolder::getFolderId, folderId)
                .eq(MoocUserQuestionFolder::getDelFlag, 0);
        return moocUserQuestionFolderMapper.selectCount(queryWrapper) > 0;
    }

    /**
     * 获取默认目录ID（defaultType=1）
     *
     * @return 默认目录ID
     */
    private Long getDefaultFolderId() {
        LambdaQueryWrapper<MoocUserQuestionFolder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MoocUserQuestionFolder::getDefaultType, "1")
                .eq(MoocUserQuestionFolder::getDelFlag, 0)
                .eq(MoocUserQuestionFolder::getUserId, SecurityUtils.getUserId());

        MoocUserQuestionFolder folder = moocUserQuestionFolderMapper.selectOne(queryWrapper);

        if (folder != null) {
            return folder.getFolderId();
        } else {
            // 如果没有找到默认目录，可以创建一个
            MoocUserQuestionFolder newFolder = new MoocUserQuestionFolder();
            newFolder.setFolderName("默认目录");
            newFolder.setDefaultType("1");
            newFolder.setParentId(0L);
            newFolder.setUserId(SecurityUtils.getUserId());
            newFolder.setCreateBy(SecurityUtils.getUsername());
            newFolder.setCreateTime(new Date());

            moocUserQuestionFolderMapper.insert(newFolder);
            return newFolder.getFolderId();
        }
    }

    @Override
    public List<MoocUserQuestion> recycleBinList(MoocUserQuestion moocUserQuestion) {
        return moocUserQuestionMapper.recycleBinList(moocUserQuestion);
    }

    @Override
    public MoocUserQuestion getUserAnswerInfo(MoocUserQuestion moocUserQuestion) {
        // 获取登录用户id
        Long userId = SecurityUtils.getUserId();
        Long questionId = moocUserQuestion.getQuestionId();

        // 获取题干信息
        MoocUserQuestion question = this.getById(questionId);

        if (ObjectUtil.isNotNull(question)) {
            // 查询习题选项并设置到习题对象中
            LambdaQueryWrapper<MoocUserQuestionOption> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MoocUserQuestionOption::getQuestionId, questionId);
            question.setOptions(moocUserQuestionOptionMapper.selectList(queryWrapper));

            // 获取用户的答案 - 这里需要根据实际情况调整
            // TODO: 实现获取用户答案的逻辑，可能需要额外的答案表
            // 目前只返回题目信息，没有用户答案
        }
        return question;
    }

    @Override
    public boolean checkPaperReference(Long[] questionIds) {
        return moocUserQuestionMapper.checkPaperReference(questionIds) > 0;
    }
}

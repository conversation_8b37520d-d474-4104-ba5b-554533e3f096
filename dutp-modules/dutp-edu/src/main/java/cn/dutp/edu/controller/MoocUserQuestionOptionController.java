package cn.dutp.edu.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.domain.MoocUserQuestionOption;
import cn.dutp.edu.service.IMoocUserQuestionOptionService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * DUTP-DTB_010数字教材选择题选项Controller
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/moocQuestionOption")
public class MoocUserQuestionOptionController extends BaseController
{
    @Autowired
    private IMoocUserQuestionOptionService moocUserQuestionOptionService;

    /**
     * 查询DUTP-DTB_010数字教材选择题选项列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocUserQuestionOption moocUserQuestionOption)
    {
        startPage();
        List<MoocUserQuestionOption> list = moocUserQuestionOptionService.selectMoocUserQuestionOptionList(moocUserQuestionOption);
        return getDataTable(list);
    }


    @Log(title = "导出DUTP-DTB_010数字教材选择题选项", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocUserQuestionOption moocUserQuestionOption)
    {
        List<MoocUserQuestionOption> list = moocUserQuestionOptionService.selectMoocUserQuestionOptionList(moocUserQuestionOption);
        ExcelUtil<MoocUserQuestionOption> util = new ExcelUtil<MoocUserQuestionOption>(MoocUserQuestionOption.class);
        util.exportExcel(response, list, "DUTP-DTB_010数字教材选择题选项数据");
    }


    @GetMapping(value = "/{optionId}")
    public AjaxResult getInfo(@PathVariable("optionId") Long optionId)
    {
        return success(moocUserQuestionOptionService.selectMoocUserQuestionOptionByOptionId(optionId));
    }


    @Log(title = "新增DUTP-DTB_010数字教材选择题选项", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocUserQuestionOption moocUserQuestionOption)
    {
        return toAjax(moocUserQuestionOptionService.insertMoocUserQuestionOption(moocUserQuestionOption));
    }


    @Log(title = "修改DUTP-DTB_010数字教材选择题选项", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocUserQuestionOption moocUserQuestionOption)
    {
        return toAjax(moocUserQuestionOptionService.updateMoocUserQuestionOption(moocUserQuestionOption));
    }

    /**
     * 删除DUTP-DTB_010数字教材选择题选项
     */

    @Log(title = "删除DUTP-DTB_010数字教材选择题选项", businessType = BusinessType.DELETE)
    @DeleteMapping("/{optionIds}")
    public AjaxResult remove(@PathVariable Long[] optionIds)
    {
        return toAjax(moocUserQuestionOptionService.deleteMoocUserQuestionOptionByOptionIds(Arrays.asList(optionIds)));
    }
}

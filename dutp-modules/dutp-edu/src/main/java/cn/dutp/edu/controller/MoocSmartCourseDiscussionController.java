package cn.dutp.edu.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.edu.domain.MoocSmartCourseDiscussion;
import cn.dutp.edu.service.IMoocSmartCourseDiscussionService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 互动课堂的课上主题讨论Controller
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@RestController
@RequestMapping("/discussion")
public class MoocSmartCourseDiscussionController extends BaseController
{
    @Autowired
    private IMoocSmartCourseDiscussionService moocSmartCourseDiscussionService;

    /**
     * 查询互动课堂的课上主题讨论列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocSmartCourseDiscussion moocSmartCourseDiscussion)
    {
        startPage();
        List<MoocSmartCourseDiscussion> list = moocSmartCourseDiscussionService.selectMoocSmartCourseDiscussionList(moocSmartCourseDiscussion);
        return getDataTable(list);
    }

    /**
     * 导出互动课堂的课上主题讨论列表
     */
    @RequiresPermissions("edu:discussion:export")
    @Log(title = "导出互动课堂的课上主题讨论", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocSmartCourseDiscussion moocSmartCourseDiscussion)
    {
        List<MoocSmartCourseDiscussion> list = moocSmartCourseDiscussionService.selectMoocSmartCourseDiscussionList(moocSmartCourseDiscussion);
        ExcelUtil<MoocSmartCourseDiscussion> util = new ExcelUtil<MoocSmartCourseDiscussion>(MoocSmartCourseDiscussion.class);
        util.exportExcel(response, list, "互动课堂的课上主题讨论数据");
    }

    /**
     * 获取互动课堂的课上主题讨论详细信息
     */
    @RequiresPermissions("edu:discussion:query")
    @GetMapping(value = "/{discussionId}")
    public AjaxResult getInfo(@PathVariable("discussionId") Long discussionId)
    {
        return success(moocSmartCourseDiscussionService.selectMoocSmartCourseDiscussionByDiscussionId(discussionId));
    }

    /**
     * 新增互动课堂的课上主题讨论
     */
    @RequiresPermissions("edu:discussion:add")
    @Log(title = "新增互动课堂的课上主题讨论", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocSmartCourseDiscussion moocSmartCourseDiscussion)
    {
        return toAjax(moocSmartCourseDiscussionService.insertMoocSmartCourseDiscussion(moocSmartCourseDiscussion));
    }

    /**
     * 修改互动课堂的课上主题讨论
     */
    @RequiresPermissions("edu:discussion:edit")
    @Log(title = "修改互动课堂的课上主题讨论", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocSmartCourseDiscussion moocSmartCourseDiscussion)
    {
        return toAjax(moocSmartCourseDiscussionService.updateMoocSmartCourseDiscussion(moocSmartCourseDiscussion));
    }

    /**
     * 删除互动课堂的课上主题讨论
     */
    @RequiresPermissions("edu:discussion:remove")
    @Log(title = "删除互动课堂的课上主题讨论", businessType = BusinessType.DELETE)
    @DeleteMapping("/{discussionIds}")
    public AjaxResult remove(@PathVariable Long[] discussionIds)
    {
        return toAjax(moocSmartCourseDiscussionService.deleteMoocSmartCourseDiscussionByDiscussionIds(Arrays.asList(discussionIds)));
    }
}

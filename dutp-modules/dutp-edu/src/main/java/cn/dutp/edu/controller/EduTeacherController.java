package cn.dutp.edu.controller;

import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.edu.domain.vo.TeacherVo;
import cn.dutp.edu.domian.DutpUser;
import cn.dutp.edu.service.IEduTeacherService;
import cn.dutp.system.api.model.LoginUser;
import org.apache.ibatis.annotations.Delete;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

/**
 * @author: dutp
 * @date: 2025/7/18 10:00
 */
@RestController
@RequestMapping("/eduTeacher")
public class EduTeacherController extends BaseController {

    @Autowired
    private IEduTeacherService eduTeacherService;

    /**
     * 查询当前学校下的教师
     * @param teacher 对象
     * @return 结果
     */
    @GetMapping("teacherList")
    public TableDataInfo teacherList(TeacherVo teacher){
        startPage();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long schoolId = loginUser.getSysUser().getSchoolId();
        teacher.setSchoolId(schoolId);
        return getDataTable(eduTeacherService.selectTeacherList(teacher));
    }

    /**
     * 查询当前学校下的教师
     * @param userId ID
     * @return 结果
     */
    @GetMapping("/getTeacher/{userId}")
    public AjaxResult getTeacherById(@PathVariable("userId")Long userId){
        return success(eduTeacherService.getByUserId(userId));
    }

    /**
     * 添加教师
     * @param teacher 教师对象
     * @return 结果
     */
    @PostMapping("addTeacher")
    @RequiresPermissions("edu:teacher:addTeacher")
    @Log(title = "新增教书信息", businessType = BusinessType.INSERT)
    public AjaxResult addTeacher(@RequestBody DutpUser teacher){
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long schoolId = loginUser.getSysUser().getSchoolId();
        teacher.setSchoolId(schoolId);
//        teacher.setPassword(SecurityUtils.encryptPassword(teacher.getPassword()));
        return toAjax(eduTeacherService.addTeacher(teacher));
    }

    @PutMapping("editTeacher")
    @RequiresPermissions("edu:teacher:editTeacher")
    @Log(title = "修改教师信息", businessType = BusinessType.UPDATE)
    public AjaxResult editTeacher(@RequestBody DutpUser teacher){
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long schoolId = loginUser.getSysUser().getSchoolId();
        teacher.setSchoolId(schoolId);
        return toAjax(eduTeacherService.editTeacher(teacher));
    }

    @Log(title = "删除教师", businessType = BusinessType.DELETE)
    @RequiresPermissions("edu:teacher:deleteTeacher")
    @DeleteMapping("/{userId}")
    public AjaxResult remove (@PathVariable("userId") Long[] userId){
        return toAjax(eduTeacherService.deleteTeacher(Arrays.asList(userId)));
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocCoursewareDesignShareMapper">
    
    <resultMap type="MoocCoursewareDesignShare" id="MoocCoursewareDesignShareResult">
        <result property="shareId"    column="share_id"    />
        <result property="toUserId"    column="to_user_id"    />
        <result property="fromUserId"    column="from_user_id"    />
        <result property="coursewareDesignId"    column="courseware_design_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMoocCoursewareDesignShareVo">
        select share_id, to_user_id, from_user_id, courseware_design_id, del_flag, create_by, create_time, update_by, update_time from mooc_courseware_design_share
    </sql>

    <select id="selectMoocCoursewareDesignShareList" parameterType="MoocCoursewareDesignShare" resultMap="MoocCoursewareDesignShareResult">
        <include refid="selectMoocCoursewareDesignShareVo"/>
        <where>  
            <if test="toUserId != null "> and to_user_id = #{toUserId}</if>
            <if test="fromUserId != null "> and from_user_id = #{fromUserId}</if>
            <if test="coursewareDesignId != null "> and courseware_design_id = #{coursewareDesignId}</if>
        </where>
    </select>
    
    <select id="selectMoocCoursewareDesignShareByShareId" parameterType="Long" resultMap="MoocCoursewareDesignShareResult">
        <include refid="selectMoocCoursewareDesignShareVo"/>
        where share_id = #{shareId}
    </select>

    <insert id="insertMoocCoursewareDesignShare" parameterType="MoocCoursewareDesignShare">
        insert into mooc_courseware_design_share
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shareId != null">share_id,</if>
            <if test="toUserId != null">to_user_id,</if>
            <if test="fromUserId != null">from_user_id,</if>
            <if test="coursewareDesignId != null">courseware_design_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="shareId != null">#{shareId},</if>
            <if test="toUserId != null">#{toUserId},</if>
            <if test="fromUserId != null">#{fromUserId},</if>
            <if test="coursewareDesignId != null">#{coursewareDesignId},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMoocCoursewareDesignShare" parameterType="MoocCoursewareDesignShare">
        update mooc_courseware_design_share
        <trim prefix="SET" suffixOverrides=",">
            <if test="toUserId != null">to_user_id = #{toUserId},</if>
            <if test="fromUserId != null">from_user_id = #{fromUserId},</if>
            <if test="coursewareDesignId != null">courseware_design_id = #{coursewareDesignId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where share_id = #{shareId}
    </update>

    <delete id="deleteMoocCoursewareDesignShareByShareId" parameterType="Long">
        delete from mooc_courseware_design_share where share_id = #{shareId}
    </delete>

    <delete id="deleteMoocCoursewareDesignShareByShareIds" parameterType="String">
        delete from mooc_courseware_design_share where share_id in 
        <foreach item="shareId" collection="array" open="(" separator="," close=")">
            #{shareId}
        </foreach>
    </delete>
</mapper>
package cn.dutp.edu.domain.dto;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class DtbUserBookDto {

    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userBookId;

    /**
     * 用户ID
     */
    @Excel(name = "用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 教材ID
     */
    @Excel(name = "教材ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 学校id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolId;

    /**
     * 工号
     */
    private String userNo;

    /**
     * 院系id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long academyId;

    /**
     * 专业id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long specialityId;

    /**
     * 手机号码
     */
    private String phonenumber;

    /**
     * 用户名(姓名)
     */
    private String userName;

    /**
     * 用户类型
     */
    @Excel(name = "用户类型", readConverterExp = "0 = 读者,1=学生,2=教师", sort = 3)
    private String userType;

    /** 版本ID */
    @Excel(name = "版本ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long versionId;

    /** 过期时间 */
    @Excel(name = "过期时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expireDate;

    /** 校本教材推送id */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolBookId;
}

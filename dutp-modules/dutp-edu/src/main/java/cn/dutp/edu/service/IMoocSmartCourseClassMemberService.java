package cn.dutp.edu.service;

import java.util.List;

import cn.dutp.edu.domain.MoocSmartCourseClass;
import cn.dutp.edu.domain.MoocSmartCourseClassMember;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 互动课堂班级成员Service接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IMoocSmartCourseClassMemberService extends IService<MoocSmartCourseClassMember>
{
    /**
     * 查询互动课堂班级成员
     *
     * @param classMemberId 互动课堂班级成员主键
     * @return 互动课堂班级成员
     */
    public MoocSmartCourseClassMember selectMoocSmartCourseClassMemberByClassMemberId(Long classMemberId);

    /**
     * 查询互动课堂班级成员列表
     *
     * @param moocSmartCourseClassMember 互动课堂班级成员
     * @return 互动课堂班级成员集合
     */
    public List<MoocSmartCourseClassMember> selectMoocSmartCourseClassMemberList(MoocSmartCourseClassMember moocSmartCourseClassMember);

    /**
     * 新增互动课堂班级成员
     *
     * @param moocSmartCourseClassMember 互动课堂班级成员
     * @return 结果
     */
    public boolean insertMoocSmartCourseClassMember(MoocSmartCourseClassMember moocSmartCourseClassMember);

    /**
     * 修改互动课堂班级成员
     *
     * @param moocSmartCourseClassMember 互动课堂班级成员
     * @return 结果
     */
    public boolean updateMoocSmartCourseClassMember(MoocSmartCourseClassMember moocSmartCourseClassMember);

    /**
     * 批量删除互动课堂班级成员
     *
     * @param classMemberIds 需要删除的互动课堂班级成员主键集合
     * @return 结果
     */
    public boolean deleteMoocSmartCourseClassMemberByClassMemberIds(List<Long> classMemberIds);

    /**
     * 互动课堂成员信息统计
     *
     * @return 互动课堂班级成员
     */
    public MoocSmartCourseClassMember getLearnInformationStatistics();

    /**
     * 互动课堂成员信息统计
     *
     * @return 互动课堂班级
     */
    public List<MoocSmartCourseClass> getLearnClass(MoocSmartCourseClass moocSmartCourseClass);

}

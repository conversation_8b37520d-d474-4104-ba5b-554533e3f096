<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.EduHomeMapper">

    <resultMap id="BaseResultMap" type="cn.dutp.edu.domain.DutpSchool">
        <id property="schoolId" column="school_id"/>
        <result property="schoolName" column="school_name"/>
        <result property="parentId" column="parent_id"/>
        <result property="schoolCode" column="school_code"/>
        <result property="logoUrl" column="logo_url"/>
        <result property="isPartner" column="is_partner"/>
        <result property="dataType" column="data_type"/>
        <result property="degreeId" column="degree_id"/>
        <result property="sort" column="sort"/>
        <result property="description" column="description"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <resultMap id="OrderBookResultMap" type="cn.dutp.domain.DtbBookOrderVo">
        <result property="sellerOrderNumber" column="sellerOrderNumber"/>
        <result property="proxyOrderNumber" column="proxyOrderNumber"/>
        <result property="totalOrderNumber" column="totalOrderNumber"/>
        <collection property="orderBookList" ofType="cn.dutp.domain.DtbBookOrderItem">
            <result property="orderNo" column="orderNo"/>
            <result property="bookName" column="bookName"/>
            <result property="isbn" column="isbn"/>
            <result property="orderType" column="orderType"/>
            <result property="bookQuantity" column="bookQuantity"/>
        </collection>
    </resultMap>

    <select id="countMajorByDepartmentList" resultType="Integer">
        SELECT count(*)
        FROM dutp_school
        WHERE parent_id IN
        <foreach collection='departmentList' item='departmentId' index="index" open='(' separator=',' close=')'>
            #{departmentId}
        </foreach>
        and data_type = 2
        and del_flag = '0'
    </select>

    <!--根据登陆用户的学校id获取订单数量-->
    <select id="countOrderNumber" resultType="cn.dutp.domain.DtbBookOrderVo">
        SELECT SUM(CASE WHEN dbo.order_type = 5 THEN 1 ELSE 0 END) AS sellerOrderNumber,
               SUM(CASE WHEN dbo.order_type = 3 THEN 1 ELSE 0 END) +
               SUM(CASE WHEN dbo.order_type = 2 THEN 1 ELSE 0 END) AS proxyOrderNumber
        FROM dtb_book_order dbo
                 LEFT JOIN
             dutp_school dbs
             ON dbo.school_id = dbs.school_id
        WHERE dbs.school_id = #{schoolId}
          AND order_status in ('settlement', 'completed')
          AND dbo.deleted = 1
    </select>

    <!--根据登陆用户的学校id获取订单信息-->
    <select id="selectOrderInfoBySchoolId" resultType="cn.dutp.domain.vo.DtbEduBookOrderVo">
        SELECT order_id,
        order_no
        FROM dtb_book_order
        WHERE school_id = #{schoolId}
        AND order_status in ('settlement', 'completed')
        AND deleted = 1
        order by create_time desc
        <if test="limitNum != null">
            LIMIT #{limitNum}
        </if>
    </select>
    <!--查询子订单详情-->
    <select id="selectOrderDetailById" resultType="cn.dutp.domain.vo.DtbEduBookOrderDetailVo">
        SELECT dboi.order_type,
               dbo.book_quantity,
               book.book_name,
               ifnull(book.isbn, book.issn) as isbn
        FROM dtb_book_order_item dbo
                 LEFT JOIN dtb_book_order dboi
                           on dbo.order_id = dboi.order_id
                 LEFT JOIN dtb_book book
                           on dbo.book_id = book.book_id
        WHERE dbo.order_id = #{orderId}
    </select>
    <!--据登陆用户id获取消息列表-->
    <select id="messageList" resultType="cn.dutp.message.api.domain.DutpUserMessage">
        SELECT message_id, title, content, create_time, read_flag
        FROM dutp_user_message
        WHERE to_user_id = #{toUserId}
          AND to_user_type = 1
          and del_flag = '0'
        order by read_flag ASC, create_time DESC
        limit 10
    </select>

    <!--根据登陆用户的学校id获取售后订单信息-->
    <select id="countRefundOrderNumber" resultType="cn.dutp.edu.domain.vo.DtbBookRefundOrderManagementVo">
        SELECT COUNT(order_id)                                    AS refundTotalOrderNumber,
               SUM(CASE WHEN refund_status = 0 THEN 1 ELSE 0 END) AS refundInProgressNumber,
               SUM(CASE WHEN refund_status = 1 THEN 1 ELSE 0 END) +
               SUM(CASE WHEN refund_status = 2 THEN 1 ELSE 0 END) AS completedNumber
        FROM dtb_book_refund_order
        WHERE school_id = #{schoolId}
    </select>

    <!--根据登陆用户的学校id获取教材信息-->
    <select id="overviewOfTextbooks" resultType="cn.dutp.domain.DtbBookVo">
        SELECT count(*)                                               AS textbooksNumber,
               SUM(CASE WHEN db.publish_status = 2 THEN 1 ELSE 0 END) AS publishedNumber,
               SUM(CASE WHEN db.publish_status = 1  AND db.current_step_id = 1 THEN 1 ELSE 0 END) AS inProductionNumber,
               SUM(CASE WHEN db.shelf_state = 1 THEN 1 ELSE 0 END)    AS shelvesNumber
        FROM dtb_book db
             INNER JOIN
             dtb_book_school dbs
             ON db.book_id = dbs.book_id
        WHERE dbs.school_id = #{schoolId}
          AND db.del_flag = 0
    </select>

</mapper>

package cn.dutp.edu.service;

import java.util.List;
import cn.dutp.edu.domain.MoocSmartCourseDiscussion;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 互动课堂的课上主题讨论Service接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IMoocSmartCourseDiscussionService extends IService<MoocSmartCourseDiscussion>
{
    /**
     * 查询互动课堂的课上主题讨论
     *
     * @param discussionId 互动课堂的课上主题讨论主键
     * @return 互动课堂的课上主题讨论
     */
    public MoocSmartCourseDiscussion selectMoocSmartCourseDiscussionByDiscussionId(Long discussionId);

    /**
     * 查询互动课堂的课上主题讨论列表
     *
     * @param moocSmartCourseDiscussion 互动课堂的课上主题讨论
     * @return 互动课堂的课上主题讨论集合
     */
    public List<MoocSmartCourseDiscussion> selectMoocSmartCourseDiscussionList(MoocSmartCourseDiscussion moocSmartCourseDiscussion);

    /**
     * 新增互动课堂的课上主题讨论
     *
     * @param moocSmartCourseDiscussion 互动课堂的课上主题讨论
     * @return 结果
     */
    public boolean insertMoocSmartCourseDiscussion(MoocSmartCourseDiscussion moocSmartCourseDiscussion);

    /**
     * 修改互动课堂的课上主题讨论
     *
     * @param moocSmartCourseDiscussion 互动课堂的课上主题讨论
     * @return 结果
     */
    public boolean updateMoocSmartCourseDiscussion(MoocSmartCourseDiscussion moocSmartCourseDiscussion);

    /**
     * 批量删除互动课堂的课上主题讨论
     *
     * @param discussionIds 需要删除的互动课堂的课上主题讨论主键集合
     * @return 结果
     */
    public boolean deleteMoocSmartCourseDiscussionByDiscussionIds(List<Long> discussionIds);

}

package cn.dutp.edu.service;

import java.util.List;
import cn.dutp.edu.domain.MoocSmartCourseMessage;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 互动课堂的课上互动讨论消息Service接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IMoocSmartCourseMessageService extends IService<MoocSmartCourseMessage>
{
    /**
     * 查询互动课堂的课上互动讨论消息
     *
     * @param messageId 互动课堂的课上互动讨论消息主键
     * @return 互动课堂的课上互动讨论消息
     */
    public MoocSmartCourseMessage selectMoocSmartCourseMessageByMessageId(Long messageId);

    /**
     * 查询互动课堂的课上互动讨论消息列表
     *
     * @param moocSmartCourseMessage 互动课堂的课上互动讨论消息
     * @return 互动课堂的课上互动讨论消息集合
     */
    public List<MoocSmartCourseMessage> selectMoocSmartCourseMessageList(MoocSmartCourseMessage moocSmartCourseMessage);

    /**
     * 新增互动课堂的课上互动讨论消息
     *
     * @param moocSmartCourseMessage 互动课堂的课上互动讨论消息
     * @return 结果
     */
    public boolean insertMoocSmartCourseMessage(MoocSmartCourseMessage moocSmartCourseMessage);

    /**
     * 修改互动课堂的课上互动讨论消息
     *
     * @param moocSmartCourseMessage 互动课堂的课上互动讨论消息
     * @return 结果
     */
    public boolean updateMoocSmartCourseMessage(MoocSmartCourseMessage moocSmartCourseMessage);

    /**
     * 批量删除互动课堂的课上互动讨论消息
     *
     * @param messageIds 需要删除的互动课堂的课上互动讨论消息主键集合
     * @return 结果
     */
    public boolean deleteMoocSmartCourseMessageByMessageIds(List<Long> messageIds);

}

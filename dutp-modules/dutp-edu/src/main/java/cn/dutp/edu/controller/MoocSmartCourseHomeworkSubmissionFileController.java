package cn.dutp.edu.controller;

import java.util.List;
import java.security.Security;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import org.apache.catalina.security.SecurityUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.edu.domain.MoocSmartCourseHomeworkSubmissionFile;
import cn.dutp.edu.service.IMoocSmartCourseHomeworkSubmissionFileService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 作业提交文件Controller
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@RestController
@RequestMapping("/file")
public class MoocSmartCourseHomeworkSubmissionFileController extends BaseController
{
    @Autowired
    private IMoocSmartCourseHomeworkSubmissionFileService moocSmartCourseHomeworkSubmissionFileService;

    /**
     * 查询作业提交文件列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocSmartCourseHomeworkSubmissionFile moocSmartCourseHomeworkSubmissionFile)
    {
        startPage();
        List<MoocSmartCourseHomeworkSubmissionFile> list = moocSmartCourseHomeworkSubmissionFileService.selectMoocSmartCourseHomeworkSubmissionFileList(moocSmartCourseHomeworkSubmissionFile);
        return getDataTable(list);
    }

    /**
     * 导出作业提交文件列表
     */

    @Log(title = "导出作业提交文件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocSmartCourseHomeworkSubmissionFile moocSmartCourseHomeworkSubmissionFile)
    {
        
        List<MoocSmartCourseHomeworkSubmissionFile> list = moocSmartCourseHomeworkSubmissionFileService.selectMoocSmartCourseHomeworkSubmissionFileList(moocSmartCourseHomeworkSubmissionFile);
        ExcelUtil<MoocSmartCourseHomeworkSubmissionFile> util = new ExcelUtil<MoocSmartCourseHomeworkSubmissionFile>(MoocSmartCourseHomeworkSubmissionFile.class);
        util.exportExcel(response, list, "作业提交文件数据");
    }

    /**
     * 获取作业提交文件详细信息
     */

    @GetMapping(value = "/{fileId}")
    public AjaxResult getInfo(@PathVariable("fileId") Long fileId)
    {
        return success(moocSmartCourseHomeworkSubmissionFileService.selectMoocSmartCourseHomeworkSubmissionFileByFileId(fileId));
    }

    /**
     * 新增作业提交文件
     */
 
    @Log(title = "新增作业提交文件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocSmartCourseHomeworkSubmissionFile moocSmartCourseHomeworkSubmissionFile)
    {
        return toAjax(moocSmartCourseHomeworkSubmissionFileService.insertMoocSmartCourseHomeworkSubmissionFile(moocSmartCourseHomeworkSubmissionFile));
    }

    /**
     * 修改作业提交文件
     */

    @Log(title = "修改作业提交文件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocSmartCourseHomeworkSubmissionFile moocSmartCourseHomeworkSubmissionFile)
    {
        return toAjax(moocSmartCourseHomeworkSubmissionFileService.updateMoocSmartCourseHomeworkSubmissionFile(moocSmartCourseHomeworkSubmissionFile));
    }

    /**
     * 删除作业提交文件
     */

    @Log(title = "删除作业提交文件", businessType = BusinessType.DELETE)
    @DeleteMapping("/{fileIds}")
    public AjaxResult remove(@PathVariable Long[] fileIds)
    {
        return toAjax(moocSmartCourseHomeworkSubmissionFileService.deleteMoocSmartCourseHomeworkSubmissionFileByFileIds(Arrays.asList(fileIds)));
    }
}

package cn.dutp.edu.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.edu.mapper.MoocSmartCourseClassMemberApplyMapper;
import cn.dutp.edu.domain.MoocSmartCourseClassMemberApply;
import cn.dutp.edu.service.IMoocSmartCourseClassMemberApplyService;

/**
 * 互动课堂班级成员申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class MoocSmartCourseClassMemberApplyServiceImpl extends ServiceImpl<MoocSmartCourseClassMemberApplyMapper, MoocSmartCourseClassMemberApply> implements IMoocSmartCourseClassMemberApplyService
{
    @Autowired
    private MoocSmartCourseClassMemberApplyMapper moocSmartCourseClassMemberApplyMapper;

    /**
     * 查询互动课堂班级成员申请
     *
     * @param applyId 互动课堂班级成员申请主键
     * @return 互动课堂班级成员申请
     */
    @Override
    public MoocSmartCourseClassMemberApply selectMoocSmartCourseClassMemberApplyByApplyId(Long applyId)
    {
        return this.getById(applyId);
    }

    /**
     * 查询互动课堂班级成员申请列表
     *
     * @param moocSmartCourseClassMemberApply 互动课堂班级成员申请
     * @return 互动课堂班级成员申请
     */
    @Override
    public List<MoocSmartCourseClassMemberApply> selectMoocSmartCourseClassMemberApplyList(MoocSmartCourseClassMemberApply moocSmartCourseClassMemberApply)
    {
        LambdaQueryWrapper<MoocSmartCourseClassMemberApply> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(moocSmartCourseClassMemberApply.getClassId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClassMemberApply::getClassId
                ,moocSmartCourseClassMemberApply.getClassId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseClassMemberApply.getUserId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClassMemberApply::getUserId
                ,moocSmartCourseClassMemberApply.getUserId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseClassMemberApply.getStatus())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClassMemberApply::getStatus
                ,moocSmartCourseClassMemberApply.getStatus());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseClassMemberApply.getAuditUserId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClassMemberApply::getAuditUserId
                ,moocSmartCourseClassMemberApply.getAuditUserId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseClassMemberApply.getApproveTime())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClassMemberApply::getApproveTime
                ,moocSmartCourseClassMemberApply.getApproveTime());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseClassMemberApply.getCreatedBy())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClassMemberApply::getCreatedBy
                ,moocSmartCourseClassMemberApply.getCreatedBy());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseClassMemberApply.getUpdatedBy())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClassMemberApply::getUpdatedBy
                ,moocSmartCourseClassMemberApply.getUpdatedBy());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增互动课堂班级成员申请
     *
     * @param moocSmartCourseClassMemberApply 互动课堂班级成员申请
     * @return 结果
     */
    @Override
    public boolean insertMoocSmartCourseClassMemberApply(MoocSmartCourseClassMemberApply moocSmartCourseClassMemberApply)
    {
        return this.save(moocSmartCourseClassMemberApply);
    }

    /**
     * 修改互动课堂班级成员申请
     *
     * @param moocSmartCourseClassMemberApply 互动课堂班级成员申请
     * @return 结果
     */
    @Override
    public boolean updateMoocSmartCourseClassMemberApply(MoocSmartCourseClassMemberApply moocSmartCourseClassMemberApply)
    {
        return this.updateById(moocSmartCourseClassMemberApply);
    }

    /**
     * 批量删除互动课堂班级成员申请
     *
     * @param applyIds 需要删除的互动课堂班级成员申请主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocSmartCourseClassMemberApplyByApplyIds(List<Long> applyIds)
    {
        return this.removeByIds(applyIds);
    }

}

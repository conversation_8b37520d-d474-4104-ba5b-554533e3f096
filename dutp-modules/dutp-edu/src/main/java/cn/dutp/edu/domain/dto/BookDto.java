package cn.dutp.edu.domain.dto;

import cn.dutp.book.domain.DtbBookFile;
import cn.dutp.common.core.serialize.LongListToStringSerializer;
import cn.dutp.edu.domain.vo.DtbBookUserExport;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 教材管理
 *
 * <AUTHOR>
 * @date
 */
@Data
public class BookDto {

    /**
     * 1公开教材2校本教材
     */
    private Integer bookOrganize;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 1零售2教务采购3代采订单4样书订单5书商采购
     */
    private Integer orderType;

    /**
     * 教材名称
     */
    private String bookName;

    /**
     * 用户名(姓名)
     */
    private String nickName;

    /**
     * 工号
     */
    private String userNo;

    /**
     * 分类id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long subjectId;

    /**
     * 用户id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;
    /**
     * 学校id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolId;

    /**
     * 教材id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    private Integer pageNum;
    private Integer pageSize;
    private Integer offset;

    /**
     * 数据类型：1数据总览 2应用数据
     */
    private Integer dataType;

    /**
     * 章节ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    /**
     * 教育层次ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long topSubjectId;
    /**
     * 专业ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long secondSubjectId;
    /**
     * 学科分类ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long thirdSubjectId;

    /**
     * 学科专业ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long forthSubjectId;

    @JsonSerialize(using = LongListToStringSerializer.class)
    private List<Long> userIdList;

    /**
     * 明细ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderItemId;

    /**
     * 院系/专业
     */
    @JsonSerialize(using = LongListToStringSerializer.class)
    private List<Long> schoolIdList;

    /**
     * 下载类型，1单文件下载2批量下载
     */
    private Integer downloadType;

    private DtbBookFile downloadBookFile;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userBookId;

    /**
     * 资源文件id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookFileId;

    /**
     * 教材下绑定的人员
     */
    List<DtbBookUserExport> userBookList;
    // 订单内的书籍id集合
    @JsonSerialize(using = LongListToStringSerializer.class)
    private List<Long> bookIdList;
    // 导出的购书码数量
    private Integer exportQuantity;
    // 导出的学院id集合
    @JsonSerialize(using = LongListToStringSerializer.class)
    private List<Long> collegeIdList;

    /**
     * 导入人员失败数据
     */
    List<DtbBookUserExport> errorUserBookList;

    // 订单id
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderId;

    /** 校本教材推送id */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolBookId;

    /** 专业id */
    private Long specialityId;

    /** 院系id */
    private Long academyId;

    /** 到期时间 */
    private Date expireDate;

    /**
     * 真实姓名
     */
    private String realName;
}

package cn.dutp.edu.service;

import java.util.List;
import cn.dutp.edu.domain.MoocSmartCourseNotice;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 互动课堂公告Service接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IMoocSmartCourseNoticeService extends IService<MoocSmartCourseNotice>
{
    /**
     * 查询互动课堂公告
     *
     * @param noticeId 互动课堂公告主键
     * @return 互动课堂公告
     */
    public MoocSmartCourseNotice selectMoocSmartCourseNoticeByNoticeId(Long noticeId);

    /**
     * 查询互动课堂公告列表
     *
     * @param moocSmartCourseNotice 互动课堂公告
     * @return 互动课堂公告集合
     */
    public List<MoocSmartCourseNotice> selectMoocSmartCourseNoticeList(MoocSmartCourseNotice moocSmartCourseNotice);

    /**
     * 新增互动课堂公告
     *
     * @param moocSmartCourseNotice 互动课堂公告
     * @return 结果
     */
    public boolean insertMoocSmartCourseNotice(MoocSmartCourseNotice moocSmartCourseNotice);

    /**
     * 修改互动课堂公告
     *
     * @param moocSmartCourseNotice 互动课堂公告
     * @return 结果
     */
    public boolean updateMoocSmartCourseNotice(MoocSmartCourseNotice moocSmartCourseNotice);

    /**
     * 批量删除互动课堂公告
     *
     * @param noticeIds 需要删除的互动课堂公告主键集合
     * @return 结果
     */
    public boolean deleteMoocSmartCourseNoticeByNoticeIds(List<Long> noticeIds);

}

package cn.dutp.edu.mapper;

import java.util.List;

import cn.dutp.edu.domain.dto.DutpUserCommonFileDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import cn.dutp.edu.domain.MoocSmartCourseBrainstorm;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
/**
 * 互动课堂的头脑风暴Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Repository
public interface MoocSmartCourseBrainstormMapper extends BaseMapper<MoocSmartCourseBrainstorm>
{
    void batchInsertDutpUserCommonFile (List<DutpUserCommonFileDto> list);

    void deleteDutpUserCommonFileByBusinessId (@Param("businessId") long businessId);

    List<DutpUserCommonFileDto> getDutpUserCommonFile (@Param("businessId") long businessId);
}

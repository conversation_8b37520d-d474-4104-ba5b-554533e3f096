package cn.dutp.edu.service.impl;

import cn.dutp.api.common.constant.DutpConstant;
import cn.dutp.api.common.constant.NotificationConstants;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.edu.domain.EduTeacherAuthentication;
import cn.dutp.edu.domian.DutpUser;
import cn.dutp.edu.mapper.DutpUserMapper;
import cn.dutp.edu.mapper.EduTeacherAuthenticationMapper;
import cn.dutp.edu.service.IEduTeacherAuthenticationService;
import cn.dutp.message.api.RemoteUserMessageService;
import cn.dutp.message.api.domain.DutpUserMessage;
import cn.dutp.system.api.model.LoginUser;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 教师认证Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
@Service
public class EduTeacherAuthenticationServiceImpl extends ServiceImpl<EduTeacherAuthenticationMapper, EduTeacherAuthentication> implements IEduTeacherAuthenticationService
{
    @Autowired
    private EduTeacherAuthenticationMapper eduTeacherAuthenticationMapper;
    @Autowired
    private DutpUserMapper dutpUserMapper;
    @Autowired
    private RemoteUserMessageService remoteUserMessageService;

    /**
     * 查询教师认证
     *
     * @param Id 教师认证主键
     * @return 教师认证
     */
    @Override
    public EduTeacherAuthentication selectEduTeacherAuthenticationById(Long Id)
    {
        return eduTeacherAuthenticationMapper.selectEduTeacherAuthenticationById(Id);
    }

    /**
     * 查询教师认证列表
     *
     * @param eduTeacherAuthentication 教师认证
     * @return 教师认证
     */
    @Override
    public List<EduTeacherAuthentication> selectEduTeacherAuthenticationList(EduTeacherAuthentication eduTeacherAuthentication)
    {
        return eduTeacherAuthenticationMapper.selectEduTeacherAuthenticationList(eduTeacherAuthentication);
    }

    /**
     * 新增教师认证
     *
     * @param eduTeacherAuthentication 教师认证
     * @return 结果
     */
    @Override
    public boolean insertEduTeacherAuthentication(EduTeacherAuthentication eduTeacherAuthentication)
    {
        return this.save(eduTeacherAuthentication);
    }

    /**
     * 新增教师认证
     *
     * @param eduTeacherAuthentication 教师认证
     * @return 结果
     */
    @Override
    public boolean insertEduTeacherAuthenticationEducation(EduTeacherAuthentication eduTeacherAuthentication)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        // 根据 userId 删除现有数据
        baseMapper.deleteEduTeacherAuthenticationById(eduTeacherAuthentication.getAuthId());
        eduTeacherAuthentication.setAuthId(null);
        eduTeacherAuthentication.setAuthState(0);
        eduTeacherAuthentication.setDelFlag("0");
        // 查询管理员用户信息
        List<Long> superUser = baseMapper.getSuperUser();
        superUser.forEach(userId -> {
            // 发送消息通知管理员
            DutpUserMessage dutpUserMessage = new DutpUserMessage();
            dutpUserMessage.setContent(String.format(NotificationConstants.TEACHER_IDENTITY, eduTeacherAuthentication.getRealName()));
            dutpUserMessage.setTitle(NotificationConstants.TEACHER_TITLE);
            dutpUserMessage.setFromUserId(loginUser.getUserid());
            dutpUserMessage.setToUserId(userId);
            dutpUserMessage.setMessageType(DutpConstant.NUM_ONE);
            dutpUserMessage.setReadFlag(DutpConstant.NUM_ZERO);
            dutpUserMessage.setFromUserType(DutpConstant.NUM_TWO);
            dutpUserMessage.setToUserType(DutpConstant.NUM_ONE);
            boolean isSendMessageSuccess = remoteUserMessageService.addMessage(dutpUserMessage).getCode() == 200;
            // 检查消息发送是否成功
            if (!isSendMessageSuccess) {
                throw new RuntimeException(DutpConstant.SENDING_MESSAGE_FAILED);
            }
        });

        // 保存数据
        return this.save(eduTeacherAuthentication);
    }

    /**
     * 发送教师认证消息
     * @param userId 接收消息的用户ID
     * @param authState 认证状态
     * @param rejectReason 驳回原因（当认证未通过时）
     */
    private void sendTeacherAuthMessage(Long userId, Integer authState, String rejectReason) {
        DutpUserMessage dutpUserMessage = new DutpUserMessage();
        String content;
        if (authState == 1) {
            content = "尊敬的用户您好，您已通过教师认证，可以开启您的教师身份之旅啦！";
        } else {
            content = String.format(NotificationConstants.TEACHER_REJECTED, rejectReason);
        }
        dutpUserMessage.setContent(content);
        dutpUserMessage.setTitle(NotificationConstants.TEACHER_TITLE);
        dutpUserMessage.setFromUserId(SecurityUtils.getUserId());
        dutpUserMessage.setToUserId(userId);
        dutpUserMessage.setMessageType(DutpConstant.NUM_ONE);
        dutpUserMessage.setReadFlag(DutpConstant.NUM_ZERO);
        dutpUserMessage.setFromUserType(DutpConstant.NUM_ONE);
        dutpUserMessage.setToUserType(DutpConstant.NUM_TWO);
        boolean isSendMessageSuccess = remoteUserMessageService.addMessage(dutpUserMessage).getCode() == 200;
        if (!isSendMessageSuccess) {
            throw new RuntimeException(DutpConstant.SENDING_MESSAGE_FAILED);
        }
    }

    /**
     * 修改教师认证
     *
     * @param eduTeacherAuthentication 教师认证
     * @return 结果
     */
    @Override
    public boolean updateEduTeacherAuthentication(EduTeacherAuthentication eduTeacherAuthentication)
    {
        boolean result = this.updateById(eduTeacherAuthentication);
        if(eduTeacherAuthentication.getAuthState() == 1){
            LambdaQueryWrapper<EduTeacherAuthentication> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(EduTeacherAuthentication::getUserId,eduTeacherAuthentication.getUserId());
            EduTeacherAuthentication eduTeacherAuthentication1 = eduTeacherAuthenticationMapper.selectOne(queryWrapper);
            LambdaUpdateWrapper<DutpUser> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(DutpUser::getUserId,eduTeacherAuthentication.getUserId()).set(DutpUser::getUserType,2)
                    .set(DutpUser::getSchoolId,eduTeacherAuthentication1.getSchoolId())
                    .set(DutpUser::getAcademyId,eduTeacherAuthentication1.getAcademyId())
                    .set(DutpUser::getSpecialityId,eduTeacherAuthentication1.getSpecialityId())
                    .set(DutpUser::getEmail,eduTeacherAuthentication1.getEmail()).set(DutpUser::getRealName,eduTeacherAuthentication1.getRealName());
            dutpUserMapper.update(null,updateWrapper);
        }
        // 发送认证结果消息
        sendTeacherAuthMessage(eduTeacherAuthentication.getUserId(), eduTeacherAuthentication.getAuthState(), eduTeacherAuthentication.getRejectReason());
        return result;
    }

    /**
     * 批量删除教师认证
     *
     * @param Ids 需要删除的教师认证主键
     * @return 结果
     */
    @Override
    public boolean deleteEduTeacherAuthenticationByIds(List<Long> Ids)
    {
        return this.removeByIds(Ids);
    }

}

package cn.dutp.edu.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.edu.mapper.MoocSmartCourseClassAnswerListMapper;
import cn.dutp.edu.domain.MoocSmartCourseClassAnswerList;
import cn.dutp.edu.service.IMoocSmartCourseClassAnswerListService;

/**
 * 互动课堂班级回答人员Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class MoocSmartCourseClassAnswerListServiceImpl extends ServiceImpl<MoocSmartCourseClassAnswerListMapper, MoocSmartCourseClassAnswerList> implements IMoocSmartCourseClassAnswerListService
{
    @Autowired
    private MoocSmartCourseClassAnswerListMapper moocSmartCourseClassAnswerListMapper;

    /**
     * 查询互动课堂班级回答人员
     *
     * @param studentId 互动课堂班级回答人员主键
     * @return 互动课堂班级回答人员
     */
    @Override
    public MoocSmartCourseClassAnswerList selectMoocSmartCourseClassAnswerListByStudentId(Long studentId)
    {
        return this.getById(studentId);
    }

    /**
     * 查询互动课堂班级回答人员列表
     *
     * @param moocSmartCourseClassAnswerList 互动课堂班级回答人员
     * @return 互动课堂班级回答人员
     */
    @Override
    public List<MoocSmartCourseClassAnswerList> selectMoocSmartCourseClassAnswerListList(MoocSmartCourseClassAnswerList moocSmartCourseClassAnswerList)
    {
        LambdaQueryWrapper<MoocSmartCourseClassAnswerList> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(moocSmartCourseClassAnswerList.getQuestionId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClassAnswerList::getQuestionId
                ,moocSmartCourseClassAnswerList.getQuestionId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseClassAnswerList.getScore())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClassAnswerList::getScore
                ,moocSmartCourseClassAnswerList.getScore());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增互动课堂班级回答人员
     *
     * @param moocSmartCourseClassAnswerList 互动课堂班级回答人员
     * @return 结果
     */
    @Override
    public boolean insertMoocSmartCourseClassAnswerList(MoocSmartCourseClassAnswerList moocSmartCourseClassAnswerList)
    {
        return this.save(moocSmartCourseClassAnswerList);
    }

    /**
     * 修改互动课堂班级回答人员
     *
     * @param moocSmartCourseClassAnswerList 互动课堂班级回答人员
     * @return 结果
     */
    @Override
    public boolean updateMoocSmartCourseClassAnswerList(MoocSmartCourseClassAnswerList moocSmartCourseClassAnswerList)
    {
        return this.updateById(moocSmartCourseClassAnswerList);
    }

    /**
     * 批量删除互动课堂班级回答人员
     *
     * @param studentIds 需要删除的互动课堂班级回答人员主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocSmartCourseClassAnswerListByStudentIds(List<Long> studentIds)
    {
        return this.removeByIds(studentIds);
    }

}

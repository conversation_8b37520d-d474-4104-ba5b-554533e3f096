<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocSmartCourseClassQuestionMapper">
    
    <resultMap type="MoocSmartCourseClassQuestion" id="MoocSmartCourseClassQuestionResult">
        <result property="questionId"    column="question_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="classId"    column="class_id"    />
        <result property="lessonId"    column="lesson_id"    />
        <result property="questionCreatorId"    column="question_creator_id"    />
        <result property="questionTitle"    column="question_title"    />
        <result property="questionContent"    column="question_content"    />
        <result property="questionStartDate"    column="question_start_date"    />
        <result property="status"    column="status"    />
        <result property="questionEndDate"    column="question_end_date"    />
        <result property="signInFlag"    column="sign_in_flag"    />
        <result property="questionType"    column="question_type"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMoocSmartCourseClassQuestionVo">
        select question_id, course_id, class_id, lesson_id, question_creator_id,status,question_start_date, question_title, question_content, question_end_date, sign_in_flag, question_type, del_flag, create_by, create_time, update_by, update_time from mooc_smart_course_class_question
    </sql>

    <select id="selectMoocSmartCourseClassQuestionList" parameterType="MoocSmartCourseClassQuestion" resultMap="MoocSmartCourseClassQuestionResult">
        <include refid="selectMoocSmartCourseClassQuestionVo"/>
        <where>  
            <if test="courseId != null "> and course_id = #{courseId}</if>
            <if test="questionCreatorId != null "> and question_creator_id = #{questionCreatorId}</if>
            <if test="questionTitle != null  and questionTitle != ''"> and question_title = #{questionTitle}</if>
            <if test="questionContent != null  and questionContent != ''"> and question_content = #{questionContent}</if>
            <if test="questionEndDate != null "> and question_end_date = #{questionEndDate}</if>
            <if test="signInFlag != null  and signInFlag != ''"> and sign_in_flag = #{signInFlag}</if>
            <if test="questionType != null  and questionType != ''"> and question_type = #{questionType}</if>
        </where>
    </select>
    
    <select id="selectMoocSmartCourseClassQuestionByQuestionId" parameterType="Long" resultMap="MoocSmartCourseClassQuestionResult">
        <include refid="selectMoocSmartCourseClassQuestionVo"/>
        where question_id = #{questionId}
    </select>

    <insert id="insertMoocSmartCourseClassQuestion" parameterType="MoocSmartCourseClassQuestion">
        insert into mooc_smart_course_class_question
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="questionId != null">question_id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="questionCreatorId != null">question_creator_id,</if>
            <if test="questionTitle != null">question_title,</if>
            <if test="questionContent != null">question_content,</if>
            <if test="questionEndDate != null">question_end_date,</if>
            <if test="signInFlag != null">sign_in_flag,</if>
            <if test="questionType != null">question_type,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="questionId != null">#{questionId},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="questionCreatorId != null">#{questionCreatorId},</if>
            <if test="questionTitle != null">#{questionTitle},</if>
            <if test="questionContent != null">#{questionContent},</if>
            <if test="questionEndDate != null">#{questionEndDate},</if>
            <if test="signInFlag != null">#{signInFlag},</if>
            <if test="questionType != null">#{questionType},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMoocSmartCourseClassQuestion" parameterType="MoocSmartCourseClassQuestion">
        update mooc_smart_course_class_question
        <trim prefix="SET" suffixOverrides=",">
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="questionCreatorId != null">question_creator_id = #{questionCreatorId},</if>
            <if test="questionTitle != null">question_title = #{questionTitle},</if>
            <if test="questionContent != null">question_content = #{questionContent},</if>
            <if test="questionEndDate != null">question_end_date = #{questionEndDate},</if>
            <if test="signInFlag != null">sign_in_flag = #{signInFlag},</if>
            <if test="questionType != null">question_type = #{questionType},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where question_id = #{questionId}
    </update>

    <delete id="deleteMoocSmartCourseClassQuestionByQuestionId" parameterType="Long">
        delete from mooc_smart_course_class_question where question_id = #{questionId}
    </delete>

    <delete id="deleteMoocSmartCourseClassQuestionByQuestionIds" parameterType="String">
        delete from mooc_smart_course_class_question where question_id in 
        <foreach item="questionId" collection="array" open="(" separator="," close=")">
            #{questionId}
        </foreach>
    </delete>
</mapper>
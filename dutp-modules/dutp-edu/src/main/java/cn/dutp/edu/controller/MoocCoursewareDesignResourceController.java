package cn.dutp.edu.controller;

import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.domain.MoocCoursewareDesignResource;
import cn.dutp.edu.service.IMoocCoursewareDesignResourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 公开课课件设计资源Controller
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@RestController
@RequestMapping("/coursewareResource")
public class MoocCoursewareDesignResourceController extends BaseController {
    @Autowired
    private IMoocCoursewareDesignResourceService moocCoursewareDesignResourceService;

    /**
     * 查询公开课课件设计资源列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocCoursewareDesignResource moocCoursewareDesignResource) {
        startPage();
        List<MoocCoursewareDesignResource> list = moocCoursewareDesignResourceService.selectMoocCoursewareDesignResourceList(moocCoursewareDesignResource);
        return getDataTable(list);
    }

    /**
     * 导出公开课课件设计资源列表
     */
    @Log(title = "导出公开课课件设计资源", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocCoursewareDesignResource moocCoursewareDesignResource) {
        List<MoocCoursewareDesignResource> list = moocCoursewareDesignResourceService.selectMoocCoursewareDesignResourceList(moocCoursewareDesignResource);
        ExcelUtil<MoocCoursewareDesignResource> util = new ExcelUtil<MoocCoursewareDesignResource>(MoocCoursewareDesignResource.class);
        util.exportExcel(response, list, "公开课课件设计资源数据");
    }

    /**
     * 获取公开课课件设计资源详细信息
     */
    @GetMapping(value = "/{chapterResourceId}")
    public AjaxResult getInfo(@PathVariable("chapterResourceId") Long chapterResourceId) {
        return success(moocCoursewareDesignResourceService.selectMoocCoursewareDesignResourceByChapterResourceId(chapterResourceId));
    }

    /**
     * 新增公开课课件设计资源
     */
    @Log(title = "新增公开课课件设计资源", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocCoursewareDesignResource moocCoursewareDesignResource) {
        return toAjax(moocCoursewareDesignResourceService.insertMoocCoursewareDesignResource(moocCoursewareDesignResource));
    }

    /**
     * 修改公开课课件设计资源
     */
    @Log(title = "修改公开课课件设计资源", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocCoursewareDesignResource moocCoursewareDesignResource) {
        return toAjax(moocCoursewareDesignResourceService.updateMoocCoursewareDesignResource(moocCoursewareDesignResource));
    }

    /**
     * 删除公开课课件设计资源
     */
    @Log(title = "删除公开课课件设计资源", businessType = BusinessType.DELETE)
    @DeleteMapping("/{chapterResourceIds}")
    public AjaxResult remove(@PathVariable Long[] chapterResourceIds) {
        return toAjax(moocCoursewareDesignResourceService.deleteMoocCoursewareDesignResourceByChapterResourceIds(Arrays.asList(chapterResourceIds)));
    }
}

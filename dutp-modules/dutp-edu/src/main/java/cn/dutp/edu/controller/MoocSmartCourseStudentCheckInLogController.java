package cn.dutp.edu.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.edu.domain.MoocSmartCourseStudentCheckInLog;
import cn.dutp.edu.service.IMoocSmartCourseStudentCheckInLogService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 学生出勤日志Controller
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@RestController
@RequestMapping("/log")
public class MoocSmartCourseStudentCheckInLogController extends BaseController
{
    @Autowired
    private IMoocSmartCourseStudentCheckInLogService moocSmartCourseStudentCheckInLogService;

    /**
     * 查询学生出勤日志列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocSmartCourseStudentCheckInLog moocSmartCourseStudentCheckInLog)
    {
        startPage();
        List<MoocSmartCourseStudentCheckInLog> list = moocSmartCourseStudentCheckInLogService.selectMoocSmartCourseStudentCheckInLogList(moocSmartCourseStudentCheckInLog);
        return getDataTable(list);
    }

    /**
     * 导出学生出勤日志列表
     */
    @RequiresPermissions("edu:log:export")
    @Log(title = "导出学生出勤日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocSmartCourseStudentCheckInLog moocSmartCourseStudentCheckInLog)
    {
        List<MoocSmartCourseStudentCheckInLog> list = moocSmartCourseStudentCheckInLogService.selectMoocSmartCourseStudentCheckInLogList(moocSmartCourseStudentCheckInLog);
        ExcelUtil<MoocSmartCourseStudentCheckInLog> util = new ExcelUtil<MoocSmartCourseStudentCheckInLog>(MoocSmartCourseStudentCheckInLog.class);
        util.exportExcel(response, list, "学生出勤日志数据");
    }

    /**
     * 获取学生出勤日志详细信息
     */
    @RequiresPermissions("edu:log:query")
    @GetMapping(value = "/{logId}")
    public AjaxResult getInfo(@PathVariable("logId") Long logId)
    {
        return success(moocSmartCourseStudentCheckInLogService.selectMoocSmartCourseStudentCheckInLogByLogId(logId));
    }

    /**
     * 新增学生出勤日志
     */
    @RequiresPermissions("edu:log:add")
    @Log(title = "新增学生出勤日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocSmartCourseStudentCheckInLog moocSmartCourseStudentCheckInLog)
    {
        return toAjax(moocSmartCourseStudentCheckInLogService.insertMoocSmartCourseStudentCheckInLog(moocSmartCourseStudentCheckInLog));
    }

    /**
     * 修改学生出勤日志
     */
    @RequiresPermissions("edu:log:edit")
    @Log(title = "修改学生出勤日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocSmartCourseStudentCheckInLog moocSmartCourseStudentCheckInLog)
    {
        return toAjax(moocSmartCourseStudentCheckInLogService.updateMoocSmartCourseStudentCheckInLog(moocSmartCourseStudentCheckInLog));
    }

    /**
     * 删除学生出勤日志
     */
    @RequiresPermissions("edu:log:remove")
    @Log(title = "删除学生出勤日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{logIds}")
    public AjaxResult remove(@PathVariable Long[] logIds)
    {
        return toAjax(moocSmartCourseStudentCheckInLogService.deleteMoocSmartCourseStudentCheckInLogByLogIds(Arrays.asList(logIds)));
    }
}

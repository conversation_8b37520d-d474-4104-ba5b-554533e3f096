package cn.dutp.edu.mapper;

import cn.dutp.domain.DtbBook;
import cn.dutp.edu.domain.DutpSchool;
import cn.dutp.edu.domain.dto.BookDto;
import cn.dutp.edu.domain.dto.DtbUserBookDto;
import cn.dutp.edu.domain.vo.BookVo;
import cn.dutp.edu.domain.vo.DutpEduOrderDetailVo;
import cn.dutp.edu.domain.vo.DutpEduOrderVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * description 针对表【dtb_book(DUTP-DTB_002数字教材)】的数据库操作Mapper
 * createDate 2025-01-14 09:22:08
 * Entity cn.dutp.edu.domain.DtbBook
 */
@Repository
public interface DtbBookMapper extends BaseMapper<DtbBook> {

    /**
     * 教务端查询教材商城教材列表
     */
    List<BookVo> selectOpenBookList(BookDto dto);

    /**
     * 教务端教材管理-查询校本教材列表
     */
    List<BookVo> selectEduSchoolBookList(BookDto dto);

    /**
     * 教务端教材管理-查询公开教材列表
     */
    List<BookVo> selectBookList(@Param("dto") BookDto dto,@Param("orderIds")  List<Long> orderIds);

    /**
     * 查询公开教材详情
     */
    BookVo getOpenBookDetail(@Param("bookId") Long bookId, @Param("schoolId") Long schoolId);

    /**
     * 查询校本教材详情
     */
    BookVo getSchoolBookDetail(@Param("bookId") Long bookId);

    List<BookVo> selectBookListForSchool(BookDto dto);

    /**
     * 根据学校id和主订单id，查询子订单的列表
     *
     * @param orderId  主订单id
     * @param schoolId 学校id
     * @return 子订单的列表
     */
    List<DutpEduOrderDetailVo> selectItemListBySchoolAndOrder(@Param("orderId") Long orderId, @Param("schoolId") Long schoolId);

    /**
     * 根据学校id和订单类型，查询订单的列表
     *
     * @param dto 入参
     * @return 返回的主订单列表
     */
    List<DutpEduOrderVo> selectOrderListBySchoolAndType(BookDto dto);

    DutpEduOrderDetailVo getBookDetailByItemId(Long orderItemId);

    // 根据订单id获取订单明细的书籍集合
    List<BookVo> selectBookListByOrderId(Long orderId);

    List<DutpSchool> selectSchoolById(@Param("schoolId") Long schoolId);

    DtbUserBookDto selectSchoolBookBySchoolBookId(@Param("schoolBookId") Long schoolBookId);
}





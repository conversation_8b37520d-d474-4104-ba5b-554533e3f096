package cn.dutp.edu.mapper;

import cn.dutp.domain.vo.DtbBookCodeExportInfoVo;
import cn.dutp.domain.vo.DtbBookPurchaseCodeVO;
import cn.dutp.domain.vo.DtbBookSchoolVo;
import cn.dutp.edu.domain.DtbBookCodeExport;
import cn.dutp.edu.domain.dto.BookDto;
import cn.dutp.edu.domain.vo.DtbBookPurchaseCodeExport;
import cn.dutp.edu.domain.vo.DtbBookdtbPurchaseCodeUnboundExport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【dtb_book_code_export_info(购书码导出记录)】的数据库操作Mapper
* @createDate 2025-02-24 10:46:45
* @Entity cn.dutp.edu.domain.DtbBookCodeExportInfo
*/
@Repository
public interface DtbBookCodeExportMapper extends BaseMapper<DtbBookCodeExport> {

    /**
     * 查询当前书籍下已兌換的购书码信息列表
     *
     * @param bookDto 信息
     * @return 已兌換的购书码信息列表
     */
    List<DtbBookPurchaseCodeVO> alreadyBoundCode(BookDto bookDto);

    /**
     * 查询登录人学校名下当前书籍下的未兌換的购书码
     *
     * @param bookDto 信息
     * @return 当前书籍下的未兌換的购书码
     */
    List<DtbBookPurchaseCodeVO> getUnboundCodeList(BookDto bookDto);

    /**
     * 获取持有当前书籍购书码的学院集合
     *
     * @param bookDto 信息
     * @return 学院集合
     */
    List<DtbBookSchoolVo> getExportCollegeList(BookDto bookDto);

    /**
     * 获取导出记录列表
     *
     * @param bookDto 信息
     * @return 导出记录列表
     */
    List<DtbBookCodeExportInfoVo> getExportRecordList(BookDto bookDto);

    /**
     * 获取要导出的购书码信息
     *
     * @param bookDto 信息
     * @return 要导出的购书码集合
     */
    List<DtbBookdtbPurchaseCodeUnboundExport> getUnboundExport(BookDto bookDto);

    /**
     * 获取要导出的已兌換购书码信息
     *
     * @param bookDto 信息
     * @return 已兌換购书码信息
     */
    List<DtbBookPurchaseCodeExport> alreadyBoundExport(BookDto bookDto);

}





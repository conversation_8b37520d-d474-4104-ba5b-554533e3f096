package cn.dutp.edu.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.edu.mapper.MoocSmartCourseChatMapper;
import cn.dutp.edu.domain.MoocSmartCourseChat;
import cn.dutp.edu.service.IMoocSmartCourseChatService;

/**
 * 互动课堂的问卷调查Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class MoocSmartCourseChatServiceImpl extends ServiceImpl<MoocSmartCourseChatMapper, MoocSmartCourseChat> implements IMoocSmartCourseChatService
{
    @Autowired
    private MoocSmartCourseChatMapper moocSmartCourseChatMapper;

    /**
     * 查询互动课堂的问卷调查
     *
     * @param chatId 互动课堂的问卷调查主键
     * @return 互动课堂的问卷调查
     */
    @Override
    public MoocSmartCourseChat selectMoocSmartCourseChatByChatId(Long chatId)
    {
        return this.getById(chatId);
    }

    /**
     * 查询互动课堂的问卷调查列表
     *
     * @param moocSmartCourseChat 互动课堂的问卷调查
     * @return 互动课堂的问卷调查
     */
    @Override
    public List<MoocSmartCourseChat> selectMoocSmartCourseChatList(MoocSmartCourseChat moocSmartCourseChat)
    {
        LambdaQueryWrapper<MoocSmartCourseChat> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(moocSmartCourseChat.getClassId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseChat::getClassId
                ,moocSmartCourseChat.getClassId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseChat.getCreatorId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseChat::getCreatorId
                ,moocSmartCourseChat.getCreatorId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseChat.getChatTitle())) {
                lambdaQueryWrapper.eq(MoocSmartCourseChat::getChatTitle
                ,moocSmartCourseChat.getChatTitle());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseChat.getChatTheme())) {
                lambdaQueryWrapper.eq(MoocSmartCourseChat::getChatTheme
                ,moocSmartCourseChat.getChatTheme());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseChat.getStartTime())) {
                lambdaQueryWrapper.eq(MoocSmartCourseChat::getStartTime
                ,moocSmartCourseChat.getStartTime());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseChat.getEndTime())) {
                lambdaQueryWrapper.eq(MoocSmartCourseChat::getEndTime
                ,moocSmartCourseChat.getEndTime());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增互动课堂的问卷调查
     *
     * @param moocSmartCourseChat 互动课堂的问卷调查
     * @return 结果
     */
    @Override
    public boolean insertMoocSmartCourseChat(MoocSmartCourseChat moocSmartCourseChat)
    {
        return this.save(moocSmartCourseChat);
    }

    /**
     * 修改互动课堂的问卷调查
     *
     * @param moocSmartCourseChat 互动课堂的问卷调查
     * @return 结果
     */
    @Override
    public boolean updateMoocSmartCourseChat(MoocSmartCourseChat moocSmartCourseChat)
    {
        return this.updateById(moocSmartCourseChat);
    }

    /**
     * 批量删除互动课堂的问卷调查
     *
     * @param chatIds 需要删除的互动课堂的问卷调查主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocSmartCourseChatByChatIds(List<Long> chatIds)
    {
        return this.removeByIds(chatIds);
    }

}

package cn.dutp.edu.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.edu.domain.MoocSmartCourseChatMember;
import cn.dutp.edu.service.IMoocSmartCourseChatMemberService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 互动课堂的课上互动讨论成员Controller
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@RestController
@RequestMapping("/chatMember")
public class MoocSmartCourseChatMemberController extends BaseController
{
    @Autowired
    private IMoocSmartCourseChatMemberService moocSmartCourseChatMemberService;

    /**
     * 查询互动课堂的课上互动讨论成员列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocSmartCourseChatMember moocSmartCourseChatMember)
    {
        startPage();
        List<MoocSmartCourseChatMember> list = moocSmartCourseChatMemberService.selectMoocSmartCourseChatMemberList(moocSmartCourseChatMember);
        return getDataTable(list);
    }

    /**
     * 导出互动课堂的课上互动讨论成员列表
     */
    @RequiresPermissions("edu:member:export")
    @Log(title = "导出互动课堂的课上互动讨论成员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocSmartCourseChatMember moocSmartCourseChatMember)
    {
        List<MoocSmartCourseChatMember> list = moocSmartCourseChatMemberService.selectMoocSmartCourseChatMemberList(moocSmartCourseChatMember);
        ExcelUtil<MoocSmartCourseChatMember> util = new ExcelUtil<MoocSmartCourseChatMember>(MoocSmartCourseChatMember.class);
        util.exportExcel(response, list, "互动课堂的课上互动讨论成员数据");
    }

    /**
     * 获取互动课堂的课上互动讨论成员详细信息
     */
    @RequiresPermissions("edu:member:query")
    @GetMapping(value = "/{memberId}")
    public AjaxResult getInfo(@PathVariable("memberId") Long memberId)
    {
        return success(moocSmartCourseChatMemberService.selectMoocSmartCourseChatMemberByMemberId(memberId));
    }

    /**
     * 新增互动课堂的课上互动讨论成员
     */
    @RequiresPermissions("edu:member:add")
    @Log(title = "新增互动课堂的课上互动讨论成员", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocSmartCourseChatMember moocSmartCourseChatMember)
    {
        return toAjax(moocSmartCourseChatMemberService.insertMoocSmartCourseChatMember(moocSmartCourseChatMember));
    }

    /**
     * 修改互动课堂的课上互动讨论成员
     */
    @RequiresPermissions("edu:member:edit")
    @Log(title = "修改互动课堂的课上互动讨论成员", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocSmartCourseChatMember moocSmartCourseChatMember)
    {
        return toAjax(moocSmartCourseChatMemberService.updateMoocSmartCourseChatMember(moocSmartCourseChatMember));
    }

    /**
     * 删除互动课堂的课上互动讨论成员
     */
    @RequiresPermissions("edu:member:remove")
    @Log(title = "删除互动课堂的课上互动讨论成员", businessType = BusinessType.DELETE)
    @DeleteMapping("/{memberIds}")
    public AjaxResult remove(@PathVariable Long[] memberIds)
    {
        return toAjax(moocSmartCourseChatMemberService.deleteMoocSmartCourseChatMemberByMemberIds(Arrays.asList(memberIds)));
    }
}

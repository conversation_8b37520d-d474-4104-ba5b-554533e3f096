package cn.dutp.edu.service;

import java.util.List;
import cn.dutp.edu.domain.MoocSmartCourseGroupMember;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 互动课堂的课上学习小组成员关系Service接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IMoocSmartCourseGroupMemberService extends IService<MoocSmartCourseGroupMember>
{
    /**
     * 查询互动课堂的课上学习小组成员关系
     *
     * @param studentId 互动课堂的课上学习小组成员关系主键
     * @return 互动课堂的课上学习小组成员关系
     */
    public MoocSmartCourseGroupMember selectMoocSmartCourseGroupMemberByStudentId(Long studentId);

    /**
     * 查询互动课堂的课上学习小组成员关系列表
     *
     * @param moocSmartCourseGroupMember 互动课堂的课上学习小组成员关系
     * @return 互动课堂的课上学习小组成员关系集合
     */
    public List<MoocSmartCourseGroupMember> selectMoocSmartCourseGroupMemberList(MoocSmartCourseGroupMember moocSmartCourseGroupMember);

    /**
     * 新增互动课堂的课上学习小组成员关系
     *
     * @param moocSmartCourseGroupMember 互动课堂的课上学习小组成员关系
     * @return 结果
     */
    public boolean insertMoocSmartCourseGroupMember(MoocSmartCourseGroupMember moocSmartCourseGroupMember);

    /**
     * 修改互动课堂的课上学习小组成员关系
     *
     * @param moocSmartCourseGroupMember 互动课堂的课上学习小组成员关系
     * @return 结果
     */
    public boolean updateMoocSmartCourseGroupMember(MoocSmartCourseGroupMember moocSmartCourseGroupMember);

    /**
     * 批量删除互动课堂的课上学习小组成员关系
     *
     * @param studentIds 需要删除的互动课堂的课上学习小组成员关系主键集合
     * @return 结果
     */
    public boolean deleteMoocSmartCourseGroupMemberByStudentIds(List<Long> studentIds);

}

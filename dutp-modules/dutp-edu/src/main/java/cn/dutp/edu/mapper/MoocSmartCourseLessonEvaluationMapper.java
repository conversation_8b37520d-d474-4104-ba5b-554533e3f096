package cn.dutp.edu.mapper;

import java.util.List;
import org.springframework.stereotype.Repository;
import cn.dutp.edu.domain.MoocSmartCourseLessonEvaluation;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * 课程评价Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Repository
public interface MoocSmartCourseLessonEvaluationMapper extends BaseMapper<MoocSmartCourseLessonEvaluation>
{

    List<MoocSmartCourseLessonEvaluation> getByClassId(MoocSmartCourseLessonEvaluation moocSmartCourseLessonEvaluation);
}

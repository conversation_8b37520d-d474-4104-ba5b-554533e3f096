<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.DutpShopSubjectMapper">
    
    <resultMap type="DutpSubject" id="DutpSubjectResult">
        <result property="subjectId"    column="subject_id"    />
        <result property="subjectName"    column="subject_name"    />
        <result property="sort"    column="sort"    />
        <result property="topParentId"    column="top_parent_id"    />
        <result property="secondParentId"    column="second_parent_id"    />
        <result property="thirdParentId"    column="third_parent_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDutpSubjectVo">
        select subject_id, subject_name, sort, top_parent_id, second_parent_id, third_parent_id, del_flag, create_by, create_time, update_by, update_time from dutp_subject
    </sql>

    <select id="selectDutpSubjectList" parameterType="DutpSubject" resultMap="DutpSubjectResult">
        <include refid="selectDutpSubjectVo"/>
        <where>  
            <if test="subjectName != null  and subjectName != ''"> and subject_name like concat('%', #{subjectName}, '%')</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="topParentId != null "> and top_parent_id = #{topParentId}</if>
            <if test="secondParentId != null "> and second_parent_id = #{secondParentId}</if>
            <if test="thirdParentId != null "> and third_parent_id = #{thirdParentId}</if>
        </where>
    </select>
    
    <select id="selectDutpSubjectBySubjectId" parameterType="Long" resultMap="DutpSubjectResult">
        <include refid="selectDutpSubjectVo"/>
        where subject_id = #{subjectId}
    </select>

    <insert id="insertDutpSubject" parameterType="DutpSubject" useGeneratedKeys="true" keyProperty="subjectId">
        insert into dutp_subject
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="subjectName != null">subject_name,</if>
            <if test="sort != null">sort,</if>
            <if test="topParentId != null">top_parent_id,</if>
            <if test="secondParentId != null">second_parent_id,</if>
            <if test="thirdParentId != null">third_parent_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="subjectName != null">#{subjectName},</if>
            <if test="sort != null">#{sort},</if>
            <if test="topParentId != null">#{topParentId},</if>
            <if test="secondParentId != null">#{secondParentId},</if>
            <if test="thirdParentId != null">#{thirdParentId},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDutpSubject" parameterType="DutpSubject">
        update dutp_subject
        <trim prefix="SET" suffixOverrides=",">
            <if test="subjectName != null">subject_name = #{subjectName},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="topParentId != null">top_parent_id = #{topParentId},</if>
            <if test="secondParentId != null">second_parent_id = #{secondParentId},</if>
            <if test="thirdParentId != null">third_parent_id = #{thirdParentId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where subject_id = #{subjectId}
    </update>

    <delete id="deleteDutpSubjectBySubjectId" parameterType="Long">
        delete from dutp_subject where subject_id = #{subjectId}
    </delete>

    <delete id="deleteDutpSubjectBySubjectIds" parameterType="String">
        delete from dutp_subject where subject_id in 
        <foreach item="subjectId" collection="array" open="(" separator="," close=")">
            #{subjectId}
        </foreach>
    </delete>
</mapper>
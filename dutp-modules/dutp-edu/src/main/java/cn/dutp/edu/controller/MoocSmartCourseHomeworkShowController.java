package cn.dutp.edu.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.edu.domain.MoocSmartCourseHomeworkShow;
import cn.dutp.edu.service.IMoocSmartCourseHomeworkShowService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 互动课堂的作业Controller
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@RestController
@RequestMapping("/show")
public class MoocSmartCourseHomeworkShowController extends BaseController
{
    @Autowired
    private IMoocSmartCourseHomeworkShowService moocSmartCourseHomeworkShowService;

    /**
     * 查询互动课堂的作业列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocSmartCourseHomeworkShow moocSmartCourseHomeworkShow)
    {
        startPage();
        List<MoocSmartCourseHomeworkShow> list = moocSmartCourseHomeworkShowService.selectMoocSmartCourseHomeworkShowList(moocSmartCourseHomeworkShow);
        return getDataTable(list);
    }

    /**
     * 导出互动课堂的作业列表
     */
    @RequiresPermissions("edu:show:export")
    @Log(title = "导出互动课堂的作业", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocSmartCourseHomeworkShow moocSmartCourseHomeworkShow)
    {
        List<MoocSmartCourseHomeworkShow> list = moocSmartCourseHomeworkShowService.selectMoocSmartCourseHomeworkShowList(moocSmartCourseHomeworkShow);
        ExcelUtil<MoocSmartCourseHomeworkShow> util = new ExcelUtil<MoocSmartCourseHomeworkShow>(MoocSmartCourseHomeworkShow.class);
        util.exportExcel(response, list, "互动课堂的作业数据");
    }

    /**
     * 获取互动课堂的作业详细信息
     */
    @RequiresPermissions("edu:show:query")
    @GetMapping(value = "/{homeworkShowId}")
    public AjaxResult getInfo(@PathVariable("homeworkShowId") Long homeworkShowId)
    {
        return success(moocSmartCourseHomeworkShowService.selectMoocSmartCourseHomeworkShowByHomeworkShowId(homeworkShowId));
    }

    /**
     * 新增互动课堂的作业
     */
    @RequiresPermissions("edu:show:add")
    @Log(title = "新增互动课堂的作业", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocSmartCourseHomeworkShow moocSmartCourseHomeworkShow)
    {
        return toAjax(moocSmartCourseHomeworkShowService.insertMoocSmartCourseHomeworkShow(moocSmartCourseHomeworkShow));
    }

    /**
     * 修改互动课堂的作业
     */
    @RequiresPermissions("edu:show:edit")
    @Log(title = "修改互动课堂的作业", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocSmartCourseHomeworkShow moocSmartCourseHomeworkShow)
    {
        return toAjax(moocSmartCourseHomeworkShowService.updateMoocSmartCourseHomeworkShow(moocSmartCourseHomeworkShow));
    }

    /**
     * 删除互动课堂的作业
     */
    @RequiresPermissions("edu:show:remove")
    @Log(title = "删除互动课堂的作业", businessType = BusinessType.DELETE)
    @DeleteMapping("/{homeworkShowIds}")
    public AjaxResult remove(@PathVariable Long[] homeworkShowIds)
    {
        return toAjax(moocSmartCourseHomeworkShowService.deleteMoocSmartCourseHomeworkShowByHomeworkShowIds(Arrays.asList(homeworkShowIds)));
    }
}

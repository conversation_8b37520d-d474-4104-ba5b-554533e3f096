package cn.dutp.edu.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 教师认证对象 edu_teacher_authentication
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@Data
@TableName("edu_teacher_authentication")
public class EduTeacherAuthentication extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long authId;

    /**
     * 用户id
     */
    @Excel(name = "用户id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 用户账号
     */
    @Excel(name = "用户账号")
    private String userName;

    /**
     * 真实姓名
     */
    @Excel(name = "真实姓名")
    private String realName;

    /**
     * 用户昵称
     */
    @Excel(name = "用户昵称")
    private String nickName;

    /**
     * 工号
     */
    @Excel(name = "工号")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long workNo;

    /**
     * 电话号
     */
    @Excel(name = "电话号")
    private Long phone;

    /**
     * 邮箱
     */
    @Excel(name = "邮箱")
    private String email;

    /**
     * 教师认证学校的学校表id
     */
    @Excel(name = "教师认证学校的学校表id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolId;

    /**
     * 院系ID
     */
    @Excel(name = "院系ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long academyId;

    /**
     * 专业表id
     */
    @Excel(name = "专业表id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long specialityId;

    /**
     * 0未审核1通过2拒绝
     */
    @Excel(name = "0未审核1通过2拒绝")
    private Integer authState;

    /**
     * 职位
     */
    @Excel(name = "职位")
    private String positionName;

    /**
     * 职称
     */
    @Excel(name = "职称")
    private String titleName;

    /**
     * 职务
     */
    @Excel(name = "职务")
    private String dutyName;

    /**
     * 课程信息
     */
    @Excel(name = "课程信息")
    private String courseName;

    /**
     * 学生人数
     */
    @Excel(name = "学生人数")
    private Integer classmateTotal;

    /**
     * 学期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "学期开始", width = 30, dateFormat = "yyyy-MM-dd")
    private Date termBegin;

    /**
     * 学期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "学期结束", width = 30, dateFormat = "yyyy-MM-dd")
    private Date termEnd;

    /**
     * 选书依据
     */
    @Excel(name = "选书依据")
    private String bookPreference;

    /**
     * 上传的认证图片
     */
    @Excel(name = "上传的认证图片")
    private String authenticationImage;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 学校名
     */
    @Excel(name = "学校名")
    @TableField(exist = false)
    private String schoolName;

    /**
     * 专业
     */
    @Excel(name = "专业名名")
    @TableField(exist = false)
    private String specialityName;

    /**
     * 院系名称
     */
    @TableField(exist = false)
    private String collegeName;


    @Excel(name = "拒绝原因")
    private String rejectReason;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("authId", getAuthId())
                .append("userId", getUserId())
                .append("userName", getUserName())
                .append("realName", getRealName())
                .append("nickName", getNickName())
                .append("workNo", getWorkNo())
                .append("phone", getPhone())
                .append("email", getEmail())
                .append("schoolId", getSchoolId())
                .append("academyId", getAcademyId())
                .append("specialityId", getSpecialityId())
                .append("authState", getAuthState())
                .append("positionName", getPositionName())
                .append("titleName", getTitleName())
                .append("dutyName", getDutyName())
                .append("courseName", getCourseName())
                .append("classmateTotal", getClassmateTotal())
                .append("termBegin", getTermBegin())
                .append("termEnd", getTermEnd())
                .append("bookPreference", getBookPreference())
                .append("authenticationImage", getAuthenticationImage())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("schoolName", getSchoolName())
                .append("specialityName", getSpecialityName())
                .toString();
    }
}

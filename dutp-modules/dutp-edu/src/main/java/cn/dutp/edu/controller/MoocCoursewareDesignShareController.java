package cn.dutp.edu.controller;

import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.domain.MoocCoursewareDesignShare;
import cn.dutp.edu.service.IMoocCoursewareDesignShareService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 【请填写功能名称】Controller
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@RestController
@RequestMapping("/coursewareShare")
public class MoocCoursewareDesignShareController extends BaseController {
    @Autowired
    private IMoocCoursewareDesignShareService moocCoursewareDesignShareService;

    /**
     * 查询【请填写功能名称】列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocCoursewareDesignShare moocCoursewareDesignShare) {
        startPage();
        List<MoocCoursewareDesignShare> list = moocCoursewareDesignShareService.selectMoocCoursewareDesignShareList(moocCoursewareDesignShare);
        return getDataTable(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @Log(title = "导出【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocCoursewareDesignShare moocCoursewareDesignShare) {
        List<MoocCoursewareDesignShare> list = moocCoursewareDesignShareService.selectMoocCoursewareDesignShareList(moocCoursewareDesignShare);
        ExcelUtil<MoocCoursewareDesignShare> util = new ExcelUtil<MoocCoursewareDesignShare>(MoocCoursewareDesignShare.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @GetMapping(value = "/{shareId}")
    public AjaxResult getInfo(@PathVariable("shareId") Long shareId) {
        return success(moocCoursewareDesignShareService.selectMoocCoursewareDesignShareByShareId(shareId));
    }

    /**
     * 新增【请填写功能名称】
     */
    @Log(title = "新增【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocCoursewareDesignShare moocCoursewareDesignShare) {
        return toAjax(moocCoursewareDesignShareService.insertMoocCoursewareDesignShare(moocCoursewareDesignShare));
    }

    /**
     * 修改【请填写功能名称】
     */
    @Log(title = "修改【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocCoursewareDesignShare moocCoursewareDesignShare) {
        return toAjax(moocCoursewareDesignShareService.updateMoocCoursewareDesignShare(moocCoursewareDesignShare));
    }

    /**
     * 删除【请填写功能名称】
     */
    @Log(title = "删除【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{shareIds}")
    public AjaxResult remove(@PathVariable Long[] shareIds) {
        return toAjax(moocCoursewareDesignShareService.deleteMoocCoursewareDesignShareByShareIds(Arrays.asList(shareIds)));
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocSmartCourseClassMapper">

    <resultMap id="lessionResult" type="cn.dutp.edu.domain.MoocSmartCourseClass">
        <result property="classId"    column="class_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="className"    column="class_name"    />
        <result property="classCode"    column="class_code"    />
        <result property="classType"    column="class_type"    />
        <result property="userId"    column="user_id"    />
        <result property="bookId"    column="book_id"    />
        <result property="description"    column="description"    />
        <result property="coverImageUrl"    column="cover_image_url"    />
        <result property="status"    column="status"    />
        <result property="eduClassId"    column="edu_class_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="schoolName"    column="schoolName"    />
        <result property="realName"    column="realName"    />
        <result property="bookName"    column="bookName"    />
        <result property="studentCount"    column="studentCount"    />
        <result property="hour"    column="hour"    />
        <result property="useHour"    column="useHour"    />
        <result property="academyId"    column="academyId"    />
        <result property="schoolId"    column="schoolId"    />
        <result property="courseName"    column="courseName"    />
        <result property="courseCode"    column="courseCode"    />
        <result property="assistantName"    column="assistantName"    />
        <collection property="lessonList" ofType="cn.dutp.edu.domain.MoocSmartCourseLesson">
            <result property="lessonId"    column="lesson_id"    />
            <result property="classId"    column="class_id"    />
            <result property="teacherId"    column="teacher_id"    />
            <result property="title"    column="title"    />
            <result property="scheduledStartDatetime"    column="scheduled_start_datetime"    />
            <result property="scheduledEndDatetime"    column="scheduled_end_datetime"    />
            <result property="startDatetime"    column="start_datetime"    />
            <result property="endDatetime"    column="end_datetime"    />
            <result property="teacherCheckDatetime"    column="teacher_check_datetime"    />
            <result property="status"    column="status"    />
            <result property="createdBy"    column="created_by"    />
            <result property="updatedBy"    column="updated_by"    />
            <result property="delFlag"    column="del_flag"    />
            <result property="createTime"    column="create_time"    />
            <result property="updateTime"    column="update_time"    />
        </collection>
    </resultMap>
    
    <resultMap type="MoocSmartCourseClass" id="MoocSmartCourseClassResult">
        <result property="classId"    column="class_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="className"    column="class_name"    />
        <result property="classCode"    column="class_code"    />
        <result property="classType"    column="class_type"    />
        <result property="userId"    column="user_id"    />
        <result property="bookId"    column="book_id"    />
        <result property="description"    column="description"    />
        <result property="coverImageUrl"    column="cover_image_url"    />
        <result property="status"    column="status"    />
        <result property="eduClassId"    column="edu_class_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMoocSmartCourseClassVo">
        select class_id, course_id, class_name, class_code, class_type, user_id, book_id, description, cover_image_url, status, edu_class_id, create_by, update_by, del_flag, create_time, update_time from mooc_smart_course_class
    </sql>

    <select id="selectMoocSmartCourseClassList" parameterType="MoocSmartCourseClass" resultMap="MoocSmartCourseClassResult">
        <include refid="selectMoocSmartCourseClassVo"/>
        <where>  
            <if test="courseId != null "> and course_id = #{courseId}</if>
            <if test="className != null  and className != ''"> and class_name like concat('%', #{className}, '%')</if>
            <if test="classCode != null  and classCode != ''"> and class_code = #{classCode}</if>
            <if test="classType != null "> and class_type = #{classType}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="bookId != null "> and book_id = #{bookId}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="coverImageUrl != null  and coverImageUrl != ''"> and cover_image_url = #{coverImageUrl}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="eduClassId != null  and eduClassId != ''"> and edu_class_id = #{eduClassId}</if>
            <if test="createBy != null "> and create_by = #{createBy}</if>
            <if test="updateBy != null "> and update_by = #{updateBy}</if>
        </where>
    </select>
    
    <select id="selectMoocSmartCourseClassByClassId" parameterType="Long" resultMap="MoocSmartCourseClassResult">
        <include refid="selectMoocSmartCourseClassVo"/>
        where class_id = #{classId}
    </select>

    <insert id="insertMoocSmartCourseClass" parameterType="MoocSmartCourseClass" useGeneratedKeys="true" keyProperty="classId">
        insert into mooc_smart_course_class
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="courseId != null">course_id,</if>
            <if test="className != null and className != ''">class_name,</if>
            <if test="classCode != null and classCode != ''">class_code,</if>
            <if test="classType != null">class_type,</if>
            <if test="userId != null">user_id,</if>
            <if test="bookId != null">book_id,</if>
            <if test="description != null">description,</if>
            <if test="coverImageUrl != null">cover_image_url,</if>
            <if test="status != null">status,</if>
            <if test="eduClassId != null">edu_class_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="courseId != null">#{courseId},</if>
            <if test="className != null and className != ''">#{className},</if>
            <if test="classCode != null and classCode != ''">#{classCode},</if>
            <if test="classType != null">#{classType},</if>
            <if test="userId != null">#{userId},</if>
            <if test="bookId != null">#{bookId},</if>
            <if test="description != null">#{description},</if>
            <if test="coverImageUrl != null">#{coverImageUrl},</if>
            <if test="status != null">#{status},</if>
            <if test="eduClassId != null">#{eduClassId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMoocSmartCourseClass" parameterType="MoocSmartCourseClass">
        update mooc_smart_course_class
        <trim prefix="SET" suffixOverrides=",">
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="className != null and className != ''">class_name = #{className},</if>
            <if test="classCode != null and classCode != ''">class_code = #{classCode},</if>
            <if test="classType != null">class_type = #{classType},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="description != null">description = #{description},</if>
            <if test="coverImageUrl != null">cover_image_url = #{coverImageUrl},</if>
            <if test="status != null">status = #{status},</if>
            <if test="eduClassId != null">edu_class_id = #{eduClassId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where class_id = #{classId}
    </update>

    <delete id="deleteMoocSmartCourseClassByClassId" parameterType="Long">
        delete from mooc_smart_course_class where class_id = #{classId}
    </delete>

    <delete id="deleteMoocSmartCourseClassByClassIds" parameterType="String">
        delete from mooc_smart_course_class where class_id in 
        <foreach item="classId" collection="array" open="(" separator="," close=")">
            #{classId}
        </foreach>
    </delete>
    <select id="getInfo" parameterType="cn.dutp.edu.domain.MoocSmartCourseClass" resultType="cn.dutp.edu.domain.MoocSmartCourseClass">
        SELECT
        C.class_id,
        K.course_type,
        K.course_name,
        K.description,
        C.class_name,
        C.class_type,
        C.STATUS,
        u.real_name
        FROM
        mooc_smart_course_class c
        LEFT JOIN mooc_smart_course k ON k.course_id = c.course_id
        AND k.del_flag = 0
        LEFT JOIN dutp_user u ON c.user_id = u.user_id
        AND u.del_flag = 0
        WHERE
        c.del_flag = 0
        <if test="courseName != null and courseName != '' || realName != null and realName != ''">
            AND (
            k.course_name LIKE concat('%', #{courseName}, '%')
            OR u.real_name LIKE concat('%', #{realName}, '%')
            )
        </if>
        <if test="classCode != null">
            AND c.class_code = #{classCode}
        </if>
    </select>

    <select id="getListBySchool" parameterType="cn.dutp.edu.domain.MoocSmartCourseClass" resultType="cn.dutp.edu.domain.MoocSmartCourseClass">
        select
            c.class_id,
            c.course_id,
            c.class_name,
            c.class_code,
            c.class_type,
            c.user_id,
            c.book_id,
            c.description,
            c.create_time,
            c.cover_image_url,
            (
                select GROUP_CONCAT(real_name SEPARATOR ', ')
                from dutp_user de left join mooc_smart_course_class_member ccm
                on de.user_id = ccm.user_id and ccm.del_flag = 0
                where ccm.role = 2 and ccm.class_id = c.class_id
            ) as assistantName,
            c.status,
            c.edu_class_id,
            k.course_name,
            k.course_type,
            k.course_code,
            (select count(0) from mooc_smart_course_class_member where del_flag = 0 and class_id = c.class_id) as studentCount,
            k.hour,
            (SUM(TIMESTAMPDIFF(HOUR, cl.start_datetime, cl.end_datetime))) as useHour,
            dtb.book_name,
            u.real_name,
            ds.school_name
        from
            mooc_smart_course_class c
        left join
            mooc_smart_course k on k.course_id = c.course_id and k.del_flag = 0
        left join
            dutp_user u ON c.user_id = u.user_id AND u.del_flag = 0
        left join
            dtb_book dtb on dtb.book_id = c.book_id and dtb.del_flag = 0
        left join
            dutp_school ds on ds.school_id = u.academy_id and ds.del_flag = 0
        left join
            mooc_smart_course_lesson cl on cl.class_id = c.class_id and cl.del_flag = 0
        <where>
            c.del_flag = 0 AND ds.parent_id = #{schoolId}
            <if test="courseType != null">
                and k.course_type = #{courseType}
            </if>
            <if test="courseName!= null and courseName!= ''">
                AND k.course_name LIKE concat('%', #{courseName}, '%')
            </if>
            <if test="realName!= null and realName!= ''">
                AND u.real_name LIKE concat('%', #{realName}, '%')
            </if>
            <if test="className!= null and className!= ''">
                AND c.class_name LIKE concat('%', #{className}, '%')
            </if>
            <if test="academyId != null">
                AND u.academy_id = #{academyId}
            </if>
            <if test="status != null">
                AND c.status = #{status}
            </if>
            <if test="classType != null">
                AND c.class_type = #{classType}
            </if>
        </where>
        GROUP BY
            c.class_id, c.course_id, c.class_name, c.class_code, c.class_type,
            c.user_id, c.book_id, c.description, c.cover_image_url, c.status,
            c.edu_class_id, k.course_name, k.course_type, k.course_code, k.hour,
            dtb.book_name, u.real_name
        order by c.create_time desc
    </select>

    <select id="getInfoByClassId" resultMap="lessionResult">
        select
            c.class_id,
            c.course_id,
            c.class_name,
            c.class_code,
            c.class_type,
            k.course_name as courseName,
            ds.school_name as schoolName,
            dtb.book_name as bookName,
            k.hour,
            u.real_name as realName,
            (
                select GROUP_CONCAT(real_name SEPARATOR ', ')
                from dutp_user de left join mooc_smart_course_class_member ccm
                on de.user_id = ccm.user_id and ccm.del_flag = 0
                where ccm.role = 2 and ccm.class_id = c.class_id
            ) as assistantName,
            cl.lesson_id,
            cl.title,
            cl.start_datetime,
            (select count(0) from mooc_smart_course_class_member where del_flag = 0 and class_id = c.class_id) as studentCount,
            cl.end_datetime
        from
            mooc_smart_course_class c
        left join
            mooc_smart_course k on k.course_id = c.course_id and k.del_flag = 0
        left join
            dutp_user u ON c.user_id = u.user_id AND u.del_flag = 0
        left join
            dtb_book dtb on dtb.book_id = c.book_id and dtb.del_flag = 0
        left join
            dutp_school ds on ds.school_id = u.academy_id and ds.del_flag = 0
        left join
            mooc_smart_course_lesson cl on cl.class_id = c.class_id and cl.del_flag = 0
        left join
            mooc_smart_course_class_member cm on cm.class_id = c.class_id and cm.del_flag = 0
        <where>
            c.del_flag = 0 and c.class_id = #{classId}
            and YEAR(cl.start_datetime) = #{year}
            and MONTH(cl.start_datetime) = #{month}
        </where>
    </select>
</mapper>
package cn.dutp.edu.service;

import cn.dutp.domain.MoocCoursewareDesignShare;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
public interface IMoocCoursewareDesignShareService extends IService<MoocCoursewareDesignShare> {
    /**
     * 查询【请填写功能名称】
     *
     * @param shareId 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public MoocCoursewareDesignShare selectMoocCoursewareDesignShareByShareId(Long shareId);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param moocCoursewareDesignShare 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<MoocCoursewareDesignShare> selectMoocCoursewareDesignShareList(MoocCoursewareDesignShare moocCoursewareDesignShare);

    /**
     * 新增【请填写功能名称】
     *
     * @param moocCoursewareDesignShare 【请填写功能名称】
     * @return 结果
     */
    public boolean insertMoocCoursewareDesignShare(MoocCoursewareDesignShare moocCoursewareDesignShare);

    /**
     * 修改【请填写功能名称】
     *
     * @param moocCoursewareDesignShare 【请填写功能名称】
     * @return 结果
     */
    public boolean updateMoocCoursewareDesignShare(MoocCoursewareDesignShare moocCoursewareDesignShare);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param shareIds 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public boolean deleteMoocCoursewareDesignShareByShareIds(List<Long> shareIds);

}

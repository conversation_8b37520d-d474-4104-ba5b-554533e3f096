package cn.dutp.edu.service.impl;

import cn.dutp.domain.MoocCoursewareDesignShare;
import cn.dutp.edu.mapper.MoocCoursewareDesignShareMapper;
import cn.dutp.edu.service.IMoocCoursewareDesignShareService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Service
public class MoocCoursewareDesignShareServiceImpl extends ServiceImpl<MoocCoursewareDesignShareMapper, MoocCoursewareDesignShare> implements IMoocCoursewareDesignShareService {
    @Autowired
    private MoocCoursewareDesignShareMapper moocCoursewareDesignShareMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param shareId 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public MoocCoursewareDesignShare selectMoocCoursewareDesignShareByShareId(Long shareId) {
        return this.getById(shareId);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param moocCoursewareDesignShare 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<MoocCoursewareDesignShare> selectMoocCoursewareDesignShareList(MoocCoursewareDesignShare moocCoursewareDesignShare) {
        LambdaQueryWrapper<MoocCoursewareDesignShare> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotEmpty(moocCoursewareDesignShare.getToUserId())) {
            lambdaQueryWrapper.eq(MoocCoursewareDesignShare::getToUserId
                    , moocCoursewareDesignShare.getToUserId());
        }
        if (ObjectUtil.isNotEmpty(moocCoursewareDesignShare.getFromUserId())) {
            lambdaQueryWrapper.eq(MoocCoursewareDesignShare::getFromUserId
                    , moocCoursewareDesignShare.getFromUserId());
        }
        if (ObjectUtil.isNotEmpty(moocCoursewareDesignShare.getCoursewareDesignId())) {
            lambdaQueryWrapper.eq(MoocCoursewareDesignShare::getCoursewareDesignId
                    , moocCoursewareDesignShare.getCoursewareDesignId());
        }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param moocCoursewareDesignShare 【请填写功能名称】
     * @return 结果
     */
    @Override
    public boolean insertMoocCoursewareDesignShare(MoocCoursewareDesignShare moocCoursewareDesignShare) {
        return this.save(moocCoursewareDesignShare);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param moocCoursewareDesignShare 【请填写功能名称】
     * @return 结果
     */
    @Override
    public boolean updateMoocCoursewareDesignShare(MoocCoursewareDesignShare moocCoursewareDesignShare) {
        return this.updateById(moocCoursewareDesignShare);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param shareIds 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocCoursewareDesignShareByShareIds(List<Long> shareIds) {
        return this.removeByIds(shareIds);
    }

}

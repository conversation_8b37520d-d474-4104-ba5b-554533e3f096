package cn.dutp.edu.service;

import java.util.List;
import cn.dutp.edu.domain.MoocSmartCourseStudentCheckInLog;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 学生出勤日志Service接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IMoocSmartCourseStudentCheckInLogService extends IService<MoocSmartCourseStudentCheckInLog>
{
    /**
     * 查询学生出勤日志
     *
     * @param logId 学生出勤日志主键
     * @return 学生出勤日志
     */
    public MoocSmartCourseStudentCheckInLog selectMoocSmartCourseStudentCheckInLogByLogId(Long logId);

    /**
     * 查询学生出勤日志列表
     *
     * @param moocSmartCourseStudentCheckInLog 学生出勤日志
     * @return 学生出勤日志集合
     */
    public List<MoocSmartCourseStudentCheckInLog> selectMoocSmartCourseStudentCheckInLogList(MoocSmartCourseStudentCheckInLog moocSmartCourseStudentCheckInLog);

    /**
     * 新增学生出勤日志
     *
     * @param moocSmartCourseStudentCheckInLog 学生出勤日志
     * @return 结果
     */
    public boolean insertMoocSmartCourseStudentCheckInLog(MoocSmartCourseStudentCheckInLog moocSmartCourseStudentCheckInLog);

    /**
     * 修改学生出勤日志
     *
     * @param moocSmartCourseStudentCheckInLog 学生出勤日志
     * @return 结果
     */
    public boolean updateMoocSmartCourseStudentCheckInLog(MoocSmartCourseStudentCheckInLog moocSmartCourseStudentCheckInLog);

    /**
     * 批量删除学生出勤日志
     *
     * @param logIds 需要删除的学生出勤日志主键集合
     * @return 结果
     */
    public boolean deleteMoocSmartCourseStudentCheckInLogByLogIds(List<Long> logIds);

}

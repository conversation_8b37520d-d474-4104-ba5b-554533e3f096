package cn.dutp.edu.service.impl;

import java.util.List;

import cn.dutp.edu.domain.MoocSmartCourseBrainstormReply;
import cn.dutp.edu.mapper.MoocSmartCourseBrainstormReplyMapper;
import cn.dutp.edu.service.IMoocSmartCourseBrainstormReplyService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.edu.mapper.MoocSmartCourseBrainstormMapper;
import cn.dutp.edu.domain.MoocSmartCourseBrainstorm;
import cn.dutp.edu.service.IMoocSmartCourseBrainstormService;

/**
 * 互动课堂的头脑风暴Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class MoocSmartCourseBrainstormServiceImpl extends ServiceImpl<MoocSmartCourseBrainstormMapper, MoocSmartCourseBrainstorm> implements IMoocSmartCourseBrainstormService
{
    @Autowired
    private MoocSmartCourseBrainstormMapper moocSmartCourseBrainstormMapper;

    @Autowired
    private MoocSmartCourseBrainstormReplyMapper replyMapper;

    /**
     * 查询互动课堂的头脑风暴
     *
     * @param brainstormId 互动课堂的头脑风暴主键
     * @return 互动课堂的头脑风暴
     */
    @Override
    public MoocSmartCourseBrainstorm selectMoocSmartCourseBrainstormByBrainstormId(Long brainstormId)
    {
        MoocSmartCourseBrainstorm res = this.getById(brainstormId);

        // 查询附件
        res.setFileList(baseMapper.getDutpUserCommonFile(brainstormId));

        // 查询学生回答
        List<MoocSmartCourseBrainstormReply> replyList = replyMapper.selectReplyList(brainstormId);
        res.setReplyList(replyList);
        return res;
    }

    /**
     * 查询互动课堂的头脑风暴列表
     *
     * @param moocSmartCourseBrainstorm 互动课堂的头脑风暴
     * @return 互动课堂的头脑风暴
     */
    @Override
    public List<MoocSmartCourseBrainstorm> selectMoocSmartCourseBrainstormList(MoocSmartCourseBrainstorm moocSmartCourseBrainstorm)
    {
        LambdaQueryWrapper<MoocSmartCourseBrainstorm> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(moocSmartCourseBrainstorm.getCourseId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseBrainstorm::getCourseId
                ,moocSmartCourseBrainstorm.getCourseId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseBrainstorm.getBrainstormTitle())) {
                lambdaQueryWrapper.eq(MoocSmartCourseBrainstorm::getBrainstormTitle
                ,moocSmartCourseBrainstorm.getBrainstormTitle());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseBrainstorm.getBrainstormContent())) {
                lambdaQueryWrapper.eq(MoocSmartCourseBrainstorm::getBrainstormContent
                ,moocSmartCourseBrainstorm.getBrainstormContent());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseBrainstorm.getBrainstormCreatorId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseBrainstorm::getBrainstormCreatorId
                ,moocSmartCourseBrainstorm.getBrainstormCreatorId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseBrainstorm.getBrainstormRemark())) {
                lambdaQueryWrapper.eq(MoocSmartCourseBrainstorm::getBrainstormRemark
                ,moocSmartCourseBrainstorm.getBrainstormRemark());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增互动课堂的头脑风暴
     *
     * @param moocSmartCourseBrainstorm 互动课堂的头脑风暴
     * @return 结果
     */
    @Override
    public boolean insertMoocSmartCourseBrainstorm(MoocSmartCourseBrainstorm moocSmartCourseBrainstorm)
    {
        return this.save(moocSmartCourseBrainstorm);
    }

    /**
     * 修改互动课堂的头脑风暴
     *
     * @param moocSmartCourseBrainstorm 互动课堂的头脑风暴
     * @return 结果
     */
    @Override
    public boolean updateMoocSmartCourseBrainstorm(MoocSmartCourseBrainstorm moocSmartCourseBrainstorm)
    {
        return this.updateById(moocSmartCourseBrainstorm);
    }

    /**
     * 批量删除互动课堂的头脑风暴
     *
     * @param brainstormIds 需要删除的互动课堂的头脑风暴主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocSmartCourseBrainstormByBrainstormIds(List<Long> brainstormIds)
    {
        return this.removeByIds(brainstormIds);
    }

}

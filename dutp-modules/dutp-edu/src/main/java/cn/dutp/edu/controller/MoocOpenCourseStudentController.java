package cn.dutp.edu.controller;

import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.domain.MoocOpenCourseStudent;
import cn.dutp.edu.service.IMoocOpenCourseStudentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 公开课加入的学员Controller
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@RestController
@RequestMapping("/openCourseStudent")
public class MoocOpenCourseStudentController extends BaseController {
    @Autowired
    private IMoocOpenCourseStudentService moocOpenCourseStudentService;

    /**
     * 查询公开课加入的学员列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocOpenCourseStudent moocOpenCourseStudent) {
        startPage();
        List<MoocOpenCourseStudent> list = moocOpenCourseStudentService.selectMoocOpenCourseStudentList(moocOpenCourseStudent);
        return getDataTable(list);
    }

    /**
     * 导出公开课加入的学员列表
     */
    @Log(title = "导出公开课加入的学员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocOpenCourseStudent moocOpenCourseStudent) {
        List<MoocOpenCourseStudent> list = moocOpenCourseStudentService.selectMoocOpenCourseStudentList(moocOpenCourseStudent);
        ExcelUtil<MoocOpenCourseStudent> util = new ExcelUtil<MoocOpenCourseStudent>(MoocOpenCourseStudent.class);
        util.exportExcel(response, list, "公开课加入的学员数据");
    }

    /**
     * 获取公开课加入的学员详细信息
     */
    @GetMapping(value = "/{studentId}")
    public AjaxResult getInfo(@PathVariable("studentId") Long studentId) {
        return success(moocOpenCourseStudentService.selectMoocOpenCourseStudentByStudentId(studentId));
    }

    /**
     * 新增公开课加入的学员
     */
    @Log(title = "新增公开课加入的学员", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocOpenCourseStudent moocOpenCourseStudent) {
        return toAjax(moocOpenCourseStudentService.insertMoocOpenCourseStudent(moocOpenCourseStudent));
    }

    /**
     * 修改公开课加入的学员
     */
    @Log(title = "修改公开课加入的学员", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocOpenCourseStudent moocOpenCourseStudent) {
        return toAjax(moocOpenCourseStudentService.updateMoocOpenCourseStudent(moocOpenCourseStudent));
    }

    /**
     * 删除公开课加入的学员
     */
    @Log(title = "删除公开课加入的学员", businessType = BusinessType.DELETE)
    @DeleteMapping("/{studentIds}")
    public AjaxResult remove(@PathVariable Long[] studentIds) {
        return toAjax(moocOpenCourseStudentService.deleteMoocOpenCourseStudentByStudentIds(Arrays.asList(studentIds)));
    }

    /**
     * 学生端退出公开课
     */
    @Log(title = "学生端退出公开课", businessType = BusinessType.UPDATE)
    @PutMapping("/exitTheCourse")
    public AjaxResult exitTheCourse(@RequestBody MoocOpenCourseStudent moocOpenCourseStudent) {
        return toAjax(moocOpenCourseStudentService.exitTheCourse(moocOpenCourseStudent));
    }
}

package cn.dutp.edu.controller;

import cn.dutp.api.common.constant.DutpConstant;
import cn.dutp.common.core.domain.R;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.common.sms.utils.AliyunSmsUtil;
import cn.dutp.edu.domain.dto.DutpUserDto;
import cn.dutp.edu.domian.DutpUser;
import cn.dutp.edu.service.DutpUserService;
import cn.hutool.core.util.ObjectUtil;
import org.springdoc.core.ReturnTypeParser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @author: dutp
 * @date: 2024/11/5 10:24
 */
@RestController
@RequestMapping("/userInfo")
public class DutpUserController extends BaseController {

    @Autowired
    private DutpUserService dutpUserService;
    @Autowired
    private ReturnTypeParser genericReturnTypeParser;

    /**
     * 查询全部列表
     * @param dutpUser 用户信息
     * @return 结果
     */
    @RequiresPermissions("edu:user:list")
    @GetMapping("/allList")
    public TableDataInfo allList(DutpUser dutpUser){
        startPage();
        List<DutpUser> allList = dutpUserService.selectAll(dutpUser);
        return getDataTable(allList);
    }

    /**
     * 查询学生列表
     * @param dutpUser 用户信息
     * @return 结果
     */
    @RequiresPermissions("edu:user:studentList")
    @GetMapping("/studentList")
    public TableDataInfo studentList(DutpUser dutpUser){
        startPage();
        List<DutpUser> studentList = dutpUserService.selectStudent(dutpUser);
        return getDataTable(studentList);
    }

    /**
     * 查询教师列表
     * @param dutpUser 用户信息
     * @return 结果
     */
    @RequiresPermissions("edu:user:teacherList")
    @GetMapping("/teacherList")
    public TableDataInfo teacherList(DutpUser dutpUser){
        startPage();
        List<DutpUser> teacherList = dutpUserService.selectTeacher(dutpUser);
        return getDataTable(teacherList);
    }


    /**
     * 获取用户信息详情
     * @return 结果
     */
    @RequiresPermissions("edu:user:query")
    @GetMapping(value = "/getInfo/{userId}")
    public AjaxResult getInfo(){
        return success(dutpUserService.getUserById());
    }

    /**
     * 后台获取用户详情
     * @return 结果
     */
    @RequiresPermissions("edu:user:query")
    @GetMapping(value = "/getDetails/{userId}")
    public AjaxResult getDetail(DutpUser dutpUser){
        return success(dutpUserService.getDetail(dutpUser));
    }

    /**
     * 学生教师端获取用户信息详情
     * @return 结果
     */
    @GetMapping(value = "/getInfoEducation")
    public AjaxResult getInfoEducation(){
        return success(dutpUserService.getUserById());
    }

    /**
     * 添加学生
     * @param dutpUser 用户信息
     * @return 结果
     */
    @RequiresPermissions("edu:user:add")
    @Log(title = "添加学生", businessType = BusinessType.INSERT)
    @PostMapping("/addStudent")
    public AjaxResult addStudent(@RequestBody DutpUser dutpUser){
        dutpUser.setPassword(SecurityUtils.encryptPassword(dutpUser.getPassword()));
        return toAjax(dutpUserService.insertStudent(dutpUser));
    }

    /**
     * 添加教师
     * @param dutpUser 用户信息
     * @return 结果
     */
    @RequiresPermissions("edu:user:add")
    @Log(title = "添加教师", businessType = BusinessType.INSERT)
    @PostMapping("/addTeacher")
    public AjaxResult addTeacher(@RequestBody DutpUser dutpUser){
        dutpUser.setPassword(SecurityUtils.encryptPassword(dutpUser.getPassword()));
        return toAjax(dutpUserService.insertTeacher(dutpUser));
    }

    /**
     * 添加读者
     * @param dutpUser 用户信息
     * @return 结果
     */
    @RequiresPermissions("edu:user:add")
    @Log(title = "添加读者", businessType = BusinessType.INSERT)
    @PostMapping("/addUser")
    public AjaxResult addUser(@RequestBody DutpUser dutpUser){
        dutpUser.setPassword(SecurityUtils.encryptPassword(dutpUser.getPassword()));
        return toAjax(dutpUserService.insertUser(dutpUser));
    }

    /**
     * 学生教师端修改用户信息
     * @param dutpUser 用户信息
     * @return 结果
     */
    @PutMapping("/editUserEducation")
    public AjaxResult editUserEducation(@RequestBody DutpUser dutpUser){
        if(ObjectUtil.isNull(dutpUser.getAcademyId())){
            dutpUser.setAcademyId(null);
        }
        if(ObjectUtil.isNull(dutpUser.getSpecialityId())){
            dutpUser.setSpecialityId(null);
        }
        return toAjax(dutpUserService.updateById(dutpUser));
    }

    /**
     * 修改用户信息
     * @param dutpUser 用户信息
     * @return 结果
     */
    @RequiresPermissions("edu:user:edit")
    @Log(title = "修改用户信息", businessType = BusinessType.UPDATE)
    @PutMapping("/editUser")
    public AjaxResult editUser(@RequestBody DutpUser dutpUser){
        return toAjax(dutpUserService.updateUser(dutpUser));
    }

    /**
     * 禁用状态
     * @param userId 用户id
     * @return 结果
     */
    @RequiresPermissions("edu:user:close")
    @PutMapping("/changeStatus/{userId}")
    public AjaxResult changeStatus(@PathVariable("userId") Long userId){
        return toAjax(dutpUserService.changeStatus(userId));
    }

    /**
     * 启用状态
     * @param userId 用户id
     * @return 结果
     */
    @RequiresPermissions("edu:user:open")
    @PutMapping("/openStatus/{userId}")
    public AjaxResult openStatus(@PathVariable("userId") Long userId){
        return toAjax(dutpUserService.openStatus(userId));
    }

    /**
     * 导出用户信息
     */
    @RequiresPermissions("edu:user:export")
    @Log(title = "导出用户信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DutpUser dutpUser)
    {
        List<DutpUser> list = dutpUserService.selectAll(dutpUser);
        ExcelUtil<DutpUser> util = new ExcelUtil<>(DutpUser.class);
        util.exportExcel(response, list, "用户信息");
    }

    /**
     * 导入读者信息
     * @param dutpUserDto 列表
     * @return 结果
     */
    @RequiresPermissions("edu:user:import")
    @Log(title = "导入读者用户信息", businessType = BusinessType.EXPORT)
    @PostMapping("/importUser")
    public AjaxResult importUser(@RequestBody DutpUserDto dutpUserDto){
        return success(dutpUserService.importUser(dutpUserDto));
    }

    /**
     * 导入学生信息
     * @param dutpUserDto 对象
     * @return 结果
     */
    @RequiresPermissions("edu:user:import")
    @Log(title = "导入学生信息", businessType = BusinessType.EXPORT)
    @PostMapping("/importStudent")
    public AjaxResult importStudent(@RequestBody DutpUserDto dutpUserDto){
        return success(dutpUserService.importStudent(dutpUserDto));
    }

    /**
     * 导入教师信息
     * @param dutpUserDto 对象
     * @return 结果
     */
    @RequiresPermissions("edu:user:import")
    @Log(title = "导出教师信息", businessType = BusinessType.EXPORT)
    @PostMapping("/importTeacher")
    public AjaxResult importTeacher(@RequestBody DutpUserDto dutpUserDto){
        return success(dutpUserService.importTeacher(dutpUserDto));
    }
    /**
     * 注销账号
     */
    @Log(title = "注销账号", businessType = BusinessType.DELETE)
    @PostMapping("/removeUser")
    public R<Boolean> removeUser(@RequestBody DutpUser user)
    {
        Integer checkCode = AliyunSmsUtil.checkCode(user.getPhonenumber(), user.getCode(), "SMS_314725755");
        if (checkCode != 200) {
            return R.fail(DutpConstant.VERIFICATION_CODE_ERROR);
        }
        return R.ok(dutpUserService.cancelAccount(Collections.singletonList(user.getUserId())));
    }

    /**
     * 注销当前登录账号
     */
    @Log(title = "注销当前登录账号", businessType = BusinessType.DELETE)
    @GetMapping("/removeDutpUser")
    public AjaxResult removeDutpUser(DutpUser user)
    {
        return toAjax(dutpUserService.cancelDutpUser(user));
    }



    /**
     * 重置密码
     */
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public AjaxResult resetPwd(@RequestBody DutpUser user)
    {
        dutpUserService.checkUserAllowed(user);
        dutpUserService.checkUserDataScope(user.getUserId());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        user.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(dutpUserService.resetPwd(user));
    }
}

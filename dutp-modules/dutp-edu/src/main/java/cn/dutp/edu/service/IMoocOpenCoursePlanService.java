package cn.dutp.edu.service;

import cn.dutp.domain.MoocOpenCoursePlan;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 公开课开课计划Service接口
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
public interface IMoocOpenCoursePlanService extends IService<MoocOpenCoursePlan> {
    /**
     * 查询公开课开课计划
     *
     * @param planId 公开课开课计划主键
     * @return 公开课开课计划
     */
    public MoocOpenCoursePlan selectMoocOpenCoursePlanByPlanId(Long planId);

    /**
     * 查询公开课开课计划列表
     *
     * @param moocOpenCoursePlan 公开课开课计划
     * @return 公开课开课计划集合
     */
    public List<MoocOpenCoursePlan> selectMoocOpenCoursePlanList(MoocOpenCoursePlan moocOpenCoursePlan);

    /**
     * 新增公开课开课计划
     *
     * @param moocOpenCoursePlan 公开课开课计划
     * @return 结果
     */
    public boolean insertMoocOpenCoursePlan(MoocOpenCoursePlan moocOpenCoursePlan);

    /**
     * 修改公开课开课计划
     *
     * @param moocOpenCoursePlan 公开课开课计划
     * @return 结果
     */
    public boolean updateMoocOpenCoursePlan(MoocOpenCoursePlan moocOpenCoursePlan);

    /**
     * 批量删除公开课开课计划
     *
     * @param planIds 需要删除的公开课开课计划主键集合
     * @return 结果
     */
    public boolean deleteMoocOpenCoursePlanByPlanIds(List<Long> planIds);

}

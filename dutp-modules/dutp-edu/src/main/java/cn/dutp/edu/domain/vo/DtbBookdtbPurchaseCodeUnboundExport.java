package cn.dutp.edu.domain.vo;

import cn.dutp.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

@Data
public class DtbBookdtbPurchaseCodeUnboundExport {

    /**
     * 教材名称
     */
    @Excel(name = "教材名称")
    private String bookName;

    /**
     * ISBN序列号
     */
    @Excel(name = "ISBN序列号")
    private String isbn;

    // 购书码
    @Excel(name = "购书码")
    private String code;
    // 订单下的购书码表id
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderCodeId;
    // 购书码id
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long codeId;

}

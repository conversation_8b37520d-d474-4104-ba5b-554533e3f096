package cn.dutp.edu.service.impl;

import java.util.Collections;
import java.util.List;

import cn.dutp.edu.domain.MoocSmartCourseLessonEvaluation;
import cn.dutp.edu.mapper.MoocSmartCourseLessonEvaluationMapper;
import cn.dutp.edu.service.IMoocSmartCourseLessonEvaluationService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.edu.mapper.MoocSmartCourseLessonEvaluationMapper;
import cn.dutp.edu.domain.MoocSmartCourseLessonEvaluation;
import cn.dutp.edu.service.IMoocSmartCourseLessonEvaluationService;

/**
 * 课程评价Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Service
public class MoocSmartCourseLessonEvaluationServiceImpl extends ServiceImpl<MoocSmartCourseLessonEvaluationMapper, MoocSmartCourseLessonEvaluation> implements IMoocSmartCourseLessonEvaluationService
{
    @Autowired
    private MoocSmartCourseLessonEvaluationMapper moocSmartCourseLessonEvaluationMapper;

    /**
     * 查询课程评价
     *
     * @param evaluationId 课程评价主键
     * @return 课程评价
     */
    @Override
    public MoocSmartCourseLessonEvaluation selectMoocSmartCourseLessonEvaluationByEvaluationId(Long evaluationId)
    {
        return this.getById(evaluationId);
    }

    /**
     * 查询课程评价列表
     *
     * @param moocSmartCourseLessonEvaluation 课程评价
     * @return 课程评价
     */
    @Override
    public List<MoocSmartCourseLessonEvaluation> selectMoocSmartCourseLessonEvaluationList(MoocSmartCourseLessonEvaluation moocSmartCourseLessonEvaluation)
    {
        LambdaQueryWrapper<MoocSmartCourseLessonEvaluation> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(moocSmartCourseLessonEvaluation.getLessonId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseLessonEvaluation::getLessonId
                ,moocSmartCourseLessonEvaluation.getLessonId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseLessonEvaluation.getStudentId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseLessonEvaluation::getStudentId
                ,moocSmartCourseLessonEvaluation.getStudentId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseLessonEvaluation.getRating())) {
                lambdaQueryWrapper.eq(MoocSmartCourseLessonEvaluation::getRating
                ,moocSmartCourseLessonEvaluation.getRating());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseLessonEvaluation.getComment())) {
                lambdaQueryWrapper.eq(MoocSmartCourseLessonEvaluation::getComment
                ,moocSmartCourseLessonEvaluation.getComment());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增课程评价
     *
     * @param moocSmartCourseLessonEvaluation 课程评价
     * @return 结果
     */
    @Override
    public boolean insertMoocSmartCourseLessonEvaluation(MoocSmartCourseLessonEvaluation moocSmartCourseLessonEvaluation)
    {
        return this.save(moocSmartCourseLessonEvaluation);
    }

    /**
     * 修改课程评价
     *
     * @param moocSmartCourseLessonEvaluation 课程评价
     * @return 结果
     */
    @Override
    public boolean updateMoocSmartCourseLessonEvaluation(MoocSmartCourseLessonEvaluation moocSmartCourseLessonEvaluation)
    {
        return this.updateById(moocSmartCourseLessonEvaluation);
    }

    /**
     * 批量删除课程评价
     *
     * @param evaluationIds 需要删除的课程评价主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocSmartCourseLessonEvaluationByEvaluationIds(List<Long> evaluationIds)
    {
        return this.removeByIds(evaluationIds);
    }

    @Override
    public List<MoocSmartCourseLessonEvaluation> getByClassId(MoocSmartCourseLessonEvaluation moocSmartCourseLessonEvaluation) {
        return moocSmartCourseLessonEvaluationMapper.getByClassId(moocSmartCourseLessonEvaluation);
    }

}

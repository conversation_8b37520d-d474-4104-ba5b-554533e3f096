package cn.dutp.edu.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import cn.dutp.edu.domain.MoocSmartCourseBrainstormReply;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
/**
 * 互动课堂的头脑风暴讨论成员Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Repository
public interface MoocSmartCourseBrainstormReplyMapper extends BaseMapper<MoocSmartCourseBrainstormReply>
{

    public List<MoocSmartCourseBrainstormReply> selectReplyList(@Param("doingsId") Long doingsId);
}

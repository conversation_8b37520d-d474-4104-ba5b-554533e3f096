<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.EduClassMemberMapper">
    
    <resultMap type="EduClassMember" id="EduClassMemberResult">
        <result property="eduClassMemberId"    column="edu_class_member_id"    />
        <result property="eduClassId"    column="edu_class_id"    />
        <result property="userId"    column="user_id"    />
        <result property="teacherUserId"    column="teacher_user_id"    />
        <result property="joinTime"    column="join_time"    />
        <result property="createdBy"    column="created_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updatedBy"    column="updated_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectEduClassMemberVo">
        select edu_class_member_id, edu_class_id, user_id, teacher_user_id, join_time, created_by, create_time, updated_by, update_time, del_flag from edu_class_member
    </sql>

    <select id="selectEduClassMemberList" parameterType="EduClassMember" resultMap="EduClassMemberResult">
        <include refid="selectEduClassMemberVo"/>
        <where>  
            <if test="eduClassId != null "> and edu_class_id = #{eduClassId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="teacherUserId != null "> and teacher_user_id = #{teacherUserId}</if>
            <if test="joinTime != null "> and join_time = #{joinTime}</if>
            <if test="createdBy != null  and createdBy != ''"> and created_by = #{createdBy}</if>
            <if test="updatedBy != null  and updatedBy != ''"> and updated_by = #{updatedBy}</if>
        </where>
    </select>
    
    <select id="selectEduClassMemberByEduClassMemberId" parameterType="Long" resultMap="EduClassMemberResult">
        <include refid="selectEduClassMemberVo"/>
        where edu_class_member_id = #{eduClassMemberId}
    </select>

    <insert id="insertEduClassMember" parameterType="EduClassMember" useGeneratedKeys="true" keyProperty="eduClassMemberId">
        insert into edu_class_member
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="eduClassId != null">edu_class_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="teacherUserId != null">teacher_user_id,</if>
            <if test="joinTime != null">join_time,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="eduClassId != null">#{eduClassId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="teacherUserId != null">#{teacherUserId},</if>
            <if test="joinTime != null">#{joinTime},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateEduClassMember" parameterType="EduClassMember">
        update edu_class_member
        <trim prefix="SET" suffixOverrides=",">
            <if test="eduClassId != null">edu_class_id = #{eduClassId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="teacherUserId != null">teacher_user_id = #{teacherUserId},</if>
            <if test="joinTime != null">join_time = #{joinTime},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where edu_class_member_id = #{eduClassMemberId}
    </update>

    <delete id="deleteEduClassMemberByEduClassMemberId" parameterType="Long">
        delete from edu_class_member where edu_class_member_id = #{eduClassMemberId}
    </delete>

    <delete id="deleteEduClassMemberByEduClassMemberIds" parameterType="String">
        delete from edu_class_member where edu_class_member_id in 
        <foreach item="eduClassMemberId" collection="array" open="(" separator="," close=")">
            #{eduClassMemberId}
        </foreach>
    </delete>
</mapper>
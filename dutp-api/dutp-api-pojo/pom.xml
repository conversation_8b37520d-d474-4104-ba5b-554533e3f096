<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.dutp</groupId>
        <artifactId>dutp</artifactId>
        <version>1.0.0</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>dutp-api-pojo</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>

        <!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- Mysql Connector -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>

        <!-- dutp Common DataSource -->
        <dependency>
            <groupId>cn.dutp</groupId>
            <artifactId>dutp-common-datasource</artifactId>
        </dependency>

        <!-- dutp Common DataScope -->
        <dependency>
            <groupId>cn.dutp</groupId>
            <artifactId>dutp-common-datascope</artifactId>
        </dependency>

        <!-- dutp Common Log -->
        <dependency>
            <groupId>cn.dutp</groupId>
            <artifactId>dutp-common-log</artifactId>
        </dependency>

        <!-- dutp Common Swagger -->
        <dependency>
            <groupId>cn.dutp</groupId>
            <artifactId>dutp-common-swagger</artifactId>
        </dependency>

        <!-- dutp Common mail -->
        <dependency>
            <groupId>cn.dutp</groupId>
            <artifactId>dutp-common-mail</artifactId>
        </dependency>

        <!-- dutp Common mail -->
        <dependency>
            <groupId>cn.dutp</groupId>
            <artifactId>dutp-common-sms</artifactId>
        </dependency>

        <!-- dutp Common elasticSearch -->
        <dependency>
            <groupId>cn.dutp</groupId>
            <artifactId>dutp-common-es</artifactId>
        </dependency>

        <!-- dutp Common websocket -->
        <dependency>
            <groupId>cn.dutp</groupId>
            <artifactId>dutp-common-websocket</artifactId>
        </dependency>

        <!-- dutp Common rocketmq -->
        <dependency>
            <groupId>cn.dutp</groupId>
            <artifactId>dutp-common-rocketmq</artifactId>
        </dependency>
    </dependencies>

</project>
package cn.dutp.domain;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 订单审核人对象 dtb_book_order_audit_user
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Data
@TableName("dtb_book_order_audit_user")
public class DtbBookOrderAuditUser extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long auditUserId;

    /**
     * 订单ID
     */
    @Excel(name = "订单ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderId;

    /**
     * 审核人ID关联sys_user
     */
    @Excel(name = "审核人ID关联sys_user")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 1采购审核2修改审核3作废审核4样书一级审核5样书二级审核
     */
    @Excel(name = "1采购审核2修改审核3作废审核4样书一级审核5样书二级审核")
    private Integer auditType;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date auditDate;

    /**
     * 审核状态0待审核1通过2驳回
     */
    @Excel(name = "审核状态0待审核1通过2驳回")
    private Integer auditStatus;

    /**
     * 审核备注
     */
    @Excel(name = "审核备注")
    private String auditContent;

    /**
     * 修改的内容JSON
     */
    @Excel(name = "修改的内容JSON")
    private String editContent;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("auditUserId", getAuditUserId())
                .append("orderId", getOrderId())
                .append("userId", getUserId())
                .append("auditType", getAuditType())
                .append("auditDate", getAuditDate())
                .append("auditStatus", getAuditStatus())
                .append("auditContent", getAuditContent())
                .append("editContent", getEditContent())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

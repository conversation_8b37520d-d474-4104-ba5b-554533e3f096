package cn.dutp.domain;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * DUTP-BASE-002学校表
 *
 * <AUTHOR> @since 2024-10-24
 */
@Data
public class DutpBookSchoolVo{
    private static final long serialVersionUID = 1L;

    /**
     * 学校ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolId;

    /**
     * 学校名称
     */
    private String schoolName;

}

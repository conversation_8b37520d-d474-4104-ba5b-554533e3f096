package cn.dutp.domain.vo;

import cn.dutp.domain.DtbBookPurchaseCode;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 购书码发行管理对象 dtb_book_purchase_code
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
public class DtbBookPurchaseCodeVO {

    /**
     * 批量生成购书码数据
     */
    private List<DtbBookPurchaseCode> codeList;
    // 名字
    private String nickName;
    // 类别 0普通读者1学生2教师
    private String userType;
    // 购书码绑定手机号
    private String phone;
    // 院系id
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolId;
    // 院系名称
    private String schoolName;
    // 购书码id
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long codeId;
    // 购书码
    private String code;
    // 教材ID
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;
    // 解绑次数
    private Integer unbindQuantity;
    // 导出次数
    private Integer exportQuantity;
    // 绑定日期
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date bindDate;
    // 是否可解绑 0否1是(超过绑定日期30天不可以解绑)
    private Integer isUnbind;
    // 订单下的购书码id
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderCodeId;
    // 教材名称
    private String bookName;
    // 教材ISBN
    private String isbn;
    // 上架状态1已上架2未上架3召回4即将上架
    private Integer shelfState;
}

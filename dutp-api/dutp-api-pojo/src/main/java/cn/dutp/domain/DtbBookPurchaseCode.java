package cn.dutp.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;

/**
 * 购书码发行管理对象 dtb_book_purchase_code
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@TableName("dtb_book_purchase_code")
public class DtbBookPurchaseCode extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long codeId;

    /**
     * 教材ID
     */
    @Excel(name = "教材ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 购书码绑定手机号
     */
    @Excel(name = "购书码绑定手机号")
    private String phone;

    /**
     * 购书码
     */
    @Excel(name = "购书码")
    private String code;

    /**
     * 购书码来源sys_user里的user_id
     */
    @Excel(name = "购书码来源sys_user里的user_id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long codeFrom;

    /**
     * 1未绑定2未兑换3已兑换4已过期
     */
    @Excel(name = "1未绑定2未兑换3已兑换4已过期")
    private Integer state;


    /**
     * 1长期码 2临时码
     */
    @Excel(name = "1长期码2临时码")
    private Integer codeType;

    /**
     * 1未绑定2未兑换3已兑换4已过期
     */
    @Excel(name = "0未冻结1冻结")
    private Integer isFrozen;

    /**
     * 兑换日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "兑换日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date exchangeDate;

    /**
     * 使用期限
     */
    @Excel(name = "使用期限(天)")
    private Integer timeLimit;
    /**
     * 到期日期，空永不过期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "到期日期，空永不过期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date expiryDate;

    /**
     * 绑定日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "绑定日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date bindDate;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 用户名id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;


    /**
     * 书名
     */
    @TableField(exist = false)
    private String bookName;

    /**
     * 订单ID
     */
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderId;

    /**
     * isbn
     */
    @TableField(exist = false)
    private String isbn;

    /**
     * issn
     */
    @TableField(exist = false)
    private String issn;

    /**
     * 1其他2主教材3副教材
     */
    @TableField(exist = false)
    private Integer masterFlag;

    /**
     * 上架状态1已上架2未上架3召回4即将上架
     */
    @TableField(exist = false)
    private Integer shelfState;

    /**
     * 出版状态1未出版2已出版
     */
    @TableField(exist = false)
    private Integer publishStatus;

    /**
     * 购书码数量
     */
    @TableField(exist = false)
    private Integer codeTotal;

    /**
     * 已使用
     */
    @TableField(exist = false)
    private Integer useTotal;

    /**
     * 已占用
     */
    @TableField(exist = false)
    private Integer occupyTotal;

    /**
     * 未占用
     */
    @TableField(exist = false)
    private Integer notOccupyTotal;

    /**
     * 已过期
     */
    @TableField(exist = false)
    private Integer expireTotal;

    /**
     * 已作废
     */
    @TableField(exist = false)
    private Integer cancelTotal;

    /**
     * 长期码的未使用数量(不统计临时码的库存数量)
     */
    @TableField(exist = false)
    private Integer longTermTotal;

    /**
     * 售价
     */
    @TableField(exist = false)
    private Double priceSale;




    /**
     * 前端页面中输入的需要批量生成购书码数量
     */
    @TableField(exist = false)
    private Integer codeNum;

    /**
     * 绑定的用户账号信息
     */
    @TableField(exist = false)
    private String userName;

    /**
     * 绑定的用户名
     */
    @TableField(exist = false)
    private String nickName;

    /**
     * 绑定用户的学校名称
     */
    @TableField(exist = false)
    private String schoolName;

    /**
     * 绑定用户的院系名称
     */
    @TableField(exist = false)
    private String academyName;

    /**
     * 绑定用户的专业名称
     */
    @TableField(exist = false)
    private String specialityName;

    /**
     * 使用期限 (天)
     */
    @TableField(exist = false)
    private Integer useDays;

    /**
     * 短信验证码
     */
    @TableField(exist = false)
    private String verificationCode;

    /**
     * 封面图片地址
     */
    @TableField(exist = false)
    private String cover;

    /**
     * 主教材Id
     */
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long masterBookId;

    /**
     * 当前版本id
     */
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private String currentVersionId;

    @TableField(exist = false)
    private List<DtbBookPurchaseCode> children;


    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private Date operateTime;

    /**
     * 操作人
     */
    @TableField(exist = false)
    private String operateBy;

    @TableField(exist = false)
    private Date startTime;

    @TableField(exist = false)
    private Date endTime;

    /**
     * 出版日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    @Excel(name = "出版日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date publishDate;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("codeId", getCodeId())
                .append("bookId", getBookId())
                .append("phone", getPhone())
                .append("code", getCode())
                .append("codeFrom", getCodeFrom())
                .append("state", getState())
                .append("exchangeDate", getExchangeDate())
                .append("expiryDate", getExpiryDate())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("bindDate", getBindDate())
                .append("userId", getUserId())
                .append("bookName", getBookName())
                .append("orderId", getOrderId())
                .append("isbn", getIsbn())
                .append("cover", getCover())
                .toString();
    }
}

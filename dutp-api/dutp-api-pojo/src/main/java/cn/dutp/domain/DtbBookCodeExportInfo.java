package cn.dutp.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 购书码导出记录对象 dtb_book_code_export_info
 *
 * <AUTHOR>
 * @date 2025-02-21
 */
@Data
@TableName("dtb_book_code_export_info")
public class DtbBookCodeExportInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long exportDataId;

    /**
     * $column.columnComment
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long bookId;

    /**
     * 导出人
     */
    @Excel(name = "导出人")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long exportUserId;

    /**
     * 导出日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "导出日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date exportDate;

    /**
     * 导出购书码数量
     */
    @Excel(name = "导出购书码数量")
    private Integer codeQuantity;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("exportDataId", getExportDataId())
                .append("bookId", getBookId())
                .append("exportUserId", getExportUserId())
                .append("exportDate", getExportDate())
                .append("codeQuantity", getCodeQuantity())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

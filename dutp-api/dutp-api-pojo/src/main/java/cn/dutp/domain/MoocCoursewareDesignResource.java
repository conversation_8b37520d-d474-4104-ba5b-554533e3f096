package cn.dutp.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import cn.dutp.domain.MoocUserQuestion;

/**
 * 公开课课件设计资源对象 mooc_courseware_design_resource
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
@TableName("mooc_courseware_design_resource")
public class MoocCoursewareDesignResource extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterResourceId;

    /**
     * 章节ID
     */
    @Excel(name = "章节ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    /**
     * 公开课程设计ID
     */
    @Excel(name = "公开课程设计ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long coursewareDesignId;

    /**
     * 资源标题
     */
    @Excel(name = "资源标题")
    private String resourceTitle;

    /**
     * 资源内容
     */
    @Excel(name = "资源内容")
    private String resourceContent;

    /**
     * 资源类型1=图文，2=资源，3=虚拟仿真，4=链接，5=题库，6=压缩包
     */
    @Excel(name = "资源类型1=图文，2=资源，3=虚拟仿真，4=链接，5=题库，6=压缩包")
    private String resourceType;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Integer resourceSort;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 用户题目
     */
    @TableField(exist = false)
    private MoocUserQuestion userQuestion;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("chapterResourceId", getChapterResourceId())
                .append("chapterId", getChapterId())
                .append("coursewareDesignId", getCoursewareDesignId())
                .append("resourceTitle", getResourceTitle())
                .append("resourceContent", getResourceContent())
                .append("resourceType", getResourceType())
                .append("resourceSort", getResourceSort())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("userQuestion", getUserQuestion())
                .toString();
    }
}

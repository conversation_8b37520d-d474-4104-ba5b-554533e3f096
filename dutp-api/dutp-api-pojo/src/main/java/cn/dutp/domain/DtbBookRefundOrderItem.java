package cn.dutp.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 退款明细对象 dtb_book_refund_order_item
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Data
@TableName("dtb_book_refund_order_item")
public class DtbBookRefundOrderItem extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long refundItemId;

    /**
     * 订单明细id
     */
    @Excel(name = "订单明细id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderItemId;

    /**
     *
     */
    @Excel(name = "")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long refundOrderId;

    /**
     * 退款数量
     */
    @Excel(name = "退款数量")
    private Long refundQuantity;

    /**
     * 退款金额
     */
    @Excel(name = "退款金额")
    private BigDecimal refundAmount;

    /**
     * 教材名称
     */
    @TableField(exist = false)
    private String bookName;

    /**
     * ISBN序列号
     */
    @TableField(exist = false)
    private String isbn;

    /**
     * ISSN序列号
     */
    @TableField(exist = false)
    private String issn;


    /**
     * 申请原因
     */
    @TableField(exist = false)
    private String remark;

    /**
     * 审核人
     */
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long auditUserId;

    /**
     * 审核人
     */
    @TableField(exist = false)
    private String auditUserName;

    /**
     * 审核状态0未退款1退款中2部分退款3全额退款
     */
    @TableField(exist = false)
    private Integer refundStatus;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /**
     * 教材ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("refundItemId", getRefundItemId())
                .append("orderItemId", getOrderItemId())
                .append("refundOrderId", getRefundOrderId())
                .append("refundQuantity", getRefundQuantity())
                .append("refundAmount", getRefundAmount())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("bookId", getBookId())
                .toString();
    }
}

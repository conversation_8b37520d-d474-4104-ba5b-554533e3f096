package cn.dutp.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 课程团队成员对象 mooc_open_course_teammate
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
@TableName("mooc_open_course_teammate")
public class MoocOpenCourseTeammate extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long teammateId;

    /**
     * 所属课程
     */
    @Excel(name = "所属课程")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long courseId;

    /**
     * 成员展示头像
     */
    @Excel(name = "成员展示头像")
    private String avatar;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Integer sort;

    /**
     * 成员姓名
     */
    @Excel(name = "成员姓名")
    private String name;

    /**
     * 学校名称
     */
    @Excel(name = "学校名称")
    private String schoolName;

    /**
     * 用户职务
     */
    @Excel(name = "用户职务")
    private String title;

    /**
     * 个人描述
     */
    @Excel(name = "个人描述")
    private String description;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("teammateId", getTeammateId())
                .append("courseId", getCourseId())
                .append("avatar", getAvatar())
                .append("sort", getSort())
                .append("name", getName())
                .append("schoolName", getSchoolName())
                .append("title", getTitle())
                .append("description", getDescription())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

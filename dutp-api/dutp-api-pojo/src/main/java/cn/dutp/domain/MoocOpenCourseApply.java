package cn.dutp.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 公开课的审核过程对象 mooc_open_course_apply
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
@TableName("mooc_open_course_apply")
public class MoocOpenCourseApply extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long applyId;

    /**
     * 所属课程
     */
    @Excel(name = "所属课程")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long courseId;

    /**
     * 审核提交人
     */
    @Excel(name = "审核提交人")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 审核人
     */
    @Excel(name = "审核人")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long auditUserId;

    /**
     * 审核状态 1 课程审核 2课程通过  3课程驳回
     */
    @Excel(name = "审核状态 1 课程审核 2课程通过  3课程驳回")
    private String applyStatus;

    /**
     * 驳回理由
     */
    @Excel(name = "驳回理由")
    private String applyRemark;

    /**
     * 1 课程审核 2课程内容审核
     */
    @Excel(name = "1 课程审核 2课程内容审核")
    private String step;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("applyId", getApplyId())
                .append("courseId", getCourseId())
                .append("userId", getUserId())
                .append("auditUserId", getAuditUserId())
                .append("applyStatus", getApplyStatus())
                .append("applyRemark", getApplyRemark())
                .append("step", getStep())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

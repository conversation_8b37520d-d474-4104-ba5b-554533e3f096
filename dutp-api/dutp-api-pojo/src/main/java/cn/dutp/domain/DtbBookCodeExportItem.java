package cn.dutp.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 导出购书码明细对象 dtb_book_code_export_item
 *
 * <AUTHOR>
 * @date 2025-02-21
 */
@Data
@TableName("dtb_book_code_export_item")
public class DtbBookCodeExportItem extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long exportItemId;

    /**
     * dtb_book_code_export_info表主键
     */
    @Excel(name = "dtb_book_code_export_info表主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long exportDataId;

    /**
     * $column.columnComment
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long codeId;

    /**
     * $column.columnComment
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long bookId;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("exportItemId", getExportItemId())
                .append("exportDataId", getExportDataId())
                .append("codeId", getCodeId())
                .append("bookId", getBookId())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

package cn.dutp.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import lombok.Data;

/**
 * 导出购书码发行管理对象
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
@Data
public class DtbBookPurchaseCodeExcel extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 教材名称
     */
    @Excel(name = "教材名称")
    private String bookName;

    /**
     * ISBN序列号
     */
    @Excel(name = "ISBN/ISSN")
    private String isbn;

    /**
     * 购书码
     */
    @Excel(name = "购书码")
    private String code;

    /**
     * 使用期限
     */
    @Excel(name = "使用期限(天)")
    private Integer timeLimit;
}

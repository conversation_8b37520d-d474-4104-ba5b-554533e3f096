package cn.dutp.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 客服咨询热词对象 dutp_customer_hot_word
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
@Data
@TableName("dutp_customer_hot_word")
public class DutpCustomerHotWord extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long wordId;

    /**
     *
     */
    @Excel(name = "")
    private String word;

    /**
     * 出现次数
     */
    @Excel(name = "出现次数")
    private Integer quantity;

    /**
     * 0未被收录1已被收录
     */
    @Excel(name = "0未被收录1已被收录")
    private Integer inUse;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("wordId", getWordId())
                .append("word", getWord())
                .append("quantity", getQuantity())
                .append("inUse", getInUse())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }

    public DutpCustomerHotWord(Long wordId, String word, Integer quantity, Integer inUse, String delFlag) {
        this.wordId = wordId;
        this.word = word;
        this.quantity = quantity;
        this.inUse = inUse;
        this.delFlag = delFlag;
    }
}

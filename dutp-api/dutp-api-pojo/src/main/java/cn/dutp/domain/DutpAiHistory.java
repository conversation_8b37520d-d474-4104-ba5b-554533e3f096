package cn.dutp.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * ai请求记录对象 dutp_ai_history
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@TableName("dutp_ai_history")
public class DutpAiHistory extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long promptHistoryId;

    /**
     * 功能1续写2缩写3扩写4润色5生成试题6生成总结7生成学习目标8生成脑图9生成大纲10匹配案例11文生语音12文生图片13百度图片增强14百度图像修复15百度视频生成16文字纠错17文字纠错(上传黑名单 (违禁词替换) 和白名单 (免审词设置))18视频合规19文本合规20文本合规(上传黑白名单)21图片合规22音频合规23翻译
     */
    @Excel(name = "功能")
    private Integer promptAbility;

    /**
     * 问题
     */
    @Excel(name = "问题")
    private String question;

    /**
     * 回答
     */
    @Excel(name = "回答")
    private String answer;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 章节ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    /**
     * userID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;
    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("promptHistoryId", getPromptHistoryId())
                .append("promptAbility", getPromptAbility())
                .append("question", getQuestion())
                .append("answer", getAnswer())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

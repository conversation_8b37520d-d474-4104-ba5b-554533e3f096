package cn.dutp.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * 公开课加入的学员对象 mooc_open_course_student
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
@TableName("mooc_open_course_student")
public class MoocOpenCourseStudent extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long studentId;

    /**
     * 所属课程
     */
    @Excel(name = "所属课程")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long courseId;

    /**
     * 学生的用户id
     */
    @Excel(name = "学生的用户id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Integer sort;

    /**
     * 加入班级时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "加入班级时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date joinTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 学校名称
     */
    @TableField(exist = false)
    private String schoolName;

    /**
     * 绑定用户的专业名称
     */
    @TableField(exist = false)
    private String specialityName;

    /**
     * 姓名
     */
    @TableField(exist = false)
    private String realName;


    /** 联系方式 */
    @TableField(exist = false)
    private String phonenumber;

    /**
     * 课程状态 (用于查询参数，非数据库字段)
     */
    @TableField(exist = false)
    private Integer courseStatus;

    /**
     * 关联的公开课信息 (非数据库字段，由查询填充)
     */
    @TableField(exist = false)
    private MoocOpenCourse moocOpenCourse; // 如果MoocOpenCourse类名不同或包名不同，请修改

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("studentId", getStudentId())
                .append("courseId", getCourseId())
                .append("userId", getUserId())
                .append("sort", getSort())
                .append("joinTime", getJoinTime())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

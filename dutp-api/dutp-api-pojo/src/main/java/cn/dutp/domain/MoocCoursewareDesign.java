package cn.dutp.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;

import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 公开课课件设计对象 mooc_courseware_design
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
@TableName("mooc_courseware_design")
public class MoocCoursewareDesign extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 课件设计ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long coursewareDesignId;

    /**
     * 课件名称
     */
    @Excel(name = "课件名称")
    private String coursewareName;

    /** 课件封面 */
    @Excel(name = "课件封面")
    private String coursewareCover;

    /**
     * 资源下载(1可以 2不可以)
     */
    @Excel(name = "资源下载(1可以 2不可以)")
    private Integer resourceDownload;

    /**
     * 所有者id
     */
    @Excel(name = "所有者id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 所有者id
     */
    @Excel(name = "所有者id")
    @TableField(exist = false)
    private String realName;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 课件文件夹列表
     */
    @TableField(exist = false)
    private List<MoocCoursewareDesignFolder> coursewareDesignFolders;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("coursewareDesignId", getCoursewareDesignId())
                .append("coursewareName", getCoursewareName())
                .append("coursewareCover", getCoursewareCover())
                .append("resourceDownload", getResourceDownload())
                .append("userId", getUserId())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

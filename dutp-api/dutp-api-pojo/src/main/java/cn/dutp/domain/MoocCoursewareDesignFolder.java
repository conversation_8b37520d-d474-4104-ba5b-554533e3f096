package cn.dutp.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;

import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 公开课课件设计目录对象 mooc_courseware_design_folder
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
@TableName("mooc_courseware_design_folder")
public class MoocCoursewareDesignFolder extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    /**
     * 章节名称
     */
    @Excel(name = "章节名称")
    private String chapterName;

    /**
     * 公开课程设计ID
     */
    @Excel(name = "公开课程设计ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long coursewareDesignId;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Integer sort;

    /**
     * 父级目录
     */
    @Excel(name = "父级目录")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 课件资源列表
     */
    @TableField(exist = false)
    private List<MoocCoursewareDesignResource> coursewareDesignResources;
    
    /**
     * 子文件夹列表
     */
    @TableField(exist = false)
    private List<MoocCoursewareDesignFolder> children;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("chapterId", getChapterId())
                .append("chapterName", getChapterName())
                .append("coursewareDesignId", getCoursewareDesignId())
                .append("sort", getSort())
                .append("parentId", getParentId())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

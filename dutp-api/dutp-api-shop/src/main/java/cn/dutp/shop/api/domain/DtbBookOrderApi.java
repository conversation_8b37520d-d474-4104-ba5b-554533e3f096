package cn.dutp.shop.api.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * DUTP-DTB_012订单对象 dtb_book_order
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
public class DtbBookOrderApi extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderId;

    /**
     * 订单编号
     */
    @Excel(name = "订单编号")
    private String orderNo;

    /**
     * 1直接购买2教务统一购买3管理端直采4样书订单
     */
    @Excel(name = "1直接购买2教务统一购买3管理端直采4样书订单")
    private Integer orderType;

    /**
     * 教材ID
     */
    @Excel(name = "教材ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 购书者ID，dutp_user表中userId
     */
    @Excel(name = "购书者ID，dutp_user表中userId")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 教务购买的用户ID=sys_user的userId
     */
    @Excel(name = "教务购买的用户ID=sys_user的userId")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolUserId;

    /**
     * 采购学校,单独购买0
     */
    @Excel(name = "采购学校,单独购买0")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolId;

    /**
     * 支付状态，N：待支付、A：已支付、F：支付失败
     */
    @Excel(name = "支付状态，N：待支付、A：已支付、F：支付失败")
    private String paymentStatus;

    /**
     * 支付方式，如信用卡、支付宝、微信支付等
     */
    @Excel(name = "支付方式，如信用卡、支付宝、微信支付等")
    private String paymentMethod;

    /**
     * 订单状态，pending：待处理、processing：处理中、completed：已完成、cancelled：已取消等
     */
    @Excel(name = "订单状态，pending：待处理、processing：处理中、completed：已完成、cancelled：已取消等")
    private String orderStatus;

    /**
     * 发票状态0未申请1申请开票2已开票
     */
    @Excel(name = "发票状态0未申请1申请开票2已开票")
    private Integer invoiceStatus;

    /**
     * 付款日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "付款日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date payTime;

    /**
     * 支付金额
     */
    @Excel(name = "支付金额")
    private BigDecimal payAmount;

    /**
     * 支付凭证URL
     */
    @Excel(name = "支付凭证URL")
    private String payCertImageUrl;

    /**
     * 订单审核状态0待审核1通过2驳回【教务订单用】
     */
    @Excel(name = "订单审核状态0待审核1通过2驳回【教务订单用】")
    private Integer orderAuditStatus;

    /**
     * 0待审核1通过2驳回，支付凭证审核【教务订单用】
     */
    @Excel(name = "0待审核1通过2驳回，支付凭证审核【教务订单用】")
    private Integer paymentAuditStatus;

    /**
     * 原价
     */
    @Excel(name = "原价")
    private BigDecimal price;

    /**
     * 折扣
     */
    @Excel(name = "折扣")
    private BigDecimal discount;

    /**
     * 是否删除1未删除2用户删除3管理员删除
     */
    @Excel(name = "是否删除1未删除2用户删除3管理员删除")
    private Integer deleted;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("orderId", getOrderId())
                .append("orderNo", getOrderNo())
                .append("orderType", getOrderType())
                .append("bookId", getBookId())
                .append("userId", getUserId())
                .append("schoolUserId", getSchoolUserId())
                .append("schoolId", getSchoolId())
                .append("paymentStatus", getPaymentStatus())
                .append("paymentMethod", getPaymentMethod())
                .append("orderStatus", getOrderStatus())
                .append("invoiceStatus", getInvoiceStatus())
                .append("payTime", getPayTime())
                .append("payAmount", getPayAmount())
                .append("payCertImageUrl", getPayCertImageUrl())
                .append("orderAuditStatus", getOrderAuditStatus())
                .append("paymentAuditStatus", getPaymentAuditStatus())
                .append("price", getPrice())
                .append("discount", getDiscount())
                .append("remark", getRemark())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("deleted", getDeleted())
                .toString();
    }
}

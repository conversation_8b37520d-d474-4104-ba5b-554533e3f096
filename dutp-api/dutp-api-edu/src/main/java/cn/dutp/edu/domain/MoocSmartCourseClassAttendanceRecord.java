package cn.dutp.edu.domain;

    import java.util.Date;
    import com.fasterxml.jackson.annotation.JsonFormat;
import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 互动课堂班级学生签到记录对象 mooc_smart_course_class_attendance_record
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName("mooc_smart_course_class_attendance_record")
public class MoocSmartCourseClassAttendanceRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** 学生签到记录ID */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long attendanceRecordId;

    /** 签到活动ID (mooc_smart_course_class_attendance) */
        @Excel(name = "签到活动ID (mooc_smart_course_class_attendance)")
    private Long attendanceId;

    /** 学生用户ID */
        @Excel(name = "学生用户ID")
    private Long userId;

    /** 实际签到时间 (当学生成功签到时) */
        @JsonFormat(pattern = "yyyy-MM-dd")
        @Excel(name = "实际签到时间 (当学生成功签到时)", width = 30, dateFormat = "yyyy-MM-dd")
    private Date signInTime;

    /** 创建者用户ID */
        @Excel(name = "创建者用户ID")
    private String createdBy;

    /** 最后更新者用户ID */
        @Excel(name = "最后更新者用户ID")
    private String updatedBy;

    /** 删除标志 (0: 存在, 2: 删除) */
    private String delFlag;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("attendanceRecordId", getAttendanceRecordId())
            .append("attendanceId", getAttendanceId())
            .append("userId", getUserId())
            .append("signInTime", getSignInTime())
            .append("createdBy", getCreatedBy())
            .append("createTime", getCreateTime())
            .append("updatedBy", getUpdatedBy())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
        .toString();
        }
        }

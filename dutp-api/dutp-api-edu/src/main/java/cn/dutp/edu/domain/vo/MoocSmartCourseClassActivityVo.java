package cn.dutp.edu.domain.vo;

import cn.dutp.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 互动课堂班级活动对象
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Data
public class MoocSmartCourseClassActivityVo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 活动ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long activityId;

    /**
     * 班级ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long classId;

    /**
     * 所属课程ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long courseId;

    /** 课堂ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long lessonId;

    /** 状态 */
    private Integer state;

    /** 标题 */
    private String title;

    /** 人数 */
    private Integer quantity;

    /** 题数 */
    private Integer questionsNumber;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 活动类型 */
    private Integer type;

    /** 签到教师姓名 */
    private String signInTeacherName;

    /** 总人数 */
    private Integer totalNumber;

    /** 任务完成人数 */
    private Integer completeNumber;

    /** 显示类型 */
    private String displayType;

    /** 提问类型 */
    private String questionType;

    /** 作业类型 */
    private String jobType;

    /** 防止定时器循环调用 */
    private boolean upFlag;

    /** 结束方式 1：手动，2：自动*/
    private Integer endMethod;

}

package cn.dutp.edu.domain.dto;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 【用户普通文件】对象 dutp_user_common_file
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Data
@TableName("dutp_user_common_file")
public class DutpUserCommonFileDto extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** $column.columnComment */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long fileId;

    /** 文件名 */
    @Excel(name = "文件名")
    private String fileName;

    /** 文件地址 */
    @Excel(name = "文件地址")
    private String fileUrl;

    /** 文件后缀名 */
    @Excel(name = "文件后缀名")
    private String fileType;

    /** 文件大小 */
    @Excel(name = "文件大小")
    private Long fileSize;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 业务Id */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long businessId;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("fileId", getFileId())
                .append("fileName", getFileName())
                .append("fileUrl", getFileUrl())
                .append("fileType", getFileType())
                .append("fileSize", getFileSize())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("businessId", getBusinessId())
                .toString();
    }
}

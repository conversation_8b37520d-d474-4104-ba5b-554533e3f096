package cn.dutp.edu.domain;

    import java.util.Date;
    import com.fasterxml.jackson.annotation.JsonFormat;
import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 互动课堂统计数据快照对象 mooc_smart_course_static
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName("mooc_smart_course_static")
public class MoocSmartCourseStatic extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** 统计记录ID */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long staticId;

    /** 课程ID (mooc_smart_course) */
        @Excel(name = "课程ID (mooc_smart_course)")
    private Long courseId;

    /** 班级ID ( mooc_smart_course_class) */
        @Excel(name = "班级ID ( mooc_smart_course_class)")
    private Long classId;

    /** 数据快照时间 (本次统计数据生成的时间) */
        @JsonFormat(pattern = "yyyy-MM-dd")
        @Excel(name = "数据快照时间 (本次统计数据生成的时间)", width = 30, dateFormat = "yyyy-MM-dd")
    private Date snapshotDatetime;

    /** 签到活动次数 */
        @Excel(name = "签到活动次数")
    private Long signinActivitiesCount;

    /** 互动讨论总数 */
        @Excel(name = "互动讨论总数")
    private Long chatCount;

    /** 互动讨论中教师发帖数 */
        @Excel(name = "互动讨论中教师发帖数")
    private Long chatTeacherPosts;

    /** 互动讨论中学生参与数 */
        @Excel(name = "互动讨论中学生参与数")
    private Long chatStudentParticipations;

    /** 主题讨论总数 */
        @Excel(name = "主题讨论总数")
    private Long discussionsCount;

    /** 教材学习资料数量 */
        @Excel(name = "教材学习资料数量")
    private Long missonCount;

    /** 考试数量 */
        @Excel(name = "考试数量")
    private Long examsCount;

    /** 作业数量 */
        @Excel(name = "作业数量")
    private Long homeworkCount;

    /** 课堂/课时数量 */
        @Excel(name = "课堂/课时数量")
    private Long courseLessonCount;

    /** 班级学生人数 */
        @Excel(name = "班级学生人数")
    private Long studentsCount;

    /**  点名签到次数 */
        @Excel(name = " 点名签到次数")
    private Long activityAttendanceCount;

    /**  教学资源使用次数 */
        @Excel(name = " 教学资源使用次数")
    private Long activityTeachingResourceCount;

    /**  作品秀次数 */
        @Excel(name = " 作品秀次数")
    private Long activityWorkShowCount;

    /**  投票问卷次数 */
        @Excel(name = " 投票问卷次数")
    private Long activitySurveyCount;

    /**  拓展学习次数 */
        @Excel(name = " 拓展学习次数")
    private Long activityExtendedCount;

    /**  头脑风暴次数 */
        @Excel(name = " 头脑风暴次数")
    private Long activityBrainstormingCount;

    /**  提问环节次数 */
        @Excel(name = " 提问环节次数")
    private Long activityQuestionCount;

    /** 作业成绩分布  */
        @Excel(name = "作业成绩分布 ")
    private String homeworkGradeDistribution;

    /** 考试成绩分布  */
        @Excel(name = "考试成绩分布 ")
    private String examGradeDistribution;

    /** 创建者用户ID */
        @Excel(name = "创建者用户ID")
    private Long createdBy;

    /** 最后更新者用户ID */
        @Excel(name = "最后更新者用户ID")
    private Long updatedBy;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("staticId", getStaticId())
            .append("courseId", getCourseId())
            .append("classId", getClassId())
            .append("snapshotDatetime", getSnapshotDatetime())
            .append("signinActivitiesCount", getSigninActivitiesCount())
            .append("chatCount", getChatCount())
            .append("chatTeacherPosts", getChatTeacherPosts())
            .append("chatStudentParticipations", getChatStudentParticipations())
            .append("discussionsCount", getDiscussionsCount())
            .append("missonCount", getMissonCount())
            .append("examsCount", getExamsCount())
            .append("homeworkCount", getHomeworkCount())
            .append("courseLessonCount", getCourseLessonCount())
            .append("studentsCount", getStudentsCount())
            .append("activityAttendanceCount", getActivityAttendanceCount())
            .append("activityTeachingResourceCount", getActivityTeachingResourceCount())
            .append("activityWorkShowCount", getActivityWorkShowCount())
            .append("activitySurveyCount", getActivitySurveyCount())
            .append("activityExtendedCount", getActivityExtendedCount())
            .append("activityBrainstormingCount", getActivityBrainstormingCount())
            .append("activityQuestionCount", getActivityQuestionCount())
            .append("homeworkGradeDistribution", getHomeworkGradeDistribution())
            .append("examGradeDistribution", getExamGradeDistribution())
            .append("createdBy", getCreatedBy())
            .append("createTime", getCreateTime())
            .append("updatedBy", getUpdatedBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
        .toString();
        }
        }

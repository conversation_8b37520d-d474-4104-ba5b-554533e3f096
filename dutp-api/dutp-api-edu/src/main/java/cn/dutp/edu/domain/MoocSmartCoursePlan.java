package cn.dutp.edu.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 教务课程对象 mooc_smart_course_plan
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Data
@TableName("mooc_smart_course_plan")
public class MoocSmartCoursePlan extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 课程ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long courseId;

    /**
     * 课程名称
     */
    @Excel(name = "课程名称")
    private String courseName;

    /**
     * 课程编码 (0-60个字符)
     */
    @Excel(name = "课程编码 (0-60个字符)")
    private String courseCode;

    /**
     * 所属专业大类ID
     */
    @Excel(name = "所属专业大类ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long categoryId;

    /**
     * 关联云教材ID
     */
    @Excel(name = "关联云教材ID ")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 课程描述 (最多200字符)
     */
    @Excel(name = "课程描述 (最多200字符)")
    private String description;

    /**
     * 课时（小时）
     */
    @Excel(name = "课时", readConverterExp = "小=时")
    private Long hour;

    /**
     * 课程封面图片URL
     */
    @Excel(name = "课程封面图片URL")
    private String coverImageUrl;

    /**
     * 绑定的教师集合，userId逗号分隔
     */
    @Excel(name = "绑定的教师集合，userId逗号分隔")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private String userIds;

    /**
     * 课程状态 0启用1禁用
     */
    @Excel(name = "课程状态 0启用1禁用")
    private Integer status;

    /**
     * 创建者用户ID
     */
    @Excel(name = "创建者用户ID")
    private String createdBy;

    /**
     * 最后更新者用户ID
     */
    @Excel(name = "最后更新者用户ID")
    private String updatedBy;

    @TableField(exist = false)
    private String realName;

    @TableField(exist = false)
    @Excel(name = "所属学院")
    private String schoolName;

    @TableField(exist = false)
    private Long schoolId;

    @TableField(exist = false)
    private String bookName;

    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> userIdList;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;


    @Excel(name = "课程状态")
    @TableField(exist = false)
    private String statusStr;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("courseId", getCourseId())
                .append("courseName", getCourseName())
                .append("courseCode", getCourseCode())
                .append("categoryId", getCategoryId())
                .append("bookId", getBookId())
                .append("description", getDescription())
                .append("hour", getHour())
                .append("coverImageUrl", getCoverImageUrl())
                .append("userIds", getUserIds())
                .append("status", getStatus())
                .append("createdBy", getCreatedBy())
                .append("updatedBy", getUpdatedBy())
                .append("delFlag", getDelFlag())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

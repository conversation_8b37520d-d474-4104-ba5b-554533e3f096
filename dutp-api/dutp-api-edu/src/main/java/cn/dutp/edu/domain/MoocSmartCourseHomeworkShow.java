package cn.dutp.edu.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 互动课堂的作业对象 mooc_smart_course_homework_show
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName("mooc_smart_course_homework_show")
public class MoocSmartCourseHomeworkShow extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** 作业秀ID */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long homeworkShowId;

    /** 课程ID */
        @Excel(name = "课程ID")
    private Long courseId;

    /** 创建者ID */
        @Excel(name = "创建者ID")
    private Long workShowCreatorId;

    /** 作品秀标题 */
        @Excel(name = "作品秀标题")
    private String workShowTitle;

    /** 作品秀题干 */
        @Excel(name = "作品秀题干")
    private String workShowContent;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("homeworkShowId", getHomeworkShowId())
            .append("courseId", getCourseId())
            .append("workShowCreatorId", getWorkShowCreatorId())
            .append("workShowTitle", getWorkShowTitle())
            .append("workShowContent", getWorkShowContent())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
        .toString();
        }
        }

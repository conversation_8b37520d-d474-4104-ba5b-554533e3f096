package cn.dutp.edu.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 用户资源库对象 mooc_user_resource
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@TableName("mooc_user_resource")
public class MoocUserResource extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** $column.columnComment */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long resourceId;

    /** sys_user里的user_id */
        @Excel(name = "sys_user里的user_id")
    private Long userId;

    /** 文件名 */
        @Excel(name = "文件名")
    private String fileName;

    /** 文件地址 */
        @Excel(name = "文件地址")
    private String fileUrl;

    /** 文件类型1=图片，2=音频，3=视频，4=虚拟仿真，5=AR/VR，6=3D模型，7习题（弃用），8课件 */
        @Excel(name = "文件类型1=图片，2=音频，3=视频，4=虚拟仿真，5=AR/VR，6=3D模型，7习题", readConverterExp = "弃=用")
    private String fileType;

    /** 文件大小 */
        @Excel(name = "文件大小")
    private Long fileSize;

    /** 习题的题目ID */
        @Excel(name = "习题的题目ID")
    private Long questionId;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 文件夹id */
        @Excel(name = "文件夹id")
    private Long folderId;

    /** 文件夹名 */
    @Excel(name = "文件夹名")
    @TableField(exist = false)
    private String folderName;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("resourceId", getResourceId())
            .append("userId", getUserId())
            .append("fileName", getFileName())
            .append("fileUrl", getFileUrl())
            .append("fileType", getFileType())
            .append("fileSize", getFileSize())
            .append("questionId", getQuestionId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .append("folderId", getFolderId())
            .append("folderName", getFolderName())
        .toString();
        }
        }

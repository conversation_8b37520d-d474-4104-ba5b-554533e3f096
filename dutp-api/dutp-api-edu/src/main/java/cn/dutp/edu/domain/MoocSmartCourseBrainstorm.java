package cn.dutp.edu.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import cn.dutp.edu.domain.dto.DutpUserCommonFileDto;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;

/**
 * 互动课堂的头脑风暴对象 mooc_smart_course_brainstorm
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName("mooc_smart_course_brainstorm")
public class MoocSmartCourseBrainstorm extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** 头脑风暴ID */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long brainstormId;

    /** 课程ID (mooc_smart_course) */
    @Excel(name = "课程ID (mooc_smart_course)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long courseId;

    /** 班级ID (mooc_smart_course_class) */
    @Excel(name = "班级ID (mooc_smart_course_class)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long classId;

    /** 课堂ID (mooc_smart_course_lesson) */
    @Excel(name = "课堂ID (mooc_smart_course_lesson)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long lessonId;

    /** 头脑风暴标题 */
    @Excel(name = "头脑风暴标题")
    private String brainstormTitle;

    /** 活动状态 (1: 未开始, 2: 进行中, 3: 已结束) */
    @Excel(name = "活动状态 (1: 未开始, 2: 进行中, 3: 已结束)")
    private Integer status;

    /** 签到活动开始时间 (创建时间) */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "活动开始时间 (创建时间)", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date brainstormStartTime;

    /** 签到活动实际结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "活动实际结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date brainstormEndTime;

    /** 头脑风暴内容 */
    @Excel(name = "头脑风暴内容")
    private String brainstormContent;

    /** 创建者ID */
    @Excel(name = "创建者ID")
    private Long brainstormCreatorId;

    /** 头脑风暴备注 */
    @Excel(name = "头脑风暴备注")
    private String brainstormRemark;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 文件列表 */
    @TableField(exist = false)
    private List<DutpUserCommonFileDto> fileList;

    /**  回复列表 */
    @TableField(exist = false)
    private List<MoocSmartCourseBrainstormReply> replyList;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("brainstormId", getBrainstormId())
                .append("courseId", getCourseId())
                .append("brainstormTitle", getBrainstormTitle())
                .append("brainstormContent", getBrainstormContent())
                .append("brainstormCreatorId", getBrainstormCreatorId())
                .append("brainstormRemark", getBrainstormRemark())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

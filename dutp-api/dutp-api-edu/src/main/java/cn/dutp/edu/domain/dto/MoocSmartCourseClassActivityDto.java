package cn.dutp.edu.domain.dto;

import cn.dutp.common.core.web.domain.BaseEntity;
import cn.dutp.edu.domain.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 互动课堂班级活动Dto对象
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Data
public class MoocSmartCourseClassActivityDto extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 活动类型 */
    private Integer type;

    /**
     * 单次课堂ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long lessonId;
    /**
     * 所属班级ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long classId;

    /** 互动课堂的单次课堂对象 */
    private MoocSmartCourseLesson moocSmartCourseLesson;

    /** 互动课堂班级签到活动对象 */
    private MoocSmartCourseClassAttendance moocSmartCourseClassAttendance;

    /** 互动课堂班级提问活动对象 */
    private MoocSmartCourseClassQuestion moocSmartCourseClassQuestion;

    /** 互动课堂班级头脑风暴活动对象 */
    private MoocSmartCourseBrainstorm moocSmartCourseBrainstorm;

    /** 互动课堂的拓展内容活动对象 */
    private MoocSmartCourseExtension moocSmartCourseExtension;

}

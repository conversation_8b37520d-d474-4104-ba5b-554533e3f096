package cn.dutp.edu.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 教师个人资源文件夹对象 mooc_user_resource_folder
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@TableName("mooc_user_resource_folder")
public class MoocUserResourceFolder extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** $column.columnComment */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userFolderId;

    /** 文件夹名称 */
        @Excel(name = "文件夹名称")
    private String folderName;

    /** 1普通文件夹2题库【弃用】 */
        @Excel(name = "1普通文件夹2题库【弃用】")
    private Integer folderType;

    /** 父级ID */
        @Excel(name = "父级ID")
    private Long parentId;

    /** sys_user中user_id */
        @Excel(name = "sys_user中user_id")
    private Long userId;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 文件类型1默认文件夹 */
        @Excel(name = "文件类型1默认文件夹")
    private String defaultType;




@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("userFolderId", getUserFolderId())
            .append("folderName", getFolderName())
            .append("folderType", getFolderType())
            .append("parentId", getParentId())
            .append("userId", getUserId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .append("defaultType", getDefaultType())
        .toString();
        }
        }

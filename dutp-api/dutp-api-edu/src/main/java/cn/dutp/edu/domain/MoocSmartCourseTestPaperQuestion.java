package cn.dutp.edu.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.domain.MoocUserQuestion;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 互动课堂试卷小题对象 mooc_smart_course_test_paper_question
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName("mooc_smart_course_test_paper_question")
public class MoocSmartCourseTestPaperQuestion extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** $column.columnComment */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long paperQuestionId;

    /** $column.columnComment */
        @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long paperId;

    /** $column.columnComment */
        @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long collectionId;

    /** $column.columnComment */
        @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long questionId;

    /** 排序 */
        @Excel(name = "排序")
    private Integer sort;

    /** 分值 */
        @Excel(name = "分值")
    private Long questionScore;

    /** 题目内容 */
    @TableField(exist = false)
    private MoocUserQuestion questionContent;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("paperQuestionId", getPaperQuestionId())
            .append("paperId", getPaperId())
            .append("collectionId", getCollectionId())
            .append("questionId", getQuestionId())
            .append("sort", getSort())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("questionScore", getQuestionScore())
        .toString();
        }
        }

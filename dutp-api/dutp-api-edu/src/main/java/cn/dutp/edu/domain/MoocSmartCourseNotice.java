package cn.dutp.edu.domain;

    import java.util.Date;
    import com.fasterxml.jackson.annotation.JsonFormat;
import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 互动课堂公告对象 mooc_smart_course_notice
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName("mooc_smart_course_notice")
public class MoocSmartCourseNotice extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** 公告ID */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long noticeId;

    /** 所属课程ID (mooc_smart_course) */
        @Excel(name = "所属课程ID (mooc_smart_course)")
    private Long courseId;

    /** 所属班级ID (mooc_smart_course_class) */
        @Excel(name = "所属班级ID (mooc_smart_course_class)")
    private Long classId;

    /** 公告标题 */
        @Excel(name = "公告标题")
    private String title;

    /** 公告内容  */
        @Excel(name = "公告内容 ")
    private String content;

    /** 计划发布时间 */
        @JsonFormat(pattern = "yyyy-MM-dd")
        @Excel(name = "计划发布时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date publishTime;

    /** 公告状态 (0待发布, 1已发布 */
        @Excel(name = "公告状态 (0待发布, 1已发布")
    private Integer status;

    /** 创建者用户ID */
        @Excel(name = "创建者用户ID")
    private String createdBy;

    /** 最后更新者用户ID */
        @Excel(name = "最后更新者用户ID")
    private String updatedBy;

    /** 删除标志 (0: 存在, 2: 删除) */
    private String delFlag;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("noticeId", getNoticeId())
            .append("courseId", getCourseId())
            .append("classId", getClassId())
            .append("title", getTitle())
            .append("content", getContent())
            .append("publishTime", getPublishTime())
            .append("status", getStatus())
            .append("createdBy", getCreatedBy())
            .append("createTime", getCreateTime())
            .append("updatedBy", getUpdatedBy())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
        .toString();
        }
        }

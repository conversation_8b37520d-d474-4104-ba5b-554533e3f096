package cn.dutp.edu.domain;

    import java.util.Date;
    import com.fasterxml.jackson.annotation.JsonFormat;
import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 互动课堂的问卷调查对象 mooc_smart_course_chat
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName("mooc_smart_course_chat")
public class MoocSmartCourseChat extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** 主键ID */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chatId;

    /** 班级ID */
        @Excel(name = "班级ID")
    private Long classId;

    /** 创建者ID */
        @Excel(name = "创建者ID")
    private Long creatorId;

    /** 互动讨论标题 */
        @Excel(name = "互动讨论标题")
    private String chatTitle;

    /** 互动讨论主题 */
        @Excel(name = "互动讨论主题")
    private String chatTheme;

    /** 作业提交/考试开始时间 */
        @JsonFormat(pattern = "yyyy-MM-dd")
        @Excel(name = "作业提交/考试开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 作业提交/考试截至时间 */
        @JsonFormat(pattern = "yyyy-MM-dd")
        @Excel(name = "作业提交/考试截至时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    /** 是否删除（0：存在，2：已删除） */
    private String delFlag;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("chatId", getChatId())
            .append("classId", getClassId())
            .append("creatorId", getCreatorId())
            .append("chatTitle", getChatTitle())
            .append("chatTheme", getChatTheme())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("remark", getRemark())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
        .toString();
        }
        }

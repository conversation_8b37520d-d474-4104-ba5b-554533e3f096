package cn.dutp.edu.domain;

    import java.util.Date;
    import com.fasterxml.jackson.annotation.JsonFormat;
import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.List;

/**
 * 学生作业提交记录对象 mooc_smart_course_homework_submission
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@TableName("mooc_smart_course_homework_submission")
public class MoocSmartCourseHomeworkSubmission extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** 提交记录ID */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long submissionId;

    /** 关联的作业/考试ID */
        @Excel(name = "关联的作业/考试ID")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long assignmentId;

    /** 学生ID */
        @Excel(name = "学生ID")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long studentId;

    /** 提交的文本内容 */
        @Excel(name = "提交的文本内容")
    private String submitContent;

    /** 提交时间 */
        @JsonFormat(pattern = "yyyy-MM-dd")
        @Excel(name = "提交时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date submitTime;

    /** 删除标志（0：存在，2：已删除） */
    private String delFlag;

    /**
     * 提交的文件列表
     */
    @TableField(exist = false)
    private List<MoocSmartCourseHomeworkSubmissionFile> files;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("submissionId", getSubmissionId())
            .append("assignmentId", getAssignmentId())
            .append("studentId", getStudentId())
            .append("submitContent", getSubmitContent())
            .append("submitTime", getSubmitTime())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
        .toString();
        }
        }

package cn.dutp.edu.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.time.LocalTime;
import java.util.Date;

/**
 * 互动课堂班级签到活动对象 mooc_smart_course_class_attendance
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName("mooc_smart_course_class_attendance")
public class MoocSmartCourseClassAttendance extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** 签到活动ID */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long attendanceActivityId;

    /** 课程ID (mooc_smart_course) */
    @Excel(name = "课程ID (mooc_smart_course)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long courseId;

    /** 班级ID (mooc_smart_course_class) */
    @Excel(name = "班级ID (mooc_smart_course_class)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long classId;

    /** 课堂ID (mooc_smart_course_lesson) */
    @Excel(name = "课堂ID (mooc_smart_course_lesson)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long lessonId;

    /** 签到标题 (例如: 2025-04-09 09:35:22的签到) */
    @Excel(name = "签到标题 (例如: 2025-04-09 09:35:22的签到)")
    private String title;

    /** 签到类型 (1: 教师签到, 2: 手势签到, 3: 签到码) */
    @Excel(name = "签到类型 (1: 教师签到, 2: 手势签到, 3: 签到码)")
    private Integer attendanceType;

    /** 结束方式 (1: 手动结束, 2: 定时自动结束) */
    @Excel(name = "结束方式 (1: 手动结束, 2: 定时自动结束)")
    private Integer endMethod;

    /** 定时结束时长 (分钟, 当end_method=2时) */
    @JsonFormat(pattern = "HH:mm:ss")
    @Excel(name = "定时结束时长 (分钟, 当end_method=2时)")
    private LocalTime durationMinutes;

    /** 签到码 (当attendance_type=3或2时，如果是2代码手势顺序) */
    @Excel(name = "签到码 (当attendance_type=3或2时，如果是2代码手势顺序)")
    private String signInCode;

    /** 签到手势  */
    @Excel(name = "签到手势")
    private String gesturePattern;

    /** 活动状态 (2: 进行中, 3: 已结束) */
    @Excel(name = "活动状态 (2: 进行中, 3: 已结束)")
    private Integer status;

    /** 签到活动开始时间 (创建时间) */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "签到活动开始时间 (创建时间)", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date activityStartTime;

    /** 签到活动实际结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "签到活动实际结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date activityEndTime;

    /** 删除标志 (0: 存在, 2: 删除) */
    private String delFlag;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("attendanceActivityId", getAttendanceActivityId())
                .append("courseId", getCourseId())
                .append("classId", getClassId())
                .append("lessonId", getLessonId())
                .append("title", getTitle())
                .append("attendanceType", getAttendanceType())
                .append("endMethod", getEndMethod())
                .append("durationMinutes", getDurationMinutes())
                .append("signInCode", getSignInCode())
                .append("status", getStatus())
                .append("activityStartTime", getActivityStartTime())
                .append("activityEndTime", getActivityEndTime())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("delFlag", getDelFlag())
                .toString();
    }
}

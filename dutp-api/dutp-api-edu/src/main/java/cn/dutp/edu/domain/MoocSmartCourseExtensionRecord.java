package cn.dutp.edu.domain;

    import java.util.Date;
    import com.fasterxml.jackson.annotation.JsonFormat;
import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableField;

import java.util.List;

/**
 * 互动课堂的拓展内容学生记录对象 mooc_smart_course_extension_record
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName("mooc_smart_course_extension_record")
public class MoocSmartCourseExtensionRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** 主键ID */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long recordId;

    /** 拓展ID */
    @Excel(name = "拓展ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long extensionId;

    /** 学生ID */
        @Excel(name = "学生ID")
    private Long studentId;

    /** 扩展学习内容 */
        @Excel(name = "扩展学习内容")
    private String questionContent;

    /** 提交时间 */
        @JsonFormat(pattern = "yyyy-MM-dd")
        @Excel(name = "提交时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date submitTime;

    /** 是否删除（0：存在，2：已删除） */
    private String delFlag;

    /**
     * 拓展内容记录文件列表
     */
    @TableField(exist = false)
    private List<MoocSmartCourseExtensionRecordFile> files;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("recordId", getRecordId())
            .append("extensionId", getExtensionId())
            .append("studentId", getStudentId())
            .append("questionContent", getQuestionContent())
            .append("submitTime", getSubmitTime())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
        .toString();
        }
        }

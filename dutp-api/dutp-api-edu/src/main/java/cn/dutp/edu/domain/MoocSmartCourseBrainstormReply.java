package cn.dutp.edu.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 互动课堂的头脑风暴讨论成员对象 mooc_smart_course_brainstorm_reply
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName("mooc_smart_course_brainstorm_reply")
public class MoocSmartCourseBrainstormReply extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** ID */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long replyId;

    /** 学生ID */
        @Excel(name = "学生ID")
    private Long studentId;

    /** 活动ID */
        @Excel(name = "活动ID")
    private Long doingsId;

    /** 活动回复 */
        @Excel(name = "活动回复")
    private String doingsRespond;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 真实姓名 */
    @TableField(exist = false)
    private String realName;
    /** 昵称 */
    @TableField(exist = false)
    private String nickName;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("replyId", getReplyId())
            .append("studentId", getStudentId())
            .append("doingsId", getDoingsId())
            .append("doingsRespond", getDoingsRespond())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
        .toString();
        }
        }

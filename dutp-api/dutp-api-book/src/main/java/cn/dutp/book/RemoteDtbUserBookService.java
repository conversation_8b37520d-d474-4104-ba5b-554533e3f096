package cn.dutp.book;

import cn.dutp.book.domain.DtbUserBook;
import cn.dutp.book.factory.RemoteDtbUserBookFallbackFactory;
import cn.dutp.common.core.constant.ServiceNameConstants;
import cn.dutp.common.core.domain.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 学生教师端学生/教师书架
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteDtbUserBookService", value = ServiceNameConstants.BOOK_SERVICE, fallbackFactory = RemoteDtbUserBookFallbackFactory.class)
public interface RemoteDtbUserBookService
{
    /**
     * 兑换购书码状态变更
     */
    @PutMapping("/userBook/editStatus")
    public R<Object> editStatus(@RequestBody DtbUserBook dtbUserBook);
}

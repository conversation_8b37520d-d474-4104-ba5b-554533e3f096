package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 教材章节目录对象 dtb_book_chapter_catalog
 *
 * <AUTHOR>
 * @date 2024-11-30
 */
@Data
@TableName("dtb_book_chapter_catalog")
public class DtbBookChapterCatalog extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 目录id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long catalogId;

    /**
     * 目录id
     */
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long oldCatalogId;

    /**
     * 标题内容
     */
    @Excel(name = "标题内容")
    private String title;

    /**
     * 章节ID
     */
    @Excel(name = "章节ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;

    /**
     * 教材ID
     */
    @Excel(name = "教材ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 父级目录
     */
    @Excel(name = "父级目录")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    /**
     * 前端domId
     */
    @Excel(name = "前端domId")
    private String domId;

    /**
     * 版本ID
     */
    @Excel(name = "版本ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long versionId;

    /**
     * 页码
     */
    private Integer pageNumber;

    @TableField(exist = false)
    private List<DtbBookChapterCatalog> children;

    /**
     * 章节名称
     */
    @TableField(exist = false)
    private String chapterName;

    /**
     * 是否免费1付费2免费
     */
    @TableField(exist = false)
    private Integer free;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("catalogId", getCatalogId())
                .append("title", getTitle())
                .append("chapterId", getChapterId())
                .append("bookId", getBookId())
                .append("parentId", getParentId())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("free", getFree())
                .append("chapterName", getChapterName())
                .toString();
    }
}

package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * DUTP-DTB_014学生/教师书架对象 dtb_user_book
 *
 * <AUTHOR>
 * @date 2024-12-04
 */
@Data
public class DtbUserBook extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /**  */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userBookId;

    /** 用户ID */
    @Excel(name = "用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /** 教材ID */
    @Excel(name = "教材ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /** 版本ID */
    @Excel(name = "版本ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long versionId;

    /** 中图分类ID */
    @Excel(name = "中图分类ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookTypeId;

    /** 来源1购买2校本教材3试用 */
    @Excel(name = "来源1购买2校本教材3试用")
    private Integer addWay;


    /** 阅读百分比 */
    @Excel(name = "阅读百分比")
    private Integer readRate;


    /** 过期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "过期时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expireDate;

    /** 最后的查看时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最后的查看时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lastSeeDate;

    /** 排序 */
    @Excel(name = "排序")
    private Long sort;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /**
     * codeId
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long codeId;

    @TableField(exist = false)
    private String bookName;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("userBookId", getUserBookId())
                .append("userId", getUserId())
                .append("bookId", getBookId())
                .append("versionId", getVersionId())
                .append("bookTypeId", getBookTypeId())
                .append("addWay", getAddWay())
                .append("readRate", getReadRate())
                .append("expireDate", getExpireDate())
                .append("lastSeeDate", getLastSeeDate())
                .append("sort", getSort())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("delFlag", getDelFlag())
                .append("codeId", getCodeId())
                .append("bookName", getBookName())
                .toString();
    }
}

package cn.dutp.message.api.factory;

import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.message.api.RemoteUserMessageService;
import cn.dutp.message.api.domain.DutpUserMessage;
import cn.dutp.common.core.domain.R;
import cn.dutp.message.api.domain.form.BookFaultForm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 用户服务降级处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteUserMessageFallbackFactory implements FallbackFactory<RemoteUserMessageService>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteUserMessageFallbackFactory.class);

    @Override
    public RemoteUserMessageService create(Throwable throwable)
    {
        log.error("消息服务调用失败:{}", throwable.getMessage());
        return new RemoteUserMessageService()
        {
            @Override
            public R<Boolean> addMessage(DutpUserMessage dutpUserMessage) {
                log.error("添加消息失败，触发事务回滚: {}", throwable.getMessage());
                return R.fail("添加消息失败，触发事务回滚:" + throwable.getMessage());
            }

            @Override
            public R<Boolean>  saveReaderBookFault(BookFaultForm bookFaultForm) {
                log.error("添加消息失败，触发事务回滚: {}", throwable.getMessage());
                return R.fail("添加消息失败，触发事务回滚:" + throwable.getMessage());
            }
        };
    }
}

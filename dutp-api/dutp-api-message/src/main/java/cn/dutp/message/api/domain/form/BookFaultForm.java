package cn.dutp.message.api.domain.form;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.List;

@Data
public class BookFaultForm {
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;
    private String copyText;
    private String color;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;
    private Integer pageNumber;
    private String fromWordId;
    private String endWordId;
    private String faultText;
    private String comment;
    private String faultType;
    private List<DtbUserBookFeedbackImageForm> images;
}

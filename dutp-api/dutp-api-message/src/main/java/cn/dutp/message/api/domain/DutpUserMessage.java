package cn.dutp.message.api.domain;

import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 用户消息对象 dutp_user_message
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Data
public class DutpUserMessage extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long messageId;

    private String content;

    private String title;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long fromUserId;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long toUserId;

    /**
     * 教材ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 1系统消息2其他消息3推送消息
     */
    private Integer messageType;

    /**
     * 用户类型1后台用户2前台用户
     */
    private Integer fromUserType;

    /**
     * 用户类型1后台用户2前台用户
     */
    private Integer toUserType;

    /**
     * 阅读状态
     */
    private Integer readFlag;

    /**
     * 业务Id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long businessId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;
}

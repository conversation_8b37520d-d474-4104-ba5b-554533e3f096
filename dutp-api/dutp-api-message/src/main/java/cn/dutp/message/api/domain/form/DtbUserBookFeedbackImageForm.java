package cn.dutp.message.api.domain.form;

import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

@Data
public class DtbUserBookFeedbackImageForm
{
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long feedbackImageId;

    private String fileName;

    private String fileUrl;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long feedbackId;

}

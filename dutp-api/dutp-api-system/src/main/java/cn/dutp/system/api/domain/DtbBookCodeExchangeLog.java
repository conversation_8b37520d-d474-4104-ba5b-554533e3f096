package cn.dutp.system.api.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 购书码兑换记录对象 dtb_book_code_exchange_log
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@Data
public class DtbBookCodeExchangeLog extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long logId;

    /**
     * $column.columnComment
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long userId;

    /**
     * $column.columnComment
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long bookId;

    /**
     * $column.columnComment
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long codeId;

    /**
     * 兑换日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "兑换日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date exchangeDate;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("logId", getLogId())
                .append("userId", getUserId())
                .append("bookId", getBookId())
                .append("codeId", getCodeId())
                .append("exchangeDate", getExchangeDate())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

package cn.dutp.system.api;

import cn.dutp.common.core.constant.SecurityConstants;
import cn.dutp.common.core.constant.ServiceNameConstants;
import cn.dutp.common.core.domain.R;
import cn.dutp.system.api.domain.DutpUser;
import cn.dutp.system.api.domain.DutpUserWithCode;
import cn.dutp.system.api.domain.SysUser;
import cn.dutp.system.api.factory.RemoteUserFallbackFactory;
import cn.dutp.system.api.model.LoginDutpUser;
import cn.dutp.system.api.model.LoginUser;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteUserService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteUserFallbackFactory.class)
public interface RemoteUserService
{
    /**
     * 通过用户名查询用户信息
     *
     * @param username 用户名
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping("/user/info/{username}")
    public R<LoginUser> getUserInfo(@PathVariable("username") String username, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 通过用户电话查询用户信息
     *
     * @param userTel 用户电话
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping("/user/infoByTel/{userTel}")
    public R<LoginUser> infoByTel(@PathVariable("userTel") String userTel, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 通过用户电话查询用户信息
     *
     * @param userTel 用户电话
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping("/dutpUser/infoByTel/{userTel}")
    public R<LoginUser> infoDutpUserByTel(@PathVariable("userTel") String userTel, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 通过用户名查询教师，学生，无身份者用户信息
     *
     * @param username 用户名
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping("/dutpUser/info/{username}")
    public R<LoginDutpUser> getDutpUserInfo(@PathVariable("username") String username, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 注册用户信息
     *
     * @param sysUser 用户信息
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping("/user/register")
    public R<Boolean> registerUserInfo(@RequestBody SysUser sysUser, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 注册教师，学生，无身份者用户信息
     *
     * @param dutpUser 用户信息
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping("/dutpUser/register")
    public R<Boolean> registerDutpUserUserInfo(@RequestBody DutpUser dutpUser, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 注册教师，学生，无身份者用户信息
     *
     * @param dutpUser 用户信息
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping("/dutpUser/forgotPassword")
    public R<Boolean> forgotPasswordEducation(@RequestBody DutpUserWithCode dutpUser, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 记录用户登录IP地址和登录时间
     *
     * @param sysUser 用户信息
     * @param source 请求来源
     * @return 结果
     */
    @PutMapping("/user/recordlogin")
    public R<Boolean> recordUserLogin(@RequestBody SysUser sysUser, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/user/eduInfo/{username}")
    R<LoginUser> getEduUserInfo(@PathVariable("username") String username,  @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/user/infoEduByTel/{userTel}")
    public R<LoginUser> infoEduByTel(@PathVariable("userTel") String userTel, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据角色查询用户列表
     * @param roleId
     * @return
     */
    @GetMapping("/user/listUserByRoleId/{roleId}")
    public R<List<SysUser>> listUserByRoleId(@PathVariable("roleId") Long roleId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据菜单查询所有有权限的用户列表
     * @param menuId
     * @return
     */
    @GetMapping("/user/listUserByMenuId/{menuId}")
    public R<List<SysUser>> listUserByMenuId(@PathVariable("menuId") Long menuId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据教材id和学校id查询教材绑定的用户列表
     * @param user
     * @return
     */
    @PostMapping("/dutpUser/listUserByBookIdAndSchoolId")
    public R<List<DutpUser>> listUserByBookIdAndSchoolId(@RequestBody DutpUser user, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}

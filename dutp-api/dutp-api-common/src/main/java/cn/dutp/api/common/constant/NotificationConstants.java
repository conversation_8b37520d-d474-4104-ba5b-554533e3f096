package cn.dutp.api.common.constant;

/**
 * <AUTHOR>
 */
public class NotificationConstants {
    public static final String BOOK_PUSH = "亲爱的用户，为您推送《%s》书籍。";
    public static final String BOOK_RECALL = "亲爱的用户，《%s》已被召回，无法阅读，请您尽快提交退款申请进行退款操作。";
    public static final String BOOK_UPDATE = "您好，《%s》已更新到新版本。";
    public static final String BOOK_CORRECTION_PROCESSED = "您好，您提交关于《%s》的纠错信息已被处理。";
    public static final String FEEDBACK = "您好，感谢您提交的意见反馈。根据反【馈编号：%s】，我们已对您的反馈进行了处理。";
    public static final String BOOK_CODE_REDEEMED = "尊敬的用户，您已成功兑换《%s》购书码。将于%s到期，现在您就可以开启精彩的阅读之旅啦。";
    public static final String BOOK_CODE_REDEEMED_LONG = "尊敬的用户，您已成功兑换《%s》购书码。现在您就可以开启精彩的阅读之旅啦。";
    public static final String TRIAL_EXPIRING = "尊敬的用户，您的《%s》试读只剩3天啦，到期后若未购买将无法继续使用。若您在试用期间觉得满意，欢迎及时前往购买，让我们继续为您提供优质服务哦。";
    public static final String REFUND_REJECTED = "尊敬的用户，很抱歉，您关于【订单编号：%s】的售后申请未通过审核，驳回原因：%s。如果您有异议，请在%s日内联系我们的客服进行申诉，我们会重新为您审核。";
    public static final String INVOICE_REJECTED = "尊敬的用户，很抱歉，您关于【订单编号：%s】的重新开票申请未通过，驳回原因：%s，感谢您的关注与支持。如有疑问，请联系我们。";
    public static final String INVOICE_REJECTED_SALES = "尊敬的用户，很抱歉，您关于【结算单编号：%s】的重新开票申请未通过，驳回原因：%s，感谢您的关注与支持。如有疑问，请联系我们。";
    public static final String REISSUE_INVOICE = "您好【申请人：%s】发起了开票申请，请及时处理。【电商中心-发票管理】内查看";
    public static final String OPEN_INVOICE_SALE = "尊敬的用户您好，您的发票开具成功，点击可查看及下载电子发票，消费者点击消息后，可进入详情页查看。";
    public static final String OPEN_INVOICE_EDU = "尊敬的用户您好，您的发票开具成功，点击可查看及下载电子发票，消费者点击消息后，可进入详情页查看。";
    public static final String TRIAL_APPROVED = "尊敬的用户您好，《%s》的试用申请已通过，试用时间为%s天，现在您就可以开启精彩的阅读之旅啦。";
    public static final String TRIAL_REJECTED = "尊敬的用户，很抱歉，《%s》的试用申请未通过审核，驳回原因：%s，感谢您的关注与支持。如有疑问，请联系我们。";
    public static final String TRIAL_PERIOD_REMINDER = "尊敬的用户，您的《%s》试用期只剩3天啦，到期后若未购买将无法继续使用。若您在试用期间觉得满意，欢迎及时前往购买，让我们继续为您提供优质服务哦。";
    public static final String TEACHER_APPROVED = "尊敬的用户您好，您已通过教师认证，可以开启您的教师身份之旅啦！";
    public static final String TEACHER_REJECTED = "尊敬的用户您好，您的教师认证未通过审核，驳回原因：%s，如有疑问，请联系我们。";
    public static final String TEACHER_IDENTITY = "您好，用户“%s”提交了身份认证申请，请及时处理。[教师管理-教师认证]";
    public static final String BOOK_CODE_REDEEMED_TITLE = "购书码兑换提醒";
    public static final String TEACHER_TITLE = "教师审核提醒";
    public static final String TRIAL_REVIEW_REMINDER = "您好，%s学校教师，“%s”提交了试用申请，请及时处理。[电商中心-试用管理]";
    public static final String TRIAL_REVIEW_REMINDER_TITLE = "试用审核提醒";



}

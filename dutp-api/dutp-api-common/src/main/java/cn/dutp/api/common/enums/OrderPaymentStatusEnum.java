package cn.dutp.api.common.enums;
/**
 *支付状态
 */
public enum OrderPaymentStatusEnum {
    N("N", "待支付"),
    A("A", "已支付"),
    F("F", "支付失败");

    private final String code;
    private final String description;

    OrderPaymentStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static OrderPaymentStatusEnum fromCode(String code) {
        for (OrderPaymentStatusEnum status : OrderPaymentStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown OrderPaymentStatusEnum code: " + code);
    }
}

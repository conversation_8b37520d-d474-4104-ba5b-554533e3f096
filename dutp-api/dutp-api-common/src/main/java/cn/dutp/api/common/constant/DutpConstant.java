package cn.dutp.api.common.constant;

public class DutpConstant {
    public static final String DEMO = "DEMO";
    public static final String OPERATION_SUCCESSFUL= "申请成功";
    public static final String OPERATION_FAILED  = "申请失败";
    public static final String EXCEEDING_THE_TRIAL_QUANTITY = "超出试用申请数量";
    public static final String TIME_IS_NOT_OVER_YET = "试用时间未结束，无法提交试用申请";
    public static final String NOT_ACTIVE = "该教材已提交过试用申请，请到个人中心-申请历史中激活";
    public static final String PENDING_APPROVAL = "存在待审核的提交";
    public static final Integer NUM_TWO = 2;
    public static final Integer NUM_ONE = 1;
    public static final Integer NUM_THREE = 3;
    public static final Integer NUM_FOUR = 4;
    public static final Integer NUM_ZERO = 0;
    public static final String REDIS_COPY_TIME_KEY="COPY_WORD_TIME:";
    public static final String DATE_FORMAT = "yyyyMMdd";
    public static final String BUSINESS_CODE = "SZJC";
    public static final String BUSINESSTD_CODE = "SZTD";
    public static final String STATMENT_CODE = "JS";
    public static final Long LONG_ZERO = 0L;
    public static final Long LONG_ONE = 1L;
    public static final String CANCELLED = "cancelled";
    public static final String RECALL_TEXTBOOKS = "召回教材";
    public static final String BOOK_CODE_REDEMPTION_FAILED = "购书码兑换失败!";
    public static final String ADDITION_FAILED = "新增失败!";
    public static final String REJECT_FAILED = "驳回失败!";
    public static final String UPDATE_FAILED = "修改失败!";
    public static final String PASS_FAILED = "审批失败!";
    public static final String CHANGE_INVOICE_FAILED = "已经提交换开，请等待！";
    public static final String APPLY_FOR_INVOICE_FAILED = "该订单已申请发票，请等待！";
    public static final Integer SUCCESS_CODE = 200;
    public static final String VERIFICATION_CODE_ERROR = "验证码错误！";
    public static final String USER_USERNAME_PASSWORD_NOT_NULL = "用户/密码必须填写！";
    public static final String USER_PASSWORD_NOT_MATCH = "用户密码不在指定范围！";
    public static final String USER_USERNAME_NOT_MATCH = "用户名不在指定范围！";
    public static final String USER_BLACK_LIST = "很遗憾，访问IP已被列入系统黑名单！";
    public static final String USER_PASSWORD_DELETE = "对不起，您的账号已被删除！";
    public static final String USER_BLOCKED = "用户已停用，请联系管理员！";
    public static final String USER_NO_SCHOOL_ID = "对不起，您的账号未绑定学校！";
    public static final String USER_USERNAME_VERIFICATION_CODE_NOT_NULL = "电话/验证码必须填写！";
    public static final String USER_PHONE_UNREGISTERED = "电话未注册！";
    public static final String LOGIN_SUCCESS = "登录成功！";
    public static final String PASSWORD_RECOVERY_SUCCESSFUL = "找回密码成功！";
    public static final String LOGOUT_SUCCESSFUL  = "退出成功！";
    public static final String BOOK_PURCHASE_CODE = "购书码错误！";
    public static final String DATE_FORMAT_YYYY_MM_DD = "yyyy-MM-dd";
    public static final String MAX_DATE = "9999-12-31";
    public static final String INITIAL_PASSWORD = "123456";
    public static final String QR_CODE_UUID = "QR_CODE_UUID";
    public static final String NOT_SCAN = "NOT_SCAN";
    public static final String SCANNED = "SCANNED";
    public static final String VERIFIED = "VERIFIED";
    public static final String PASSWORD_ERROR = "密码错误！";
    public static final String STR_TWO = "2";
    public static final String STR_ONE = "1";
    public static final String STR_ZERO = "0";
    public static final String SENDING_MESSAGE_FAILED  = "发送消息失败";
    public static final Integer REFUND_RESOURCE_USER = 0;
    public static final Integer REFUND_RESOURCE_ADMIN = 1;
    public static final Integer REFUND_RESOURCE_SCHOOL = 2;
    public static final String PASSWORD_NOT_MATCH = "密码输入错误，请重新输入！";
    public static final String NO_CLASS = "抱歉，该课班码未查询到班级！";
    public static final String REPEAT_OPERATION = "您已经加入该班级，请勿重复操作！";
    public static final String SIGNIN_TEACHER = "教师签到";
    public static final String SIGNIN_TYPE_ONE = "手动签到";
    public static final String SIGNIN_TYPE_TWO = "手势签到";
    public static final String SIGNIN_TYPE_THREE = "签到码";
    public static final Integer NUM_FIVE = 5;
    public static final Integer NUM_SIX = 6;
    public static final Integer NUM_SEVEN = 7;
    public static final Integer NUM_EIGHT = 8;
    public static final Integer NUM_NINE = 9;
    public static final String QUESTION_TYPE_ZERO = "随机提问";
    public static final String QUESTION_TYPE_ONE = "点名提问";

}

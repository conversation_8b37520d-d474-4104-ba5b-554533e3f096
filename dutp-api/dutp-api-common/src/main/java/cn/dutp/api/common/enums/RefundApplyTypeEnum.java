package cn.dutp.api.common.enums;

/**
 * 申请售后类型
 */
public enum RefundApplyTypeEnum {
    PENDING(0, "零售申请"),
    PAID(1, "运营申请"),
    EDITING(2, "教务申请");

    private final Integer code;
    private final String description;

    RefundApplyTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static RefundApplyTypeEnum fromCode(String code) {
        for (RefundApplyTypeEnum status : RefundApplyTypeEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown OrderStatus code: " + code);
    }
}


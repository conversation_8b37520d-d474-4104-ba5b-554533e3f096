package cn.dutp.api.common.enums;

public enum OpenCourseApplyStatusEnum {
    PENDING("1", "课程审核"),
    ADOPT("2", "课程通过"),
    REFUSED("3", "课程驳回");

    private final String code;
    private final String description;

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    OpenCourseApplyStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
}
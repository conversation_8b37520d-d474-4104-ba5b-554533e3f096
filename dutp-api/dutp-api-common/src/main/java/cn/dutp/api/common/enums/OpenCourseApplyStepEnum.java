package cn.dutp.api.common.enums;

public enum OpenCourseApplyStepEnum {
    COURSEAPPLY("1", "课程审核"),
    COURSEAPPLYDETAIL("2", "课程内容审核");

    private final String code;
    private final String description;

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    OpenCourseApplyStepEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
}

package cn.dutp.api.common.enums;

/**
 * 固定角色
 */
public enum OrderStatusEnum {
    PENDING("pending", "订单待确认[待支付]"),
    PAID("paid", "已支付"),
    EDITING("editing", "修改待确认"),
    CANCELING("canceling", "作废待确认"),
    CREATE_REFUSED("create_refused", "采购驳回"),
    EDIT_REFUSED("edit_refused", "修改驳回"),
    CANCEL_REFUSED("cancel_refused", "作废驳回"),
    SETTLEMENT("settlement", "结算中"),
    COMPLETED("completed", "已完成（已结算）"),
    CANCELLED("cancelled", "已取消"),
    FIRST_REFUSED("first_refused", "一级审核驳回【样书】"),
    SECOND_REFUSED("second_refused", "二级审核驳回【样书】"),
    SECOND_AUDITTING("second_auditting", "二级审核中【样书】");

    private final String code;
    private final String description;

    OrderStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static OrderStatusEnum fromCode(String code) {
        for (OrderStatusEnum status : OrderStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown OrderStatus code: " + code);
    }
}


package cn.dutp.auth;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import cn.dutp.common.security.annotation.EnableDutpFeignClients;
import org.springframework.context.annotation.ComponentScan;

/**
 * 认证授权中心
 * 
 * <AUTHOR>
 */
@EnableDutpFeignClients
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class })
@ComponentScan(basePackages = "cn.dutp")
public class DutpAuthApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(DutpAuthApplication.class, args);
        System.out.println("认证授权中心启动成功");
    }
}

package cn.dutp.auth.controller;

import cn.dutp.api.common.constant.DutpConstant;
import cn.dutp.api.common.utils.RSAUtils;
import cn.dutp.auth.form.LoginBody;
import cn.dutp.auth.form.RegisterBody;
import cn.dutp.auth.service.SysLoginService;
import cn.dutp.common.core.domain.R;
import cn.dutp.common.core.utils.JwtUtils;
import cn.dutp.common.core.utils.StringUtils;
import cn.dutp.common.redis.service.RedisService;
import cn.dutp.common.security.auth.AuthUtil;
import cn.dutp.common.security.service.TokenService;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.system.api.model.LoginDutpUser;
import cn.dutp.system.api.model.LoginUser;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.zxing.WriterException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.awt.*;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * token 控制
 *
 * <AUTHOR>
 */
@RestController
public class TokenController {
    @Autowired
    private TokenService tokenService;

    @Autowired
    private SysLoginService sysLoginService;

    @Autowired
    private RSAUtils rsaUtils;

    @Autowired
    public RedisService redisService;

    @GetMapping("/publicKey")
    public String getPublicKey() {
        try {
            // 返回公钥字符串
            return rsaUtils.getPublicKeyString();
        } catch (Exception e) {
            return "公钥获取失败";
        }
    }

    @PostMapping("login")
    public R<?> login(@RequestBody LoginBody form) {
        String entrance = "sys";
        // 用户登录
        LoginUser userInfo = sysLoginService.login(form.getUsername(), form.getPassword(), entrance);
        // 获取登录token
        return R.ok(tokenService.createToken(userInfo, entrance));
    }

    /**
     * 作者编辑端账号密码登录
     *
     * @param form
     * @return
     */
    @PostMapping("autLogin")
    public R<?> autLogin(@RequestBody LoginBody form) {
        String entrance = "aut";
        // 用户登录
        LoginUser userInfo = sysLoginService.login(form.getUsername(), form.getPassword(), entrance);
        // 获取登录token
        return R.ok(tokenService.createToken(userInfo, entrance));
    }

    @PostMapping("eduLogin")
    public R<?> eduLogin(@RequestBody LoginBody form) {
        String entrance = "edu";
        // 用户登录
        LoginUser userInfo = sysLoginService.login(form.getUsername(), form.getPassword(), entrance);
        // 获取登录token
        return R.ok(tokenService.createToken(userInfo, entrance));
    }

    @PostMapping("codeLogin")
    public R<?> codeLogin(@RequestBody LoginBody form) {
        String entrance = "sys";
        // 用户登录
        LoginUser userInfo = sysLoginService.codeLogin(form.getPhoneNumber(), form.getCode(), entrance);
        // 获取登录token
        return R.ok(tokenService.createToken(userInfo, entrance));
    }

    @PostMapping("eduCodeLogin")
    public R<?> eduCodeLogin(@RequestBody LoginBody form) {
        String entrance = "edu";
        // 用户登录
        LoginUser userInfo = sysLoginService.codeLogin(form.getPhoneNumber(), form.getCode(), "edu");
        // 获取登录token
        return R.ok(tokenService.createToken(userInfo, entrance));
    }

    @PostMapping("codeLoginEducation")
    public R<?> codeLoginEducation(@RequestBody LoginBody form) {
        // 用户登录
        LoginUser userInfo = sysLoginService.codeLoginEducation(form.getPhoneNumber(), form.getCode());
        // 获取登录token
        return R.ok(tokenService.createToken(userInfo, "stu"));
    }

    @PostMapping("/loginEducation")
    public R<?> loginEducation(@RequestBody Map<String, String> request) throws Exception {
//        String encryptedData = request.get("encryptedData");
//        if (encryptedData == null) {
//            return R.fail("登录失败");
//        }
//        String decryptedStr = rsaUtils.decrypt(encryptedData);
        if (StringUtils.isNotNull(request.get("loginForm"))) {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(request.get("loginForm"));
            LoginBody form = new LoginBody();
            form.setUsername(jsonNode.get("username").asText());
            form.setPassword(jsonNode.get("password").asText());
            // 用户登录
            LoginDutpUser userInfo = sysLoginService.loginEducation(form.getUsername(), form.getPassword());
            // 获取登录token
            return R.ok(tokenService.createToken(userInfo));
        }
        return R.fail("登录失败");
    }

    @DeleteMapping("logout")
    public R<?> logout(HttpServletRequest request) {
        String token = SecurityUtils.getToken(request);
        if (StringUtils.isNotEmpty(token)) {
            String username = JwtUtils.getUserName(token);
            // 删除用户缓存记录
            AuthUtil.logoutByToken(token);
            // 记录用户退出日志
            sysLoginService.logout(username, "sys");
        }
        return R.ok();
    }

    @DeleteMapping("eduLogout")
    public R<?> eduLogout(HttpServletRequest request) {
        String token = SecurityUtils.getToken(request);
        if (StringUtils.isNotEmpty(token)) {
            String username = JwtUtils.getUserName(token);
            // 删除用户缓存记录
            AuthUtil.logoutByToken(token);
            // 记录用户退出日志
            sysLoginService.logout(username, "edu");
        }
        return R.ok();
    }

    @DeleteMapping("logoutEducation")
    public R<?> logoutEducation(HttpServletRequest request) {
        String token = SecurityUtils.getToken(request);
        if (StringUtils.isNotEmpty(token)) {
            String username = JwtUtils.getUserName(token);
            // 删除用户缓存记录
            AuthUtil.logoutByToken(token);
            // 记录用户退出日志
            sysLoginService.logoutEducation(username);
        }
        return R.ok();
    }

    @PostMapping("refresh")
    public R<?> refresh(HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (StringUtils.isNotNull(loginUser)) {
            // 刷新令牌有效期
            tokenService.refreshToken(loginUser);
            return R.ok();
        }
        return R.ok();
    }

    @PostMapping("register")
    public R<?> register(@RequestBody RegisterBody registerBody) {
        // 用户注册
        sysLoginService.register(registerBody.getUsername(), registerBody.getPassword());
        return R.ok();
    }

    @PostMapping("registerEducation")
    public R<?> registerEducation(@RequestBody RegisterBody registerBody) {
        // 用户注册
        sysLoginService.registerEducation(registerBody.getUsername(), registerBody.getPhoneNumber(), registerBody.getPassword(), registerBody.getCode());
        return R.ok();
    }

    @PostMapping("forgotPasswordEducation")
    public R<?> forgotPasswordEducation(@RequestBody RegisterBody registerBody) {
        // 用户注册
        sysLoginService.forgotPasswordEducation(registerBody.getUsername(), registerBody.getPhoneNumber(), registerBody.getPassword(), registerBody.getCode());
        return R.ok();
    }

    @PostMapping("/decrypt")
    public R<String> decryptData(@RequestBody Map<String, String> request) throws Exception {

        String encryptedData = request.get("encryptedData");

        if (encryptedData == null) {
            return R.fail();
        }

        // 解密数据
        String decryptedStr = rsaUtils.decrypt(encryptedData);

        return R.ok(decryptedStr);
    }

    @GetMapping(value = "/createQrCode")
    public R<?> createQrCode(@RequestParam("qrLogUrl") String qrLogUrl) throws IOException, WriterException {
        // 生成随机的UUID
        String uuid =  UUID.randomUUID().toString();

        // 使用生成的UUID生成二维码图片的base64字符串
        String qrCode = sysLoginService.createQrCode(uuid,200,200,
                qrLogUrl,
                new Color(9, 102, 180),
                Color.WHITE);
        // 保存UUID及UUID的状态到redis
        redisService.setCacheMapValue(DutpConstant.QR_CODE_UUID+uuid,"status", DutpConstant.NOT_SCAN);
        redisService.expire(DutpConstant.QR_CODE_UUID+uuid,5, TimeUnit.MINUTES);
        // 保存UUID和base64格式信息的二维码
        redisService.setCacheObject(qrCode, uuid,5L, TimeUnit.MINUTES);
        return R.ok(qrCode, "二维码生成");
    }
    @PostMapping("/queryIsScannedOrVerified")
    public R<?>  queryIsScannedOrVerified(@RequestBody Map<String, String> requestBody){
        String img = requestBody.get("img");
        // 前端传过来的base64信息在redis查询对应的UUID
        String base64Url = img.replaceAll(" ","+");
        //通过uuid作为键名查询UUID
        String uuid =  redisService.getCacheObject(base64Url);
        // 查询UUID对应的状态并返回给前端
        String s = redisService.getCacheMapValue(DutpConstant.QR_CODE_UUID+uuid,"status");
        JSONObject data = new JSONObject();
        data.put("status",s);
        //如果此时UUID的状态为已验证的话，就查询出uuid对应绑定的用户信息，并返回给前端
        if("VERIFIED".equals(s)){
            String str = redisService.getCacheMapValue(DutpConstant.QR_CODE_UUID+uuid,"info");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(str);
            data.put("info",object);
            data.put("access_token",redisService.getCacheMapValue(DutpConstant.QR_CODE_UUID+uuid,"access_token"));
            return object != null ? R.ok(data, "授权成功") : R.fail("授权失败");
        }else{
            return R.ok(data, "查询成功");
        }
    }
    @GetMapping("/queryuuidVerified")
    public  R<?>  queryuuidVerified(@RequestParam("uuid")String uuid){
        // 前端传过来的UUID
        String s = redisService.getCacheMapValue(DutpConstant.QR_CODE_UUID+uuid,"status");
        return R.ok(s, "查询成功");
    }
    /**
     * app扫描接口
     * @param uuid
     * @return
     */
    @GetMapping("/doScan")
    public R<?>  doAppScanQrCode(@RequestParam("uuid")String uuid){
        //查询二维码当前状态，如果查询为空，二维码就已经过期，告诉app二维码已经过期
        String status = redisService.getCacheMapValue(DutpConstant.QR_CODE_UUID+uuid,"status");
        if (status != null && status.isEmpty()) return R.fail("UUID状态查询出错");
        if (status != null) {
            switch (status){
                case "NOT_SCAN":
                    //修改二维码状态为已扫描SCAND
                    redisService.setCacheMapValue(DutpConstant.QR_CODE_UUID+uuid,"status",DutpConstant.SCANNED);
                    redisService.expire(DutpConstant.QR_CODE_UUID+uuid,5,TimeUnit.MINUTES);
                    return R.ok("扫码成功");
                case "SCANNED":
                    return R.fail("二维码已扫描");
                case "VERIFIED":
                    return R.fail("已验证二维码");
            }
        }
        return R.fail("二维码验证错误");
    }

    /**
     * app确认登录接口
     * @param uuid
     * @return
     */
    @GetMapping("/verify")
    public R<?> verifyQrCode(@RequestParam("uid") String uid, @RequestParam("token") String token, @RequestParam("uuid") String uuid) throws Exception {
        //去除当前二维码的状态
        String status = redisService.getCacheMapValue(DutpConstant.QR_CODE_UUID+uuid,"status");
        if (status != null && status.isEmpty()) return R.fail("二维码验证失败");
        // 使用 token 获取用户信息
        LoginUser loginUser = tokenService.getLoginUser(token);
        // 用户登录
        R<LoginDutpUser> res = sysLoginService.scanCodeToLoginEducation(loginUser.getSysUser().getUserName(),loginUser.getSysUser().getPassword());
        Map<String, Object> map =  tokenService.createToken(res.getData());
        if(res.getCode() == 200 && ObjectUtil.isNotEmpty(map.get("access_token"))){
            // 将二维码绑定的uuid状态修改为已验证
            redisService.setCacheMapValue(DutpConstant.QR_CODE_UUID+uuid,"info",JSON.toJSONString(res.getData()));
            redisService.setCacheMapValue(DutpConstant.QR_CODE_UUID+uuid,"access_token",map.get("access_token"));
            redisService.setCacheMapValue(DutpConstant.QR_CODE_UUID+uuid,"status",DutpConstant.VERIFIED);
            redisService.expire(DutpConstant.QR_CODE_UUID+uuid,5,TimeUnit.MINUTES);
            // 获取登录token
            return R.ok("授权成功");
        }else{
            return R.fail("授权失败");
        }
    }


}

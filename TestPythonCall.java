import java.io.*;
import java.util.*;
import java.util.concurrent.TimeUnit;

public class TestPythonCall {
    public static void main(String[] args) {
        try {
            // Test Python script call
            String pythonScriptPath = "textbook_parser_fixed.py";
            String pdfPath = "C:\\Users\\<USER>\\Desktop\\222222.pdf";

            List<String> command = Arrays.asList(
                "python",
                pythonScriptPath,
                pdfPath
            );

            System.out.println("Executing Python command: " + String.join(" ", command));

            ProcessBuilder pb = new ProcessBuilder(command);
            pb.directory(new File("."));
            pb.redirectErrorStream(true);

            Process process = pb.start();

            // Read output
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), "UTF-8"))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }

            // Wait for process completion
            boolean finished = process.waitFor(300, TimeUnit.SECONDS);
            if (!finished) {
                process.destroyForcibly();
                throw new RuntimeException("Python script execution timeout");
            }

            int exitCode = process.exitValue();
            if (exitCode != 0) {
                throw new RuntimeException("Python script execution failed, exit code: " + exitCode + "\nOutput: " + output.toString());
            }

            String result = output.toString().trim();
            System.out.println("Python script executed successfully!");
            System.out.println("Output length: " + result.length());

            // Show first 500 characters
            if (result.length() > 500) {
                System.out.println("First 500 characters: " + result.substring(0, 500) + "...");
            } else {
                System.out.println("Complete output: " + result);
            }

        } catch (Exception e) {
            System.err.println("Test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
